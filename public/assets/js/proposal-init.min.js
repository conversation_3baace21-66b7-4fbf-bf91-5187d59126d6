"use strict";$(document).ready(function(){$(".progress-1").circleProgress({max:100,value:50,textFormat:function(){return"$450 USD"}}),$(".progress-2").circleProgress({max:100,value:60,textFormat:function(){return"$550 USD"}}),$(".progress-3").circleProgress({max:100,value:70,textFormat:function(){return"$850 USD"}}),$(".progress-4").circleProgress({max:100,value:80,textFormat:function(){return"$900 USD"}})}),$(document).ready(function(){$("#proposalList").DataTable({pageLength:10,lengthMenu:[10,20,50,100,200,500]})}),$(document).ready(function(){$("#checkAllProposal").change(function(){this.checked?$(".checkbox").each(function(){this.checked=!0,$(this).parent().parent().parent().parent().addClass("selected")}):$(".checkbox").each(function(){this.checked=!1,$(this).parent().parent().parent().parent().removeClass("selected")})}),$(".checkbox").click(function(){var e;$(this).is(":checked")?(e=0,$(".checkbox").each(function(){this.checked||(e=1)}),0==e&&$("#checkAllProposal").prop("checked",!0)):$("#checkAllProposal").prop("checked",!1)}),$(".items-wrapper").on("click","input:checkbox",function(){$(this).closest(".single-items").toggleClass("selected",this.checked)}),$(".items-wrapper input:checkbox:checked").closest(".single-items").addClass("selected")}),$(document).ready(function(){new Quill('[data-editor-target="editor"]',{placeholder:"Compose an epic...",theme:"snow"})}),$(document).ready(function(){$('[data-alert-target="alertMessage"]').click(function(e){e.preventDefault();const t=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You want to sent this Proposal!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, sent it!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.value?t.fire("Sent!","Proposal sent successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})})});
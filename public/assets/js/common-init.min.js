"use strict";$(window).resize(function(){var e=$(this);1024<=e.width()&&($("html").addClass("minimenu"),$(".logo-full").hide(),$(".logo-abbr").show(),$("#menu-mini-button").hide(),$("#menu-expend-button").show()),1600<e.width()&&($("html").removeClass("minimenu"),$(".logo-full").show(),$(".logo-abbr").hide(),$("#menu-mini-button").show(),$("#menu-expend-button").hide())}),$(document).ready(function(){$("#nxl-lavel-mega-menu-open").on("click",function(e){e.preventDefault(),$("html").addClass("nxl-lavel-mega-menu-open")}),$("#nxl-lavel-mega-menu-hide").on("click",function(e){e.preventDefault(),$("html").removeClass("nxl-lavel-mega-menu-open")})}),$(document).ready(function(){$(".nxl-mega-menu-sm").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-sm").removeClass("nxl-mega-menu-md nxl-mega-menu-lg nxl-mega-menu-xl nxl-mega-menu-xxl nxl-mega-menu-full")}),$(".nxl-mega-menu-md").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-md").removeClass("nxl-mega-menu-sm nxl-mega-menu-lg nxl-mega-menu-xl nxl-mega-menu-xxl nxl-mega-menu-full")}),$(".nxl-mega-menu-lg").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-lg").removeClass("nxl-mega-menu-sm nxl-mega-menu-md nxl-mega-menu-xl nxl-mega-menu-xxl nxl-mega-menu-full")}),$(".nxl-mega-menu-xl").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-xl").removeClass("nxl-mega-menu-sm nxl-mega-menu-md nxl-mega-menu-lg nxl-mega-menu-xxl nxl-mega-menu-full")}),$(".nxl-mega-menu-xxl").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-xxl").removeClass("nxl-mega-menu-sm nxl-mega-menu-md nxl-mega-menu-lg nxl-mega-menu-xl nxl-mega-menu-full")}),$(".nxl-mega-menu-full").on("click",function(e){e.preventDefault(),$("#mega-menu-dropdown").addClass("nxl-mega-menu-full").removeClass("nxl-mega-menu-sm nxl-mega-menu-md nxl-mega-menu-lg nxl-mega-menu-xl nxl-mega-menu-xxl")})}),$(function(){$("#menu-mini-button").on("click",function(){$("#menu-mini-button").hide(),$("#menu-expend-button").show(),$("html").addClass("minimenu"),localStorage.setItem("nexel-classic-dashboard-menu-mini-theme","menu-mini-theme")}),$("#menu-expend-button").on("click",function(){$("#menu-expend-button").hide(),$("#menu-mini-button").show(),$("html").removeClass("minimenu"),localStorage.setItem("nexel-classic-dashboard-menu-mini-theme","menu-expend-theme")})}),$(document).on("click",".search-form-open-toggle",function(e){e.preventDefault(),$(".search-form").addClass("search-form-active").show(300)}),$(document).on("click",".search-form-close-toggle",function(e){e.preventDefault(),$(".search-form").removeClass("search-form-active").hide(300)}),$(document).ready(function(){$(".app-sidebar-open-trigger").on("click",function(e){e.preventDefault(),$(".content-sidebar").addClass("app-sidebar-open")}),$(".app-sidebar-close-trigger").on("click",function(e){e.preventDefault(),$(".content-sidebar").removeClass("app-sidebar-open")})}),$(document).on("click",".page-header-right-open-toggle",function(e){e.preventDefault(),$(".page-header-right-items").addClass("page-header-right-open").removeClass("page-header-right-close")}),$(document).on("click",".page-header-right-close-toggle",function(e){e.preventDefault(),$(".page-header-right-items").addClass("page-header-right-close").removeClass("page-header-right-open")}),$(document).on("click",".page-content-left-open-toggle",function(e){e.preventDefault(),$(".page-content-left-sidebar-wrapper").addClass("page-content-left-open").removeClass("page-content-left-close")}),$(document).on("click",".page-content-left-close-toggle",function(e){e.preventDefault(),$(".page-content-left-sidebar-wrapper").addClass("page-content-left-close").removeClass("page-content-left-open")}),$(document).on("click",".page-content-right-open-toggle",function(e){e.preventDefault(),$(".page-content-right-sidebar-wrapper").addClass("page-content-right-open").removeClass("page-content-right-close")}),$(document).on("click",".page-content-right-close-toggle",function(e){e.preventDefault(),$(".page-content-right-sidebar-wrapper").addClass("page-content-right-close").removeClass("page-content-right-open")}),$(document).ready(function(){$(".file-download").on("click",function(e){e.preventDefault(),$("#toast").toast("show")})}),$(document).ready(function(){var e={init:function(){this.initComponent()},initComponent:function(){$(document).on("click","[data-bs-toggle=refresh]",function(e){e.preventDefault();var n,t=$(this).closest(".card");$(t).hasClass("card-loading")||(n=$(t).find(".custom-card-action"),e='<div class="card-loader"><div class="spinner-border '+($(this).attr("data-spinner-class")?$(this).attr("data-spinner-class"):"text-primary")+'"></div></div>',$(t).addClass("card-loading"),(0!==$(n).length?$(n):$(t)).append(e),setTimeout(function(){$(t).removeClass("card-loading"),$(t).find(".card-loader").remove()},3e3))}),$(document).on("click","[data-bs-toggle=expand]",function(e){e.preventDefault();var n=$(this).closest(".card"),t=$(n).find(".custom-card-action"),a="card-expand";0!==$(t).length&&(e=$(n).offset().top,$(t).offset().top),$("body").hasClass(a)&&$(n).hasClass(a)?($("body, .card").removeClass(a),$(".card").removeAttr("style"),$(t).removeAttr("style")):($("body").addClass(a),$(this).closest(".card").addClass(a)),$(window).trigger("resize")}),$(document).on("click","[data-bs-toggle=remove]",function(e){e.preventDefault(),$(this).tooltip("dispose"),$(this).closest(".card").remove()})}};$(document).ready(function(){e.init()})}),$(document).ready(function(){$('[data-scrollbar-target="#psScrollbarInit"]').each(function(){new PerfectScrollbar($(this)[0],{useBothWheelAxes:!1,suppressScrollX:!0})})}),$(document).ready(function(){$(".successAlertMessage").click(function(e){e.preventDefault(),Swal.mixin({toast:!0,position:"top-end",showConfirmButton:!1,timer:3e3,timerProgressBar:!0,onOpen:function(e){e.addEventListener("mouseenter",Swal.stopTimer),e.addEventListener("mouseleave",Swal.resumeTimer)}}).fire({icon:"success",title:"Action Execute Successfully"})})}),$(document).ready(function(){$("#checkAll").change(function(){this.checked?$(".checkbox").each(function(){this.checked=!0,$(this).parent().parent().parent().parent().addClass("selected")}):$(".checkbox").each(function(){this.checked=!1,$(this).parent().parent().parent().parent().removeClass("selected")})}),$(".checkbox").click(function(){var e;$(this).is(":checked")?(e=0,$(".checkbox").each(function(){this.checked||(e=1)}),0==e&&$("#checkAll").prop("checked",!0)):$("#checkAll").prop("checked",!1)}),$(".items-wrapper").on("click","input:checkbox",function(){$(this).closest(".single-items").toggleClass("selected",this.checked)}),$(".items-wrapper input:checkbox:checked").closest(".single-items").addClass("selected")}),document.addEventListener("DOMContentLoaded",function(){992<window.innerWidth&&document.querySelectorAll(".nxl-header .header-wrapper .dropdown").forEach(function(e){e.addEventListener("mouseover",function(e){var n,t=this.querySelector("a[data-bs-toggle]");null!=t&&(n=t.nextElementSibling,t.classList.add("show"),n.classList.add("show"))}),e.addEventListener("mouseleave",function(e){var n,t=this.querySelector("a[data-bs-toggle]");null!=t&&(n=t.nextElementSibling,t.classList.remove("show"),n.classList.remove("show"))})})});
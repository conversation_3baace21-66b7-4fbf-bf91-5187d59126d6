"use strict";$(document).ready(function(){$(".file-upload").on("change",function(){var e,t;(e=this).files&&e.files[0]&&((t=new FileReader).onload=function(e){$(".upload-pic").attr("src",e.target.result)},t.readAsDataURL(e.files[0]))}),$(".upload-button").on("click",function(){$(".file-upload").click()})}),$(document).ready(function(){var e=document.getElementById("issueDate"),t=document.getElementById("dueDate");new Datepicker(e,{clearBtn:!0,allowOneSidedRange:!0}),new Datepicker(t,{clearBtn:!0,allowOneSidedRange:!0})}),$(document).ready(function(){new Cleave(".input-credit-card",{creditCard:!0,onCreditCardTypeChanged:function(e){console.log(e);e=$(".input-credit-card-type").find("."+e);(e.length?(e.addClass("text-primary"),e.siblings()):$(".input-credit-card-type div")).removeClass("text-primary")}}),new Cleave(".input-date-formatting",{date:!0,datePattern:["m","Y"]}),new Cleave(".input-Blocks-formatting",{blocks:[3]})});
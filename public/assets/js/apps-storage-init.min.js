"use strict";$(document).ready(function(){$("#storageSearch").bind("keyup",function(){var e=$(this).val().toLowerCase();$(".content-area-body .card").filter(function(){$(this).toggle(-1<$(this).text().toLowerCase().indexOf(e))})})}),$(document).ready(function(){$('[data-action-target="#fileFolderDeleteAction"]').click(function(e){e.preventDefault();const t=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You want to delete this file or folder!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.value?t.fire("Deleted!","File or Folder Delete Successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})})});
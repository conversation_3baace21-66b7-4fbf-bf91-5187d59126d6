"use strict";$(document).ready(function(){$("#project-type").validate(),$("#project-details").validate(),$("#project-settings").validate(),$("#project-budgets").validate(),$("#project-create-steps").steps({headerTag:"div.step-title",bodyTag:"section.step-body",transitionEffect:"slideLeft",titleTemplate:"#title#",enableAllSteps:!0,onStepChanging:function(e,t,n){return n<t||1!=(n=$(".body.current form")).length||(n.validate().settings.ignore=":disabled,:hidden",n.valid())},onFinishing:function(e,t){var n=$(".body.current form");return 1!=n.length||(n.validate().settings.ignore=":disabled",n.valid())},onFinished:function(e,t){const n=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});n.fire({title:"Created & Finished",text:"You want to create and finish this project!!!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, Create it!",cancelButtonText:"No, cancel!",reverseButtons:!0,type:"success"}).then(e=>{e.value?n.fire("Project Created!","Project Created Successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&n.fire("Cancelled","Your imaginary file is safe :)","error")})}}),$("#projectClient, #tragetAssigned").select2({theme:"bootstrap-5",templateResult:userformat,templateSelection:userformat}),$("#billingType, #projectStatus, #projectTags, #tragetTags, [data-select2-teammates='teammates']").select2({theme:"bootstrap-5",templateResult:bgformat,templateSelection:bgformat}),$("#sendcontactsNotifications").select2({theme:"bootstrap-5",templateResult:iformat,templateSelection:iformat})}),$(document).ready(function(){new Quill("#projectDescription",{placeholder:"Compose an epic...",theme:"snow"}),new Quill("#targetDescription",{placeholder:"Compose an epic...",theme:"snow"})}),$(document).ready(function(){var e=document.getElementById("projectReleaseDate"),t=document.getElementById("targetReleaseDate");new Datepicker(e,{clearBtn:!0,allowOneSidedRange:!0}),new Datepicker(t,{clearBtn:!0,allowOneSidedRange:!0})}),$(document).ready(function(){$("#choose-file").change(function(){$(this).prev("label").clone();var e=$("#choose-file")[0].files[0].name;$(this).prev("label").text(e)})});
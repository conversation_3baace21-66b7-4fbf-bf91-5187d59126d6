"use strict";$(document).ready(function(){new Quill('[data-editor-target="editor"]',{placeholder:"Compose an epic...",theme:"snow"})}),$(document).ready(function(){$('[data-alert-target="#alertMessage"]').click(function(e){e.preventDefault();const t=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You want to sent this Proposal!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, sent it!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.value?t.fire("Sent!","Proposal sent successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})})}),$(document).ready(function(){var e=document.getElementById("startDate"),t=document.getElementById("dueDate");new Datepicker(e,{clearBtn:!0,allowOneSidedRange:!0}),new Datepicker(t,{clearBtn:!0,allowOneSidedRange:!0})});
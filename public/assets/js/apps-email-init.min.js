"use strict";$(document).ready(function(){$("#emailSearch").bind("keyup",function(){var e=$(this).val().toLowerCase();$(".single-items").filter(function(){$(this).toggle(-1<$(this).text().toLowerCase().indexOf(e))})})}),$(document).on("change","[data-checked-action='show-options']",function(){$(this).prop("checked")?$(".page-header-left").addClass("show-action"):$(".page-header-left").removeClass("show-action")}),$(document).on("click","[data-view-toggle='details']",function(){$(".content-area").addClass("items-details-active")}),$(document).on("click",".item-info-close",function(){$(".content-area").removeClass("items-details-active")}),$(document).ready(function(){new Quill("#mailEditorModal",{placeholder:"Compose an epic...@mention, #tag",theme:"snow"}),new Quill("#mailEditor",{placeholder:"Compose an epic...@mention, #tag",theme:"snow"})}),$(document).ready(function(){$("#ccbccToggleModal").click(function(e){e.preventDefault(),$("#ccbccToggleModalFileds").toggle("1000")}),$("#ccbccToggleContent").click(function(e){e.preventDefault(),$("#ccbccToggleContentFileds").toggle("1000")})}),$(document).ready(function(){$('[data-action-target="#mailActionMessage"]').click(function(e){e.preventDefault();const t=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You want to sent this message!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, sent it!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.value?t.fire("Sent!","Mail Sent Successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})}),$('[data-action-target="#mailDeleteMessage"]').click(function(e){e.preventDefault();const t=Swal.mixin({customClass:{confirmButton:"btn btn-success m-1",cancelButton:"btn btn-danger m-1"},buttonsStyling:!1});t.fire({title:"Are you sure?",text:"You want to delete this message!",icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete!",cancelButtonText:"No, cancel!",reverseButtons:!0}).then(e=>{e.value?t.fire("Deleted!","Mail Delete Successfully.","success"):e.dismiss===Swal.DismissReason.cancel&&t.fire("Cancelled","Your imaginary file is safe :)","error")})})});
/*
<--!----------------------------------------------------------------!-->
<--! Common SCSS Start !-->
<--!----------------------------------------------------------------!-->
*/
.nxl-container {
	position: relative;
	top: $header-height;
	margin-left: $navigation-width;
	min-height: calc(100vh - #{$header-height});
	transition: all 0.3s ease;

	.nxl-content {
		&.without-header {
			padding-top: 0px;
		}

		&.nxl-full-content {
			.main-content {
				padding: 0px;
			}
		}

		.main-content {
			overflow-x: hidden;
			padding: 30px 30px 5px;

			.content-sidebar,
			.content-area {
				width: 100%;
				height: 100vh;
				position: relative;
			}

			.content-sidebar {
				overflow-y: auto;
				background-color: $white;
				border-right: 1px solid $border-color;

				.content-sidebar-header {
					height: 75px;
					padding: 25px 30px;
					border-bottom: 1px solid $border-color;
					display: flex;
					align-items: center;
				}
			}

			.content-area {
				.content-area-header {
					height: 75px;
					padding: 25px 30px;
					background-color: $white;
					border-bottom: 1px solid $border-color;
					gap: 15px;
					display: flex;
					align-items: center;

					.search-form {
						&.search-form-active {
							display: block;
						}

						.search-form-inner {
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							gap: 25px;
							z-index: 1;
							width: 100%;
							display: flex;
							padding: 0 25px;
							position: absolute;
							align-items: center;
							background-color: $white;
						}
					}
				}

				.content-area-body {
					padding: 30px 30px;
				}
			}
		}
	}

	.page-header+.nxl-content {
		padding-top: calc(30px + 65px);
	}

	.page-header {
		.page-header-right {
			.reportrange-picker {
				cursor: pointer;
				width: max-content;

				.reportrange-picker-field {
					color: $brand-dark;
					padding: 10px 15px;
					font-size: $font-12;
					font-weight: $font-500;
					text-transform: $font-uppercase;
					border: 1px solid $border-color-2;
					border-radius: $radius-sm;
				}
			}
		}

		.filter-btn {
			cursor: pointer;
			gap: 10px;
			display: flex;
			align-items: center;
			padding: 9px 15px 8px;
			border-radius: $radius-sm;
			border: 1px solid $border-color-2;
		}
	}

	.page-header-collapse {
		.accordion-body {
			padding: 30px 30px;
			background-color: $white;
			border-bottom: 1px solid $border-color;
		}
	}

	.footer {
		padding: 20px 30px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: $white;
		border-top: 1px solid $border-color;
	}
}

.minimenu {
	.nxl-container {
		margin-left: $navigation-collapsed-width;
	}

	.page-header {
		left: $navigation-collapsed-width;
	}
}

.page-header {
	display: flex;
	align-items: center;
	top: $header-height;
	left: $navigation-width;
	right: 0;
	z-index: 1023;
	min-height: 65px;
	padding: 0 30px;
	background: $white;
	box-shadow: $card-shadow;

	.page-block {
		width: calc(100% - 50%);
	}

	.page-header-title {
		display: inline-block;
	}

	h5 {
		margin-bottom: 0;
		margin-right: 8px;
		padding-right: 10px;
		color: $brand-dark;
		font-weight: $font-600;
		border-right: 1px solid darken($body-bg, 10%);
	}

	.breadcrumb {
		padding: 0;
		display: inline-flex;
		margin-bottom: 0;
		background: transparent;
		font-size: 13px;
		color: $brand-muted;
		font-weight: $font-800;

		a {
			color: $brand-dark;
		}

		.breadcrumb-item {
			a:hover {
				color: $primary;
			}

			+.breadcrumb-item::before {
				position: relative;
				top: 2px;
			}

			&:last-child {
				opacity: 0.75;
				font-weight: $font-400;
			}
		}

		svg {
			width: 14px;
			height: 14px;
			vertical-align: baseline;
		}
	}
}

.upgrade-card {
	.card-body {
		padding-bottom: 100px;
		z-index: 1;
		position: relative;
	}

	>.container {
		z-index: 5;
		position: relative;
		margin-top: -60px;
	}

	.up-price-table {
		td {
			border-color: $body-bg;
		}

		th {
			border: none;

			+th {
				border-radius: 5px 5px 0 0;
				padding: 20px 0;
			}
		}

		tbody {
			tr {
				&:first-child td {
					border-top: none;
				}
			}
		}

		td,
		th {
			text-align: right;

			+td,
			+th {
				text-align: center;
			}

			+td {
				border-left: 1px solid $body-bg;
			}
		}
	}
}

// Burgur menu start
.hamburger {
	display: inline-block;
	cursor: pointer;
	transition: filter 0.15s linear;
	width: 20px;
	height: 20px;

	&.is-active {
		.hamburger-inner {
			background-color: #000;

			&::after,
			&::before {
				background-color: #000;
			}
		}
	}
}

.hamburger-inner {
	display: block;
	top: 50%;
	margin-top: -2px;
	width: 18px;
	height: 2px;
	background-color: $header-color;
	border-radius: 4px;
	position: absolute;
	transition: transform 0.15s ease;

	&::after,
	&::before {
		width: 18px;
		height: 2px;
		background-color: $header-color;
		border-radius: 4px;
		position: absolute;
		transition: transform 0.15s ease;
	}

	&::after,
	&::before {
		content: "";
		display: block;
	}

	&::before {
		top: -6px;
	}

	&::after {
		bottom: -6px;
	}
}

.hamburger--arrowturn.is-active .hamburger-inner {
	transform: rotate(-180deg);
}

.hamburger--arrowturn.is-active .hamburger-inner::before {
	transform: translate3d(4px, 2px, 0) rotate(45deg) scale(0.7, 1);
}

.hamburger--arrowturn.is-active .hamburger-inner::after {
	transform: translate3d(4px, -2px, 0) rotate(-45deg) scale(0.7, 1);
}

// Burgur menu End
.ps {
	touch-action: auto;
	overflow-anchor: none;
	overflow: hidden !important;
}

.ps__rail-x {
	display: none;
	opacity: 0;
	height: 15px;
	bottom: 0;
	position: absolute;
	transition: background-color 0.2s linear, opacity 0.2s linear;
}

.ps__rail-y {
	right: 0;
	opacity: 0;
	width: 15px;
	display: none;
	position: absolute;
	transition: background-color 0.2s linear, opacity 0.2s linear;
}

.ps--active-x>.ps__rail-x,
.ps--active-y>.ps__rail-y {
	display: block;
	background-color: transparent;
}

.ps:hover> {

	.ps__rail-x,
	.ps__rail-y {
		opacity: 0.6;
	}
}

.ps--focus> {

	.ps__rail-x,
	.ps__rail-y {
		opacity: 0.6;
	}
}

.ps--scrolling-x>.ps__rail-x,
.ps--scrolling-y>.ps__rail-y {
	opacity: 0.6;
}

.ps {

	.ps__rail-x.ps--clicking,
	.ps__rail-x:focus,
	.ps__rail-x:hover,
	.ps__rail-y.ps--clicking,
	.ps__rail-y:focus,
	.ps__rail-y:hover {
		opacity: 0.9;
		background-color: #eee;
	}
}

.ps__thumb-x {
	height: 6px;
	bottom: 2px;
	border-radius: 6px;
	position: absolute;
	background-color: #aaa;
	transition: background-color 0.2s linear, height 0.2s ease-in-out;
}

.ps__thumb-y {
	width: 6px;
	right: 2px;
	position: absolute;
	border-radius: 6px;
	background-color: #aaa;
	transition: background-color 0.2s linear, width 0.2s ease-in-out;
}

.ps__rail-x {

	&.ps--clicking .ps__thumb-x,
	&:focus>.ps__thumb-x,
	&:hover>.ps__thumb-x {
		height: 10px;
		background-color: #999;
	}
}

.ps__rail-y {

	&.ps--clicking .ps__thumb-y,
	&:focus>.ps__thumb-y,
	&:hover>.ps__thumb-y {
		width: 10px;
		background-color: #999;
	}
}

@supports (-ms-overflow-style: none) {
	.ps {
		overflow: auto !important;
	}
}

@media screen and (-ms-high-contrast: active),
(-ms-high-contrast: none) {
	.ps {
		overflow: auto !important;
	}
}

.navbar-content {
	position: relative;
}

.ps__rail-y {
	z-index: 5;
}

.ps .ps__rail-x.ps--clicking,
.ps .ps__rail-x:focus,
.ps .ps__rail-x:hover,
.ps .ps__rail-y.ps--clicking,
.ps .ps__rail-y:focus,
.ps .ps__rail-y:hover {
	background: transparent;
}

.ps__thumb-y {
	background: $gray-400;
}

.ps__rail-y.ps--clicking .ps__thumb-y,
.ps__rail-y:focus>.ps__thumb-y,
.ps__rail-y:hover>.ps__thumb-y {
	background: $gray-500;
}

// Common layout css end
/*
<--!----------------------------------------------------------------!-->
<--! Navigarion CSS Start !-->
<--!----------------------------------------------------------------!-->
*/
.nxl-navigation {
	top: 0;
	bottom: 0;
	z-index: 1026;
	position: fixed;
	background: $white;
	width: $navigation-width;
	transition: all 0.3s ease;

	.m-header {
		z-index: 1;
		overflow: hidden;
		position: relative;
		display: flex;
		padding: 15px 30px;
		align-items: center;
		height: $header-height;
		background: $white;
		border-right: 1px solid $border-color;
		border-bottom: 1px solid $border-color;

		.logo-sm {
			display: none;
		}
	}

	.navbar-content {
		padding: 10px 0;
		position: relative;
		border-right: 1px solid $border-color;
		height: calc(100vh - #{$header-height});

		ul {
			list-style: none;
			padding-left: 0;
			margin-bottom: 0;
		}

		.nxl-caption {
			display: block;
			color: $brand-muted;
			font-size: $font-10;
			font-weight: $font-700;
			letter-spacing: 0.07em;
			padding: 15px 20px 10px;
			text-transform: uppercase;

			label {
				margin-bottom: 0;
			}

			span:not(.badge) {
				display: block;
				color: #67758a;
				text-transform: $font-capitalize;

				font: {
					size: 10px;
					weight: 500;
				}
			}
		}

		.nxl-micon {
			margin-right: 12px;
			display: inline-block;
			vertical-align: middle;

			i {
				font-size: 18px;
			}
		}

		.nxl-link {
			display: block;
			line-height: 1.2;
			padding: 10px 15px;
			font-size: $font-13;
			color: $brand-dark;
			font-weight: $font-600;
			transition: all 0.3s ease;

			&.active,
			&:hover {
				text-decoration: none;
				color: $brand-dark;
				transition: all 0.3s ease;

				.nxl-micon {
					i {
						color: $brand-dark;
						transition: all 0.3s ease;
					}
				}
			}
		}

		.nxl-arrow {
			float: right;
			//display: inline-block;
			transition: all 0.2s ease-in-out;
		}

		.nxl-badge {
			margin-left: 8px;
		}

		.nxl-submenu {
			.nxl-link {
				font-size: 0.8rem;
				padding: 10px 20px;
				margin-left: 35px !important;
				margin-right: 5px !important;
			}

			.nxl-submenu {
				.nxl-link {
					margin-left: 45px !important;
				}

				.nxl-submenu {
					.nxl-link {
						margin-left: 55px !important;
					}
				}
			}
		}

		.nxl-item {
			&.disabled {
				a {
					cursor: not-allowed;
					user-select: none;
				}
			}

			&.active,
			&:hover {
				>.nxl-link {
					color: $darken;
					background-color: $brand-light;
					transition: all 0.3s ease;

					.nxl-micon {
						i {
							color: $darken;
							transition: all 0.3s ease;
						}
					}
				}
			}
		}

		.nxl-navbar {
			>.nxl-item {

				&.active,
				&:hover {
					>.nxl-link {
						color: $darken;
						background-color: $brand-light;
						transition: all 0.3s ease;
					}
				}
			}
		}

		.nxl-hasmenu {
			&.nxl-trigger {
				>.nxl-submenu {
					display: block;
				}

				>.nxl-link {
					>.nxl-arrow {
						transform: rotate(90deg);
					}
				}
			}
		}

		.card {
			margin: 20px;
			color: $brand-dark;
			background: $gray-100;
			border-color: $border-color;
		}
	}
}

.nxl-menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 1;
	background: rgba(0, 0, 0, 0.2);
}

html:not(.minimenu) {
	.nxl-navigation {
		.nxl-link {
			margin: 3px 15px;
			border-radius: 5px;
		}

		.nxl-submenu {
			position: relative;

			.nxl-item {
				margin: 3px 10px;
				border-radius: 5px;
			}

			.nxl-submenu {
				>.nxl-item {
					&:before {
						left: 60px;
					}
				}

				.nxl-submenu {
					>.nxl-item {
						&:before {
							left: 80px;
						}
					}
				}
			}
		}
	}
}

html.minimenu {
	.nxl-navigation {
		width: $navigation-collapsed-width;
		transition: all 0.3s ease;

		.logo-sm {
			width: 42px;
			display: block;
			transition: all 0.3s ease;
		}

		.logo-lg,
		.nxl-mtext,
		.nxl-arrow,
		.card {
			display: none;
			transition: all 0.3s ease;
		}

		.navbar-content {
			width: 100px;
			transition: all 0.3s ease;

			.nxl-link {
				margin: 0 20px;
				padding: 12px 20px;
				border-radius: 6px 6px;
			}

			.nxl-hasmenu.nxl-trigger {
				>.nxl-submenu {
					display: none;
				}
			}

			.nxl-caption {
				position: relative;

				&:before {
					top: 0;
					display: flex;
					align-items: center;
					width: 100%;
					height: 100%;
					font-size: 16px;
					padding-left: 20px;
					content: "\e9a4";
					position: absolute;
					font-family: feather !important;
				}

				label {
					display: none;
				}
			}
		}
	}

	/*minimenu-hover-expend*/
	.nxl-navigation {
		&:hover {
			.navbar-content {
				position: absolute !important;
				width: $navigation-width;
				background-color: $white;
				border-right: 1px solid $border-color;
				transition: all 0.3s ease;

				.nxl-caption,
				.nxl-mtext,
				.nxl-arrow,
				.card {
					display: inline-block;
					transition: all 0.3s ease;
				}

				.nxl-link {
					margin: 0 15px;
					margin-bottom: 3px;
					padding: 10px 20px;
				}

				.nxl-submenu {
					.nxl-link {
						margin-bottom: 3px;
						padding-left: 20px;
						margin-left: 50px !important;
						margin-right: 15px !important;
					}

					.nxl-submenu {
						.nxl-link {
							margin-left: 65px !important;
						}

						.nxl-submenu {
							.nxl-link {
								margin-left: 80px !important;
							}
						}
					}
				}

				.nxl-hasmenu.nxl-trigger {
					>.nxl-submenu {
						display: block;
					}
				}

				.nxl-caption {
					&:before {
						content: none;
					}

					label {
						display: block;
					}
				}
			}
		}
	}
}
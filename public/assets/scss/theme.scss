/*!
<--!----------------------------------------------------------------!-->
<--!----------------------------------------------------------------!-->
 * Template Name: Duralux - CRM Admin Dashboard Template
 * Description: Fully Responsive Multipurpose CRM  Admin Dashboard Template.
 * Copyright: WRAPCODERS
 * Author: WRAPCODERS
 * Version: 1.0.0
 * File: theme.min.css
<--!----------------------------------------------------------------!-->
<--!----------------------------------------------------------------!-->
*/

// Import Google Fonts
@import url("https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800;900&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=IBM+Plex+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Josefin+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Maven+Pro:wght@400;500;600;700;800;900&family=Montserrat+Alternates:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Noto+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Quicksand:wght@300;400;500;600;700&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto+Slab:wght@100;200;300;400;500;600;700;800;900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=Work+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap");

// Variables
@import "themes/variables";

// General
@import "themes/components/general";

// Layouts
@import "themes/layouts/nxl-common";
@import "themes/layouts/nxl-header";
@import "themes/layouts/nxl-navigation";
@import "themes/layouts/nxl-sidebar";

// Modals
@import "themes/components/search";
@import "themes/components/language";

// Applications
@import "themes/applications/apps-common";
@import "themes/applications/chat";
@import "themes/applications/email";
@import "themes/applications/tasks";
@import "themes/applications/notes";
@import "themes/applications/calendar";
@import "themes/applications/storage";

// Componants
@import "themes/components/alert";
@import "themes/components/accordion";
@import "themes/components/badge";
@import "themes/components/button";
@import "themes/components/card";
@import "themes/components/dropdown";
@import "themes/components/form";
@import "themes/components/modal";
@import "themes/components/navs-tabs";
@import "themes/components/table";
@import "themes/components/offcanvas";
@import "themes/components/miscellaneous";

// Widgets
@import "themes/widgets/widgets-lists";
@import "themes/widgets/widgets-tables";
@import "themes/widgets/widgets-charts";
@import "themes/widgets/widgets-statistics";
@import "themes/widgets/widgets-miscellaneous";

// Pages
@import "themes/pages/icon-lauouts";
@import "themes/pages/authentication";
@import "themes/pages/dashboard";
@import "themes/pages/analytics";
@import "themes/pages/proposal";
@import "themes/pages/invoice-create";
@import "themes/pages/customers-view";
@import "themes/pages/customers-create";
@import "themes/pages/projects";
@import "themes/pages/report-sales";
@import "themes/pages/report-leads";
@import "themes/pages/report-projects";
@import "themes/pages/report-tmesheets";
@import "themes/pages/help-knowledgebase";

// Pluginss
@import "themes/plugins/pace";
@import "themes/plugins/select2";
@import "themes/plugins/daterange";
@import "themes/plugins/dataTables";
@import "themes/plugins/sweetalert2";
@import "themes/plugins/jauery-steps";
@import "themes/plugins/circle-progress";

/*!
<--!----------------------------------------------------------------!-->
* Customizer CSS
<--!----------------------------------------------------------------!-->
*/
@import "themes/options/theme-options";

/*!
<--!----------------------------------------------------------------!-->
* Responsive CSS
<--!----------------------------------------------------------------!-->
*/
@import "themes/layouts/nxl-responsive";
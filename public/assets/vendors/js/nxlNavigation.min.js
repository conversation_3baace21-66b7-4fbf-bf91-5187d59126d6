"use strict";var hpx,docH,docW,nxl="0";function horizontalmobilemenuclick(){$(window)[0].innerWidth;$(".nxl-navbar li").off("click"),$(".nxl-navbar > li:not(.nxl-caption)").on("click",function(){$(this).children(".nxl-submenu").removeAttr("style"),$(this).hasClass("nxl-trigger")?$(this).removeClass("nxl-trigger"):($("li.nxl-trigger").removeClass("nxl-trigger"),$(this).addClass("nxl-trigger"))}),$(".nxl-navbar > li:not(.nxl-caption) li").on("click",function(n){n.stopPropagation(),$(this).hasClass("nxl-trigger")?$(this).removeClass("nxl-trigger"):($(this).parent(".nxl-submenu").find("li.nxl-trigger").removeClass("nxl-trigger"),$(this).addClass("nxl-trigger"))})}function addscroller(){rmmini(),menuclick(),$(".navbar-content")[0]&&new PerfectScrollbar(".navbar-content",{wheelSpeed:.5,swipeEasing:0,suppressScrollX:!0,wheelPropagation:1,minScrollbarLength:40})}function menuclick(){$(window)[0].innerWidth;$(".nxl-navbar li").off("click"),$("html").hasClass("minimenu")||($(".nxl-navbar li:not(.nxl-trigger) .nxl-submenu").slideUp(),$(".nxl-navbar > li:not(.nxl-caption)").on("click",function(){$(this).hasClass("nxl-trigger")?($(this).removeClass("nxl-trigger"),$(this).children(".nxl-submenu").slideUp("fast")):($("li.nxl-trigger").children(".nxl-submenu").slideUp("fast"),$("li.nxl-trigger").removeClass("nxl-trigger"),$(this).addClass("nxl-trigger"),$(this).children(".nxl-submenu").slideDown("fast"))}),$(".nxl-navbar > li:not(.nxl-caption) li").on("click",function(n){n.stopPropagation(),$(this).hasClass("nxl-trigger")?($(this).removeClass("nxl-trigger"),$(this).children(".nxl-submenu").slideUp("fast")):($(this).parent(".nxl-submenu").find("li.nxl-trigger").children(".nxl-submenu").slideUp("fast"),$(this).parent(".nxl-submenu").find("li.nxl-trigger").removeClass("nxl-trigger"),$(this).addClass("nxl-trigger"),$(this).children(".nxl-submenu").slideDown("fast"))}))}function rmdrp(){$(".nxl-header:not(.nxl-mob-header) .nxl-mob-drp").removeClass("mob-drp-active"),$(".nxl-header:not(.nxl-mob-header) .nxl-md-overlay").remove()}function rmthead(){$(".nxl-header:not(.nxl-mob-header)").removeClass("mob-header-active"),$(".nxl-header:not(.nxl-mob-header) .nxl-md-overlay").remove()}function rmmenu(){$(".nxl-navigation").removeClass("mob-navigation-active"),$(".topbar").removeClass("mob-navigation-active"),$(".nxl-navigation .nxl-menu-overlay").remove(),$(".topbar .nxl-menu-overlay").remove()}function rmovermenu(){$(".nxl-navigation").removeClass("nxl-over-menu-active"),$(".topbar").removeClass("mob-navigation-active"),$(".nxl-navigation .nxl-menu-overlay").remove(),$(".topbar .nxl-menu-overlay").remove()}function rmactive(){$(".nxl-navigation .nxl-navbar li").removeClass("active"),$(".nxl-navigation .nxl-navbar li").removeClass("nxl-trigger"),$(".topbar .dropdown").removeClass("show"),$(".topbar .dropdown-menu").removeClass("show"),$(".nxl-navigation .nxl-menu-overlay").remove(),$(".topbar .nxl-menu-overlay").remove()}function rmmini(){var n=$(window)[0].innerWidth;n<=1024?$("html").hasClass("minimenu")&&($("html").removeClass("minimenu"),nxl="1"):1024<n&&"1"==nxl&&($("html").addClass("minimenu"),nxl="0")}function collapseedge(){var i,o=$(window).height();1024<$(window).width()&&$(".minimenu .nxl-navigation .nxl-submenu .nxl-hasmenu").hover(function(){var n=$(this).children(".nxl-submenu"),e=n.offset(),e=(e.left,e.top),l=(n.width(),n.height()),a=$(window).scrollTop();e+l<=o||(l=e-a,n.addClass("scroll-menu"),n.css("max-height","calc(100vh - "+l+"px)"),i=new PerfectScrollbar(".scroll-menu",{wheelSpeed:.5,swipeEasing:0,suppressScrollX:!0,wheelPropagation:1,minScrollbarLength:40}))},function(){i.destroy(),$(".scroll-menu").removeAttr("style"),$(".scroll-menu").removeClass("scroll-menu")})}$(document).ready(function(){function n(n){try{n.attr("placeholder").length}catch(n){0}0<n.val().length?n.parent(".form-group").addClass("fill"):n.parent(".form-group").removeClass("fill")}setTimeout(function(){$(".loader-bg").fadeOut("slow",function(){$(this).remove()})},400),$("html").hasClass("nxl-horizontal")||addscroller(),$(".nxl-horizontal").hasClass("navbar-overlay")&&addscroller(),$(".hamburger:not(.is-active)").on("click",function(){$(this).hasClass("is-active")?$(this).removeClass("is-active"):$(this).addClass("is-active")}),$("#overlay-menu").on("click",function(){menuclick(),$(".nxl-navigation").hasClass("nxl-over-menu-active")?rmovermenu():($(".nxl-navigation").addClass("nxl-over-menu-active"),$(".nxl-navigation").append('<div class="nxl-menu-overlay"></div>'),$(".nxl-menu-overlay").on("click",function(){rmovermenu(),$(".hamburger").removeClass("is-active")}))}),$("#vertical-nav-toggle").on("click",function(){console.log("123"),($("html").hasClass("minimenu")?($("html").removeClass("minimenu"),menuclick):($("html").addClass("minimenu"),$(".nxl-navbar li:not(.nxl-trigger) .nxl-submenu").removeAttr("style"),collapseedge))()}),$("#mobile-collapse").on("click",function(){$("html").hasClass("nxl-horizontal")||menuclick(),$(".nxl-navigation").hasClass("mob-navigation-active")?rmmenu():($(".nxl-navigation").addClass("mob-navigation-active"),$(".nxl-navigation").append('<div class="nxl-menu-overlay"></div>'),$(".nxl-menu-overlay").on("click",function(){rmmenu(),$(".hamburger").removeClass("is-active")}))}),$(".nxl-horizontal #mobile-collapse").on("click",function(){$(".topbar").hasClass("mob-navigation-active")?rmmenu():($(".topbar").addClass("mob-navigation-active"),$(".topbar").append('<div class="nxl-menu-overlay"></div>'),$(".nxl-menu-overlay").on("click",function(){rmmenu(),$(".hamburger").removeClass("is-active")}))}),$("#header-collapse").on("click",function(){$(".nxl-header:not(.nxl-mob-header)").hasClass("mob-header-active")?rmthead():($(".nxl-header:not(.nxl-mob-header)").addClass("mob-header-active"),$(".nxl-header:not(.nxl-mob-header)").append('<div class="nxl-md-overlay"></div>'),$(".nxl-md-overlay").on("click",function(){rmthead()}))}),$("#headerdrp-collapse").on("click",function(){$(".nxl-header:not(.nxl-mob-header) .nxl-mob-drp").hasClass("mob-drp-active")?rmdrp():($(".nxl-header:not(.nxl-mob-header) .nxl-mob-drp").addClass("mob-drp-active"),$(".nxl-header:not(.nxl-mob-header)").append('<div class="nxl-md-overlay"></div>'),$(".nxl-md-overlay").on("click",function(){rmdrp()}))}),$(".nxl-horizontal .topbar .nxl-navbar>li>a").on("click",function(n){setTimeout(function(){$(this).parents(".dropdown").children(".dropdown-menu").removeAttr("style")},1e3)}),$(".form-v2 .form-control").each(function(){n($(this))}),$(".form-v2 .form-control").on("blur",function(){n($(this))}),$(".form-v2 .form-control").on("focus",function(){$(this).parent(".form-group").addClass("fill")}),$("html").hasClass("nxl-horizontal")&&horizontalmobilemenuclick(),$("html").hasClass("minimenu")&&collapseedge()}),$(".email-more-link").on("click",function(n){$(this).children("span").slideToggle(1)}),$(window).resize(function(){$("html").hasClass("nxl-horizontal")||(rmmini(),menuclick()),$("html").hasClass("nxl-horizontal")&&rmactive()}),$(window).scroll(function(){}),$(window).on("load",function(){[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(n){return new bootstrap.Tooltip(n)}),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map(function(n){return new bootstrap.Popover(n)})}),$(".nxl-navigation .nxl-navbar a").each(function(){var n=window.location.href.split(/[?#]/)[0];this.href==n&&""!=$(this).attr("href")&&($(this).parent("li").addClass("active"),$(this).parent("li").parent().parent(".nxl-hasmenu").addClass("active").addClass("nxl-trigger"),$(this).parent("li").parent().parent(".nxl-hasmenu").parent().parent(".nxl-hasmenu").addClass("active").addClass("nxl-trigger"),$(this).parent("li").parent().parent(".sidelink").addClass("active"),$(this).parents(".nxl-tabcontent").addClass("active"),$("html").hasClass("tab-layout"))&&(n=$(".nxl-tabcontent.active").attr("data-value"),$(".tab-sidemenu > li").removeClass("active"),$('.tab-sidemenu > li > a[data-cont="'+n+'"]').parent("li").addClass("active"))}),$(".tab-sidemenu > ul >li").on("click",function(){var n=$(this).children("a").attr("data-cont");$(".navbar-content .nxl-tabcontent").removeClass("active"),$(".tab-sidemenu > ul > li").removeClass("active"),$(this).addClass("active"),$('.navbar-content .nxl-tabcontent[data-value="'+n+'"]').addClass("active")}),$(".nxl-toggle-sidemenu").click(function(){$(".nxl-toggle-sidemenu").hasClass("active")?$(".nxl-sideoverlay,.page-navigation,.nxl-toggle-sidemenu").removeClass("active"):$(".nxl-sideoverlay,.page-navigation,.nxl-toggle-sidemenu").addClass("active")}),$(".nxl-sideoverlay, .nxl-toggle-sidemenu.active").click(function(){$(".nxl-sideoverlay,.page-navigation,.nxl-toggle-sidemenu").removeClass("active")}),$("html").hasClass("layout-topbar")&&$(".nxl-header .list-unstyled > .dropdown").hover(function(){$(this).children(".dropdown-menu").addClass("show")},function(){$(this).children(".dropdown-menu").removeClass("show")}),$("html").hasClass("nxl-horizontal")&&(docH=$(window).height(),1024<(docW=$(window).width()))&&$(".nxl-horizontal .topbar .nxl-submenu .nxl-hasmenu").hover(function(){var n=$(this).children(".nxl-submenu"),e=n.offset(),l=e.left,e=e.top,a=n.width(),i=n.height(),o=$(window).scrollTop();l+a<=docW||n.addClass("edge"),e+i<=docH||(l=e-o,n.addClass("scroll-menu"),n.css("max-height","calc(100vh - "+l+"px)"),hpx=new PerfectScrollbar(".scroll-menu",{wheelSpeed:.5,swipeEasing:0,suppressScrollX:!0,wheelPropagation:1,minScrollbarLength:40}))},function(){hpx.destroy(),$(".scroll-menu").removeAttr("style"),$(".scroll-menu").removeClass("scroll-menu")}),$(".prod-likes .form-check-input").change(function(){$(this).is(":checked")?($(this).parent(".prod-likes").append('<div class="nxl-like"><div class="like-wrapper"><span><span class="nxl-group"><span class="nxl-dots"></span><span class="nxl-dots"></span><span class="nxl-dots"></span><span class="nxl-dots"></span></span></span></div></div>'),$(this).parent(".prod-likes").find(".nxl-like").addClass("nxl-like-animate"),setTimeout(function(){$(this).parent(".prod-likes").find(".nxl-like").remove()},3e3)):$(this).parent(".prod-likes").find(".nxl-like").remove()});
//# sourceMappingURL=nxlNavigation.min.js.map

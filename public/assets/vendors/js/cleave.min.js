!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Cleave=t():e.Cleave=t()}(this,function(){return r=[function(t,e,i){!function(e){"use strict";function o(e,t){var i=this,r=!1;if("string"==typeof e?(i.element=document.querySelector(e),r=1<document.querySelectorAll(e).length):void 0!==e.length&&0<e.length?(i.element=e[0],r=1<e.length):i.element=e,!i.element)throw new Error("[cleave.js] Please check the element");if(r)try{console.warn("[cleave.js] Multiple input fields matched, cleave.js will only take the first one.")}catch(e){}t.initValue=i.element.value,i.properties=o.DefaultProperties.assign({},t),i.init()}o.prototype={init:function(){var e=this,t=e.properties;return t.numeral||t.phone||t.creditCard||t.time||t.date||0!==t.blocksLength||t.prefix?(t.maxLength=o.Util.getMaxLength(t.blocks),e.isAndroid=o.Util.isAndroid(),e.lastInputValue="",e.isBackward="",e.onChangeListener=e.onChange.bind(e),e.onKeyDownListener=e.onKeyDown.bind(e),e.onFocusListener=e.onFocus.bind(e),e.onCutListener=e.onCut.bind(e),e.onCopyListener=e.onCopy.bind(e),e.initSwapHiddenInput(),e.element.addEventListener("input",e.onChangeListener),e.element.addEventListener("keydown",e.onKeyDownListener),e.element.addEventListener("focus",e.onFocusListener),e.element.addEventListener("cut",e.onCutListener),e.element.addEventListener("copy",e.onCopyListener),e.initPhoneFormatter(),e.initDateFormatter(),e.initTimeFormatter(),e.initNumeralFormatter(),void((t.initValue||t.prefix&&!t.noImmediatePrefix)&&e.onInput(t.initValue))):void e.onInput(t.initValue)},initSwapHiddenInput:function(){var e,t=this;t.properties.swapHiddenInput&&(e=t.element.cloneNode(!0),t.element.parentNode.insertBefore(e,t.element),t.elementSwapHidden=t.element,t.elementSwapHidden.type="hidden",t.element=e,t.element.id="")},initNumeralFormatter:function(){var e=this.properties;e.numeral&&(e.numeralFormatter=new o.NumeralFormatter(e.numeralDecimalMark,e.numeralIntegerScale,e.numeralDecimalScale,e.numeralThousandsGroupStyle,e.numeralPositiveOnly,e.stripLeadingZeroes,e.prefix,e.signBeforePrefix,e.tailPrefix,e.delimiter))},initTimeFormatter:function(){var e=this.properties;e.time&&(e.timeFormatter=new o.TimeFormatter(e.timePattern,e.timeFormat),e.blocks=e.timeFormatter.getBlocks(),e.blocksLength=e.blocks.length,e.maxLength=o.Util.getMaxLength(e.blocks))},initDateFormatter:function(){var e=this.properties;e.date&&(e.dateFormatter=new o.DateFormatter(e.datePattern,e.dateMin,e.dateMax),e.blocks=e.dateFormatter.getBlocks(),e.blocksLength=e.blocks.length,e.maxLength=o.Util.getMaxLength(e.blocks))},initPhoneFormatter:function(){var e=this.properties;if(e.phone)try{e.phoneFormatter=new o.PhoneFormatter(new e.root.Cleave.AsYouTypeFormatter(e.phoneRegionCode),e.delimiter)}catch(e){throw new Error("[cleave.js] Please include phone-type-formatter.{country}.js lib")}},onKeyDown:function(e){var t=this,e=e.which||e.keyCode;t.lastInputValue=t.element.value,t.isBackward=8===e},onChange:function(e){var t=this,i=t.properties,r=o.Util,e=(t.isBackward=t.isBackward||"deleteContentBackward"===e.inputType,r.getPostDelimiter(t.lastInputValue,i.delimiter,i.delimiters));t.isBackward&&e?i.postDelimiterBackspace=e:i.postDelimiterBackspace=!1,this.onInput(this.element.value)},onFocus:function(){var e=this,t=e.properties;e.lastInputValue=e.element.value,t.prefix&&t.noImmediatePrefix&&!e.element.value&&this.onInput(t.prefix),o.Util.fixPrefixCursor(e.element,t.prefix,t.delimiter,t.delimiters)},onCut:function(e){o.Util.checkFullSelection(this.element.value)&&(this.copyClipboardData(e),this.onInput(""))},onCopy:function(e){o.Util.checkFullSelection(this.element.value)&&this.copyClipboardData(e)},copyClipboardData:function(e){var t=this.properties,i=o.Util,r=this.element.value,i=t.copyDelimiter?r:i.stripDelimiters(r,t.delimiter,t.delimiters);try{(e.clipboardData?e:window).clipboardData.setData("Text",i),e.preventDefault()}catch(e){}},onInput:function(e){var t=this,i=t.properties,r=o.Util,n=r.getPostDelimiter(e,i.delimiter,i.delimiters);i.numeral||!i.postDelimiterBackspace||n||(e=r.headStr(e,e.length-i.postDelimiterBackspace.length)),i.phone?!i.prefix||i.noImmediatePrefix&&!e.length?i.result=i.phoneFormatter.format(e):i.result=i.prefix+i.phoneFormatter.format(e).slice(i.prefix.length):i.numeral?i.prefix&&i.noImmediatePrefix&&0===e.length?i.result="":i.result=i.numeralFormatter.format(e):(i.date&&(e=i.dateFormatter.getValidatedDate(e)),i.time&&(e=i.timeFormatter.getValidatedTime(e)),e=r.stripDelimiters(e,i.delimiter,i.delimiters),e=r.getPrefixStrippedValue(e,i.prefix,i.prefixLength,i.result,i.delimiter,i.delimiters,i.noImmediatePrefix,i.tailPrefix,i.signBeforePrefix),e=i.numericOnly?r.strip(e,/[^\d]/g):e,e=i.uppercase?e.toUpperCase():e,e=i.lowercase?e.toLowerCase():e,i.prefix&&(i.tailPrefix?e+=i.prefix:e=i.prefix+e,0===i.blocksLength)?i.result=e:(i.creditCard&&t.updateCreditCardPropsByValue(e),e=r.headStr(e,i.maxLength),i.result=r.getFormattedValue(e,i.blocks,i.blocksLength,i.delimiter,i.delimiters,i.delimiterLazyShow))),t.updateValueState()},updateCreditCardPropsByValue:function(e){var t=this.properties,i=o.Util;i.headStr(t.result,4)!==i.headStr(e,4)&&(e=o.CreditCardDetector.getInfo(e,t.creditCardStrictMode),t.blocks=e.blocks,t.blocksLength=t.blocks.length,t.maxLength=i.getMaxLength(t.blocks),t.creditCardType!==e.type)&&(t.creditCardType=e.type,t.onCreditCardTypeChanged.call(this,t.creditCardType))},updateValueState:function(){var e,t,i,r=this,n=o.Util,a=r.properties;r.element&&(e=r.element.selectionEnd,t=r.element.value,i=a.result,e=n.getNextCursorPosition(e,t,i,a.delimiter,a.delimiters),r.isAndroid?window.setTimeout(function(){r.element.value=i,n.setSelection(r.element,e,a.document,!1),r.callOnValueChanged()},1):(r.element.value=i,a.swapHiddenInput&&(r.elementSwapHidden.value=r.getRawValue()),n.setSelection(r.element,e,a.document,!1),r.callOnValueChanged()))},callOnValueChanged:function(){var e=this.properties;e.onValueChanged.call(this,{target:{name:this.element.name,value:e.result,rawValue:this.getRawValue()}})},setPhoneRegionCode:function(e){this.properties.phoneRegionCode=e,this.initPhoneFormatter(),this.onChange()},setRawValue:function(e){var t=this.properties;e=null!=e?e.toString():"",t.numeral&&(e=e.replace(".",t.numeralDecimalMark)),t.postDelimiterBackspace=!1,this.element.value=e,this.onInput(e)},getRawValue:function(){var e=this.properties,t=o.Util,i=this.element.value;return e.rawValueTrimPrefix&&(i=t.getPrefixStrippedValue(i,e.prefix,e.prefixLength,e.result,e.delimiter,e.delimiters,e.noImmediatePrefix,e.tailPrefix,e.signBeforePrefix)),e.numeral?e.numeralFormatter.getRawValue(i):t.stripDelimiters(i,e.delimiter,e.delimiters)},getISOFormatDate:function(){var e=this.properties;return e.date?e.dateFormatter.getISOFormatDate():""},getISOFormatTime:function(){var e=this.properties;return e.time?e.timeFormatter.getISOFormatTime():""},getFormattedValue:function(){return this.element.value},destroy:function(){var e=this;e.element.removeEventListener("input",e.onChangeListener),e.element.removeEventListener("keydown",e.onKeyDownListener),e.element.removeEventListener("focus",e.onFocusListener),e.element.removeEventListener("cut",e.onCutListener),e.element.removeEventListener("copy",e.onCopyListener)},toString:function(){return"[Cleave Object]"}},o.NumeralFormatter=i(1),o.DateFormatter=i(2),o.TimeFormatter=i(3),o.PhoneFormatter=i(4),o.CreditCardDetector=i(5),o.Util=i(6),o.DefaultProperties=i(7),("object"==typeof e&&e?e:window).Cleave=o,t.exports=o}.call(e,function(){return this}())},function(e,t){"use strict";function d(e,t,i,r,n,a,o,l,s,c){var u=this;u.numeralDecimalMark=e||".",u.numeralIntegerScale=0<t?t:0,u.numeralDecimalScale=0<=i?i:2,u.numeralThousandsGroupStyle=r||d.groupStyle.thousand,u.numeralPositiveOnly=!!n,u.stripLeadingZeroes=!1!==a,u.prefix=o||""===o?o:"",u.signBeforePrefix=!!l,u.tailPrefix=!!s,u.delimiter=c||""===c?c:",",u.delimiterRE=c?new RegExp("\\"+c,"g"):""}d.groupStyle={thousand:"thousand",lakh:"lakh",wan:"wan",none:"none"},d.prototype={getRawValue:function(e){return e.replace(this.delimiterRE,"").replace(this.numeralDecimalMark,".")},format:function(e){var t,i,r,n=this,a="";switch(e=e.replace(/[A-Za-z]/g,"").replace(n.numeralDecimalMark,"M").replace(/[^\dM-]/g,"").replace(/^\-/,"N").replace(/\-/g,"").replace("N",n.numeralPositiveOnly?"":"-").replace("M",n.numeralDecimalMark),t="-"===(e=n.stripLeadingZeroes?e.replace(/^(-)?0+(?=\d)/,"$1"):e).slice(0,1)?"-":"",i=void 0!==n.prefix?n.signBeforePrefix?t+n.prefix:n.prefix+t:t,0<=(r=e).indexOf(n.numeralDecimalMark)&&(r=(e=e.split(n.numeralDecimalMark))[0],a=n.numeralDecimalMark+e[1].slice(0,n.numeralDecimalScale)),"-"==t&&(r=r.slice(1)),0<n.numeralIntegerScale&&(r=r.slice(0,n.numeralIntegerScale)),n.numeralThousandsGroupStyle){case d.groupStyle.lakh:r=r.replace(/(\d)(?=(\d\d)+\d$)/g,"$1"+n.delimiter);break;case d.groupStyle.wan:r=r.replace(/(\d)(?=(\d{4})+$)/g,"$1"+n.delimiter);break;case d.groupStyle.thousand:r=r.replace(/(\d)(?=(\d{3})+$)/g,"$1"+n.delimiter)}return n.tailPrefix?t+r.toString()+(0<n.numeralDecimalScale?a.toString():"")+n.prefix:i+r.toString()+(0<n.numeralDecimalScale?a.toString():"")}},e.exports=d},function(e,t){"use strict";function i(e,t,i){var r=this;r.date=[],r.blocks=[],r.datePattern=e,r.dateMin=t.split("-").reverse().map(function(e){return parseInt(e,10)}),2===r.dateMin.length&&r.dateMin.unshift(0),r.dateMax=i.split("-").reverse().map(function(e){return parseInt(e,10)}),2===r.dateMax.length&&r.dateMax.unshift(0),r.initBlocks()}i.prototype={initBlocks:function(){var t=this;t.datePattern.forEach(function(e){"Y"===e?t.blocks.push(4):t.blocks.push(2)})},getISOFormatDate:function(){var e=this.date;return e[2]?e[2]+"-"+this.addLeadingZero(e[1])+"-"+this.addLeadingZero(e[0]):""},getBlocks:function(){return this.blocks},getValidatedDate:function(n){var a=this,o="";return n=n.replace(/[^\d]/g,""),a.blocks.forEach(function(e,t){if(0<n.length){var i=n.slice(0,e),r=i.slice(0,1),e=n.slice(e);switch(a.datePattern[t]){case"d":"00"===i?i="01":3<parseInt(r,10)?i="0"+r:31<parseInt(i,10)&&(i="31");break;case"m":"00"===i?i="01":1<parseInt(r,10)?i="0"+r:12<parseInt(i,10)&&(i="12")}o+=i,n=e}}),this.getFixedDateString(o)},getFixedDateString:function(e){var t,i,r,n=this,a=n.datePattern,o=[],l=0,s=0,c=0,u=0,d=0,m=0,p=!1;return 4===e.length&&"y"!==a[0].toLowerCase()&&"y"!==a[1].toLowerCase()&&(d=2-(u="d"===a[0]?0:2),t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),o=this.getFixedDate(t,i,0)),8===e.length&&(a.forEach(function(e,t){switch(e){case"d":l=t;break;case"m":s=t;break;default:c=t}}),m=2*c,u=l<=c?2*l:2*l+2,d=s<=c?2*s:2*s+2,t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(m,m+4),10),p=4===e.slice(m,m+4).length,o=this.getFixedDate(t,i,r)),4!==e.length||"y"!==a[0]&&"y"!==a[1]||(m=2-(d="m"===a[0]?0:2),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(m,m+2),10),p=2===e.slice(m,m+2).length,o=[0,i,r]),6!==e.length||"Y"!==a[0]&&"Y"!==a[1]||(m=2-.5*(d="m"===a[0]?0:4),i=parseInt(e.slice(d,d+2),10),r=parseInt(e.slice(m,m+4),10),p=4===e.slice(m,m+4).length,o=[0,i,r]),o=n.getRangeFixedDate(o),0===(n.date=o).length?e:a.reduce(function(e,t){switch(t){case"d":return e+(0===o[0]?"":n.addLeadingZero(o[0]));case"m":return e+(0===o[1]?"":n.addLeadingZero(o[1]));case"y":return e+(p?n.addLeadingZeroForYear(o[2],!1):"");case"Y":return e+(p?n.addLeadingZeroForYear(o[2],!0):"")}},"")},getRangeFixedDate:function(e){var t=this.datePattern,i=this.dateMin||[],r=this.dateMax||[];return!e.length||i.length<3&&r.length<3||t.find(function(e){return"y"===e.toLowerCase()})&&0===e[2]?e:r.length&&(r[2]<e[2]||r[2]===e[2]&&(r[1]<e[1]||r[1]===e[1]&&r[0]<e[0]))?r:i.length&&(i[2]>e[2]||i[2]===e[2]&&(i[1]>e[1]||i[1]===e[1]&&i[0]>e[0]))?i:e},getFixedDate:function(e,t,i){return e=Math.min(e,31),t=Math.min(t,12),i=parseInt(i||0,10),[e=t<7&&t%2==0||8<t&&t%2==1?Math.min(e,2===t?this.isLeapYear(i)?29:28:30):e,t,i]},isLeapYear:function(e){return e%4==0&&e%100!=0||e%400==0},addLeadingZero:function(e){return(e<10?"0":"")+e},addLeadingZeroForYear:function(e,t){return t?(e<10?"000":e<100?"00":e<1e3?"0":"")+e:(e<10?"0":"")+e}},e.exports=i},function(e,t){"use strict";function i(e,t){var i=this;i.time=[],i.blocks=[],i.timePattern=e,i.timeFormat=t,i.initBlocks()}i.prototype={initBlocks:function(){var e=this;e.timePattern.forEach(function(){e.blocks.push(2)})},getISOFormatTime:function(){var e=this.time;return e[2]?this.addLeadingZero(e[0])+":"+this.addLeadingZero(e[1])+":"+this.addLeadingZero(e[2]):""},getBlocks:function(){return this.blocks},getTimeFormatOptions:function(){return"12"===String(this.timeFormat)?{maxHourFirstDigit:1,maxHours:12,maxMinutesFirstDigit:5,maxMinutes:60}:{maxHourFirstDigit:2,maxHours:23,maxMinutesFirstDigit:5,maxMinutes:60}},getValidatedTime:function(n){var a=this,o="",l=(n=n.replace(/[^\d]/g,""),a.getTimeFormatOptions());return a.blocks.forEach(function(e,t){if(0<n.length){var i=n.slice(0,e),r=i.slice(0,1),e=n.slice(e);switch(a.timePattern[t]){case"h":parseInt(r,10)>l.maxHourFirstDigit?i="0"+r:parseInt(i,10)>l.maxHours&&(i=l.maxHours+"");break;case"m":case"s":parseInt(r,10)>l.maxMinutesFirstDigit?i="0"+r:parseInt(i,10)>l.maxMinutes&&(i=l.maxMinutes+"")}o+=i,n=e}}),this.getFixedTimeString(o)},getFixedTimeString:function(e){var t,i,r,n=this,a=n.timePattern,o=[],l=0,s=0,c=0,u=0,d=0;return 6===e.length&&(a.forEach(function(e,t){switch(e){case"s":l=2*t;break;case"m":s=2*t;break;case"h":c=2*t}}),d=c,u=s,r=l,r=parseInt(e.slice(r,r+2),10),t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),o=this.getFixedTime(i,t,r)),4===e.length&&n.timePattern.indexOf("s")<0&&(a.forEach(function(e,t){switch(e){case"m":s=2*t;break;case"h":c=2*t}}),d=c,u=s,r=0,t=parseInt(e.slice(u,u+2),10),i=parseInt(e.slice(d,d+2),10),o=this.getFixedTime(i,t,r)),0===(n.time=o).length?e:a.reduce(function(e,t){switch(t){case"s":return e+n.addLeadingZero(o[2]);case"m":return e+n.addLeadingZero(o[1]);case"h":return e+n.addLeadingZero(o[0])}},"")},getFixedTime:function(e,t,i){return i=Math.min(parseInt(i||0,10),60),t=Math.min(t,60),[e=Math.min(e,60),t,i]},addLeadingZero:function(e){return(e<10?"0":"")+e}},e.exports=i},function(e,t){"use strict";function i(e,t){this.delimiter=t||""===t?t:" ",this.delimiterRE=t?new RegExp("\\"+t,"g"):"",this.formatter=e}i.prototype={setFormatter:function(e){this.formatter=e},format:function(e){this.formatter.clear();for(var t,i="",r=!1,n=0,a=(e=(e=(e=e.replace(/[^\d+]/g,"")).replace(/^\+/,"B").replace(/\+/g,"").replace("B","+")).replace(this.delimiterRE,"")).length;n<a;n++)t=this.formatter.inputDigit(e.charAt(n)),/[\s()-]/g.test(t)?(i=t,r=!0):r||(i=t);return(i=i.replace(/[()]/g,"")).replace(/[\s-]/g,this.delimiter)}},e.exports=i},function(e,t){"use strict";var o={blocks:{uatp:[4,5,6],amex:[4,6,5],diners:[4,6,4],discover:[4,4,4,4],mastercard:[4,4,4,4],dankort:[4,4,4,4],instapayment:[4,4,4,4],jcb15:[4,6,5],jcb:[4,4,4,4],maestro:[4,4,4,4],visa:[4,4,4,4],mir:[4,4,4,4],unionPay:[4,4,4,4],general:[4,4,4,4]},re:{uatp:/^(?!1800)1\d{0,14}/,amex:/^3[47]\d{0,13}/,discover:/^(?:6011|65\d{0,2}|64[4-9]\d?)\d{0,12}/,diners:/^3(?:0([0-5]|9)|[689]\d?)\d{0,11}/,mastercard:/^(5[1-5]\d{0,2}|22[2-9]\d{0,1}|2[3-7]\d{0,2})\d{0,12}/,dankort:/^(5019|4175|4571)\d{0,12}/,instapayment:/^63[7-9]\d{0,13}/,jcb15:/^(?:2131|1800)\d{0,11}/,jcb:/^(?:35\d{0,2})\d{0,12}/,maestro:/^(?:5[0678]\d{0,2}|6304|67\d{0,2})\d{0,12}/,mir:/^220[0-4]\d{0,12}/,visa:/^4\d{0,15}/,unionPay:/^(62|81)\d{0,14}/},getStrictBlocks:function(e){var t=e.reduce(function(e,t){return e+t},0);return e.concat(19-t)},getInfo:function(e,t){var i,r,n=o.blocks,a=o.re;for(i in t=!!t,a)if(a[i].test(e))return r=n[i],{type:i,blocks:t?this.getStrictBlocks(r):r};return{type:"unknown",blocks:t?this.getStrictBlocks(n.general):n.general}}};e.exports=o},function(e,t){"use strict";e.exports={noop:function(){},strip:function(e,t){return e.replace(t,"")},getPostDelimiter:function(t,e,i){var r;return 0===i.length?t.slice(-e.length)===e?e:"":(r="",i.forEach(function(e){t.slice(-e.length)===e&&(r=e)}),r)},getDelimiterREByDelimiter:function(e){return new RegExp(e.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),"g")},getNextCursorPosition:function(e,t,i,r,n){return t.length===e?i.length:e+this.getPositionOffset(e,t,i,r,n)},getPositionOffset:function(e,t,i,r,n){t=this.stripDelimiters(t.slice(0,e),r,n),i=this.stripDelimiters(i.slice(0,e),r,n),e=t.length-i.length;return 0!=e?e/Math.abs(e):0},stripDelimiters:function(t,e,i){var r=this;return 0===i.length?(e=e?r.getDelimiterREByDelimiter(e):"",t.replace(e,"")):(i.forEach(function(e){e.split("").forEach(function(e){t=t.replace(r.getDelimiterREByDelimiter(e),"")})}),t)},headStr:function(e,t){return e.slice(0,t)},getMaxLength:function(e){return e.reduce(function(e,t){return e+t},0)},getPrefixStrippedValue:function(e,t,i,r,n,a,o,l,s){var c;return 0===i?e:e===t&&""!==e?"":s&&"-"==e.slice(0,1)?(c="-"==r.slice(0,1)?r.slice(1):r,"-"+this.getPrefixStrippedValue(e.slice(1),t,i,c,n,a,o,l,s)):r.slice(0,i)!==t&&!l||r.slice(-i)!==t&&l?o&&!r&&e?e:"":(c=this.stripDelimiters(r,n,a),e.slice(0,i)===t||l?e.slice(-i)!==t&&l?c.slice(0,-i-1):l?e.slice(0,-i):e.slice(i):c.slice(i))},getFirstDiffIndex:function(e,t){for(var i=0;e.charAt(i)===t.charAt(i);)if(""===e.charAt(i++))return-1;return i},getFormattedValue:function(n,e,a,o,l,s){var c="",u=0<l.length,d="";return 0===a?n:(e.forEach(function(e,t){var i,r;0<n.length&&(i=n.slice(0,e),r=n.slice(e),d=u?l[s?t-1:t]||d:o,s?(0<t&&(c+=d),c+=i):(c+=i,i.length===e&&t<a-1&&(c+=d)),n=r)}),c)},fixPrefixCursor:function(e,t,i,r){var n,a;e&&(n=e.value,i=i||r[0]||" ",!e.setSelectionRange||!t||t.length+i.length<=n.length||(a=2*n.length,setTimeout(function(){e.setSelectionRange(a,a)},1)))},checkFullSelection:function(e){try{return(window.getSelection()||document.getSelection()||{}).toString().length===e.length}catch(e){}return!1},setSelection:function(e,t,i){if(e===this.getActiveElement(i)&&!(e&&e.value.length<=t))if(e.createTextRange){i=e.createTextRange();i.move("character",t),i.select()}else try{e.setSelectionRange(t,t)}catch(e){console.warn("The input element type does not support selection")}},getActiveElement:function(e){e=e.activeElement;return e&&e.shadowRoot?this.getActiveElement(e.shadowRoot):e},isAndroid:function(){return navigator&&/android/i.test(navigator.userAgent)},isAndroidBackspaceKeydown:function(e,t){return!!(this.isAndroid()&&e&&t)&&t===e.slice(0,-1)}}},function(e,t){!function(i){"use strict";e.exports={assign:function(e,t){return(e=e||{}).creditCard=!!(t=t||{}).creditCard,e.creditCardStrictMode=!!t.creditCardStrictMode,e.creditCardType="",e.onCreditCardTypeChanged=t.onCreditCardTypeChanged||function(){},e.phone=!!t.phone,e.phoneRegionCode=t.phoneRegionCode||"AU",e.phoneFormatter={},e.time=!!t.time,e.timePattern=t.timePattern||["h","m","s"],e.timeFormat=t.timeFormat||"24",e.timeFormatter={},e.date=!!t.date,e.datePattern=t.datePattern||["d","m","Y"],e.dateMin=t.dateMin||"",e.dateMax=t.dateMax||"",e.dateFormatter={},e.numeral=!!t.numeral,e.numeralIntegerScale=0<t.numeralIntegerScale?t.numeralIntegerScale:0,e.numeralDecimalScale=0<=t.numeralDecimalScale?t.numeralDecimalScale:2,e.numeralDecimalMark=t.numeralDecimalMark||".",e.numeralThousandsGroupStyle=t.numeralThousandsGroupStyle||"thousand",e.numeralPositiveOnly=!!t.numeralPositiveOnly,e.stripLeadingZeroes=!1!==t.stripLeadingZeroes,e.signBeforePrefix=!!t.signBeforePrefix,e.tailPrefix=!!t.tailPrefix,e.swapHiddenInput=!!t.swapHiddenInput,e.numericOnly=e.creditCard||e.date||!!t.numericOnly,e.uppercase=!!t.uppercase,e.lowercase=!!t.lowercase,e.prefix=!e.creditCard&&!e.date&&t.prefix||"",e.noImmediatePrefix=!!t.noImmediatePrefix,e.prefixLength=e.prefix.length,e.rawValueTrimPrefix=!!t.rawValueTrimPrefix,e.copyDelimiter=!!t.copyDelimiter,e.initValue=void 0!==t.initValue&&null!==t.initValue?t.initValue.toString():"",e.delimiter=t.delimiter||""===t.delimiter?t.delimiter:t.date?"/":t.time?":":t.numeral?",":(t.phone," "),e.delimiterLength=e.delimiter.length,e.delimiterLazyShow=!!t.delimiterLazyShow,e.delimiters=t.delimiters||[],e.blocks=t.blocks||[],e.blocksLength=e.blocks.length,e.root="object"==typeof i&&i?i:window,e.document=t.document||e.root.document,e.maxLength=0,e.backspace=!1,e.result="",e.onValueChanged=t.onValueChanged||function(){},e}}}.call(t,function(){return this}())}],n={},i.m=r,i.c=n,i.p="",i(0);function i(e){var t;return(n[e]||(t=n[e]={exports:{},id:e,loaded:!1},r[e].call(t.exports,t,t.exports,i),t.loaded=!0,t)).exports}var r,n});
//# sourceMappingURL=cleave.min.js.map

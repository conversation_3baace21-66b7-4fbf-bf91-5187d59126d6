{"version": 3, "file": "tui-schedules.min.js", "sources": ["tui-schedules.min.js"], "sourcesContent": ["\"use strict\";\n\n/*eslint-disable*/\n\nvar ScheduleList = [];\n\nvar SCHEDULE_CATEGORY = [\"milestone\", \"task\"];\n\nfunction ScheduleInfo() {\n\tthis.id = null;\n\tthis.calendarId = null;\n\n\tthis.title = null;\n\tthis.body = null;\n\tthis.location = null;\n\tthis.isAllday = false;\n\tthis.start = null;\n\tthis.end = null;\n\tthis.category = \"\";\n\tthis.dueDateClass = \"\";\n\n\tthis.color = null;\n\tthis.bgColor = null;\n\tthis.dragBgColor = null;\n\tthis.borderColor = null;\n\tthis.customStyle = \"\";\n\n\tthis.isFocused = false;\n\tthis.isPending = false;\n\tthis.isVisible = true;\n\tthis.isReadOnly = false;\n\tthis.isPrivate = false;\n\tthis.goingDuration = 0;\n\tthis.comingDuration = 0;\n\tthis.recurrenceRule = \"\";\n\tthis.state = \"\";\n\n\tthis.raw = {\n\t\tmemo: \"\",\n\t\thasToOrCc: false,\n\t\thasRecurrenceRule: false,\n\t\tlocation: null,\n\t\tcreator: {\n\t\t\tname: \"\",\n\t\t\tavatar: \"\",\n\t\t\tcompany: \"\",\n\t\t\temail: \"\",\n\t\t\tphone: \"\",\n\t\t},\n\t};\n}\n\nfunction generateTime(schedule, renderStart, renderEnd) {\n\tvar startDate = moment(renderStart.getTime());\n\tvar endDate = moment(renderEnd.getTime());\n\tvar diffDate = endDate.diff(startDate, \"days\");\n\n\tschedule.isAllday = chance.bool({ likelihood: 30 });\n\tif (schedule.isAllday) {\n\t\tschedule.category = \"allday\";\n\t} else if (chance.bool({ likelihood: 30 })) {\n\t\tschedule.category = SCHEDULE_CATEGORY[chance.integer({ min: 0, max: 1 })];\n\t\tif (schedule.category === SCHEDULE_CATEGORY[1]) {\n\t\t\tschedule.dueDateClass = \"morning\";\n\t\t}\n\t} else {\n\t\tschedule.category = \"time\";\n\t}\n\n\tstartDate.add(chance.integer({ min: 0, max: diffDate }), \"days\");\n\tstartDate.hours(chance.integer({ min: 0, max: 23 }));\n\tstartDate.minutes(chance.bool() ? 0 : 30);\n\tschedule.start = startDate.toDate();\n\n\tendDate = moment(startDate);\n\tif (schedule.isAllday) {\n\t\tendDate.add(chance.integer({ min: 0, max: 3 }), \"days\");\n\t}\n\n\tschedule.end = endDate.add(chance.integer({ min: 1, max: 4 }), \"hour\").toDate();\n\n\tif (!schedule.isAllday && chance.bool({ likelihood: 20 })) {\n\t\tschedule.goingDuration = chance.integer({ min: 30, max: 120 });\n\t\tschedule.comingDuration = chance.integer({ min: 30, max: 120 });\n\n\t\tif (chance.bool({ likelihood: 50 })) {\n\t\t\tschedule.end = schedule.start;\n\t\t}\n\t}\n}\n\nfunction generateNames() {\n\tvar names = [];\n\tvar i = 0;\n\tvar length = chance.integer({ min: 1, max: 10 });\n\n\tfor (; i < length; i += 1) {\n\t\tnames.push(chance.name());\n\t}\n\n\treturn names;\n}\n\nfunction generateRandomSchedule(calendar, renderStart, renderEnd) {\n\tvar schedule = new ScheduleInfo();\n\n\tschedule.id = chance.guid();\n\tschedule.calendarId = calendar.id;\n\n\tschedule.title = chance.sentence({ words: 3 });\n\tschedule.body = chance.bool({ likelihood: 20 }) ? chance.sentence({ words: 10 }) : \"\";\n\tschedule.isReadOnly = chance.bool({ likelihood: 20 });\n\tgenerateTime(schedule, renderStart, renderEnd);\n\n\tschedule.isPrivate = chance.bool({ likelihood: 10 });\n\tschedule.location = chance.address();\n\tschedule.attendees = chance.bool({ likelihood: 70 }) ? generateNames() : [];\n\tschedule.recurrenceRule = chance.bool({ likelihood: 20 }) ? \"Repeated Events\" : \"\";\n\tschedule.state = chance.bool({ likelihood: 20 }) ? \"Free\" : \"Busy\";\n\tschedule.color = calendar.color;\n\tschedule.bgColor = calendar.bgColor;\n\tschedule.dragBgColor = calendar.dragBgColor;\n\tschedule.borderColor = calendar.borderColor;\n\n\tif (schedule.category === \"milestone\") {\n\t\tschedule.color = schedule.bgColor;\n\t\tschedule.bgColor = \"transparent\";\n\t\tschedule.dragBgColor = \"transparent\";\n\t\tschedule.borderColor = \"transparent\";\n\t}\n\n\tschedule.raw.memo = chance.sentence();\n\tschedule.raw.creator.name = chance.name();\n\tschedule.raw.creator.avatar = chance.avatar();\n\tschedule.raw.creator.company = chance.company();\n\tschedule.raw.creator.email = chance.email();\n\tschedule.raw.creator.phone = chance.phone();\n\n\tif (chance.bool({ likelihood: 20 })) {\n\t\tvar travelTime = chance.minute();\n\t\tschedule.goingDuration = travelTime;\n\t\tschedule.comingDuration = travelTime;\n\t}\n\n\tScheduleList.push(schedule);\n}\n\nfunction generateSchedule(viewName, renderStart, renderEnd) {\n\tScheduleList = [];\n\tCalendarList.forEach(function (calendar) {\n\t\tvar i = 0,\n\t\t\tlength = 10;\n\t\tif (viewName === \"month\") {\n\t\t\tlength = 3;\n\t\t} else if (viewName === \"day\") {\n\t\t\tlength = 4;\n\t\t}\n\t\tfor (; i < length; i += 1) {\n\t\t\tgenerateRandomSchedule(calendar, renderStart, renderEnd);\n\t\t}\n\t});\n}\n"], "names": ["ScheduleList", "SCHEDULE_CATEGORY", "ScheduleInfo", "this", "id", "calendarId", "title", "body", "location", "isAllday", "start", "end", "category", "dueDateClass", "color", "bgColor", "dragBgColor", "borderColor", "customStyle", "isFocused", "isPending", "isVisible", "isReadOnly", "isPrivate", "goingDuration", "comingDuration", "recurrenceRule", "state", "raw", "memo", "hasToOrCc", "hasRecurrenceRule", "creator", "name", "avatar", "company", "email", "phone", "generateTime", "schedule", "renderStart", "renderEnd", "startDate", "moment", "getTime", "endDate", "diffDate", "diff", "chance", "bool", "likelihood", "integer", "min", "max", "add", "hours", "minutes", "toDate", "generateNames", "names", "i", "length", "push", "generateRandomSchedule", "calendar", "guid", "sentence", "words", "address", "attendees", "travelTime", "minute", "generateSchedule", "viewName", "CalendarList", "for<PERSON>ach"], "mappings": "AAAA,aAIA,IAAIA,aAAe,GAEfC,kBAAoB,CAAC,YAAa,QAEtC,SAASC,eACRC,KAAKC,GAAK,KACVD,KAAKE,WAAa,KAElBF,KAAKG,MAAQ,KACbH,KAAKI,KAAO,KACZJ,KAAKK,SAAW,KAChBL,KAAKM,SAAW,CAAA,EAChBN,KAAKO,MAAQ,KACbP,KAAKQ,IAAM,KACXR,KAAKS,SAAW,GAChBT,KAAKU,aAAe,GAEpBV,KAAKW,MAAQ,KACbX,KAAKY,QAAU,KACfZ,KAAKa,YAAc,KACnBb,KAAKc,YAAc,KACnBd,KAAKe,YAAc,GAEnBf,KAAKgB,UAAY,CAAA,EACjBhB,KAAKiB,UAAY,CAAA,EACjBjB,KAAKkB,UAAY,CAAA,EACjBlB,KAAKmB,WAAa,CAAA,EAClBnB,KAAKoB,UAAY,CAAA,EACjBpB,KAAKqB,cAAgB,EACrBrB,KAAKsB,eAAiB,EACtBtB,KAAKuB,eAAiB,GACtBvB,KAAKwB,MAAQ,GAEbxB,KAAKyB,IAAM,CACVC,KAAM,GACNC,UAAW,CAAA,EACXC,kBAAmB,CAAA,EACnBvB,SAAU,KACVwB,QAAS,CACRC,KAAM,GACNC,OAAQ,GACRC,QAAS,GACTC,MAAO,GACPC,MAAO,EACR,CACD,CACD,CAEA,SAASC,aAAaC,EAAUC,EAAaC,GAC5C,IAAIC,EAAYC,OAAOH,EAAYI,QAAQ,CAAC,EACxCC,EAAUF,OAAOF,EAAUG,QAAQ,CAAC,EACpCE,EAAWD,EAAQE,KAAKL,EAAW,MAAM,EAE7CH,EAAS9B,SAAWuC,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EAC9CX,EAAS9B,SACZ8B,EAAS3B,SAAW,SACVoC,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,GACxCX,EAAS3B,SAAWX,kBAAkB+C,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAK,CAAE,CAAC,GACnEd,EAAS3B,WAAaX,kBAAkB,KAC3CsC,EAAS1B,aAAe,YAGzB0B,EAAS3B,SAAW,OAGrB8B,EAAUY,IAAIN,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAKP,CAAS,CAAC,EAAG,MAAM,EAC/DJ,EAAUa,MAAMP,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAK,EAAG,CAAC,CAAC,EACnDX,EAAUc,QAAQR,OAAOC,KAAK,EAAI,EAAI,EAAE,EACxCV,EAAS7B,MAAQgC,EAAUe,OAAO,EAElCZ,EAAUF,OAAOD,CAAS,EACtBH,EAAS9B,UACZoC,EAAQS,IAAIN,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAK,CAAE,CAAC,EAAG,MAAM,EAGvDd,EAAS5B,IAAMkC,EAAQS,IAAIN,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAK,CAAE,CAAC,EAAG,MAAM,EAAEI,OAAO,EAE1E,CAAClB,EAAS9B,UAAYuC,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,IACvDX,EAASf,cAAgBwB,OAAOG,QAAQ,CAAEC,IAAK,GAAIC,IAAK,GAAI,CAAC,EAC7Dd,EAASd,eAAiBuB,OAAOG,QAAQ,CAAEC,IAAK,GAAIC,IAAK,GAAI,CAAC,EAE1DL,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,KACjCX,EAAS5B,IAAM4B,EAAS7B,MAG3B,CAEA,SAASgD,gBAKR,IAJA,IAAIC,EAAQ,GACRC,EAAI,EACJC,EAASb,OAAOG,QAAQ,CAAEC,IAAK,EAAGC,IAAK,EAAG,CAAC,EAExCO,EAAIC,EAAQD,GAAK,EACvBD,EAAMG,KAAKd,OAAOf,KAAK,CAAC,EAGzB,OAAO0B,CACR,CAEA,SAASI,uBAAuBC,EAAUxB,EAAaC,GACtD,IAAIF,EAAW,IAAIrC,aAEnBqC,EAASnC,GAAK4C,OAAOiB,KAAK,EAC1B1B,EAASlC,WAAa2D,EAAS5D,GAE/BmC,EAASjC,MAAQ0C,OAAOkB,SAAS,CAAEC,MAAO,CAAE,CAAC,EAC7C5B,EAAShC,KAAOyC,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EAAIF,OAAOkB,SAAS,CAAEC,MAAO,EAAG,CAAC,EAAI,GACnF5B,EAASjB,WAAa0B,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EACpDZ,aAAaC,EAAUC,EAAaC,CAAS,EAE7CF,EAAShB,UAAYyB,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EACnDX,EAAS/B,SAAWwC,OAAOoB,QAAQ,EACnC7B,EAAS8B,UAAYrB,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EAAIQ,cAAc,EAAI,GACzEnB,EAASb,eAAiBsB,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EAAI,kBAAoB,GAChFX,EAASZ,MAAQqB,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,EAAI,OAAS,OAC5DX,EAASzB,MAAQkD,EAASlD,MAC1ByB,EAASxB,QAAUiD,EAASjD,QAC5BwB,EAASvB,YAAcgD,EAAShD,YAChCuB,EAAStB,YAAc+C,EAAS/C,YAEN,cAAtBsB,EAAS3B,WACZ2B,EAASzB,MAAQyB,EAASxB,QAC1BwB,EAASxB,QAAU,cACnBwB,EAASvB,YAAc,cACvBuB,EAAStB,YAAc,eAGxBsB,EAASX,IAAIC,KAAOmB,OAAOkB,SAAS,EACpC3B,EAASX,IAAII,QAAQC,KAAOe,OAAOf,KAAK,EACxCM,EAASX,IAAII,QAAQE,OAASc,OAAOd,OAAO,EAC5CK,EAASX,IAAII,QAAQG,QAAUa,OAAOb,QAAQ,EAC9CI,EAASX,IAAII,QAAQI,MAAQY,OAAOZ,MAAM,EAC1CG,EAASX,IAAII,QAAQK,MAAQW,OAAOX,MAAM,EAEtCW,OAAOC,KAAK,CAAEC,WAAY,EAAG,CAAC,IAC7BoB,EAAatB,OAAOuB,OAAO,EAC/BhC,EAASf,cAAgB8C,EACzB/B,EAASd,eAAiB6C,GAG3BtE,aAAa8D,KAAKvB,CAAQ,CAC3B,CAEA,SAASiC,iBAAiBC,EAAUjC,EAAaC,GAChDzC,aAAe,GACf0E,aAAaC,QAAQ,SAAUX,GAC9B,IAAIJ,EAAI,EACPC,EAAS,GAMV,IALiB,UAAbY,EACHZ,EAAS,EACc,QAAbY,IACVZ,EAAS,GAEHD,EAAIC,EAAQD,GAAK,EACvBG,uBAAuBC,EAAUxB,EAAaC,CAAS,CAEzD,CAAC,CACF"}
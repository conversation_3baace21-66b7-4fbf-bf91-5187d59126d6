{"version": 3, "file": "moment.min.js", "sources": ["moment.min.js"], "sourcesContent": ["//! moment.js\r\n//! version : 2.20.1\r\n//! authors : <PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors\r\n//! license : MIT\r\n//! momentjs.com\r\n\r\n(function (global, factory) {\r\n\ttypeof exports === \"object\" && typeof module !== \"undefined\" ? (module.exports = factory()) : typeof define === \"function\" && define.amd ? define(factory) : (global.moment = factory());\r\n})(this, function () {\r\n\t\"use strict\";\r\n\r\n\tvar hookCallback;\r\n\r\n\tfunction hooks() {\r\n\t\treturn hookCallback.apply(null, arguments);\r\n\t}\r\n\r\n\t// This is done to register the method called with moment()\r\n\t// without creating circular dependencies.\r\n\tfunction setHookCallback(callback) {\r\n\t\thookCallback = callback;\r\n\t}\r\n\r\n\tfunction isArray(input) {\r\n\t\treturn input instanceof Array || Object.prototype.toString.call(input) === \"[object Array]\";\r\n\t}\r\n\r\n\tfunction isObject(input) {\r\n\t\t// IE8 will treat undefined and null as object if it wasn't for\r\n\t\t// input != null\r\n\t\treturn input != null && Object.prototype.toString.call(input) === \"[object Object]\";\r\n\t}\r\n\r\n\tfunction isObjectEmpty(obj) {\r\n\t\tif (Object.getOwnPropertyNames) {\r\n\t\t\treturn Object.getOwnPropertyNames(obj).length === 0;\r\n\t\t} else {\r\n\t\t\tvar k;\r\n\t\t\tfor (k in obj) {\r\n\t\t\t\tif (obj.hasOwnProperty(k)) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction isUndefined(input) {\r\n\t\treturn input === void 0;\r\n\t}\r\n\r\n\tfunction isNumber(input) {\r\n\t\treturn typeof input === \"number\" || Object.prototype.toString.call(input) === \"[object Number]\";\r\n\t}\r\n\r\n\tfunction isDate(input) {\r\n\t\treturn input instanceof Date || Object.prototype.toString.call(input) === \"[object Date]\";\r\n\t}\r\n\r\n\tfunction map(arr, fn) {\r\n\t\tvar res = [],\r\n\t\t\ti;\r\n\t\tfor (i = 0; i < arr.length; ++i) {\r\n\t\t\tres.push(fn(arr[i], i));\r\n\t\t}\r\n\t\treturn res;\r\n\t}\r\n\r\n\tfunction hasOwnProp(a, b) {\r\n\t\treturn Object.prototype.hasOwnProperty.call(a, b);\r\n\t}\r\n\r\n\tfunction extend(a, b) {\r\n\t\tfor (var i in b) {\r\n\t\t\tif (hasOwnProp(b, i)) {\r\n\t\t\t\ta[i] = b[i];\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (hasOwnProp(b, \"toString\")) {\r\n\t\t\ta.toString = b.toString;\r\n\t\t}\r\n\r\n\t\tif (hasOwnProp(b, \"valueOf\")) {\r\n\t\t\ta.valueOf = b.valueOf;\r\n\t\t}\r\n\r\n\t\treturn a;\r\n\t}\r\n\r\n\tfunction createUTC(input, format, locale, strict) {\r\n\t\treturn createLocalOrUTC(input, format, locale, strict, true).utc();\r\n\t}\r\n\r\n\tfunction defaultParsingFlags() {\r\n\t\t// We need to deep clone this object.\r\n\t\treturn {\r\n\t\t\tempty: false,\r\n\t\t\tunusedTokens: [],\r\n\t\t\tunusedInput: [],\r\n\t\t\toverflow: -2,\r\n\t\t\tcharsLeftOver: 0,\r\n\t\t\tnullInput: false,\r\n\t\t\tinvalidMonth: null,\r\n\t\t\tinvalidFormat: false,\r\n\t\t\tuserInvalidated: false,\r\n\t\t\tiso: false,\r\n\t\t\tparsedDateParts: [],\r\n\t\t\tmeridiem: null,\r\n\t\t\trfc2822: false,\r\n\t\t\tweekdayMismatch: false,\r\n\t\t};\r\n\t}\r\n\r\n\tfunction getParsingFlags(m) {\r\n\t\tif (m._pf == null) {\r\n\t\t\tm._pf = defaultParsingFlags();\r\n\t\t}\r\n\t\treturn m._pf;\r\n\t}\r\n\r\n\tvar some;\r\n\tif (Array.prototype.some) {\r\n\t\tsome = Array.prototype.some;\r\n\t} else {\r\n\t\tsome = function (fun) {\r\n\t\t\tvar t = Object(this);\r\n\t\t\tvar len = t.length >>> 0;\r\n\r\n\t\t\tfor (var i = 0; i < len; i++) {\r\n\t\t\t\tif (i in t && fun.call(this, t[i], i, t)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn false;\r\n\t\t};\r\n\t}\r\n\r\n\tfunction isValid(m) {\r\n\t\tif (m._isValid == null) {\r\n\t\t\tvar flags = getParsingFlags(m);\r\n\t\t\tvar parsedParts = some.call(flags.parsedDateParts, function (i) {\r\n\t\t\t\treturn i != null;\r\n\t\t\t});\r\n\t\t\tvar isNowValid = !isNaN(m._d.getTime()) && flags.overflow < 0 && !flags.empty && !flags.invalidMonth && !flags.invalidWeekday && !flags.weekdayMismatch && !flags.nullInput && !flags.invalidFormat && !flags.userInvalidated && (!flags.meridiem || (flags.meridiem && parsedParts));\r\n\r\n\t\t\tif (m._strict) {\r\n\t\t\t\tisNowValid = isNowValid && flags.charsLeftOver === 0 && flags.unusedTokens.length === 0 && flags.bigHour === undefined;\r\n\t\t\t}\r\n\r\n\t\t\tif (Object.isFrozen == null || !Object.isFrozen(m)) {\r\n\t\t\t\tm._isValid = isNowValid;\r\n\t\t\t} else {\r\n\t\t\t\treturn isNowValid;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn m._isValid;\r\n\t}\r\n\r\n\tfunction createInvalid(flags) {\r\n\t\tvar m = createUTC(NaN);\r\n\t\tif (flags != null) {\r\n\t\t\textend(getParsingFlags(m), flags);\r\n\t\t} else {\r\n\t\t\tgetParsingFlags(m).userInvalidated = true;\r\n\t\t}\r\n\r\n\t\treturn m;\r\n\t}\r\n\r\n\t// Plugins that add properties should also add the key here (null value),\r\n\t// so we can properly clone ourselves.\r\n\tvar momentProperties = (hooks.momentProperties = []);\r\n\r\n\tfunction copyConfig(to, from) {\r\n\t\tvar i, prop, val;\r\n\r\n\t\tif (!isUndefined(from._isAMomentObject)) {\r\n\t\t\tto._isAMomentObject = from._isAMomentObject;\r\n\t\t}\r\n\t\tif (!isUndefined(from._i)) {\r\n\t\t\tto._i = from._i;\r\n\t\t}\r\n\t\tif (!isUndefined(from._f)) {\r\n\t\t\tto._f = from._f;\r\n\t\t}\r\n\t\tif (!isUndefined(from._l)) {\r\n\t\t\tto._l = from._l;\r\n\t\t}\r\n\t\tif (!isUndefined(from._strict)) {\r\n\t\t\tto._strict = from._strict;\r\n\t\t}\r\n\t\tif (!isUndefined(from._tzm)) {\r\n\t\t\tto._tzm = from._tzm;\r\n\t\t}\r\n\t\tif (!isUndefined(from._isUTC)) {\r\n\t\t\tto._isUTC = from._isUTC;\r\n\t\t}\r\n\t\tif (!isUndefined(from._offset)) {\r\n\t\t\tto._offset = from._offset;\r\n\t\t}\r\n\t\tif (!isUndefined(from._pf)) {\r\n\t\t\tto._pf = getParsingFlags(from);\r\n\t\t}\r\n\t\tif (!isUndefined(from._locale)) {\r\n\t\t\tto._locale = from._locale;\r\n\t\t}\r\n\r\n\t\tif (momentProperties.length > 0) {\r\n\t\t\tfor (i = 0; i < momentProperties.length; i++) {\r\n\t\t\t\tprop = momentProperties[i];\r\n\t\t\t\tval = from[prop];\r\n\t\t\t\tif (!isUndefined(val)) {\r\n\t\t\t\t\tto[prop] = val;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn to;\r\n\t}\r\n\r\n\tvar updateInProgress = false;\r\n\r\n\t// Moment prototype object\r\n\tfunction Moment(config) {\r\n\t\tcopyConfig(this, config);\r\n\t\tthis._d = new Date(config._d != null ? config._d.getTime() : NaN);\r\n\t\tif (!this.isValid()) {\r\n\t\t\tthis._d = new Date(NaN);\r\n\t\t}\r\n\t\t// Prevent infinite loop in case updateOffset creates new moment\r\n\t\t// objects.\r\n\t\tif (updateInProgress === false) {\r\n\t\t\tupdateInProgress = true;\r\n\t\t\thooks.updateOffset(this);\r\n\t\t\tupdateInProgress = false;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction isMoment(obj) {\r\n\t\treturn obj instanceof Moment || (obj != null && obj._isAMomentObject != null);\r\n\t}\r\n\r\n\tfunction absFloor(number) {\r\n\t\tif (number < 0) {\r\n\t\t\t// -0 -> 0\r\n\t\t\treturn Math.ceil(number) || 0;\r\n\t\t} else {\r\n\t\t\treturn Math.floor(number);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction toInt(argumentForCoercion) {\r\n\t\tvar coercedNumber = +argumentForCoercion,\r\n\t\t\tvalue = 0;\r\n\r\n\t\tif (coercedNumber !== 0 && isFinite(coercedNumber)) {\r\n\t\t\tvalue = absFloor(coercedNumber);\r\n\t\t}\r\n\r\n\t\treturn value;\r\n\t}\r\n\r\n\t// compare two arrays, return the number of differences\r\n\tfunction compareArrays(array1, array2, dontConvert) {\r\n\t\tvar len = Math.min(array1.length, array2.length),\r\n\t\t\tlengthDiff = Math.abs(array1.length - array2.length),\r\n\t\t\tdiffs = 0,\r\n\t\t\ti;\r\n\t\tfor (i = 0; i < len; i++) {\r\n\t\t\tif ((dontConvert && array1[i] !== array2[i]) || (!dontConvert && toInt(array1[i]) !== toInt(array2[i]))) {\r\n\t\t\t\tdiffs++;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn diffs + lengthDiff;\r\n\t}\r\n\r\n\tfunction warn(msg) {\r\n\t\tif (hooks.suppressDeprecationWarnings === false && typeof console !== \"undefined\" && console.warn) {\r\n\t\t\tconsole.warn(\"Deprecation warning: \" + msg);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction deprecate(msg, fn) {\r\n\t\tvar firstTime = true;\r\n\r\n\t\treturn extend(function () {\r\n\t\t\tif (hooks.deprecationHandler != null) {\r\n\t\t\t\thooks.deprecationHandler(null, msg);\r\n\t\t\t}\r\n\t\t\tif (firstTime) {\r\n\t\t\t\tvar args = [];\r\n\t\t\t\tvar arg;\r\n\t\t\t\tfor (var i = 0; i < arguments.length; i++) {\r\n\t\t\t\t\targ = \"\";\r\n\t\t\t\t\tif (typeof arguments[i] === \"object\") {\r\n\t\t\t\t\t\targ += \"\\n[\" + i + \"] \";\r\n\t\t\t\t\t\tfor (var key in arguments[0]) {\r\n\t\t\t\t\t\t\targ += key + \": \" + arguments[0][key] + \", \";\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\targ = arg.slice(0, -2); // Remove trailing comma and space\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\targ = arguments[i];\r\n\t\t\t\t\t}\r\n\t\t\t\t\targs.push(arg);\r\n\t\t\t\t}\r\n\t\t\t\twarn(msg + \"\\nArguments: \" + Array.prototype.slice.call(args).join(\"\") + \"\\n\" + new Error().stack);\r\n\t\t\t\tfirstTime = false;\r\n\t\t\t}\r\n\t\t\treturn fn.apply(this, arguments);\r\n\t\t}, fn);\r\n\t}\r\n\r\n\tvar deprecations = {};\r\n\r\n\tfunction deprecateSimple(name, msg) {\r\n\t\tif (hooks.deprecationHandler != null) {\r\n\t\t\thooks.deprecationHandler(name, msg);\r\n\t\t}\r\n\t\tif (!deprecations[name]) {\r\n\t\t\twarn(msg);\r\n\t\t\tdeprecations[name] = true;\r\n\t\t}\r\n\t}\r\n\r\n\thooks.suppressDeprecationWarnings = false;\r\n\thooks.deprecationHandler = null;\r\n\r\n\tfunction isFunction(input) {\r\n\t\treturn input instanceof Function || Object.prototype.toString.call(input) === \"[object Function]\";\r\n\t}\r\n\r\n\tfunction set(config) {\r\n\t\tvar prop, i;\r\n\t\tfor (i in config) {\r\n\t\t\tprop = config[i];\r\n\t\t\tif (isFunction(prop)) {\r\n\t\t\t\tthis[i] = prop;\r\n\t\t\t} else {\r\n\t\t\t\tthis[\"_\" + i] = prop;\r\n\t\t\t}\r\n\t\t}\r\n\t\tthis._config = config;\r\n\t\t// Lenient ordinal parsing accepts just a number in addition to\r\n\t\t// number + (possibly) stuff coming from _dayOfMonthOrdinalParse.\r\n\t\t// TODO: Remove \"ordinalParse\" fallback in next major release.\r\n\t\tthis._dayOfMonthOrdinalParseLenient = new RegExp((this._dayOfMonthOrdinalParse.source || this._ordinalParse.source) + \"|\" + /\\d{1,2}/.source);\r\n\t}\r\n\r\n\tfunction mergeConfigs(parentConfig, childConfig) {\r\n\t\tvar res = extend({}, parentConfig),\r\n\t\t\tprop;\r\n\t\tfor (prop in childConfig) {\r\n\t\t\tif (hasOwnProp(childConfig, prop)) {\r\n\t\t\t\tif (isObject(parentConfig[prop]) && isObject(childConfig[prop])) {\r\n\t\t\t\t\tres[prop] = {};\r\n\t\t\t\t\textend(res[prop], parentConfig[prop]);\r\n\t\t\t\t\textend(res[prop], childConfig[prop]);\r\n\t\t\t\t} else if (childConfig[prop] != null) {\r\n\t\t\t\t\tres[prop] = childConfig[prop];\r\n\t\t\t\t} else {\r\n\t\t\t\t\tdelete res[prop];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor (prop in parentConfig) {\r\n\t\t\tif (hasOwnProp(parentConfig, prop) && !hasOwnProp(childConfig, prop) && isObject(parentConfig[prop])) {\r\n\t\t\t\t// make sure changes to properties don't modify parent config\r\n\t\t\t\tres[prop] = extend({}, res[prop]);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn res;\r\n\t}\r\n\r\n\tfunction Locale(config) {\r\n\t\tif (config != null) {\r\n\t\t\tthis.set(config);\r\n\t\t}\r\n\t}\r\n\r\n\tvar keys;\r\n\r\n\tif (Object.keys) {\r\n\t\tkeys = Object.keys;\r\n\t} else {\r\n\t\tkeys = function (obj) {\r\n\t\t\tvar i,\r\n\t\t\t\tres = [];\r\n\t\t\tfor (i in obj) {\r\n\t\t\t\tif (hasOwnProp(obj, i)) {\r\n\t\t\t\t\tres.push(i);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn res;\r\n\t\t};\r\n\t}\r\n\r\n\tvar defaultCalendar = {\r\n\t\tsameDay: \"[Today at] LT\",\r\n\t\tnextDay: \"[Tomorrow at] LT\",\r\n\t\tnextWeek: \"dddd [at] LT\",\r\n\t\tlastDay: \"[Yesterday at] LT\",\r\n\t\tlastWeek: \"[Last] dddd [at] LT\",\r\n\t\tsameElse: \"L\",\r\n\t};\r\n\r\n\tfunction calendar(key, mom, now) {\r\n\t\tvar output = this._calendar[key] || this._calendar[\"sameElse\"];\r\n\t\treturn isFunction(output) ? output.call(mom, now) : output;\r\n\t}\r\n\r\n\tvar defaultLongDateFormat = {\r\n\t\tLTS: \"h:mm:ss A\",\r\n\t\tLT: \"h:mm A\",\r\n\t\tL: \"MM/DD/YYYY\",\r\n\t\tLL: \"MMMM D, YYYY\",\r\n\t\tLLL: \"MMMM D, YYYY h:mm A\",\r\n\t\tLLLL: \"dddd, MMMM D, YYYY h:mm A\",\r\n\t};\r\n\r\n\tfunction longDateFormat(key) {\r\n\t\tvar format = this._longDateFormat[key],\r\n\t\t\tformatUpper = this._longDateFormat[key.toUpperCase()];\r\n\r\n\t\tif (format || !formatUpper) {\r\n\t\t\treturn format;\r\n\t\t}\r\n\r\n\t\tthis._longDateFormat[key] = formatUpper.replace(/MMMM|MM|DD|dddd/g, function (val) {\r\n\t\t\treturn val.slice(1);\r\n\t\t});\r\n\r\n\t\treturn this._longDateFormat[key];\r\n\t}\r\n\r\n\tvar defaultInvalidDate = \"Invalid date\";\r\n\r\n\tfunction invalidDate() {\r\n\t\treturn this._invalidDate;\r\n\t}\r\n\r\n\tvar defaultOrdinal = \"%d\";\r\n\tvar defaultDayOfMonthOrdinalParse = /\\d{1,2}/;\r\n\r\n\tfunction ordinal(number) {\r\n\t\treturn this._ordinal.replace(\"%d\", number);\r\n\t}\r\n\r\n\tvar defaultRelativeTime = {\r\n\t\tfuture: \"in %s\",\r\n\t\tpast: \"%s ago\",\r\n\t\ts: \"a few seconds\",\r\n\t\tss: \"%d seconds\",\r\n\t\tm: \"a minute\",\r\n\t\tmm: \"%d minutes\",\r\n\t\th: \"an hour\",\r\n\t\thh: \"%d hours\",\r\n\t\td: \"a day\",\r\n\t\tdd: \"%d days\",\r\n\t\tM: \"a month\",\r\n\t\tMM: \"%d months\",\r\n\t\ty: \"a year\",\r\n\t\tyy: \"%d years\",\r\n\t};\r\n\r\n\tfunction relativeTime(number, withoutSuffix, string, isFuture) {\r\n\t\tvar output = this._relativeTime[string];\r\n\t\treturn isFunction(output) ? output(number, withoutSuffix, string, isFuture) : output.replace(/%d/i, number);\r\n\t}\r\n\r\n\tfunction pastFuture(diff, output) {\r\n\t\tvar format = this._relativeTime[diff > 0 ? \"future\" : \"past\"];\r\n\t\treturn isFunction(format) ? format(output) : format.replace(/%s/i, output);\r\n\t}\r\n\r\n\tvar aliases = {};\r\n\r\n\tfunction addUnitAlias(unit, shorthand) {\r\n\t\tvar lowerCase = unit.toLowerCase();\r\n\t\taliases[lowerCase] = aliases[lowerCase + \"s\"] = aliases[shorthand] = unit;\r\n\t}\r\n\r\n\tfunction normalizeUnits(units) {\r\n\t\treturn typeof units === \"string\" ? aliases[units] || aliases[units.toLowerCase()] : undefined;\r\n\t}\r\n\r\n\tfunction normalizeObjectUnits(inputObject) {\r\n\t\tvar normalizedInput = {},\r\n\t\t\tnormalizedProp,\r\n\t\t\tprop;\r\n\r\n\t\tfor (prop in inputObject) {\r\n\t\t\tif (hasOwnProp(inputObject, prop)) {\r\n\t\t\t\tnormalizedProp = normalizeUnits(prop);\r\n\t\t\t\tif (normalizedProp) {\r\n\t\t\t\t\tnormalizedInput[normalizedProp] = inputObject[prop];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn normalizedInput;\r\n\t}\r\n\r\n\tvar priorities = {};\r\n\r\n\tfunction addUnitPriority(unit, priority) {\r\n\t\tpriorities[unit] = priority;\r\n\t}\r\n\r\n\tfunction getPrioritizedUnits(unitsObj) {\r\n\t\tvar units = [];\r\n\t\tfor (var u in unitsObj) {\r\n\t\t\tunits.push({ unit: u, priority: priorities[u] });\r\n\t\t}\r\n\t\tunits.sort(function (a, b) {\r\n\t\t\treturn a.priority - b.priority;\r\n\t\t});\r\n\t\treturn units;\r\n\t}\r\n\r\n\tfunction zeroFill(number, targetLength, forceSign) {\r\n\t\tvar absNumber = \"\" + Math.abs(number),\r\n\t\t\tzerosToFill = targetLength - absNumber.length,\r\n\t\t\tsign = number >= 0;\r\n\t\treturn (sign ? (forceSign ? \"+\" : \"\") : \"-\") + Math.pow(10, Math.max(0, zerosToFill)).toString().substr(1) + absNumber;\r\n\t}\r\n\r\n\tvar formattingTokens = /(\\[[^\\[]*\\])|(\\\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g;\r\n\r\n\tvar localFormattingTokens = /(\\[[^\\[]*\\])|(\\\\)?(LTS|LT|LL?L?L?|l{1,4})/g;\r\n\r\n\tvar formatFunctions = {};\r\n\r\n\tvar formatTokenFunctions = {};\r\n\r\n\t// token:    'M'\r\n\t// padded:   ['MM', 2]\r\n\t// ordinal:  'Mo'\r\n\t// callback: function () { this.month() + 1 }\r\n\tfunction addFormatToken(token, padded, ordinal, callback) {\r\n\t\tvar func = callback;\r\n\t\tif (typeof callback === \"string\") {\r\n\t\t\tfunc = function () {\r\n\t\t\t\treturn this[callback]();\r\n\t\t\t};\r\n\t\t}\r\n\t\tif (token) {\r\n\t\t\tformatTokenFunctions[token] = func;\r\n\t\t}\r\n\t\tif (padded) {\r\n\t\t\tformatTokenFunctions[padded[0]] = function () {\r\n\t\t\t\treturn zeroFill(func.apply(this, arguments), padded[1], padded[2]);\r\n\t\t\t};\r\n\t\t}\r\n\t\tif (ordinal) {\r\n\t\t\tformatTokenFunctions[ordinal] = function () {\r\n\t\t\t\treturn this.localeData().ordinal(func.apply(this, arguments), token);\r\n\t\t\t};\r\n\t\t}\r\n\t}\r\n\r\n\tfunction removeFormattingTokens(input) {\r\n\t\tif (input.match(/\\[[\\s\\S]/)) {\r\n\t\t\treturn input.replace(/^\\[|\\]$/g, \"\");\r\n\t\t}\r\n\t\treturn input.replace(/\\\\/g, \"\");\r\n\t}\r\n\r\n\tfunction makeFormatFunction(format) {\r\n\t\tvar array = format.match(formattingTokens),\r\n\t\t\ti,\r\n\t\t\tlength;\r\n\r\n\t\tfor (i = 0, length = array.length; i < length; i++) {\r\n\t\t\tif (formatTokenFunctions[array[i]]) {\r\n\t\t\t\tarray[i] = formatTokenFunctions[array[i]];\r\n\t\t\t} else {\r\n\t\t\t\tarray[i] = removeFormattingTokens(array[i]);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn function (mom) {\r\n\t\t\tvar output = \"\",\r\n\t\t\t\ti;\r\n\t\t\tfor (i = 0; i < length; i++) {\r\n\t\t\t\toutput += isFunction(array[i]) ? array[i].call(mom, format) : array[i];\r\n\t\t\t}\r\n\t\t\treturn output;\r\n\t\t};\r\n\t}\r\n\r\n\t// format date using native date object\r\n\tfunction formatMoment(m, format) {\r\n\t\tif (!m.isValid()) {\r\n\t\t\treturn m.localeData().invalidDate();\r\n\t\t}\r\n\r\n\t\tformat = expandFormat(format, m.localeData());\r\n\t\tformatFunctions[format] = formatFunctions[format] || makeFormatFunction(format);\r\n\r\n\t\treturn formatFunctions[format](m);\r\n\t}\r\n\r\n\tfunction expandFormat(format, locale) {\r\n\t\tvar i = 5;\r\n\r\n\t\tfunction replaceLongDateFormatTokens(input) {\r\n\t\t\treturn locale.longDateFormat(input) || input;\r\n\t\t}\r\n\r\n\t\tlocalFormattingTokens.lastIndex = 0;\r\n\t\twhile (i >= 0 && localFormattingTokens.test(format)) {\r\n\t\t\tformat = format.replace(localFormattingTokens, replaceLongDateFormatTokens);\r\n\t\t\tlocalFormattingTokens.lastIndex = 0;\r\n\t\t\ti -= 1;\r\n\t\t}\r\n\r\n\t\treturn format;\r\n\t}\r\n\r\n\tvar match1 = /\\d/; //       0 - 9\r\n\tvar match2 = /\\d\\d/; //      00 - 99\r\n\tvar match3 = /\\d{3}/; //     000 - 999\r\n\tvar match4 = /\\d{4}/; //    0000 - 9999\r\n\tvar match6 = /[+-]?\\d{6}/; // -999999 - 999999\r\n\tvar match1to2 = /\\d\\d?/; //       0 - 99\r\n\tvar match3to4 = /\\d\\d\\d\\d?/; //     999 - 9999\r\n\tvar match5to6 = /\\d\\d\\d\\d\\d\\d?/; //   99999 - 999999\r\n\tvar match1to3 = /\\d{1,3}/; //       0 - 999\r\n\tvar match1to4 = /\\d{1,4}/; //       0 - 9999\r\n\tvar match1to6 = /[+-]?\\d{1,6}/; // -999999 - 999999\r\n\r\n\tvar matchUnsigned = /\\d+/; //       0 - inf\r\n\tvar matchSigned = /[+-]?\\d+/; //    -inf - inf\r\n\r\n\tvar matchOffset = /Z|[+-]\\d\\d:?\\d\\d/gi; // +00:00 -00:00 +0000 -0000 or Z\r\n\tvar matchShortOffset = /Z|[+-]\\d\\d(?::?\\d\\d)?/gi; // +00 -00 +00:00 -00:00 +0000 -0000 or Z\r\n\r\n\tvar matchTimestamp = /[+-]?\\d+(\\.\\d{1,3})?/; // 123456789 123456789.123\r\n\r\n\t// any word (or two) characters or numbers including two/three word month in arabic.\r\n\t// includes scottish gaelic two word and hyphenated months\r\n\tvar matchWord = /[0-9]{0,256}['a-z\\u00A0-\\u05FF\\u0700-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFF07\\uFF10-\\uFFEF]{1,256}|[\\u0600-\\u06FF\\/]{1,256}(\\s*?[\\u0600-\\u06FF]{1,256}){1,2}/i;\r\n\r\n\tvar regexes = {};\r\n\r\n\tfunction addRegexToken(token, regex, strictRegex) {\r\n\t\tregexes[token] = isFunction(regex)\r\n\t\t\t? regex\r\n\t\t\t: function (isStrict, localeData) {\r\n\t\t\t\t\treturn isStrict && strictRegex ? strictRegex : regex;\r\n\t\t\t  };\r\n\t}\r\n\r\n\tfunction getParseRegexForToken(token, config) {\r\n\t\tif (!hasOwnProp(regexes, token)) {\r\n\t\t\treturn new RegExp(unescapeFormat(token));\r\n\t\t}\r\n\r\n\t\treturn regexes[token](config._strict, config._locale);\r\n\t}\r\n\r\n\t// Code from http://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript\r\n\tfunction unescapeFormat(s) {\r\n\t\treturn regexEscape(\r\n\t\t\ts.replace(\"\\\\\", \"\").replace(/\\\\(\\[)|\\\\(\\])|\\[([^\\]\\[]*)\\]|\\\\(.)/g, function (matched, p1, p2, p3, p4) {\r\n\t\t\t\treturn p1 || p2 || p3 || p4;\r\n\t\t\t})\r\n\t\t);\r\n\t}\r\n\r\n\tfunction regexEscape(s) {\r\n\t\treturn s.replace(/[-\\/\\\\^$*+?.()|[\\]{}]/g, \"\\\\$&\");\r\n\t}\r\n\r\n\tvar tokens = {};\r\n\r\n\tfunction addParseToken(token, callback) {\r\n\t\tvar i,\r\n\t\t\tfunc = callback;\r\n\t\tif (typeof token === \"string\") {\r\n\t\t\ttoken = [token];\r\n\t\t}\r\n\t\tif (isNumber(callback)) {\r\n\t\t\tfunc = function (input, array) {\r\n\t\t\t\tarray[callback] = toInt(input);\r\n\t\t\t};\r\n\t\t}\r\n\t\tfor (i = 0; i < token.length; i++) {\r\n\t\t\ttokens[token[i]] = func;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction addWeekParseToken(token, callback) {\r\n\t\taddParseToken(token, function (input, array, config, token) {\r\n\t\t\tconfig._w = config._w || {};\r\n\t\t\tcallback(input, config._w, config, token);\r\n\t\t});\r\n\t}\r\n\r\n\tfunction addTimeToArrayFromToken(token, input, config) {\r\n\t\tif (input != null && hasOwnProp(tokens, token)) {\r\n\t\t\ttokens[token](input, config._a, config, token);\r\n\t\t}\r\n\t}\r\n\r\n\tvar YEAR = 0;\r\n\tvar MONTH = 1;\r\n\tvar DATE = 2;\r\n\tvar HOUR = 3;\r\n\tvar MINUTE = 4;\r\n\tvar SECOND = 5;\r\n\tvar MILLISECOND = 6;\r\n\tvar WEEK = 7;\r\n\tvar WEEKDAY = 8;\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"Y\", 0, 0, function () {\r\n\t\tvar y = this.year();\r\n\t\treturn y <= 9999 ? \"\" + y : \"+\" + y;\r\n\t});\r\n\r\n\taddFormatToken(0, [\"YY\", 2], 0, function () {\r\n\t\treturn this.year() % 100;\r\n\t});\r\n\r\n\taddFormatToken(0, [\"YYYY\", 4], 0, \"year\");\r\n\taddFormatToken(0, [\"YYYYY\", 5], 0, \"year\");\r\n\taddFormatToken(0, [\"YYYYYY\", 6, true], 0, \"year\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"year\", \"y\");\r\n\r\n\t// PRIORITIES\r\n\r\n\taddUnitPriority(\"year\", 1);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"Y\", matchSigned);\r\n\taddRegexToken(\"YY\", match1to2, match2);\r\n\taddRegexToken(\"YYYY\", match1to4, match4);\r\n\taddRegexToken(\"YYYYY\", match1to6, match6);\r\n\taddRegexToken(\"YYYYYY\", match1to6, match6);\r\n\r\n\taddParseToken([\"YYYYY\", \"YYYYYY\"], YEAR);\r\n\taddParseToken(\"YYYY\", function (input, array) {\r\n\t\tarray[YEAR] = input.length === 2 ? hooks.parseTwoDigitYear(input) : toInt(input);\r\n\t});\r\n\taddParseToken(\"YY\", function (input, array) {\r\n\t\tarray[YEAR] = hooks.parseTwoDigitYear(input);\r\n\t});\r\n\taddParseToken(\"Y\", function (input, array) {\r\n\t\tarray[YEAR] = parseInt(input, 10);\r\n\t});\r\n\r\n\t// HELPERS\r\n\r\n\tfunction daysInYear(year) {\r\n\t\treturn isLeapYear(year) ? 366 : 365;\r\n\t}\r\n\r\n\tfunction isLeapYear(year) {\r\n\t\treturn (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;\r\n\t}\r\n\r\n\t// HOOKS\r\n\r\n\thooks.parseTwoDigitYear = function (input) {\r\n\t\treturn toInt(input) + (toInt(input) > 68 ? 1900 : 2000);\r\n\t};\r\n\r\n\t// MOMENTS\r\n\r\n\tvar getSetYear = makeGetSet(\"FullYear\", true);\r\n\r\n\tfunction getIsLeapYear() {\r\n\t\treturn isLeapYear(this.year());\r\n\t}\r\n\r\n\tfunction makeGetSet(unit, keepTime) {\r\n\t\treturn function (value) {\r\n\t\t\tif (value != null) {\r\n\t\t\t\tset$1(this, unit, value);\r\n\t\t\t\thooks.updateOffset(this, keepTime);\r\n\t\t\t\treturn this;\r\n\t\t\t} else {\r\n\t\t\t\treturn get(this, unit);\r\n\t\t\t}\r\n\t\t};\r\n\t}\r\n\r\n\tfunction get(mom, unit) {\r\n\t\treturn mom.isValid() ? mom._d[\"get\" + (mom._isUTC ? \"UTC\" : \"\") + unit]() : NaN;\r\n\t}\r\n\r\n\tfunction set$1(mom, unit, value) {\r\n\t\tif (mom.isValid() && !isNaN(value)) {\r\n\t\t\tif (unit === \"FullYear\" && isLeapYear(mom.year()) && mom.month() === 1 && mom.date() === 29) {\r\n\t\t\t\tmom._d[\"set\" + (mom._isUTC ? \"UTC\" : \"\") + unit](value, mom.month(), daysInMonth(value, mom.month()));\r\n\t\t\t} else {\r\n\t\t\t\tmom._d[\"set\" + (mom._isUTC ? \"UTC\" : \"\") + unit](value);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction stringGet(units) {\r\n\t\tunits = normalizeUnits(units);\r\n\t\tif (isFunction(this[units])) {\r\n\t\t\treturn this[units]();\r\n\t\t}\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction stringSet(units, value) {\r\n\t\tif (typeof units === \"object\") {\r\n\t\t\tunits = normalizeObjectUnits(units);\r\n\t\t\tvar prioritized = getPrioritizedUnits(units);\r\n\t\t\tfor (var i = 0; i < prioritized.length; i++) {\r\n\t\t\t\tthis[prioritized[i].unit](units[prioritized[i].unit]);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tunits = normalizeUnits(units);\r\n\t\t\tif (isFunction(this[units])) {\r\n\t\t\t\treturn this[units](value);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction mod(n, x) {\r\n\t\treturn ((n % x) + x) % x;\r\n\t}\r\n\r\n\tvar indexOf;\r\n\r\n\tif (Array.prototype.indexOf) {\r\n\t\tindexOf = Array.prototype.indexOf;\r\n\t} else {\r\n\t\tindexOf = function (o) {\r\n\t\t\t// I know\r\n\t\t\tvar i;\r\n\t\t\tfor (i = 0; i < this.length; ++i) {\r\n\t\t\t\tif (this[i] === o) {\r\n\t\t\t\t\treturn i;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn -1;\r\n\t\t};\r\n\t}\r\n\r\n\tfunction daysInMonth(year, month) {\r\n\t\tif (isNaN(year) || isNaN(month)) {\r\n\t\t\treturn NaN;\r\n\t\t}\r\n\t\tvar modMonth = mod(month, 12);\r\n\t\tyear += (month - modMonth) / 12;\r\n\t\treturn modMonth === 1 ? (isLeapYear(year) ? 29 : 28) : 31 - ((modMonth % 7) % 2);\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"M\", [\"MM\", 2], \"Mo\", function () {\r\n\t\treturn this.month() + 1;\r\n\t});\r\n\r\n\taddFormatToken(\"MMM\", 0, 0, function (format) {\r\n\t\treturn this.localeData().monthsShort(this, format);\r\n\t});\r\n\r\n\taddFormatToken(\"MMMM\", 0, 0, function (format) {\r\n\t\treturn this.localeData().months(this, format);\r\n\t});\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"month\", \"M\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"month\", 8);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"M\", match1to2);\r\n\taddRegexToken(\"MM\", match1to2, match2);\r\n\taddRegexToken(\"MMM\", function (isStrict, locale) {\r\n\t\treturn locale.monthsShortRegex(isStrict);\r\n\t});\r\n\taddRegexToken(\"MMMM\", function (isStrict, locale) {\r\n\t\treturn locale.monthsRegex(isStrict);\r\n\t});\r\n\r\n\taddParseToken([\"M\", \"MM\"], function (input, array) {\r\n\t\tarray[MONTH] = toInt(input) - 1;\r\n\t});\r\n\r\n\taddParseToken([\"MMM\", \"MMMM\"], function (input, array, config, token) {\r\n\t\tvar month = config._locale.monthsParse(input, token, config._strict);\r\n\t\t// if we didn't find a month name, mark the date as invalid.\r\n\t\tif (month != null) {\r\n\t\t\tarray[MONTH] = month;\r\n\t\t} else {\r\n\t\t\tgetParsingFlags(config).invalidMonth = input;\r\n\t\t}\r\n\t});\r\n\r\n\t// LOCALES\r\n\r\n\tvar MONTHS_IN_FORMAT = /D[oD]?(\\[[^\\[\\]]*\\]|\\s)+MMMM?/;\r\n\tvar defaultLocaleMonths = \"January_February_March_April_May_June_July_August_September_October_November_December\".split(\"_\");\r\n\tfunction localeMonths(m, format) {\r\n\t\tif (!m) {\r\n\t\t\treturn isArray(this._months) ? this._months : this._months[\"standalone\"];\r\n\t\t}\r\n\t\treturn isArray(this._months) ? this._months[m.month()] : this._months[(this._months.isFormat || MONTHS_IN_FORMAT).test(format) ? \"format\" : \"standalone\"][m.month()];\r\n\t}\r\n\r\n\tvar defaultLocaleMonthsShort = \"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec\".split(\"_\");\r\n\tfunction localeMonthsShort(m, format) {\r\n\t\tif (!m) {\r\n\t\t\treturn isArray(this._monthsShort) ? this._monthsShort : this._monthsShort[\"standalone\"];\r\n\t\t}\r\n\t\treturn isArray(this._monthsShort) ? this._monthsShort[m.month()] : this._monthsShort[MONTHS_IN_FORMAT.test(format) ? \"format\" : \"standalone\"][m.month()];\r\n\t}\r\n\r\n\tfunction handleStrictParse(monthName, format, strict) {\r\n\t\tvar i,\r\n\t\t\tii,\r\n\t\t\tmom,\r\n\t\t\tllc = monthName.toLocaleLowerCase();\r\n\t\tif (!this._monthsParse) {\r\n\t\t\t// this is not used\r\n\t\t\tthis._monthsParse = [];\r\n\t\t\tthis._longMonthsParse = [];\r\n\t\t\tthis._shortMonthsParse = [];\r\n\t\t\tfor (i = 0; i < 12; ++i) {\r\n\t\t\t\tmom = createUTC([2000, i]);\r\n\t\t\t\tthis._shortMonthsParse[i] = this.monthsShort(mom, \"\").toLocaleLowerCase();\r\n\t\t\t\tthis._longMonthsParse[i] = this.months(mom, \"\").toLocaleLowerCase();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (strict) {\r\n\t\t\tif (format === \"MMM\") {\r\n\t\t\t\tii = indexOf.call(this._shortMonthsParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else {\r\n\t\t\t\tii = indexOf.call(this._longMonthsParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (format === \"MMM\") {\r\n\t\t\t\tii = indexOf.call(this._shortMonthsParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._longMonthsParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else {\r\n\t\t\t\tii = indexOf.call(this._longMonthsParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._shortMonthsParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tfunction localeMonthsParse(monthName, format, strict) {\r\n\t\tvar i, mom, regex;\r\n\r\n\t\tif (this._monthsParseExact) {\r\n\t\t\treturn handleStrictParse.call(this, monthName, format, strict);\r\n\t\t}\r\n\r\n\t\tif (!this._monthsParse) {\r\n\t\t\tthis._monthsParse = [];\r\n\t\t\tthis._longMonthsParse = [];\r\n\t\t\tthis._shortMonthsParse = [];\r\n\t\t}\r\n\r\n\t\t// TODO: add sorting\r\n\t\t// Sorting makes sure if one month (or abbr) is a prefix of another\r\n\t\t// see sorting in computeMonthsParse\r\n\t\tfor (i = 0; i < 12; i++) {\r\n\t\t\t// make the regex if we don't have it already\r\n\t\t\tmom = createUTC([2000, i]);\r\n\t\t\tif (strict && !this._longMonthsParse[i]) {\r\n\t\t\t\tthis._longMonthsParse[i] = new RegExp(\"^\" + this.months(mom, \"\").replace(\".\", \"\") + \"$\", \"i\");\r\n\t\t\t\tthis._shortMonthsParse[i] = new RegExp(\"^\" + this.monthsShort(mom, \"\").replace(\".\", \"\") + \"$\", \"i\");\r\n\t\t\t}\r\n\t\t\tif (!strict && !this._monthsParse[i]) {\r\n\t\t\t\tregex = \"^\" + this.months(mom, \"\") + \"|^\" + this.monthsShort(mom, \"\");\r\n\t\t\t\tthis._monthsParse[i] = new RegExp(regex.replace(\".\", \"\"), \"i\");\r\n\t\t\t}\r\n\t\t\t// test the regex\r\n\t\t\tif (strict && format === \"MMMM\" && this._longMonthsParse[i].test(monthName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t} else if (strict && format === \"MMM\" && this._shortMonthsParse[i].test(monthName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t} else if (!strict && this._monthsParse[i].test(monthName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction setMonth(mom, value) {\r\n\t\tvar dayOfMonth;\r\n\r\n\t\tif (!mom.isValid()) {\r\n\t\t\t// No op\r\n\t\t\treturn mom;\r\n\t\t}\r\n\r\n\t\tif (typeof value === \"string\") {\r\n\t\t\tif (/^\\d+$/.test(value)) {\r\n\t\t\t\tvalue = toInt(value);\r\n\t\t\t} else {\r\n\t\t\t\tvalue = mom.localeData().monthsParse(value);\r\n\t\t\t\t// TODO: Another silent failure?\r\n\t\t\t\tif (!isNumber(value)) {\r\n\t\t\t\t\treturn mom;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tdayOfMonth = Math.min(mom.date(), daysInMonth(mom.year(), value));\r\n\t\tmom._d[\"set\" + (mom._isUTC ? \"UTC\" : \"\") + \"Month\"](value, dayOfMonth);\r\n\t\treturn mom;\r\n\t}\r\n\r\n\tfunction getSetMonth(value) {\r\n\t\tif (value != null) {\r\n\t\t\tsetMonth(this, value);\r\n\t\t\thooks.updateOffset(this, true);\r\n\t\t\treturn this;\r\n\t\t} else {\r\n\t\t\treturn get(this, \"Month\");\r\n\t\t}\r\n\t}\r\n\r\n\tfunction getDaysInMonth() {\r\n\t\treturn daysInMonth(this.year(), this.month());\r\n\t}\r\n\r\n\tvar defaultMonthsShortRegex = matchWord;\r\n\tfunction monthsShortRegex(isStrict) {\r\n\t\tif (this._monthsParseExact) {\r\n\t\t\tif (!hasOwnProp(this, \"_monthsRegex\")) {\r\n\t\t\t\tcomputeMonthsParse.call(this);\r\n\t\t\t}\r\n\t\t\tif (isStrict) {\r\n\t\t\t\treturn this._monthsShortStrictRegex;\r\n\t\t\t} else {\r\n\t\t\t\treturn this._monthsShortRegex;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (!hasOwnProp(this, \"_monthsShortRegex\")) {\r\n\t\t\t\tthis._monthsShortRegex = defaultMonthsShortRegex;\r\n\t\t\t}\r\n\t\t\treturn this._monthsShortStrictRegex && isStrict ? this._monthsShortStrictRegex : this._monthsShortRegex;\r\n\t\t}\r\n\t}\r\n\r\n\tvar defaultMonthsRegex = matchWord;\r\n\tfunction monthsRegex(isStrict) {\r\n\t\tif (this._monthsParseExact) {\r\n\t\t\tif (!hasOwnProp(this, \"_monthsRegex\")) {\r\n\t\t\t\tcomputeMonthsParse.call(this);\r\n\t\t\t}\r\n\t\t\tif (isStrict) {\r\n\t\t\t\treturn this._monthsStrictRegex;\r\n\t\t\t} else {\r\n\t\t\t\treturn this._monthsRegex;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (!hasOwnProp(this, \"_monthsRegex\")) {\r\n\t\t\t\tthis._monthsRegex = defaultMonthsRegex;\r\n\t\t\t}\r\n\t\t\treturn this._monthsStrictRegex && isStrict ? this._monthsStrictRegex : this._monthsRegex;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction computeMonthsParse() {\r\n\t\tfunction cmpLenRev(a, b) {\r\n\t\t\treturn b.length - a.length;\r\n\t\t}\r\n\r\n\t\tvar shortPieces = [],\r\n\t\t\tlongPieces = [],\r\n\t\t\tmixedPieces = [],\r\n\t\t\ti,\r\n\t\t\tmom;\r\n\t\tfor (i = 0; i < 12; i++) {\r\n\t\t\t// make the regex if we don't have it already\r\n\t\t\tmom = createUTC([2000, i]);\r\n\t\t\tshortPieces.push(this.monthsShort(mom, \"\"));\r\n\t\t\tlongPieces.push(this.months(mom, \"\"));\r\n\t\t\tmixedPieces.push(this.months(mom, \"\"));\r\n\t\t\tmixedPieces.push(this.monthsShort(mom, \"\"));\r\n\t\t}\r\n\t\t// Sorting makes sure if one month (or abbr) is a prefix of another it\r\n\t\t// will match the longer piece.\r\n\t\tshortPieces.sort(cmpLenRev);\r\n\t\tlongPieces.sort(cmpLenRev);\r\n\t\tmixedPieces.sort(cmpLenRev);\r\n\t\tfor (i = 0; i < 12; i++) {\r\n\t\t\tshortPieces[i] = regexEscape(shortPieces[i]);\r\n\t\t\tlongPieces[i] = regexEscape(longPieces[i]);\r\n\t\t}\r\n\t\tfor (i = 0; i < 24; i++) {\r\n\t\t\tmixedPieces[i] = regexEscape(mixedPieces[i]);\r\n\t\t}\r\n\r\n\t\tthis._monthsRegex = new RegExp(\"^(\" + mixedPieces.join(\"|\") + \")\", \"i\");\r\n\t\tthis._monthsShortRegex = this._monthsRegex;\r\n\t\tthis._monthsStrictRegex = new RegExp(\"^(\" + longPieces.join(\"|\") + \")\", \"i\");\r\n\t\tthis._monthsShortStrictRegex = new RegExp(\"^(\" + shortPieces.join(\"|\") + \")\", \"i\");\r\n\t}\r\n\r\n\tfunction createDate(y, m, d, h, M, s, ms) {\r\n\t\t// can't just apply() to create a date:\r\n\t\t// https://stackoverflow.com/q/181348\r\n\t\tvar date = new Date(y, m, d, h, M, s, ms);\r\n\r\n\t\t// the date constructor remaps years 0-99 to 1900-1999\r\n\t\tif (y < 100 && y >= 0 && isFinite(date.getFullYear())) {\r\n\t\t\tdate.setFullYear(y);\r\n\t\t}\r\n\t\treturn date;\r\n\t}\r\n\r\n\tfunction createUTCDate(y) {\r\n\t\tvar date = new Date(Date.UTC.apply(null, arguments));\r\n\r\n\t\t// the Date.UTC function remaps years 0-99 to 1900-1999\r\n\t\tif (y < 100 && y >= 0 && isFinite(date.getUTCFullYear())) {\r\n\t\t\tdate.setUTCFullYear(y);\r\n\t\t}\r\n\t\treturn date;\r\n\t}\r\n\r\n\t// start-of-first-week - start-of-year\r\n\tfunction firstWeekOffset(year, dow, doy) {\r\n\t\tvar // first-week day -- which january is always in the first week (4 for iso, 1 for other)\r\n\t\t\tfwd = 7 + dow - doy,\r\n\t\t\t// first-week day local weekday -- which local weekday is fwd\r\n\t\t\tfwdlw = (7 + createUTCDate(year, 0, fwd).getUTCDay() - dow) % 7;\r\n\r\n\t\treturn -fwdlw + fwd - 1;\r\n\t}\r\n\r\n\t// https://en.wikipedia.org/wiki/ISO_week_date#Calculating_a_date_given_the_year.2C_week_number_and_weekday\r\n\tfunction dayOfYearFromWeeks(year, week, weekday, dow, doy) {\r\n\t\tvar localWeekday = (7 + weekday - dow) % 7,\r\n\t\t\tweekOffset = firstWeekOffset(year, dow, doy),\r\n\t\t\tdayOfYear = 1 + 7 * (week - 1) + localWeekday + weekOffset,\r\n\t\t\tresYear,\r\n\t\t\tresDayOfYear;\r\n\r\n\t\tif (dayOfYear <= 0) {\r\n\t\t\tresYear = year - 1;\r\n\t\t\tresDayOfYear = daysInYear(resYear) + dayOfYear;\r\n\t\t} else if (dayOfYear > daysInYear(year)) {\r\n\t\t\tresYear = year + 1;\r\n\t\t\tresDayOfYear = dayOfYear - daysInYear(year);\r\n\t\t} else {\r\n\t\t\tresYear = year;\r\n\t\t\tresDayOfYear = dayOfYear;\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\tyear: resYear,\r\n\t\t\tdayOfYear: resDayOfYear,\r\n\t\t};\r\n\t}\r\n\r\n\tfunction weekOfYear(mom, dow, doy) {\r\n\t\tvar weekOffset = firstWeekOffset(mom.year(), dow, doy),\r\n\t\t\tweek = Math.floor((mom.dayOfYear() - weekOffset - 1) / 7) + 1,\r\n\t\t\tresWeek,\r\n\t\t\tresYear;\r\n\r\n\t\tif (week < 1) {\r\n\t\t\tresYear = mom.year() - 1;\r\n\t\t\tresWeek = week + weeksInYear(resYear, dow, doy);\r\n\t\t} else if (week > weeksInYear(mom.year(), dow, doy)) {\r\n\t\t\tresWeek = week - weeksInYear(mom.year(), dow, doy);\r\n\t\t\tresYear = mom.year() + 1;\r\n\t\t} else {\r\n\t\t\tresYear = mom.year();\r\n\t\t\tresWeek = week;\r\n\t\t}\r\n\r\n\t\treturn {\r\n\t\t\tweek: resWeek,\r\n\t\t\tyear: resYear,\r\n\t\t};\r\n\t}\r\n\r\n\tfunction weeksInYear(year, dow, doy) {\r\n\t\tvar weekOffset = firstWeekOffset(year, dow, doy),\r\n\t\t\tweekOffsetNext = firstWeekOffset(year + 1, dow, doy);\r\n\t\treturn (daysInYear(year) - weekOffset + weekOffsetNext) / 7;\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"w\", [\"ww\", 2], \"wo\", \"week\");\r\n\taddFormatToken(\"W\", [\"WW\", 2], \"Wo\", \"isoWeek\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"week\", \"w\");\r\n\taddUnitAlias(\"isoWeek\", \"W\");\r\n\r\n\t// PRIORITIES\r\n\r\n\taddUnitPriority(\"week\", 5);\r\n\taddUnitPriority(\"isoWeek\", 5);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"w\", match1to2);\r\n\taddRegexToken(\"ww\", match1to2, match2);\r\n\taddRegexToken(\"W\", match1to2);\r\n\taddRegexToken(\"WW\", match1to2, match2);\r\n\r\n\taddWeekParseToken([\"w\", \"ww\", \"W\", \"WW\"], function (input, week, config, token) {\r\n\t\tweek[token.substr(0, 1)] = toInt(input);\r\n\t});\r\n\r\n\t// HELPERS\r\n\r\n\t// LOCALES\r\n\r\n\tfunction localeWeek(mom) {\r\n\t\treturn weekOfYear(mom, this._week.dow, this._week.doy).week;\r\n\t}\r\n\r\n\tvar defaultLocaleWeek = {\r\n\t\tdow: 0, // Sunday is the first day of the week.\r\n\t\tdoy: 6, // The week that contains Jan 1st is the first week of the year.\r\n\t};\r\n\r\n\tfunction localeFirstDayOfWeek() {\r\n\t\treturn this._week.dow;\r\n\t}\r\n\r\n\tfunction localeFirstDayOfYear() {\r\n\t\treturn this._week.doy;\r\n\t}\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getSetWeek(input) {\r\n\t\tvar week = this.localeData().week(this);\r\n\t\treturn input == null ? week : this.add((input - week) * 7, \"d\");\r\n\t}\r\n\r\n\tfunction getSetISOWeek(input) {\r\n\t\tvar week = weekOfYear(this, 1, 4).week;\r\n\t\treturn input == null ? week : this.add((input - week) * 7, \"d\");\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"d\", 0, \"do\", \"day\");\r\n\r\n\taddFormatToken(\"dd\", 0, 0, function (format) {\r\n\t\treturn this.localeData().weekdaysMin(this, format);\r\n\t});\r\n\r\n\taddFormatToken(\"ddd\", 0, 0, function (format) {\r\n\t\treturn this.localeData().weekdaysShort(this, format);\r\n\t});\r\n\r\n\taddFormatToken(\"dddd\", 0, 0, function (format) {\r\n\t\treturn this.localeData().weekdays(this, format);\r\n\t});\r\n\r\n\taddFormatToken(\"e\", 0, 0, \"weekday\");\r\n\taddFormatToken(\"E\", 0, 0, \"isoWeekday\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"day\", \"d\");\r\n\taddUnitAlias(\"weekday\", \"e\");\r\n\taddUnitAlias(\"isoWeekday\", \"E\");\r\n\r\n\t// PRIORITY\r\n\taddUnitPriority(\"day\", 11);\r\n\taddUnitPriority(\"weekday\", 11);\r\n\taddUnitPriority(\"isoWeekday\", 11);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"d\", match1to2);\r\n\taddRegexToken(\"e\", match1to2);\r\n\taddRegexToken(\"E\", match1to2);\r\n\taddRegexToken(\"dd\", function (isStrict, locale) {\r\n\t\treturn locale.weekdaysMinRegex(isStrict);\r\n\t});\r\n\taddRegexToken(\"ddd\", function (isStrict, locale) {\r\n\t\treturn locale.weekdaysShortRegex(isStrict);\r\n\t});\r\n\taddRegexToken(\"dddd\", function (isStrict, locale) {\r\n\t\treturn locale.weekdaysRegex(isStrict);\r\n\t});\r\n\r\n\taddWeekParseToken([\"dd\", \"ddd\", \"dddd\"], function (input, week, config, token) {\r\n\t\tvar weekday = config._locale.weekdaysParse(input, token, config._strict);\r\n\t\t// if we didn't get a weekday name, mark the date as invalid\r\n\t\tif (weekday != null) {\r\n\t\t\tweek.d = weekday;\r\n\t\t} else {\r\n\t\t\tgetParsingFlags(config).invalidWeekday = input;\r\n\t\t}\r\n\t});\r\n\r\n\taddWeekParseToken([\"d\", \"e\", \"E\"], function (input, week, config, token) {\r\n\t\tweek[token] = toInt(input);\r\n\t});\r\n\r\n\t// HELPERS\r\n\r\n\tfunction parseWeekday(input, locale) {\r\n\t\tif (typeof input !== \"string\") {\r\n\t\t\treturn input;\r\n\t\t}\r\n\r\n\t\tif (!isNaN(input)) {\r\n\t\t\treturn parseInt(input, 10);\r\n\t\t}\r\n\r\n\t\tinput = locale.weekdaysParse(input);\r\n\t\tif (typeof input === \"number\") {\r\n\t\t\treturn input;\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t}\r\n\r\n\tfunction parseIsoWeekday(input, locale) {\r\n\t\tif (typeof input === \"string\") {\r\n\t\t\treturn locale.weekdaysParse(input) % 7 || 7;\r\n\t\t}\r\n\t\treturn isNaN(input) ? null : input;\r\n\t}\r\n\r\n\t// LOCALES\r\n\r\n\tvar defaultLocaleWeekdays = \"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday\".split(\"_\");\r\n\tfunction localeWeekdays(m, format) {\r\n\t\tif (!m) {\r\n\t\t\treturn isArray(this._weekdays) ? this._weekdays : this._weekdays[\"standalone\"];\r\n\t\t}\r\n\t\treturn isArray(this._weekdays) ? this._weekdays[m.day()] : this._weekdays[this._weekdays.isFormat.test(format) ? \"format\" : \"standalone\"][m.day()];\r\n\t}\r\n\r\n\tvar defaultLocaleWeekdaysShort = \"Sun_Mon_Tue_Wed_Thu_Fri_Sat\".split(\"_\");\r\n\tfunction localeWeekdaysShort(m) {\r\n\t\treturn m ? this._weekdaysShort[m.day()] : this._weekdaysShort;\r\n\t}\r\n\r\n\tvar defaultLocaleWeekdaysMin = \"Su_Mo_Tu_We_Th_Fr_Sa\".split(\"_\");\r\n\tfunction localeWeekdaysMin(m) {\r\n\t\treturn m ? this._weekdaysMin[m.day()] : this._weekdaysMin;\r\n\t}\r\n\r\n\tfunction handleStrictParse$1(weekdayName, format, strict) {\r\n\t\tvar i,\r\n\t\t\tii,\r\n\t\t\tmom,\r\n\t\t\tllc = weekdayName.toLocaleLowerCase();\r\n\t\tif (!this._weekdaysParse) {\r\n\t\t\tthis._weekdaysParse = [];\r\n\t\t\tthis._shortWeekdaysParse = [];\r\n\t\t\tthis._minWeekdaysParse = [];\r\n\r\n\t\t\tfor (i = 0; i < 7; ++i) {\r\n\t\t\t\tmom = createUTC([2000, 1]).day(i);\r\n\t\t\t\tthis._minWeekdaysParse[i] = this.weekdaysMin(mom, \"\").toLocaleLowerCase();\r\n\t\t\t\tthis._shortWeekdaysParse[i] = this.weekdaysShort(mom, \"\").toLocaleLowerCase();\r\n\t\t\t\tthis._weekdaysParse[i] = this.weekdays(mom, \"\").toLocaleLowerCase();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (strict) {\r\n\t\t\tif (format === \"dddd\") {\r\n\t\t\t\tii = indexOf.call(this._weekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else if (format === \"ddd\") {\r\n\t\t\t\tii = indexOf.call(this._shortWeekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else {\r\n\t\t\t\tii = indexOf.call(this._minWeekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (format === \"dddd\") {\r\n\t\t\t\tii = indexOf.call(this._weekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._shortWeekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._minWeekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else if (format === \"ddd\") {\r\n\t\t\t\tii = indexOf.call(this._shortWeekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._weekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._minWeekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t} else {\r\n\t\t\t\tii = indexOf.call(this._minWeekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._weekdaysParse, llc);\r\n\t\t\t\tif (ii !== -1) {\r\n\t\t\t\t\treturn ii;\r\n\t\t\t\t}\r\n\t\t\t\tii = indexOf.call(this._shortWeekdaysParse, llc);\r\n\t\t\t\treturn ii !== -1 ? ii : null;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tfunction localeWeekdaysParse(weekdayName, format, strict) {\r\n\t\tvar i, mom, regex;\r\n\r\n\t\tif (this._weekdaysParseExact) {\r\n\t\t\treturn handleStrictParse$1.call(this, weekdayName, format, strict);\r\n\t\t}\r\n\r\n\t\tif (!this._weekdaysParse) {\r\n\t\t\tthis._weekdaysParse = [];\r\n\t\t\tthis._minWeekdaysParse = [];\r\n\t\t\tthis._shortWeekdaysParse = [];\r\n\t\t\tthis._fullWeekdaysParse = [];\r\n\t\t}\r\n\r\n\t\tfor (i = 0; i < 7; i++) {\r\n\t\t\t// make the regex if we don't have it already\r\n\r\n\t\t\tmom = createUTC([2000, 1]).day(i);\r\n\t\t\tif (strict && !this._fullWeekdaysParse[i]) {\r\n\t\t\t\tthis._fullWeekdaysParse[i] = new RegExp(\"^\" + this.weekdays(mom, \"\").replace(\".\", \".?\") + \"$\", \"i\");\r\n\t\t\t\tthis._shortWeekdaysParse[i] = new RegExp(\"^\" + this.weekdaysShort(mom, \"\").replace(\".\", \".?\") + \"$\", \"i\");\r\n\t\t\t\tthis._minWeekdaysParse[i] = new RegExp(\"^\" + this.weekdaysMin(mom, \"\").replace(\".\", \".?\") + \"$\", \"i\");\r\n\t\t\t}\r\n\t\t\tif (!this._weekdaysParse[i]) {\r\n\t\t\t\tregex = \"^\" + this.weekdays(mom, \"\") + \"|^\" + this.weekdaysShort(mom, \"\") + \"|^\" + this.weekdaysMin(mom, \"\");\r\n\t\t\t\tthis._weekdaysParse[i] = new RegExp(regex.replace(\".\", \"\"), \"i\");\r\n\t\t\t}\r\n\t\t\t// test the regex\r\n\t\t\tif (strict && format === \"dddd\" && this._fullWeekdaysParse[i].test(weekdayName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t} else if (strict && format === \"ddd\" && this._shortWeekdaysParse[i].test(weekdayName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t} else if (strict && format === \"dd\" && this._minWeekdaysParse[i].test(weekdayName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t} else if (!strict && this._weekdaysParse[i].test(weekdayName)) {\r\n\t\t\t\treturn i;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getSetDayOfWeek(input) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn input != null ? this : NaN;\r\n\t\t}\r\n\t\tvar day = this._isUTC ? this._d.getUTCDay() : this._d.getDay();\r\n\t\tif (input != null) {\r\n\t\t\tinput = parseWeekday(input, this.localeData());\r\n\t\t\treturn this.add(input - day, \"d\");\r\n\t\t} else {\r\n\t\t\treturn day;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction getSetLocaleDayOfWeek(input) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn input != null ? this : NaN;\r\n\t\t}\r\n\t\tvar weekday = (this.day() + 7 - this.localeData()._week.dow) % 7;\r\n\t\treturn input == null ? weekday : this.add(input - weekday, \"d\");\r\n\t}\r\n\r\n\tfunction getSetISODayOfWeek(input) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn input != null ? this : NaN;\r\n\t\t}\r\n\r\n\t\t// behaves the same as moment#day except\r\n\t\t// as a getter, returns 7 instead of 0 (1-7 range instead of 0-6)\r\n\t\t// as a setter, sunday should belong to the previous week.\r\n\r\n\t\tif (input != null) {\r\n\t\t\tvar weekday = parseIsoWeekday(input, this.localeData());\r\n\t\t\treturn this.day(this.day() % 7 ? weekday : weekday - 7);\r\n\t\t} else {\r\n\t\t\treturn this.day() || 7;\r\n\t\t}\r\n\t}\r\n\r\n\tvar defaultWeekdaysRegex = matchWord;\r\n\tfunction weekdaysRegex(isStrict) {\r\n\t\tif (this._weekdaysParseExact) {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysRegex\")) {\r\n\t\t\t\tcomputeWeekdaysParse.call(this);\r\n\t\t\t}\r\n\t\t\tif (isStrict) {\r\n\t\t\t\treturn this._weekdaysStrictRegex;\r\n\t\t\t} else {\r\n\t\t\t\treturn this._weekdaysRegex;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysRegex\")) {\r\n\t\t\t\tthis._weekdaysRegex = defaultWeekdaysRegex;\r\n\t\t\t}\r\n\t\t\treturn this._weekdaysStrictRegex && isStrict ? this._weekdaysStrictRegex : this._weekdaysRegex;\r\n\t\t}\r\n\t}\r\n\r\n\tvar defaultWeekdaysShortRegex = matchWord;\r\n\tfunction weekdaysShortRegex(isStrict) {\r\n\t\tif (this._weekdaysParseExact) {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysRegex\")) {\r\n\t\t\t\tcomputeWeekdaysParse.call(this);\r\n\t\t\t}\r\n\t\t\tif (isStrict) {\r\n\t\t\t\treturn this._weekdaysShortStrictRegex;\r\n\t\t\t} else {\r\n\t\t\t\treturn this._weekdaysShortRegex;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysShortRegex\")) {\r\n\t\t\t\tthis._weekdaysShortRegex = defaultWeekdaysShortRegex;\r\n\t\t\t}\r\n\t\t\treturn this._weekdaysShortStrictRegex && isStrict ? this._weekdaysShortStrictRegex : this._weekdaysShortRegex;\r\n\t\t}\r\n\t}\r\n\r\n\tvar defaultWeekdaysMinRegex = matchWord;\r\n\tfunction weekdaysMinRegex(isStrict) {\r\n\t\tif (this._weekdaysParseExact) {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysRegex\")) {\r\n\t\t\t\tcomputeWeekdaysParse.call(this);\r\n\t\t\t}\r\n\t\t\tif (isStrict) {\r\n\t\t\t\treturn this._weekdaysMinStrictRegex;\r\n\t\t\t} else {\r\n\t\t\t\treturn this._weekdaysMinRegex;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (!hasOwnProp(this, \"_weekdaysMinRegex\")) {\r\n\t\t\t\tthis._weekdaysMinRegex = defaultWeekdaysMinRegex;\r\n\t\t\t}\r\n\t\t\treturn this._weekdaysMinStrictRegex && isStrict ? this._weekdaysMinStrictRegex : this._weekdaysMinRegex;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction computeWeekdaysParse() {\r\n\t\tfunction cmpLenRev(a, b) {\r\n\t\t\treturn b.length - a.length;\r\n\t\t}\r\n\r\n\t\tvar minPieces = [],\r\n\t\t\tshortPieces = [],\r\n\t\t\tlongPieces = [],\r\n\t\t\tmixedPieces = [],\r\n\t\t\ti,\r\n\t\t\tmom,\r\n\t\t\tminp,\r\n\t\t\tshortp,\r\n\t\t\tlongp;\r\n\t\tfor (i = 0; i < 7; i++) {\r\n\t\t\t// make the regex if we don't have it already\r\n\t\t\tmom = createUTC([2000, 1]).day(i);\r\n\t\t\tminp = this.weekdaysMin(mom, \"\");\r\n\t\t\tshortp = this.weekdaysShort(mom, \"\");\r\n\t\t\tlongp = this.weekdays(mom, \"\");\r\n\t\t\tminPieces.push(minp);\r\n\t\t\tshortPieces.push(shortp);\r\n\t\t\tlongPieces.push(longp);\r\n\t\t\tmixedPieces.push(minp);\r\n\t\t\tmixedPieces.push(shortp);\r\n\t\t\tmixedPieces.push(longp);\r\n\t\t}\r\n\t\t// Sorting makes sure if one weekday (or abbr) is a prefix of another it\r\n\t\t// will match the longer piece.\r\n\t\tminPieces.sort(cmpLenRev);\r\n\t\tshortPieces.sort(cmpLenRev);\r\n\t\tlongPieces.sort(cmpLenRev);\r\n\t\tmixedPieces.sort(cmpLenRev);\r\n\t\tfor (i = 0; i < 7; i++) {\r\n\t\t\tshortPieces[i] = regexEscape(shortPieces[i]);\r\n\t\t\tlongPieces[i] = regexEscape(longPieces[i]);\r\n\t\t\tmixedPieces[i] = regexEscape(mixedPieces[i]);\r\n\t\t}\r\n\r\n\t\tthis._weekdaysRegex = new RegExp(\"^(\" + mixedPieces.join(\"|\") + \")\", \"i\");\r\n\t\tthis._weekdaysShortRegex = this._weekdaysRegex;\r\n\t\tthis._weekdaysMinRegex = this._weekdaysRegex;\r\n\r\n\t\tthis._weekdaysStrictRegex = new RegExp(\"^(\" + longPieces.join(\"|\") + \")\", \"i\");\r\n\t\tthis._weekdaysShortStrictRegex = new RegExp(\"^(\" + shortPieces.join(\"|\") + \")\", \"i\");\r\n\t\tthis._weekdaysMinStrictRegex = new RegExp(\"^(\" + minPieces.join(\"|\") + \")\", \"i\");\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\tfunction hFormat() {\r\n\t\treturn this.hours() % 12 || 12;\r\n\t}\r\n\r\n\tfunction kFormat() {\r\n\t\treturn this.hours() || 24;\r\n\t}\r\n\r\n\taddFormatToken(\"H\", [\"HH\", 2], 0, \"hour\");\r\n\taddFormatToken(\"h\", [\"hh\", 2], 0, hFormat);\r\n\taddFormatToken(\"k\", [\"kk\", 2], 0, kFormat);\r\n\r\n\taddFormatToken(\"hmm\", 0, 0, function () {\r\n\t\treturn \"\" + hFormat.apply(this) + zeroFill(this.minutes(), 2);\r\n\t});\r\n\r\n\taddFormatToken(\"hmmss\", 0, 0, function () {\r\n\t\treturn \"\" + hFormat.apply(this) + zeroFill(this.minutes(), 2) + zeroFill(this.seconds(), 2);\r\n\t});\r\n\r\n\taddFormatToken(\"Hmm\", 0, 0, function () {\r\n\t\treturn \"\" + this.hours() + zeroFill(this.minutes(), 2);\r\n\t});\r\n\r\n\taddFormatToken(\"Hmmss\", 0, 0, function () {\r\n\t\treturn \"\" + this.hours() + zeroFill(this.minutes(), 2) + zeroFill(this.seconds(), 2);\r\n\t});\r\n\r\n\tfunction meridiem(token, lowercase) {\r\n\t\taddFormatToken(token, 0, 0, function () {\r\n\t\t\treturn this.localeData().meridiem(this.hours(), this.minutes(), lowercase);\r\n\t\t});\r\n\t}\r\n\r\n\tmeridiem(\"a\", true);\r\n\tmeridiem(\"A\", false);\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"hour\", \"h\");\r\n\r\n\t// PRIORITY\r\n\taddUnitPriority(\"hour\", 13);\r\n\r\n\t// PARSING\r\n\r\n\tfunction matchMeridiem(isStrict, locale) {\r\n\t\treturn locale._meridiemParse;\r\n\t}\r\n\r\n\taddRegexToken(\"a\", matchMeridiem);\r\n\taddRegexToken(\"A\", matchMeridiem);\r\n\taddRegexToken(\"H\", match1to2);\r\n\taddRegexToken(\"h\", match1to2);\r\n\taddRegexToken(\"k\", match1to2);\r\n\taddRegexToken(\"HH\", match1to2, match2);\r\n\taddRegexToken(\"hh\", match1to2, match2);\r\n\taddRegexToken(\"kk\", match1to2, match2);\r\n\r\n\taddRegexToken(\"hmm\", match3to4);\r\n\taddRegexToken(\"hmmss\", match5to6);\r\n\taddRegexToken(\"Hmm\", match3to4);\r\n\taddRegexToken(\"Hmmss\", match5to6);\r\n\r\n\taddParseToken([\"H\", \"HH\"], HOUR);\r\n\taddParseToken([\"k\", \"kk\"], function (input, array, config) {\r\n\t\tvar kInput = toInt(input);\r\n\t\tarray[HOUR] = kInput === 24 ? 0 : kInput;\r\n\t});\r\n\taddParseToken([\"a\", \"A\"], function (input, array, config) {\r\n\t\tconfig._isPm = config._locale.isPM(input);\r\n\t\tconfig._meridiem = input;\r\n\t});\r\n\taddParseToken([\"h\", \"hh\"], function (input, array, config) {\r\n\t\tarray[HOUR] = toInt(input);\r\n\t\tgetParsingFlags(config).bigHour = true;\r\n\t});\r\n\taddParseToken(\"hmm\", function (input, array, config) {\r\n\t\tvar pos = input.length - 2;\r\n\t\tarray[HOUR] = toInt(input.substr(0, pos));\r\n\t\tarray[MINUTE] = toInt(input.substr(pos));\r\n\t\tgetParsingFlags(config).bigHour = true;\r\n\t});\r\n\taddParseToken(\"hmmss\", function (input, array, config) {\r\n\t\tvar pos1 = input.length - 4;\r\n\t\tvar pos2 = input.length - 2;\r\n\t\tarray[HOUR] = toInt(input.substr(0, pos1));\r\n\t\tarray[MINUTE] = toInt(input.substr(pos1, 2));\r\n\t\tarray[SECOND] = toInt(input.substr(pos2));\r\n\t\tgetParsingFlags(config).bigHour = true;\r\n\t});\r\n\taddParseToken(\"Hmm\", function (input, array, config) {\r\n\t\tvar pos = input.length - 2;\r\n\t\tarray[HOUR] = toInt(input.substr(0, pos));\r\n\t\tarray[MINUTE] = toInt(input.substr(pos));\r\n\t});\r\n\taddParseToken(\"Hmmss\", function (input, array, config) {\r\n\t\tvar pos1 = input.length - 4;\r\n\t\tvar pos2 = input.length - 2;\r\n\t\tarray[HOUR] = toInt(input.substr(0, pos1));\r\n\t\tarray[MINUTE] = toInt(input.substr(pos1, 2));\r\n\t\tarray[SECOND] = toInt(input.substr(pos2));\r\n\t});\r\n\r\n\t// LOCALES\r\n\r\n\tfunction localeIsPM(input) {\r\n\t\t// IE8 Quirks Mode & IE7 Standards Mode do not allow accessing strings like arrays\r\n\t\t// Using charAt should be more compatible.\r\n\t\treturn (input + \"\").toLowerCase().charAt(0) === \"p\";\r\n\t}\r\n\r\n\tvar defaultLocaleMeridiemParse = /[ap]\\.?m?\\.?/i;\r\n\tfunction localeMeridiem(hours, minutes, isLower) {\r\n\t\tif (hours > 11) {\r\n\t\t\treturn isLower ? \"pm\" : \"PM\";\r\n\t\t} else {\r\n\t\t\treturn isLower ? \"am\" : \"AM\";\r\n\t\t}\r\n\t}\r\n\r\n\t// MOMENTS\r\n\r\n\t// Setting the hour should keep the time, because the user explicitly\r\n\t// specified which hour he wants. So trying to maintain the same hour (in\r\n\t// a new timezone) makes sense. Adding/subtracting hours does not follow\r\n\t// this rule.\r\n\tvar getSetHour = makeGetSet(\"Hours\", true);\r\n\r\n\t// months\r\n\t// week\r\n\t// weekdays\r\n\t// meridiem\r\n\tvar baseConfig = {\r\n\t\tcalendar: defaultCalendar,\r\n\t\tlongDateFormat: defaultLongDateFormat,\r\n\t\tinvalidDate: defaultInvalidDate,\r\n\t\tordinal: defaultOrdinal,\r\n\t\tdayOfMonthOrdinalParse: defaultDayOfMonthOrdinalParse,\r\n\t\trelativeTime: defaultRelativeTime,\r\n\r\n\t\tmonths: defaultLocaleMonths,\r\n\t\tmonthsShort: defaultLocaleMonthsShort,\r\n\r\n\t\tweek: defaultLocaleWeek,\r\n\r\n\t\tweekdays: defaultLocaleWeekdays,\r\n\t\tweekdaysMin: defaultLocaleWeekdaysMin,\r\n\t\tweekdaysShort: defaultLocaleWeekdaysShort,\r\n\r\n\t\tmeridiemParse: defaultLocaleMeridiemParse,\r\n\t};\r\n\r\n\t// internal storage for locale config files\r\n\tvar locales = {};\r\n\tvar localeFamilies = {};\r\n\tvar globalLocale;\r\n\r\n\tfunction normalizeLocale(key) {\r\n\t\treturn key ? key.toLowerCase().replace(\"_\", \"-\") : key;\r\n\t}\r\n\r\n\t// pick the locale from the array\r\n\t// try ['en-au', 'en-gb'] as 'en-au', 'en-gb', 'en', as in move through the list trying each\r\n\t// substring from most specific to least, but move to the next array item if it's a more specific variant than the current root\r\n\tfunction chooseLocale(names) {\r\n\t\tvar i = 0,\r\n\t\t\tj,\r\n\t\t\tnext,\r\n\t\t\tlocale,\r\n\t\t\tsplit;\r\n\r\n\t\twhile (i < names.length) {\r\n\t\t\tsplit = normalizeLocale(names[i]).split(\"-\");\r\n\t\t\tj = split.length;\r\n\t\t\tnext = normalizeLocale(names[i + 1]);\r\n\t\t\tnext = next ? next.split(\"-\") : null;\r\n\t\t\twhile (j > 0) {\r\n\t\t\t\tlocale = loadLocale(split.slice(0, j).join(\"-\"));\r\n\t\t\t\tif (locale) {\r\n\t\t\t\t\treturn locale;\r\n\t\t\t\t}\r\n\t\t\t\tif (next && next.length >= j && compareArrays(split, next, true) >= j - 1) {\r\n\t\t\t\t\t//the next array item is better than a shallower substring of this one\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tj--;\r\n\t\t\t}\r\n\t\t\ti++;\r\n\t\t}\r\n\t\treturn null;\r\n\t}\r\n\r\n\tfunction loadLocale(name) {\r\n\t\tvar oldLocale = null;\r\n\t\t// TODO: Find a better way to register and load all the locales in Node\r\n\t\tif (!locales[name] && typeof module !== \"undefined\" && module && module.exports) {\r\n\t\t\ttry {\r\n\t\t\t\toldLocale = globalLocale._abbr;\r\n\t\t\t\tvar aliasedRequire = require;\r\n\t\t\t\taliasedRequire(\"./locale/\" + name);\r\n\t\t\t\tgetSetGlobalLocale(oldLocale);\r\n\t\t\t} catch (e) {}\r\n\t\t}\r\n\t\treturn locales[name];\r\n\t}\r\n\r\n\t// This function will load locale and then set the global locale.  If\r\n\t// no arguments are passed in, it will simply return the current global\r\n\t// locale key.\r\n\tfunction getSetGlobalLocale(key, values) {\r\n\t\tvar data;\r\n\t\tif (key) {\r\n\t\t\tif (isUndefined(values)) {\r\n\t\t\t\tdata = getLocale(key);\r\n\t\t\t} else {\r\n\t\t\t\tdata = defineLocale(key, values);\r\n\t\t\t}\r\n\r\n\t\t\tif (data) {\r\n\t\t\t\t// moment.duration._locale = moment._locale = data;\r\n\t\t\t\tglobalLocale = data;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn globalLocale._abbr;\r\n\t}\r\n\r\n\tfunction defineLocale(name, config) {\r\n\t\tif (config !== null) {\r\n\t\t\tvar parentConfig = baseConfig;\r\n\t\t\tconfig.abbr = name;\r\n\t\t\tif (locales[name] != null) {\r\n\t\t\t\tdeprecateSimple(\"defineLocaleOverride\", \"use moment.updateLocale(localeName, config) to change \" + \"an existing locale. moment.defineLocale(localeName, \" + \"config) should only be used for creating a new locale \" + \"See http://momentjs.com/guides/#/warnings/define-locale/ for more info.\");\r\n\t\t\t\tparentConfig = locales[name]._config;\r\n\t\t\t} else if (config.parentLocale != null) {\r\n\t\t\t\tif (locales[config.parentLocale] != null) {\r\n\t\t\t\t\tparentConfig = locales[config.parentLocale]._config;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (!localeFamilies[config.parentLocale]) {\r\n\t\t\t\t\t\tlocaleFamilies[config.parentLocale] = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t\tlocaleFamilies[config.parentLocale].push({\r\n\t\t\t\t\t\tname: name,\r\n\t\t\t\t\t\tconfig: config,\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tlocales[name] = new Locale(mergeConfigs(parentConfig, config));\r\n\r\n\t\t\tif (localeFamilies[name]) {\r\n\t\t\t\tlocaleFamilies[name].forEach(function (x) {\r\n\t\t\t\t\tdefineLocale(x.name, x.config);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// backwards compat for now: also set the locale\r\n\t\t\t// make sure we set the locale AFTER all child locales have been\r\n\t\t\t// created, so we won't end up with the child locale set.\r\n\t\t\tgetSetGlobalLocale(name);\r\n\r\n\t\t\treturn locales[name];\r\n\t\t} else {\r\n\t\t\t// useful for testing\r\n\t\t\tdelete locales[name];\r\n\t\t\treturn null;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction updateLocale(name, config) {\r\n\t\tif (config != null) {\r\n\t\t\tvar locale,\r\n\t\t\t\ttmpLocale,\r\n\t\t\t\tparentConfig = baseConfig;\r\n\t\t\t// MERGE\r\n\t\t\ttmpLocale = loadLocale(name);\r\n\t\t\tif (tmpLocale != null) {\r\n\t\t\t\tparentConfig = tmpLocale._config;\r\n\t\t\t}\r\n\t\t\tconfig = mergeConfigs(parentConfig, config);\r\n\t\t\tlocale = new Locale(config);\r\n\t\t\tlocale.parentLocale = locales[name];\r\n\t\t\tlocales[name] = locale;\r\n\r\n\t\t\t// backwards compat for now: also set the locale\r\n\t\t\tgetSetGlobalLocale(name);\r\n\t\t} else {\r\n\t\t\t// pass null for config to unupdate, useful for tests\r\n\t\t\tif (locales[name] != null) {\r\n\t\t\t\tif (locales[name].parentLocale != null) {\r\n\t\t\t\t\tlocales[name] = locales[name].parentLocale;\r\n\t\t\t\t} else if (locales[name] != null) {\r\n\t\t\t\t\tdelete locales[name];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn locales[name];\r\n\t}\r\n\r\n\t// returns locale data\r\n\tfunction getLocale(key) {\r\n\t\tvar locale;\r\n\r\n\t\tif (key && key._locale && key._locale._abbr) {\r\n\t\t\tkey = key._locale._abbr;\r\n\t\t}\r\n\r\n\t\tif (!key) {\r\n\t\t\treturn globalLocale;\r\n\t\t}\r\n\r\n\t\tif (!isArray(key)) {\r\n\t\t\t//short-circuit everything else\r\n\t\t\tlocale = loadLocale(key);\r\n\t\t\tif (locale) {\r\n\t\t\t\treturn locale;\r\n\t\t\t}\r\n\t\t\tkey = [key];\r\n\t\t}\r\n\r\n\t\treturn chooseLocale(key);\r\n\t}\r\n\r\n\tfunction listLocales() {\r\n\t\treturn keys(locales);\r\n\t}\r\n\r\n\tfunction checkOverflow(m) {\r\n\t\tvar overflow;\r\n\t\tvar a = m._a;\r\n\r\n\t\tif (a && getParsingFlags(m).overflow === -2) {\r\n\t\t\toverflow = a[MONTH] < 0 || a[MONTH] > 11 ? MONTH : a[DATE] < 1 || a[DATE] > daysInMonth(a[YEAR], a[MONTH]) ? DATE : a[HOUR] < 0 || a[HOUR] > 24 || (a[HOUR] === 24 && (a[MINUTE] !== 0 || a[SECOND] !== 0 || a[MILLISECOND] !== 0)) ? HOUR : a[MINUTE] < 0 || a[MINUTE] > 59 ? MINUTE : a[SECOND] < 0 || a[SECOND] > 59 ? SECOND : a[MILLISECOND] < 0 || a[MILLISECOND] > 999 ? MILLISECOND : -1;\r\n\r\n\t\t\tif (getParsingFlags(m)._overflowDayOfYear && (overflow < YEAR || overflow > DATE)) {\r\n\t\t\t\toverflow = DATE;\r\n\t\t\t}\r\n\t\t\tif (getParsingFlags(m)._overflowWeeks && overflow === -1) {\r\n\t\t\t\toverflow = WEEK;\r\n\t\t\t}\r\n\t\t\tif (getParsingFlags(m)._overflowWeekday && overflow === -1) {\r\n\t\t\t\toverflow = WEEKDAY;\r\n\t\t\t}\r\n\r\n\t\t\tgetParsingFlags(m).overflow = overflow;\r\n\t\t}\r\n\r\n\t\treturn m;\r\n\t}\r\n\r\n\t// Pick the first defined of two or three arguments.\r\n\tfunction defaults(a, b, c) {\r\n\t\tif (a != null) {\r\n\t\t\treturn a;\r\n\t\t}\r\n\t\tif (b != null) {\r\n\t\t\treturn b;\r\n\t\t}\r\n\t\treturn c;\r\n\t}\r\n\r\n\tfunction currentDateArray(config) {\r\n\t\t// hooks is actually the exported moment object\r\n\t\tvar nowValue = new Date(hooks.now());\r\n\t\tif (config._useUTC) {\r\n\t\t\treturn [nowValue.getUTCFullYear(), nowValue.getUTCMonth(), nowValue.getUTCDate()];\r\n\t\t}\r\n\t\treturn [nowValue.getFullYear(), nowValue.getMonth(), nowValue.getDate()];\r\n\t}\r\n\r\n\t// convert an array to a date.\r\n\t// the array should mirror the parameters below\r\n\t// note: all values past the year are optional and will default to the lowest possible value.\r\n\t// [year, month, day , hour, minute, second, millisecond]\r\n\tfunction configFromArray(config) {\r\n\t\tvar i,\r\n\t\t\tdate,\r\n\t\t\tinput = [],\r\n\t\t\tcurrentDate,\r\n\t\t\texpectedWeekday,\r\n\t\t\tyearToUse;\r\n\r\n\t\tif (config._d) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tcurrentDate = currentDateArray(config);\r\n\r\n\t\t//compute day of the year from weeks and weekdays\r\n\t\tif (config._w && config._a[DATE] == null && config._a[MONTH] == null) {\r\n\t\t\tdayOfYearFromWeekInfo(config);\r\n\t\t}\r\n\r\n\t\t//if the day of the year is set, figure out what it is\r\n\t\tif (config._dayOfYear != null) {\r\n\t\t\tyearToUse = defaults(config._a[YEAR], currentDate[YEAR]);\r\n\r\n\t\t\tif (config._dayOfYear > daysInYear(yearToUse) || config._dayOfYear === 0) {\r\n\t\t\t\tgetParsingFlags(config)._overflowDayOfYear = true;\r\n\t\t\t}\r\n\r\n\t\t\tdate = createUTCDate(yearToUse, 0, config._dayOfYear);\r\n\t\t\tconfig._a[MONTH] = date.getUTCMonth();\r\n\t\t\tconfig._a[DATE] = date.getUTCDate();\r\n\t\t}\r\n\r\n\t\t// Default to current date.\r\n\t\t// * if no year, month, day of month are given, default to today\r\n\t\t// * if day of month is given, default month and year\r\n\t\t// * if month is given, default only year\r\n\t\t// * if year is given, don't default anything\r\n\t\tfor (i = 0; i < 3 && config._a[i] == null; ++i) {\r\n\t\t\tconfig._a[i] = input[i] = currentDate[i];\r\n\t\t}\r\n\r\n\t\t// Zero out whatever was not defaulted, including time\r\n\t\tfor (; i < 7; i++) {\r\n\t\t\tconfig._a[i] = input[i] = config._a[i] == null ? (i === 2 ? 1 : 0) : config._a[i];\r\n\t\t}\r\n\r\n\t\t// Check for 24:00:00.000\r\n\t\tif (config._a[HOUR] === 24 && config._a[MINUTE] === 0 && config._a[SECOND] === 0 && config._a[MILLISECOND] === 0) {\r\n\t\t\tconfig._nextDay = true;\r\n\t\t\tconfig._a[HOUR] = 0;\r\n\t\t}\r\n\r\n\t\tconfig._d = (config._useUTC ? createUTCDate : createDate).apply(null, input);\r\n\t\texpectedWeekday = config._useUTC ? config._d.getUTCDay() : config._d.getDay();\r\n\r\n\t\t// Apply timezone offset from input. The actual utcOffset can be changed\r\n\t\t// with parseZone.\r\n\t\tif (config._tzm != null) {\r\n\t\t\tconfig._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\r\n\t\t}\r\n\r\n\t\tif (config._nextDay) {\r\n\t\t\tconfig._a[HOUR] = 24;\r\n\t\t}\r\n\r\n\t\t// check for mismatching day of week\r\n\t\tif (config._w && typeof config._w.d !== \"undefined\" && config._w.d !== expectedWeekday) {\r\n\t\t\tgetParsingFlags(config).weekdayMismatch = true;\r\n\t\t}\r\n\t}\r\n\r\n\tfunction dayOfYearFromWeekInfo(config) {\r\n\t\tvar w, weekYear, week, weekday, dow, doy, temp, weekdayOverflow;\r\n\r\n\t\tw = config._w;\r\n\t\tif (w.GG != null || w.W != null || w.E != null) {\r\n\t\t\tdow = 1;\r\n\t\t\tdoy = 4;\r\n\r\n\t\t\t// TODO: We need to take the current isoWeekYear, but that depends on\r\n\t\t\t// how we interpret now (local, utc, fixed offset). So create\r\n\t\t\t// a now version of current config (take local/utc/offset flags, and\r\n\t\t\t// create now).\r\n\t\t\tweekYear = defaults(w.GG, config._a[YEAR], weekOfYear(createLocal(), 1, 4).year);\r\n\t\t\tweek = defaults(w.W, 1);\r\n\t\t\tweekday = defaults(w.E, 1);\r\n\t\t\tif (weekday < 1 || weekday > 7) {\r\n\t\t\t\tweekdayOverflow = true;\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tdow = config._locale._week.dow;\r\n\t\t\tdoy = config._locale._week.doy;\r\n\r\n\t\t\tvar curWeek = weekOfYear(createLocal(), dow, doy);\r\n\r\n\t\t\tweekYear = defaults(w.gg, config._a[YEAR], curWeek.year);\r\n\r\n\t\t\t// Default to current week.\r\n\t\t\tweek = defaults(w.w, curWeek.week);\r\n\r\n\t\t\tif (w.d != null) {\r\n\t\t\t\t// weekday -- low day numbers are considered next week\r\n\t\t\t\tweekday = w.d;\r\n\t\t\t\tif (weekday < 0 || weekday > 6) {\r\n\t\t\t\t\tweekdayOverflow = true;\r\n\t\t\t\t}\r\n\t\t\t} else if (w.e != null) {\r\n\t\t\t\t// local weekday -- counting starts from begining of week\r\n\t\t\t\tweekday = w.e + dow;\r\n\t\t\t\tif (w.e < 0 || w.e > 6) {\r\n\t\t\t\t\tweekdayOverflow = true;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// default to begining of week\r\n\t\t\t\tweekday = dow;\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (week < 1 || week > weeksInYear(weekYear, dow, doy)) {\r\n\t\t\tgetParsingFlags(config)._overflowWeeks = true;\r\n\t\t} else if (weekdayOverflow != null) {\r\n\t\t\tgetParsingFlags(config)._overflowWeekday = true;\r\n\t\t} else {\r\n\t\t\ttemp = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy);\r\n\t\t\tconfig._a[YEAR] = temp.year;\r\n\t\t\tconfig._dayOfYear = temp.dayOfYear;\r\n\t\t}\r\n\t}\r\n\r\n\t// iso 8601 regex\r\n\t// 0000-00-00 0000-W00 or 0000-W00-0 + T + 00 or 00:00 or 00:00:00 or 00:00:00.000 + +00:00 or +0000 or +00)\r\n\tvar extendedIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})-(?:\\d\\d-\\d\\d|W\\d\\d-\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?::\\d\\d(?::\\d\\d(?:[.,]\\d+)?)?)?)([\\+\\-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/;\r\n\tvar basicIsoRegex = /^\\s*((?:[+-]\\d{6}|\\d{4})(?:\\d\\d\\d\\d|W\\d\\d\\d|W\\d\\d|\\d\\d\\d|\\d\\d))(?:(T| )(\\d\\d(?:\\d\\d(?:\\d\\d(?:[.,]\\d+)?)?)?)([\\+\\-]\\d\\d(?::?\\d\\d)?|\\s*Z)?)?$/;\r\n\r\n\tvar tzRegex = /Z|[+-]\\d\\d(?::?\\d\\d)?/;\r\n\r\n\tvar isoDates = [\r\n\t\t[\"YYYYYY-MM-DD\", /[+-]\\d{6}-\\d\\d-\\d\\d/],\r\n\t\t[\"YYYY-MM-DD\", /\\d{4}-\\d\\d-\\d\\d/],\r\n\t\t[\"GGGG-[W]WW-E\", /\\d{4}-W\\d\\d-\\d/],\r\n\t\t[\"GGGG-[W]WW\", /\\d{4}-W\\d\\d/, false],\r\n\t\t[\"YYYY-DDD\", /\\d{4}-\\d{3}/],\r\n\t\t[\"YYYY-MM\", /\\d{4}-\\d\\d/, false],\r\n\t\t[\"YYYYYYMMDD\", /[+-]\\d{10}/],\r\n\t\t[\"YYYYMMDD\", /\\d{8}/],\r\n\t\t// YYYYMM is NOT allowed by the standard\r\n\t\t[\"GGGG[W]WWE\", /\\d{4}W\\d{3}/],\r\n\t\t[\"GGGG[W]WW\", /\\d{4}W\\d{2}/, false],\r\n\t\t[\"YYYYDDD\", /\\d{7}/],\r\n\t];\r\n\r\n\t// iso time formats and regexes\r\n\tvar isoTimes = [\r\n\t\t[\"HH:mm:ss.SSSS\", /\\d\\d:\\d\\d:\\d\\d\\.\\d+/],\r\n\t\t[\"HH:mm:ss,SSSS\", /\\d\\d:\\d\\d:\\d\\d,\\d+/],\r\n\t\t[\"HH:mm:ss\", /\\d\\d:\\d\\d:\\d\\d/],\r\n\t\t[\"HH:mm\", /\\d\\d:\\d\\d/],\r\n\t\t[\"HHmmss.SSSS\", /\\d\\d\\d\\d\\d\\d\\.\\d+/],\r\n\t\t[\"HHmmss,SSSS\", /\\d\\d\\d\\d\\d\\d,\\d+/],\r\n\t\t[\"HHmmss\", /\\d\\d\\d\\d\\d\\d/],\r\n\t\t[\"HHmm\", /\\d\\d\\d\\d/],\r\n\t\t[\"HH\", /\\d\\d/],\r\n\t];\r\n\r\n\tvar aspNetJsonRegex = /^\\/?Date\\((\\-?\\d+)/i;\r\n\r\n\t// date from iso format\r\n\tfunction configFromISO(config) {\r\n\t\tvar i,\r\n\t\t\tl,\r\n\t\t\tstring = config._i,\r\n\t\t\tmatch = extendedIsoRegex.exec(string) || basicIsoRegex.exec(string),\r\n\t\t\tallowTime,\r\n\t\t\tdateFormat,\r\n\t\t\ttimeFormat,\r\n\t\t\ttzFormat;\r\n\r\n\t\tif (match) {\r\n\t\t\tgetParsingFlags(config).iso = true;\r\n\r\n\t\t\tfor (i = 0, l = isoDates.length; i < l; i++) {\r\n\t\t\t\tif (isoDates[i][1].exec(match[1])) {\r\n\t\t\t\t\tdateFormat = isoDates[i][0];\r\n\t\t\t\t\tallowTime = isoDates[i][2] !== false;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (dateFormat == null) {\r\n\t\t\t\tconfig._isValid = false;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (match[3]) {\r\n\t\t\t\tfor (i = 0, l = isoTimes.length; i < l; i++) {\r\n\t\t\t\t\tif (isoTimes[i][1].exec(match[3])) {\r\n\t\t\t\t\t\t// match[2] should be 'T' or space\r\n\t\t\t\t\t\ttimeFormat = (match[2] || \" \") + isoTimes[i][0];\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (timeFormat == null) {\r\n\t\t\t\t\tconfig._isValid = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (!allowTime && timeFormat != null) {\r\n\t\t\t\tconfig._isValid = false;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (match[4]) {\r\n\t\t\t\tif (tzRegex.exec(match[4])) {\r\n\t\t\t\t\ttzFormat = \"Z\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconfig._isValid = false;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconfig._f = dateFormat + (timeFormat || \"\") + (tzFormat || \"\");\r\n\t\t\tconfigFromStringAndFormat(config);\r\n\t\t} else {\r\n\t\t\tconfig._isValid = false;\r\n\t\t}\r\n\t}\r\n\r\n\t// RFC 2822 regex: For details see https://tools.ietf.org/html/rfc2822#section-3.3\r\n\tvar rfc2822 = /^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\\s)?(\\d{1,2})\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s(\\d{2,4})\\s(\\d\\d):(\\d\\d)(?::(\\d\\d))?\\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\\d{4}))$/;\r\n\r\n\tfunction extractFromRFC2822Strings(yearStr, monthStr, dayStr, hourStr, minuteStr, secondStr) {\r\n\t\tvar result = [untruncateYear(yearStr), defaultLocaleMonthsShort.indexOf(monthStr), parseInt(dayStr, 10), parseInt(hourStr, 10), parseInt(minuteStr, 10)];\r\n\r\n\t\tif (secondStr) {\r\n\t\t\tresult.push(parseInt(secondStr, 10));\r\n\t\t}\r\n\r\n\t\treturn result;\r\n\t}\r\n\r\n\tfunction untruncateYear(yearStr) {\r\n\t\tvar year = parseInt(yearStr, 10);\r\n\t\tif (year <= 49) {\r\n\t\t\treturn 2000 + year;\r\n\t\t} else if (year <= 999) {\r\n\t\t\treturn 1900 + year;\r\n\t\t}\r\n\t\treturn year;\r\n\t}\r\n\r\n\tfunction preprocessRFC2822(s) {\r\n\t\t// Remove comments and folding whitespace and replace multiple-spaces with a single space\r\n\t\treturn s\r\n\t\t\t.replace(/\\([^)]*\\)|[\\n\\t]/g, \" \")\r\n\t\t\t.replace(/(\\s\\s+)/g, \" \")\r\n\t\t\t.trim();\r\n\t}\r\n\r\n\tfunction checkWeekday(weekdayStr, parsedInput, config) {\r\n\t\tif (weekdayStr) {\r\n\t\t\t// TODO: Replace the vanilla JS Date object with an indepentent day-of-week check.\r\n\t\t\tvar weekdayProvided = defaultLocaleWeekdaysShort.indexOf(weekdayStr),\r\n\t\t\t\tweekdayActual = new Date(parsedInput[0], parsedInput[1], parsedInput[2]).getDay();\r\n\t\t\tif (weekdayProvided !== weekdayActual) {\r\n\t\t\t\tgetParsingFlags(config).weekdayMismatch = true;\r\n\t\t\t\tconfig._isValid = false;\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n\r\n\tvar obsOffsets = {\r\n\t\tUT: 0,\r\n\t\tGMT: 0,\r\n\t\tEDT: -4 * 60,\r\n\t\tEST: -5 * 60,\r\n\t\tCDT: -5 * 60,\r\n\t\tCST: -6 * 60,\r\n\t\tMDT: -6 * 60,\r\n\t\tMST: -7 * 60,\r\n\t\tPDT: -7 * 60,\r\n\t\tPST: -8 * 60,\r\n\t};\r\n\r\n\tfunction calculateOffset(obsOffset, militaryOffset, numOffset) {\r\n\t\tif (obsOffset) {\r\n\t\t\treturn obsOffsets[obsOffset];\r\n\t\t} else if (militaryOffset) {\r\n\t\t\t// the only allowed military tz is Z\r\n\t\t\treturn 0;\r\n\t\t} else {\r\n\t\t\tvar hm = parseInt(numOffset, 10);\r\n\t\t\tvar m = hm % 100,\r\n\t\t\t\th = (hm - m) / 100;\r\n\t\t\treturn h * 60 + m;\r\n\t\t}\r\n\t}\r\n\r\n\t// date and time from ref 2822 format\r\n\tfunction configFromRFC2822(config) {\r\n\t\tvar match = rfc2822.exec(preprocessRFC2822(config._i));\r\n\t\tif (match) {\r\n\t\t\tvar parsedArray = extractFromRFC2822Strings(match[4], match[3], match[2], match[5], match[6], match[7]);\r\n\t\t\tif (!checkWeekday(match[1], parsedArray, config)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tconfig._a = parsedArray;\r\n\t\t\tconfig._tzm = calculateOffset(match[8], match[9], match[10]);\r\n\r\n\t\t\tconfig._d = createUTCDate.apply(null, config._a);\r\n\t\t\tconfig._d.setUTCMinutes(config._d.getUTCMinutes() - config._tzm);\r\n\r\n\t\t\tgetParsingFlags(config).rfc2822 = true;\r\n\t\t} else {\r\n\t\t\tconfig._isValid = false;\r\n\t\t}\r\n\t}\r\n\r\n\t// date from iso format or fallback\r\n\tfunction configFromString(config) {\r\n\t\tvar matched = aspNetJsonRegex.exec(config._i);\r\n\r\n\t\tif (matched !== null) {\r\n\t\t\tconfig._d = new Date(+matched[1]);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconfigFromISO(config);\r\n\t\tif (config._isValid === false) {\r\n\t\t\tdelete config._isValid;\r\n\t\t} else {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconfigFromRFC2822(config);\r\n\t\tif (config._isValid === false) {\r\n\t\t\tdelete config._isValid;\r\n\t\t} else {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Final attempt, use Input Fallback\r\n\t\thooks.createFromInputFallback(config);\r\n\t}\r\n\r\n\thooks.createFromInputFallback = deprecate(\"value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), \" + \"which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are \" + \"discouraged and will be removed in an upcoming major release. Please refer to \" + \"http://momentjs.com/guides/#/warnings/js-date/ for more info.\", function (config) {\r\n\t\tconfig._d = new Date(config._i + (config._useUTC ? \" UTC\" : \"\"));\r\n\t});\r\n\r\n\t// constant that refers to the ISO standard\r\n\thooks.ISO_8601 = function () {};\r\n\r\n\t// constant that refers to the RFC 2822 form\r\n\thooks.RFC_2822 = function () {};\r\n\r\n\t// date from string and format string\r\n\tfunction configFromStringAndFormat(config) {\r\n\t\t// TODO: Move this to another part of the creation flow to prevent circular deps\r\n\t\tif (config._f === hooks.ISO_8601) {\r\n\t\t\tconfigFromISO(config);\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (config._f === hooks.RFC_2822) {\r\n\t\t\tconfigFromRFC2822(config);\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tconfig._a = [];\r\n\t\tgetParsingFlags(config).empty = true;\r\n\r\n\t\t// This array is used to make a Date, either with `new Date` or `Date.UTC`\r\n\t\tvar string = \"\" + config._i,\r\n\t\t\ti,\r\n\t\t\tparsedInput,\r\n\t\t\ttokens,\r\n\t\t\ttoken,\r\n\t\t\tskipped,\r\n\t\t\tstringLength = string.length,\r\n\t\t\ttotalParsedInputLength = 0;\r\n\r\n\t\ttokens = expandFormat(config._f, config._locale).match(formattingTokens) || [];\r\n\r\n\t\tfor (i = 0; i < tokens.length; i++) {\r\n\t\t\ttoken = tokens[i];\r\n\t\t\tparsedInput = (string.match(getParseRegexForToken(token, config)) || [])[0];\r\n\t\t\t// console.log('token', token, 'parsedInput', parsedInput,\r\n\t\t\t//         'regex', getParseRegexForToken(token, config));\r\n\t\t\tif (parsedInput) {\r\n\t\t\t\tskipped = string.substr(0, string.indexOf(parsedInput));\r\n\t\t\t\tif (skipped.length > 0) {\r\n\t\t\t\t\tgetParsingFlags(config).unusedInput.push(skipped);\r\n\t\t\t\t}\r\n\t\t\t\tstring = string.slice(string.indexOf(parsedInput) + parsedInput.length);\r\n\t\t\t\ttotalParsedInputLength += parsedInput.length;\r\n\t\t\t}\r\n\t\t\t// don't parse if it's not a known token\r\n\t\t\tif (formatTokenFunctions[token]) {\r\n\t\t\t\tif (parsedInput) {\r\n\t\t\t\t\tgetParsingFlags(config).empty = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tgetParsingFlags(config).unusedTokens.push(token);\r\n\t\t\t\t}\r\n\t\t\t\taddTimeToArrayFromToken(token, parsedInput, config);\r\n\t\t\t} else if (config._strict && !parsedInput) {\r\n\t\t\t\tgetParsingFlags(config).unusedTokens.push(token);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// add remaining unparsed input length to the string\r\n\t\tgetParsingFlags(config).charsLeftOver = stringLength - totalParsedInputLength;\r\n\t\tif (string.length > 0) {\r\n\t\t\tgetParsingFlags(config).unusedInput.push(string);\r\n\t\t}\r\n\r\n\t\t// clear _12h flag if hour is <= 12\r\n\t\tif (config._a[HOUR] <= 12 && getParsingFlags(config).bigHour === true && config._a[HOUR] > 0) {\r\n\t\t\tgetParsingFlags(config).bigHour = undefined;\r\n\t\t}\r\n\r\n\t\tgetParsingFlags(config).parsedDateParts = config._a.slice(0);\r\n\t\tgetParsingFlags(config).meridiem = config._meridiem;\r\n\t\t// handle meridiem\r\n\t\tconfig._a[HOUR] = meridiemFixWrap(config._locale, config._a[HOUR], config._meridiem);\r\n\r\n\t\tconfigFromArray(config);\r\n\t\tcheckOverflow(config);\r\n\t}\r\n\r\n\tfunction meridiemFixWrap(locale, hour, meridiem) {\r\n\t\tvar isPm;\r\n\r\n\t\tif (meridiem == null) {\r\n\t\t\t// nothing to do\r\n\t\t\treturn hour;\r\n\t\t}\r\n\t\tif (locale.meridiemHour != null) {\r\n\t\t\treturn locale.meridiemHour(hour, meridiem);\r\n\t\t} else if (locale.isPM != null) {\r\n\t\t\t// Fallback\r\n\t\t\tisPm = locale.isPM(meridiem);\r\n\t\t\tif (isPm && hour < 12) {\r\n\t\t\t\thour += 12;\r\n\t\t\t}\r\n\t\t\tif (!isPm && hour === 12) {\r\n\t\t\t\thour = 0;\r\n\t\t\t}\r\n\t\t\treturn hour;\r\n\t\t} else {\r\n\t\t\t// this is not supposed to happen\r\n\t\t\treturn hour;\r\n\t\t}\r\n\t}\r\n\r\n\t// date from string and array of format strings\r\n\tfunction configFromStringAndArray(config) {\r\n\t\tvar tempConfig, bestMoment, scoreToBeat, i, currentScore;\r\n\r\n\t\tif (config._f.length === 0) {\r\n\t\t\tgetParsingFlags(config).invalidFormat = true;\r\n\t\t\tconfig._d = new Date(NaN);\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tfor (i = 0; i < config._f.length; i++) {\r\n\t\t\tcurrentScore = 0;\r\n\t\t\ttempConfig = copyConfig({}, config);\r\n\t\t\tif (config._useUTC != null) {\r\n\t\t\t\ttempConfig._useUTC = config._useUTC;\r\n\t\t\t}\r\n\t\t\ttempConfig._f = config._f[i];\r\n\t\t\tconfigFromStringAndFormat(tempConfig);\r\n\r\n\t\t\tif (!isValid(tempConfig)) {\r\n\t\t\t\tcontinue;\r\n\t\t\t}\r\n\r\n\t\t\t// if there is any input that was not parsed add a penalty for that format\r\n\t\t\tcurrentScore += getParsingFlags(tempConfig).charsLeftOver;\r\n\r\n\t\t\t//or tokens\r\n\t\t\tcurrentScore += getParsingFlags(tempConfig).unusedTokens.length * 10;\r\n\r\n\t\t\tgetParsingFlags(tempConfig).score = currentScore;\r\n\r\n\t\t\tif (scoreToBeat == null || currentScore < scoreToBeat) {\r\n\t\t\t\tscoreToBeat = currentScore;\r\n\t\t\t\tbestMoment = tempConfig;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\textend(config, bestMoment || tempConfig);\r\n\t}\r\n\r\n\tfunction configFromObject(config) {\r\n\t\tif (config._d) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tvar i = normalizeObjectUnits(config._i);\r\n\t\tconfig._a = map([i.year, i.month, i.day || i.date, i.hour, i.minute, i.second, i.millisecond], function (obj) {\r\n\t\t\treturn obj && parseInt(obj, 10);\r\n\t\t});\r\n\r\n\t\tconfigFromArray(config);\r\n\t}\r\n\r\n\tfunction createFromConfig(config) {\r\n\t\tvar res = new Moment(checkOverflow(prepareConfig(config)));\r\n\t\tif (res._nextDay) {\r\n\t\t\t// Adding is smart enough around DST\r\n\t\t\tres.add(1, \"d\");\r\n\t\t\tres._nextDay = undefined;\r\n\t\t}\r\n\r\n\t\treturn res;\r\n\t}\r\n\r\n\tfunction prepareConfig(config) {\r\n\t\tvar input = config._i,\r\n\t\t\tformat = config._f;\r\n\r\n\t\tconfig._locale = config._locale || getLocale(config._l);\r\n\r\n\t\tif (input === null || (format === undefined && input === \"\")) {\r\n\t\t\treturn createInvalid({ nullInput: true });\r\n\t\t}\r\n\r\n\t\tif (typeof input === \"string\") {\r\n\t\t\tconfig._i = input = config._locale.preparse(input);\r\n\t\t}\r\n\r\n\t\tif (isMoment(input)) {\r\n\t\t\treturn new Moment(checkOverflow(input));\r\n\t\t} else if (isDate(input)) {\r\n\t\t\tconfig._d = input;\r\n\t\t} else if (isArray(format)) {\r\n\t\t\tconfigFromStringAndArray(config);\r\n\t\t} else if (format) {\r\n\t\t\tconfigFromStringAndFormat(config);\r\n\t\t} else {\r\n\t\t\tconfigFromInput(config);\r\n\t\t}\r\n\r\n\t\tif (!isValid(config)) {\r\n\t\t\tconfig._d = null;\r\n\t\t}\r\n\r\n\t\treturn config;\r\n\t}\r\n\r\n\tfunction configFromInput(config) {\r\n\t\tvar input = config._i;\r\n\t\tif (isUndefined(input)) {\r\n\t\t\tconfig._d = new Date(hooks.now());\r\n\t\t} else if (isDate(input)) {\r\n\t\t\tconfig._d = new Date(input.valueOf());\r\n\t\t} else if (typeof input === \"string\") {\r\n\t\t\tconfigFromString(config);\r\n\t\t} else if (isArray(input)) {\r\n\t\t\tconfig._a = map(input.slice(0), function (obj) {\r\n\t\t\t\treturn parseInt(obj, 10);\r\n\t\t\t});\r\n\t\t\tconfigFromArray(config);\r\n\t\t} else if (isObject(input)) {\r\n\t\t\tconfigFromObject(config);\r\n\t\t} else if (isNumber(input)) {\r\n\t\t\t// from milliseconds\r\n\t\t\tconfig._d = new Date(input);\r\n\t\t} else {\r\n\t\t\thooks.createFromInputFallback(config);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction createLocalOrUTC(input, format, locale, strict, isUTC) {\r\n\t\tvar c = {};\r\n\r\n\t\tif (locale === true || locale === false) {\r\n\t\t\tstrict = locale;\r\n\t\t\tlocale = undefined;\r\n\t\t}\r\n\r\n\t\tif ((isObject(input) && isObjectEmpty(input)) || (isArray(input) && input.length === 0)) {\r\n\t\t\tinput = undefined;\r\n\t\t}\r\n\t\t// object construction must be done this way.\r\n\t\t// https://github.com/moment/moment/issues/1423\r\n\t\tc._isAMomentObject = true;\r\n\t\tc._useUTC = c._isUTC = isUTC;\r\n\t\tc._l = locale;\r\n\t\tc._i = input;\r\n\t\tc._f = format;\r\n\t\tc._strict = strict;\r\n\r\n\t\treturn createFromConfig(c);\r\n\t}\r\n\r\n\tfunction createLocal(input, format, locale, strict) {\r\n\t\treturn createLocalOrUTC(input, format, locale, strict, false);\r\n\t}\r\n\r\n\tvar prototypeMin = deprecate(\"moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/\", function () {\r\n\t\tvar other = createLocal.apply(null, arguments);\r\n\t\tif (this.isValid() && other.isValid()) {\r\n\t\t\treturn other < this ? this : other;\r\n\t\t} else {\r\n\t\t\treturn createInvalid();\r\n\t\t}\r\n\t});\r\n\r\n\tvar prototypeMax = deprecate(\"moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/\", function () {\r\n\t\tvar other = createLocal.apply(null, arguments);\r\n\t\tif (this.isValid() && other.isValid()) {\r\n\t\t\treturn other > this ? this : other;\r\n\t\t} else {\r\n\t\t\treturn createInvalid();\r\n\t\t}\r\n\t});\r\n\r\n\t// Pick a moment m from moments so that m[fn](other) is true for all\r\n\t// other. This relies on the function fn to be transitive.\r\n\t//\r\n\t// moments should either be an array of moment objects or an array, whose\r\n\t// first element is an array of moment objects.\r\n\tfunction pickBy(fn, moments) {\r\n\t\tvar res, i;\r\n\t\tif (moments.length === 1 && isArray(moments[0])) {\r\n\t\t\tmoments = moments[0];\r\n\t\t}\r\n\t\tif (!moments.length) {\r\n\t\t\treturn createLocal();\r\n\t\t}\r\n\t\tres = moments[0];\r\n\t\tfor (i = 1; i < moments.length; ++i) {\r\n\t\t\tif (!moments[i].isValid() || moments[i][fn](res)) {\r\n\t\t\t\tres = moments[i];\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn res;\r\n\t}\r\n\r\n\t// TODO: Use [].sort instead?\r\n\tfunction min() {\r\n\t\tvar args = [].slice.call(arguments, 0);\r\n\r\n\t\treturn pickBy(\"isBefore\", args);\r\n\t}\r\n\r\n\tfunction max() {\r\n\t\tvar args = [].slice.call(arguments, 0);\r\n\r\n\t\treturn pickBy(\"isAfter\", args);\r\n\t}\r\n\r\n\tvar now = function () {\r\n\t\treturn Date.now ? Date.now() : +new Date();\r\n\t};\r\n\r\n\tvar ordering = [\"year\", \"quarter\", \"month\", \"week\", \"day\", \"hour\", \"minute\", \"second\", \"millisecond\"];\r\n\r\n\tfunction isDurationValid(m) {\r\n\t\tfor (var key in m) {\r\n\t\t\tif (!(indexOf.call(ordering, key) !== -1 && (m[key] == null || !isNaN(m[key])))) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tvar unitHasDecimal = false;\r\n\t\tfor (var i = 0; i < ordering.length; ++i) {\r\n\t\t\tif (m[ordering[i]]) {\r\n\t\t\t\tif (unitHasDecimal) {\r\n\t\t\t\t\treturn false; // only allow non-integers for smallest unit\r\n\t\t\t\t}\r\n\t\t\t\tif (parseFloat(m[ordering[i]]) !== toInt(m[ordering[i]])) {\r\n\t\t\t\t\tunitHasDecimal = true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn true;\r\n\t}\r\n\r\n\tfunction isValid$1() {\r\n\t\treturn this._isValid;\r\n\t}\r\n\r\n\tfunction createInvalid$1() {\r\n\t\treturn createDuration(NaN);\r\n\t}\r\n\r\n\tfunction Duration(duration) {\r\n\t\tvar normalizedInput = normalizeObjectUnits(duration),\r\n\t\t\tyears = normalizedInput.year || 0,\r\n\t\t\tquarters = normalizedInput.quarter || 0,\r\n\t\t\tmonths = normalizedInput.month || 0,\r\n\t\t\tweeks = normalizedInput.week || 0,\r\n\t\t\tdays = normalizedInput.day || 0,\r\n\t\t\thours = normalizedInput.hour || 0,\r\n\t\t\tminutes = normalizedInput.minute || 0,\r\n\t\t\tseconds = normalizedInput.second || 0,\r\n\t\t\tmilliseconds = normalizedInput.millisecond || 0;\r\n\r\n\t\tthis._isValid = isDurationValid(normalizedInput);\r\n\r\n\t\t// representation for dateAddRemove\r\n\t\tthis._milliseconds =\r\n\t\t\t+milliseconds +\r\n\t\t\tseconds * 1e3 + // 1000\r\n\t\t\tminutes * 6e4 + // 1000 * 60\r\n\t\t\thours * 1000 * 60 * 60; //using 1000 * 60 * 60 instead of 36e5 to avoid floating point rounding errors https://github.com/moment/moment/issues/2978\r\n\t\t// Because of dateAddRemove treats 24 hours as different from a\r\n\t\t// day when working around DST, we need to store them separately\r\n\t\tthis._days = +days + weeks * 7;\r\n\t\t// It is impossible to translate months into days without knowing\r\n\t\t// which months you are are talking about, so we have to store\r\n\t\t// it separately.\r\n\t\tthis._months = +months + quarters * 3 + years * 12;\r\n\r\n\t\tthis._data = {};\r\n\r\n\t\tthis._locale = getLocale();\r\n\r\n\t\tthis._bubble();\r\n\t}\r\n\r\n\tfunction isDuration(obj) {\r\n\t\treturn obj instanceof Duration;\r\n\t}\r\n\r\n\tfunction absRound(number) {\r\n\t\tif (number < 0) {\r\n\t\t\treturn Math.round(-1 * number) * -1;\r\n\t\t} else {\r\n\t\t\treturn Math.round(number);\r\n\t\t}\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\tfunction offset(token, separator) {\r\n\t\taddFormatToken(token, 0, 0, function () {\r\n\t\t\tvar offset = this.utcOffset();\r\n\t\t\tvar sign = \"+\";\r\n\t\t\tif (offset < 0) {\r\n\t\t\t\toffset = -offset;\r\n\t\t\t\tsign = \"-\";\r\n\t\t\t}\r\n\t\t\treturn sign + zeroFill(~~(offset / 60), 2) + separator + zeroFill(~~offset % 60, 2);\r\n\t\t});\r\n\t}\r\n\r\n\toffset(\"Z\", \":\");\r\n\toffset(\"ZZ\", \"\");\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"Z\", matchShortOffset);\r\n\taddRegexToken(\"ZZ\", matchShortOffset);\r\n\taddParseToken([\"Z\", \"ZZ\"], function (input, array, config) {\r\n\t\tconfig._useUTC = true;\r\n\t\tconfig._tzm = offsetFromString(matchShortOffset, input);\r\n\t});\r\n\r\n\t// HELPERS\r\n\r\n\t// timezone chunker\r\n\t// '+10:00' > ['10',  '00']\r\n\t// '-1530'  > ['-15', '30']\r\n\tvar chunkOffset = /([\\+\\-]|\\d\\d)/gi;\r\n\r\n\tfunction offsetFromString(matcher, string) {\r\n\t\tvar matches = (string || \"\").match(matcher);\r\n\r\n\t\tif (matches === null) {\r\n\t\t\treturn null;\r\n\t\t}\r\n\r\n\t\tvar chunk = matches[matches.length - 1] || [];\r\n\t\tvar parts = (chunk + \"\").match(chunkOffset) || [\"-\", 0, 0];\r\n\t\tvar minutes = +(parts[1] * 60) + toInt(parts[2]);\r\n\r\n\t\treturn minutes === 0 ? 0 : parts[0] === \"+\" ? minutes : -minutes;\r\n\t}\r\n\r\n\t// Return a moment from input, that is local/utc/zone equivalent to model.\r\n\tfunction cloneWithOffset(input, model) {\r\n\t\tvar res, diff;\r\n\t\tif (model._isUTC) {\r\n\t\t\tres = model.clone();\r\n\t\t\tdiff = (isMoment(input) || isDate(input) ? input.valueOf() : createLocal(input).valueOf()) - res.valueOf();\r\n\t\t\t// Use low-level api, because this fn is low-level api.\r\n\t\t\tres._d.setTime(res._d.valueOf() + diff);\r\n\t\t\thooks.updateOffset(res, false);\r\n\t\t\treturn res;\r\n\t\t} else {\r\n\t\t\treturn createLocal(input).local();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction getDateOffset(m) {\r\n\t\t// On Firefox.24 Date#getTimezoneOffset returns a floating point.\r\n\t\t// https://github.com/moment/moment/pull/1871\r\n\t\treturn -Math.round(m._d.getTimezoneOffset() / 15) * 15;\r\n\t}\r\n\r\n\t// HOOKS\r\n\r\n\t// This function will be called whenever a moment is mutated.\r\n\t// It is intended to keep the offset in sync with the timezone.\r\n\thooks.updateOffset = function () {};\r\n\r\n\t// MOMENTS\r\n\r\n\t// keepLocalTime = true means only change the timezone, without\r\n\t// affecting the local hour. So 5:31:26 +0300 --[utcOffset(2, true)]-->\r\n\t// 5:31:26 +0200 It is possible that 5:31:26 doesn't exist with offset\r\n\t// +0200, so we adjust the time as needed, to be valid.\r\n\t//\r\n\t// Keeping the time actually adds/subtracts (one hour)\r\n\t// from the actual represented time. That is why we call updateOffset\r\n\t// a second time. In case it wants us to change the offset again\r\n\t// _changeInProgress == true case, then we have to adjust, because\r\n\t// there is no such time in the given timezone.\r\n\tfunction getSetOffset(input, keepLocalTime, keepMinutes) {\r\n\t\tvar offset = this._offset || 0,\r\n\t\t\tlocalAdjust;\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn input != null ? this : NaN;\r\n\t\t}\r\n\t\tif (input != null) {\r\n\t\t\tif (typeof input === \"string\") {\r\n\t\t\t\tinput = offsetFromString(matchShortOffset, input);\r\n\t\t\t\tif (input === null) {\r\n\t\t\t\t\treturn this;\r\n\t\t\t\t}\r\n\t\t\t} else if (Math.abs(input) < 16 && !keepMinutes) {\r\n\t\t\t\tinput = input * 60;\r\n\t\t\t}\r\n\t\t\tif (!this._isUTC && keepLocalTime) {\r\n\t\t\t\tlocalAdjust = getDateOffset(this);\r\n\t\t\t}\r\n\t\t\tthis._offset = input;\r\n\t\t\tthis._isUTC = true;\r\n\t\t\tif (localAdjust != null) {\r\n\t\t\t\tthis.add(localAdjust, \"m\");\r\n\t\t\t}\r\n\t\t\tif (offset !== input) {\r\n\t\t\t\tif (!keepLocalTime || this._changeInProgress) {\r\n\t\t\t\t\taddSubtract(this, createDuration(input - offset, \"m\"), 1, false);\r\n\t\t\t\t} else if (!this._changeInProgress) {\r\n\t\t\t\t\tthis._changeInProgress = true;\r\n\t\t\t\t\thooks.updateOffset(this, true);\r\n\t\t\t\t\tthis._changeInProgress = null;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn this;\r\n\t\t} else {\r\n\t\t\treturn this._isUTC ? offset : getDateOffset(this);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction getSetZone(input, keepLocalTime) {\r\n\t\tif (input != null) {\r\n\t\t\tif (typeof input !== \"string\") {\r\n\t\t\t\tinput = -input;\r\n\t\t\t}\r\n\r\n\t\t\tthis.utcOffset(input, keepLocalTime);\r\n\r\n\t\t\treturn this;\r\n\t\t} else {\r\n\t\t\treturn -this.utcOffset();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction setOffsetToUTC(keepLocalTime) {\r\n\t\treturn this.utcOffset(0, keepLocalTime);\r\n\t}\r\n\r\n\tfunction setOffsetToLocal(keepLocalTime) {\r\n\t\tif (this._isUTC) {\r\n\t\t\tthis.utcOffset(0, keepLocalTime);\r\n\t\t\tthis._isUTC = false;\r\n\r\n\t\t\tif (keepLocalTime) {\r\n\t\t\t\tthis.subtract(getDateOffset(this), \"m\");\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction setOffsetToParsedOffset() {\r\n\t\tif (this._tzm != null) {\r\n\t\t\tthis.utcOffset(this._tzm, false, true);\r\n\t\t} else if (typeof this._i === \"string\") {\r\n\t\t\tvar tZone = offsetFromString(matchOffset, this._i);\r\n\t\t\tif (tZone != null) {\r\n\t\t\t\tthis.utcOffset(tZone);\r\n\t\t\t} else {\r\n\t\t\t\tthis.utcOffset(0, true);\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction hasAlignedHourOffset(input) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tinput = input ? createLocal(input).utcOffset() : 0;\r\n\r\n\t\treturn (this.utcOffset() - input) % 60 === 0;\r\n\t}\r\n\r\n\tfunction isDaylightSavingTime() {\r\n\t\treturn this.utcOffset() > this.clone().month(0).utcOffset() || this.utcOffset() > this.clone().month(5).utcOffset();\r\n\t}\r\n\r\n\tfunction isDaylightSavingTimeShifted() {\r\n\t\tif (!isUndefined(this._isDSTShifted)) {\r\n\t\t\treturn this._isDSTShifted;\r\n\t\t}\r\n\r\n\t\tvar c = {};\r\n\r\n\t\tcopyConfig(c, this);\r\n\t\tc = prepareConfig(c);\r\n\r\n\t\tif (c._a) {\r\n\t\t\tvar other = c._isUTC ? createUTC(c._a) : createLocal(c._a);\r\n\t\t\tthis._isDSTShifted = this.isValid() && compareArrays(c._a, other.toArray()) > 0;\r\n\t\t} else {\r\n\t\t\tthis._isDSTShifted = false;\r\n\t\t}\r\n\r\n\t\treturn this._isDSTShifted;\r\n\t}\r\n\r\n\tfunction isLocal() {\r\n\t\treturn this.isValid() ? !this._isUTC : false;\r\n\t}\r\n\r\n\tfunction isUtcOffset() {\r\n\t\treturn this.isValid() ? this._isUTC : false;\r\n\t}\r\n\r\n\tfunction isUtc() {\r\n\t\treturn this.isValid() ? this._isUTC && this._offset === 0 : false;\r\n\t}\r\n\r\n\t// ASP.NET json date format regex\r\n\tvar aspNetRegex = /^(\\-|\\+)?(?:(\\d*)[. ])?(\\d+)\\:(\\d+)(?:\\:(\\d+)(\\.\\d*)?)?$/;\r\n\r\n\t// from http://docs.closure-library.googlecode.com/git/closure_goog_date_date.js.source.html\r\n\t// somewhat more in line with 4.4.3.2 2004 spec, but allows decimal anywhere\r\n\t// and further modified to allow for strings containing both week and day\r\n\tvar isoRegex = /^(-|\\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;\r\n\r\n\tfunction createDuration(input, key) {\r\n\t\tvar duration = input,\r\n\t\t\t// matching against regexp is expensive, do it on demand\r\n\t\t\tmatch = null,\r\n\t\t\tsign,\r\n\t\t\tret,\r\n\t\t\tdiffRes;\r\n\r\n\t\tif (isDuration(input)) {\r\n\t\t\tduration = {\r\n\t\t\t\tms: input._milliseconds,\r\n\t\t\t\td: input._days,\r\n\t\t\t\tM: input._months,\r\n\t\t\t};\r\n\t\t} else if (isNumber(input)) {\r\n\t\t\tduration = {};\r\n\t\t\tif (key) {\r\n\t\t\t\tduration[key] = input;\r\n\t\t\t} else {\r\n\t\t\t\tduration.milliseconds = input;\r\n\t\t\t}\r\n\t\t} else if (!!(match = aspNetRegex.exec(input))) {\r\n\t\t\tsign = match[1] === \"-\" ? -1 : 1;\r\n\t\t\tduration = {\r\n\t\t\t\ty: 0,\r\n\t\t\t\td: toInt(match[DATE]) * sign,\r\n\t\t\t\th: toInt(match[HOUR]) * sign,\r\n\t\t\t\tm: toInt(match[MINUTE]) * sign,\r\n\t\t\t\ts: toInt(match[SECOND]) * sign,\r\n\t\t\t\tms: toInt(absRound(match[MILLISECOND] * 1000)) * sign, // the millisecond decimal point is included in the match\r\n\t\t\t};\r\n\t\t} else if (!!(match = isoRegex.exec(input))) {\r\n\t\t\tsign = match[1] === \"-\" ? -1 : match[1] === \"+\" ? 1 : 1;\r\n\t\t\tduration = {\r\n\t\t\t\ty: parseIso(match[2], sign),\r\n\t\t\t\tM: parseIso(match[3], sign),\r\n\t\t\t\tw: parseIso(match[4], sign),\r\n\t\t\t\td: parseIso(match[5], sign),\r\n\t\t\t\th: parseIso(match[6], sign),\r\n\t\t\t\tm: parseIso(match[7], sign),\r\n\t\t\t\ts: parseIso(match[8], sign),\r\n\t\t\t};\r\n\t\t} else if (duration == null) {\r\n\t\t\t// checks for null or undefined\r\n\t\t\tduration = {};\r\n\t\t} else if (typeof duration === \"object\" && (\"from\" in duration || \"to\" in duration)) {\r\n\t\t\tdiffRes = momentsDifference(createLocal(duration.from), createLocal(duration.to));\r\n\r\n\t\t\tduration = {};\r\n\t\t\tduration.ms = diffRes.milliseconds;\r\n\t\t\tduration.M = diffRes.months;\r\n\t\t}\r\n\r\n\t\tret = new Duration(duration);\r\n\r\n\t\tif (isDuration(input) && hasOwnProp(input, \"_locale\")) {\r\n\t\t\tret._locale = input._locale;\r\n\t\t}\r\n\r\n\t\treturn ret;\r\n\t}\r\n\r\n\tcreateDuration.fn = Duration.prototype;\r\n\tcreateDuration.invalid = createInvalid$1;\r\n\r\n\tfunction parseIso(inp, sign) {\r\n\t\t// We'd normally use ~~inp for this, but unfortunately it also\r\n\t\t// converts floats to ints.\r\n\t\t// inp may be undefined, so careful calling replace on it.\r\n\t\tvar res = inp && parseFloat(inp.replace(\",\", \".\"));\r\n\t\t// apply sign while we're at it\r\n\t\treturn (isNaN(res) ? 0 : res) * sign;\r\n\t}\r\n\r\n\tfunction positiveMomentsDifference(base, other) {\r\n\t\tvar res = { milliseconds: 0, months: 0 };\r\n\r\n\t\tres.months = other.month() - base.month() + (other.year() - base.year()) * 12;\r\n\t\tif (base.clone().add(res.months, \"M\").isAfter(other)) {\r\n\t\t\t--res.months;\r\n\t\t}\r\n\r\n\t\tres.milliseconds = +other - +base.clone().add(res.months, \"M\");\r\n\r\n\t\treturn res;\r\n\t}\r\n\r\n\tfunction momentsDifference(base, other) {\r\n\t\tvar res;\r\n\t\tif (!(base.isValid() && other.isValid())) {\r\n\t\t\treturn { milliseconds: 0, months: 0 };\r\n\t\t}\r\n\r\n\t\tother = cloneWithOffset(other, base);\r\n\t\tif (base.isBefore(other)) {\r\n\t\t\tres = positiveMomentsDifference(base, other);\r\n\t\t} else {\r\n\t\t\tres = positiveMomentsDifference(other, base);\r\n\t\t\tres.milliseconds = -res.milliseconds;\r\n\t\t\tres.months = -res.months;\r\n\t\t}\r\n\r\n\t\treturn res;\r\n\t}\r\n\r\n\t// TODO: remove 'name' arg after deprecation is removed\r\n\tfunction createAdder(direction, name) {\r\n\t\treturn function (val, period) {\r\n\t\t\tvar dur, tmp;\r\n\t\t\t//invert the arguments, but complain about it\r\n\t\t\tif (period !== null && !isNaN(+period)) {\r\n\t\t\t\tdeprecateSimple(name, \"moment().\" + name + \"(period, number) is deprecated. Please use moment().\" + name + \"(number, period). \" + \"See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info.\");\r\n\t\t\t\ttmp = val;\r\n\t\t\t\tval = period;\r\n\t\t\t\tperiod = tmp;\r\n\t\t\t}\r\n\r\n\t\t\tval = typeof val === \"string\" ? +val : val;\r\n\t\t\tdur = createDuration(val, period);\r\n\t\t\taddSubtract(this, dur, direction);\r\n\t\t\treturn this;\r\n\t\t};\r\n\t}\r\n\r\n\tfunction addSubtract(mom, duration, isAdding, updateOffset) {\r\n\t\tvar milliseconds = duration._milliseconds,\r\n\t\t\tdays = absRound(duration._days),\r\n\t\t\tmonths = absRound(duration._months);\r\n\r\n\t\tif (!mom.isValid()) {\r\n\t\t\t// No op\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tupdateOffset = updateOffset == null ? true : updateOffset;\r\n\r\n\t\tif (months) {\r\n\t\t\tsetMonth(mom, get(mom, \"Month\") + months * isAdding);\r\n\t\t}\r\n\t\tif (days) {\r\n\t\t\tset$1(mom, \"Date\", get(mom, \"Date\") + days * isAdding);\r\n\t\t}\r\n\t\tif (milliseconds) {\r\n\t\t\tmom._d.setTime(mom._d.valueOf() + milliseconds * isAdding);\r\n\t\t}\r\n\t\tif (updateOffset) {\r\n\t\t\thooks.updateOffset(mom, days || months);\r\n\t\t}\r\n\t}\r\n\r\n\tvar add = createAdder(1, \"add\");\r\n\tvar subtract = createAdder(-1, \"subtract\");\r\n\r\n\tfunction getCalendarFormat(myMoment, now) {\r\n\t\tvar diff = myMoment.diff(now, \"days\", true);\r\n\t\treturn diff < -6 ? \"sameElse\" : diff < -1 ? \"lastWeek\" : diff < 0 ? \"lastDay\" : diff < 1 ? \"sameDay\" : diff < 2 ? \"nextDay\" : diff < 7 ? \"nextWeek\" : \"sameElse\";\r\n\t}\r\n\r\n\tfunction calendar$1(time, formats) {\r\n\t\t// We want to compare the start of today, vs this.\r\n\t\t// Getting start-of-today depends on whether we're local/utc/offset or not.\r\n\t\tvar now = time || createLocal(),\r\n\t\t\tsod = cloneWithOffset(now, this).startOf(\"day\"),\r\n\t\t\tformat = hooks.calendarFormat(this, sod) || \"sameElse\";\r\n\r\n\t\tvar output = formats && (isFunction(formats[format]) ? formats[format].call(this, now) : formats[format]);\r\n\r\n\t\treturn this.format(output || this.localeData().calendar(format, this, createLocal(now)));\r\n\t}\r\n\r\n\tfunction clone() {\r\n\t\treturn new Moment(this);\r\n\t}\r\n\r\n\tfunction isAfter(input, units) {\r\n\t\tvar localInput = isMoment(input) ? input : createLocal(input);\r\n\t\tif (!(this.isValid() && localInput.isValid())) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tunits = normalizeUnits(!isUndefined(units) ? units : \"millisecond\");\r\n\t\tif (units === \"millisecond\") {\r\n\t\t\treturn this.valueOf() > localInput.valueOf();\r\n\t\t} else {\r\n\t\t\treturn localInput.valueOf() < this.clone().startOf(units).valueOf();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction isBefore(input, units) {\r\n\t\tvar localInput = isMoment(input) ? input : createLocal(input);\r\n\t\tif (!(this.isValid() && localInput.isValid())) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tunits = normalizeUnits(!isUndefined(units) ? units : \"millisecond\");\r\n\t\tif (units === \"millisecond\") {\r\n\t\t\treturn this.valueOf() < localInput.valueOf();\r\n\t\t} else {\r\n\t\t\treturn this.clone().endOf(units).valueOf() < localInput.valueOf();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction isBetween(from, to, units, inclusivity) {\r\n\t\tinclusivity = inclusivity || \"()\";\r\n\t\treturn (inclusivity[0] === \"(\" ? this.isAfter(from, units) : !this.isBefore(from, units)) && (inclusivity[1] === \")\" ? this.isBefore(to, units) : !this.isAfter(to, units));\r\n\t}\r\n\r\n\tfunction isSame(input, units) {\r\n\t\tvar localInput = isMoment(input) ? input : createLocal(input),\r\n\t\t\tinputMs;\r\n\t\tif (!(this.isValid() && localInput.isValid())) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tunits = normalizeUnits(units || \"millisecond\");\r\n\t\tif (units === \"millisecond\") {\r\n\t\t\treturn this.valueOf() === localInput.valueOf();\r\n\t\t} else {\r\n\t\t\tinputMs = localInput.valueOf();\r\n\t\t\treturn this.clone().startOf(units).valueOf() <= inputMs && inputMs <= this.clone().endOf(units).valueOf();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction isSameOrAfter(input, units) {\r\n\t\treturn this.isSame(input, units) || this.isAfter(input, units);\r\n\t}\r\n\r\n\tfunction isSameOrBefore(input, units) {\r\n\t\treturn this.isSame(input, units) || this.isBefore(input, units);\r\n\t}\r\n\r\n\tfunction diff(input, units, asFloat) {\r\n\t\tvar that, zoneDelta, delta, output;\r\n\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn NaN;\r\n\t\t}\r\n\r\n\t\tthat = cloneWithOffset(input, this);\r\n\r\n\t\tif (!that.isValid()) {\r\n\t\t\treturn NaN;\r\n\t\t}\r\n\r\n\t\tzoneDelta = (that.utcOffset() - this.utcOffset()) * 6e4;\r\n\r\n\t\tunits = normalizeUnits(units);\r\n\r\n\t\tswitch (units) {\r\n\t\t\tcase \"year\":\r\n\t\t\t\toutput = monthDiff(this, that) / 12;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"month\":\r\n\t\t\t\toutput = monthDiff(this, that);\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"quarter\":\r\n\t\t\t\toutput = monthDiff(this, that) / 3;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"second\":\r\n\t\t\t\toutput = (this - that) / 1e3;\r\n\t\t\t\tbreak; // 1000\r\n\t\t\tcase \"minute\":\r\n\t\t\t\toutput = (this - that) / 6e4;\r\n\t\t\t\tbreak; // 1000 * 60\r\n\t\t\tcase \"hour\":\r\n\t\t\t\toutput = (this - that) / 36e5;\r\n\t\t\t\tbreak; // 1000 * 60 * 60\r\n\t\t\tcase \"day\":\r\n\t\t\t\toutput = (this - that - zoneDelta) / 864e5;\r\n\t\t\t\tbreak; // 1000 * 60 * 60 * 24, negate dst\r\n\t\t\tcase \"week\":\r\n\t\t\t\toutput = (this - that - zoneDelta) / 6048e5;\r\n\t\t\t\tbreak; // 1000 * 60 * 60 * 24 * 7, negate dst\r\n\t\t\tdefault:\r\n\t\t\t\toutput = this - that;\r\n\t\t}\r\n\r\n\t\treturn asFloat ? output : absFloor(output);\r\n\t}\r\n\r\n\tfunction monthDiff(a, b) {\r\n\t\t// difference in months\r\n\t\tvar wholeMonthDiff = (b.year() - a.year()) * 12 + (b.month() - a.month()),\r\n\t\t\t// b is in (anchor - 1 month, anchor + 1 month)\r\n\t\t\tanchor = a.clone().add(wholeMonthDiff, \"months\"),\r\n\t\t\tanchor2,\r\n\t\t\tadjust;\r\n\r\n\t\tif (b - anchor < 0) {\r\n\t\t\tanchor2 = a.clone().add(wholeMonthDiff - 1, \"months\");\r\n\t\t\t// linear across the month\r\n\t\t\tadjust = (b - anchor) / (anchor - anchor2);\r\n\t\t} else {\r\n\t\t\tanchor2 = a.clone().add(wholeMonthDiff + 1, \"months\");\r\n\t\t\t// linear across the month\r\n\t\t\tadjust = (b - anchor) / (anchor2 - anchor);\r\n\t\t}\r\n\r\n\t\t//check for negative zero, return zero if negative zero\r\n\t\treturn -(wholeMonthDiff + adjust) || 0;\r\n\t}\r\n\r\n\thooks.defaultFormat = \"YYYY-MM-DDTHH:mm:ssZ\";\r\n\thooks.defaultFormatUtc = \"YYYY-MM-DDTHH:mm:ss[Z]\";\r\n\r\n\tfunction toString() {\r\n\t\treturn this.clone().locale(\"en\").format(\"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\r\n\t}\r\n\r\n\tfunction toISOString(keepOffset) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t\tvar utc = keepOffset !== true;\r\n\t\tvar m = utc ? this.clone().utc() : this;\r\n\t\tif (m.year() < 0 || m.year() > 9999) {\r\n\t\t\treturn formatMoment(m, utc ? \"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]\" : \"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ\");\r\n\t\t}\r\n\t\tif (isFunction(Date.prototype.toISOString)) {\r\n\t\t\t// native implementation is ~50x faster, use it when we can\r\n\t\t\tif (utc) {\r\n\t\t\t\treturn this.toDate().toISOString();\r\n\t\t\t} else {\r\n\t\t\t\treturn new Date(this._d.valueOf()).toISOString().replace(\"Z\", formatMoment(m, \"Z\"));\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn formatMoment(m, utc ? \"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]\" : \"YYYY-MM-DD[T]HH:mm:ss.SSSZ\");\r\n\t}\r\n\r\n\t/**\r\n\t * Return a human readable representation of a moment that can\r\n\t * also be evaluated to get a new moment which is the same\r\n\t *\r\n\t * @link https://nodejs.org/dist/latest/docs/api/util.html#util_custom_inspect_function_on_objects\r\n\t */\r\n\tfunction inspect() {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn \"moment.invalid(/* \" + this._i + \" */)\";\r\n\t\t}\r\n\t\tvar func = \"moment\";\r\n\t\tvar zone = \"\";\r\n\t\tif (!this.isLocal()) {\r\n\t\t\tfunc = this.utcOffset() === 0 ? \"moment.utc\" : \"moment.parseZone\";\r\n\t\t\tzone = \"Z\";\r\n\t\t}\r\n\t\tvar prefix = \"[\" + func + '(\"]';\r\n\t\tvar year = 0 <= this.year() && this.year() <= 9999 ? \"YYYY\" : \"YYYYYY\";\r\n\t\tvar datetime = \"-MM-DD[T]HH:mm:ss.SSS\";\r\n\t\tvar suffix = zone + '[\")]';\r\n\r\n\t\treturn this.format(prefix + year + datetime + suffix);\r\n\t}\r\n\r\n\tfunction format(inputString) {\r\n\t\tif (!inputString) {\r\n\t\t\tinputString = this.isUtc() ? hooks.defaultFormatUtc : hooks.defaultFormat;\r\n\t\t}\r\n\t\tvar output = formatMoment(this, inputString);\r\n\t\treturn this.localeData().postformat(output);\r\n\t}\r\n\r\n\tfunction from(time, withoutSuffix) {\r\n\t\tif (this.isValid() && ((isMoment(time) && time.isValid()) || createLocal(time).isValid())) {\r\n\t\t\treturn createDuration({ to: this, from: time }).locale(this.locale()).humanize(!withoutSuffix);\r\n\t\t} else {\r\n\t\t\treturn this.localeData().invalidDate();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction fromNow(withoutSuffix) {\r\n\t\treturn this.from(createLocal(), withoutSuffix);\r\n\t}\r\n\r\n\tfunction to(time, withoutSuffix) {\r\n\t\tif (this.isValid() && ((isMoment(time) && time.isValid()) || createLocal(time).isValid())) {\r\n\t\t\treturn createDuration({ from: this, to: time }).locale(this.locale()).humanize(!withoutSuffix);\r\n\t\t} else {\r\n\t\t\treturn this.localeData().invalidDate();\r\n\t\t}\r\n\t}\r\n\r\n\tfunction toNow(withoutSuffix) {\r\n\t\treturn this.to(createLocal(), withoutSuffix);\r\n\t}\r\n\r\n\t// If passed a locale key, it will set the locale for this\r\n\t// instance.  Otherwise, it will return the locale configuration\r\n\t// variables for this instance.\r\n\tfunction locale(key) {\r\n\t\tvar newLocaleData;\r\n\r\n\t\tif (key === undefined) {\r\n\t\t\treturn this._locale._abbr;\r\n\t\t} else {\r\n\t\t\tnewLocaleData = getLocale(key);\r\n\t\t\tif (newLocaleData != null) {\r\n\t\t\t\tthis._locale = newLocaleData;\r\n\t\t\t}\r\n\t\t\treturn this;\r\n\t\t}\r\n\t}\r\n\r\n\tvar lang = deprecate(\"moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.\", function (key) {\r\n\t\tif (key === undefined) {\r\n\t\t\treturn this.localeData();\r\n\t\t} else {\r\n\t\t\treturn this.locale(key);\r\n\t\t}\r\n\t});\r\n\r\n\tfunction localeData() {\r\n\t\treturn this._locale;\r\n\t}\r\n\r\n\tfunction startOf(units) {\r\n\t\tunits = normalizeUnits(units);\r\n\t\t// the following switch intentionally omits break keywords\r\n\t\t// to utilize falling through the cases.\r\n\t\tswitch (units) {\r\n\t\t\tcase \"year\":\r\n\t\t\t\tthis.month(0);\r\n\t\t\t/* falls through */\r\n\t\t\tcase \"quarter\":\r\n\t\t\tcase \"month\":\r\n\t\t\t\tthis.date(1);\r\n\t\t\t/* falls through */\r\n\t\t\tcase \"week\":\r\n\t\t\tcase \"isoWeek\":\r\n\t\t\tcase \"day\":\r\n\t\t\tcase \"date\":\r\n\t\t\t\tthis.hours(0);\r\n\t\t\t/* falls through */\r\n\t\t\tcase \"hour\":\r\n\t\t\t\tthis.minutes(0);\r\n\t\t\t/* falls through */\r\n\t\t\tcase \"minute\":\r\n\t\t\t\tthis.seconds(0);\r\n\t\t\t/* falls through */\r\n\t\t\tcase \"second\":\r\n\t\t\t\tthis.milliseconds(0);\r\n\t\t}\r\n\r\n\t\t// weeks are a special case\r\n\t\tif (units === \"week\") {\r\n\t\t\tthis.weekday(0);\r\n\t\t}\r\n\t\tif (units === \"isoWeek\") {\r\n\t\t\tthis.isoWeekday(1);\r\n\t\t}\r\n\r\n\t\t// quarters are also special\r\n\t\tif (units === \"quarter\") {\r\n\t\t\tthis.month(Math.floor(this.month() / 3) * 3);\r\n\t\t}\r\n\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction endOf(units) {\r\n\t\tunits = normalizeUnits(units);\r\n\t\tif (units === undefined || units === \"millisecond\") {\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\t// 'date' is an alias for 'day', so it should be considered as such.\r\n\t\tif (units === \"date\") {\r\n\t\t\tunits = \"day\";\r\n\t\t}\r\n\r\n\t\treturn this.startOf(units)\r\n\t\t\t.add(1, units === \"isoWeek\" ? \"week\" : units)\r\n\t\t\t.subtract(1, \"ms\");\r\n\t}\r\n\r\n\tfunction valueOf() {\r\n\t\treturn this._d.valueOf() - (this._offset || 0) * 60000;\r\n\t}\r\n\r\n\tfunction unix() {\r\n\t\treturn Math.floor(this.valueOf() / 1000);\r\n\t}\r\n\r\n\tfunction toDate() {\r\n\t\treturn new Date(this.valueOf());\r\n\t}\r\n\r\n\tfunction toArray() {\r\n\t\tvar m = this;\r\n\t\treturn [m.year(), m.month(), m.date(), m.hour(), m.minute(), m.second(), m.millisecond()];\r\n\t}\r\n\r\n\tfunction toObject() {\r\n\t\tvar m = this;\r\n\t\treturn {\r\n\t\t\tyears: m.year(),\r\n\t\t\tmonths: m.month(),\r\n\t\t\tdate: m.date(),\r\n\t\t\thours: m.hours(),\r\n\t\t\tminutes: m.minutes(),\r\n\t\t\tseconds: m.seconds(),\r\n\t\t\tmilliseconds: m.milliseconds(),\r\n\t\t};\r\n\t}\r\n\r\n\tfunction toJSON() {\r\n\t\t// new Date(NaN).toJSON() === null\r\n\t\treturn this.isValid() ? this.toISOString() : null;\r\n\t}\r\n\r\n\tfunction isValid$2() {\r\n\t\treturn isValid(this);\r\n\t}\r\n\r\n\tfunction parsingFlags() {\r\n\t\treturn extend({}, getParsingFlags(this));\r\n\t}\r\n\r\n\tfunction invalidAt() {\r\n\t\treturn getParsingFlags(this).overflow;\r\n\t}\r\n\r\n\tfunction creationData() {\r\n\t\treturn {\r\n\t\t\tinput: this._i,\r\n\t\t\tformat: this._f,\r\n\t\t\tlocale: this._locale,\r\n\t\t\tisUTC: this._isUTC,\r\n\t\t\tstrict: this._strict,\r\n\t\t};\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(0, [\"gg\", 2], 0, function () {\r\n\t\treturn this.weekYear() % 100;\r\n\t});\r\n\r\n\taddFormatToken(0, [\"GG\", 2], 0, function () {\r\n\t\treturn this.isoWeekYear() % 100;\r\n\t});\r\n\r\n\tfunction addWeekYearFormatToken(token, getter) {\r\n\t\taddFormatToken(0, [token, token.length], 0, getter);\r\n\t}\r\n\r\n\taddWeekYearFormatToken(\"gggg\", \"weekYear\");\r\n\taddWeekYearFormatToken(\"ggggg\", \"weekYear\");\r\n\taddWeekYearFormatToken(\"GGGG\", \"isoWeekYear\");\r\n\taddWeekYearFormatToken(\"GGGGG\", \"isoWeekYear\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"weekYear\", \"gg\");\r\n\taddUnitAlias(\"isoWeekYear\", \"GG\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"weekYear\", 1);\r\n\taddUnitPriority(\"isoWeekYear\", 1);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"G\", matchSigned);\r\n\taddRegexToken(\"g\", matchSigned);\r\n\taddRegexToken(\"GG\", match1to2, match2);\r\n\taddRegexToken(\"gg\", match1to2, match2);\r\n\taddRegexToken(\"GGGG\", match1to4, match4);\r\n\taddRegexToken(\"gggg\", match1to4, match4);\r\n\taddRegexToken(\"GGGGG\", match1to6, match6);\r\n\taddRegexToken(\"ggggg\", match1to6, match6);\r\n\r\n\taddWeekParseToken([\"gggg\", \"ggggg\", \"GGGG\", \"GGGGG\"], function (input, week, config, token) {\r\n\t\tweek[token.substr(0, 2)] = toInt(input);\r\n\t});\r\n\r\n\taddWeekParseToken([\"gg\", \"GG\"], function (input, week, config, token) {\r\n\t\tweek[token] = hooks.parseTwoDigitYear(input);\r\n\t});\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getSetWeekYear(input) {\r\n\t\treturn getSetWeekYearHelper.call(this, input, this.week(), this.weekday(), this.localeData()._week.dow, this.localeData()._week.doy);\r\n\t}\r\n\r\n\tfunction getSetISOWeekYear(input) {\r\n\t\treturn getSetWeekYearHelper.call(this, input, this.isoWeek(), this.isoWeekday(), 1, 4);\r\n\t}\r\n\r\n\tfunction getISOWeeksInYear() {\r\n\t\treturn weeksInYear(this.year(), 1, 4);\r\n\t}\r\n\r\n\tfunction getWeeksInYear() {\r\n\t\tvar weekInfo = this.localeData()._week;\r\n\t\treturn weeksInYear(this.year(), weekInfo.dow, weekInfo.doy);\r\n\t}\r\n\r\n\tfunction getSetWeekYearHelper(input, week, weekday, dow, doy) {\r\n\t\tvar weeksTarget;\r\n\t\tif (input == null) {\r\n\t\t\treturn weekOfYear(this, dow, doy).year;\r\n\t\t} else {\r\n\t\t\tweeksTarget = weeksInYear(input, dow, doy);\r\n\t\t\tif (week > weeksTarget) {\r\n\t\t\t\tweek = weeksTarget;\r\n\t\t\t}\r\n\t\t\treturn setWeekAll.call(this, input, week, weekday, dow, doy);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction setWeekAll(weekYear, week, weekday, dow, doy) {\r\n\t\tvar dayOfYearData = dayOfYearFromWeeks(weekYear, week, weekday, dow, doy),\r\n\t\t\tdate = createUTCDate(dayOfYearData.year, 0, dayOfYearData.dayOfYear);\r\n\r\n\t\tthis.year(date.getUTCFullYear());\r\n\t\tthis.month(date.getUTCMonth());\r\n\t\tthis.date(date.getUTCDate());\r\n\t\treturn this;\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"Q\", 0, \"Qo\", \"quarter\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"quarter\", \"Q\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"quarter\", 7);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"Q\", match1);\r\n\taddParseToken(\"Q\", function (input, array) {\r\n\t\tarray[MONTH] = (toInt(input) - 1) * 3;\r\n\t});\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getSetQuarter(input) {\r\n\t\treturn input == null ? Math.ceil((this.month() + 1) / 3) : this.month((input - 1) * 3 + (this.month() % 3));\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"D\", [\"DD\", 2], \"Do\", \"date\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"date\", \"D\");\r\n\r\n\t// PRIOROITY\r\n\taddUnitPriority(\"date\", 9);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"D\", match1to2);\r\n\taddRegexToken(\"DD\", match1to2, match2);\r\n\taddRegexToken(\"Do\", function (isStrict, locale) {\r\n\t\t// TODO: Remove \"ordinalParse\" fallback in next major release.\r\n\t\treturn isStrict ? locale._dayOfMonthOrdinalParse || locale._ordinalParse : locale._dayOfMonthOrdinalParseLenient;\r\n\t});\r\n\r\n\taddParseToken([\"D\", \"DD\"], DATE);\r\n\taddParseToken(\"Do\", function (input, array) {\r\n\t\tarray[DATE] = toInt(input.match(match1to2)[0]);\r\n\t});\r\n\r\n\t// MOMENTS\r\n\r\n\tvar getSetDayOfMonth = makeGetSet(\"Date\", true);\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"DDD\", [\"DDDD\", 3], \"DDDo\", \"dayOfYear\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"dayOfYear\", \"DDD\");\r\n\r\n\t// PRIORITY\r\n\taddUnitPriority(\"dayOfYear\", 4);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"DDD\", match1to3);\r\n\taddRegexToken(\"DDDD\", match3);\r\n\taddParseToken([\"DDD\", \"DDDD\"], function (input, array, config) {\r\n\t\tconfig._dayOfYear = toInt(input);\r\n\t});\r\n\r\n\t// HELPERS\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getSetDayOfYear(input) {\r\n\t\tvar dayOfYear = Math.round((this.clone().startOf(\"day\") - this.clone().startOf(\"year\")) / 864e5) + 1;\r\n\t\treturn input == null ? dayOfYear : this.add(input - dayOfYear, \"d\");\r\n\t}\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"m\", [\"mm\", 2], 0, \"minute\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"minute\", \"m\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"minute\", 14);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"m\", match1to2);\r\n\taddRegexToken(\"mm\", match1to2, match2);\r\n\taddParseToken([\"m\", \"mm\"], MINUTE);\r\n\r\n\t// MOMENTS\r\n\r\n\tvar getSetMinute = makeGetSet(\"Minutes\", false);\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"s\", [\"ss\", 2], 0, \"second\");\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"second\", \"s\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"second\", 15);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"s\", match1to2);\r\n\taddRegexToken(\"ss\", match1to2, match2);\r\n\taddParseToken([\"s\", \"ss\"], SECOND);\r\n\r\n\t// MOMENTS\r\n\r\n\tvar getSetSecond = makeGetSet(\"Seconds\", false);\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"S\", 0, 0, function () {\r\n\t\treturn ~~(this.millisecond() / 100);\r\n\t});\r\n\r\n\taddFormatToken(0, [\"SS\", 2], 0, function () {\r\n\t\treturn ~~(this.millisecond() / 10);\r\n\t});\r\n\r\n\taddFormatToken(0, [\"SSS\", 3], 0, \"millisecond\");\r\n\taddFormatToken(0, [\"SSSS\", 4], 0, function () {\r\n\t\treturn this.millisecond() * 10;\r\n\t});\r\n\taddFormatToken(0, [\"SSSSS\", 5], 0, function () {\r\n\t\treturn this.millisecond() * 100;\r\n\t});\r\n\taddFormatToken(0, [\"SSSSSS\", 6], 0, function () {\r\n\t\treturn this.millisecond() * 1000;\r\n\t});\r\n\taddFormatToken(0, [\"SSSSSSS\", 7], 0, function () {\r\n\t\treturn this.millisecond() * 10000;\r\n\t});\r\n\taddFormatToken(0, [\"SSSSSSSS\", 8], 0, function () {\r\n\t\treturn this.millisecond() * 100000;\r\n\t});\r\n\taddFormatToken(0, [\"SSSSSSSSS\", 9], 0, function () {\r\n\t\treturn this.millisecond() * 1000000;\r\n\t});\r\n\r\n\t// ALIASES\r\n\r\n\taddUnitAlias(\"millisecond\", \"ms\");\r\n\r\n\t// PRIORITY\r\n\r\n\taddUnitPriority(\"millisecond\", 16);\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"S\", match1to3, match1);\r\n\taddRegexToken(\"SS\", match1to3, match2);\r\n\taddRegexToken(\"SSS\", match1to3, match3);\r\n\r\n\tvar token;\r\n\tfor (token = \"SSSS\"; token.length <= 9; token += \"S\") {\r\n\t\taddRegexToken(token, matchUnsigned);\r\n\t}\r\n\r\n\tfunction parseMs(input, array) {\r\n\t\tarray[MILLISECOND] = toInt((\"0.\" + input) * 1000);\r\n\t}\r\n\r\n\tfor (token = \"S\"; token.length <= 9; token += \"S\") {\r\n\t\taddParseToken(token, parseMs);\r\n\t}\r\n\t// MOMENTS\r\n\r\n\tvar getSetMillisecond = makeGetSet(\"Milliseconds\", false);\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"z\", 0, 0, \"zoneAbbr\");\r\n\taddFormatToken(\"zz\", 0, 0, \"zoneName\");\r\n\r\n\t// MOMENTS\r\n\r\n\tfunction getZoneAbbr() {\r\n\t\treturn this._isUTC ? \"UTC\" : \"\";\r\n\t}\r\n\r\n\tfunction getZoneName() {\r\n\t\treturn this._isUTC ? \"Coordinated Universal Time\" : \"\";\r\n\t}\r\n\r\n\tvar proto = Moment.prototype;\r\n\r\n\tproto.add = add;\r\n\tproto.calendar = calendar$1;\r\n\tproto.clone = clone;\r\n\tproto.diff = diff;\r\n\tproto.endOf = endOf;\r\n\tproto.format = format;\r\n\tproto.from = from;\r\n\tproto.fromNow = fromNow;\r\n\tproto.to = to;\r\n\tproto.toNow = toNow;\r\n\tproto.get = stringGet;\r\n\tproto.invalidAt = invalidAt;\r\n\tproto.isAfter = isAfter;\r\n\tproto.isBefore = isBefore;\r\n\tproto.isBetween = isBetween;\r\n\tproto.isSame = isSame;\r\n\tproto.isSameOrAfter = isSameOrAfter;\r\n\tproto.isSameOrBefore = isSameOrBefore;\r\n\tproto.isValid = isValid$2;\r\n\tproto.lang = lang;\r\n\tproto.locale = locale;\r\n\tproto.localeData = localeData;\r\n\tproto.max = prototypeMax;\r\n\tproto.min = prototypeMin;\r\n\tproto.parsingFlags = parsingFlags;\r\n\tproto.set = stringSet;\r\n\tproto.startOf = startOf;\r\n\tproto.subtract = subtract;\r\n\tproto.toArray = toArray;\r\n\tproto.toObject = toObject;\r\n\tproto.toDate = toDate;\r\n\tproto.toISOString = toISOString;\r\n\tproto.inspect = inspect;\r\n\tproto.toJSON = toJSON;\r\n\tproto.toString = toString;\r\n\tproto.unix = unix;\r\n\tproto.valueOf = valueOf;\r\n\tproto.creationData = creationData;\r\n\r\n\t// Year\r\n\tproto.year = getSetYear;\r\n\tproto.isLeapYear = getIsLeapYear;\r\n\r\n\t// Week Year\r\n\tproto.weekYear = getSetWeekYear;\r\n\tproto.isoWeekYear = getSetISOWeekYear;\r\n\r\n\t// Quarter\r\n\tproto.quarter = proto.quarters = getSetQuarter;\r\n\r\n\t// Month\r\n\tproto.month = getSetMonth;\r\n\tproto.daysInMonth = getDaysInMonth;\r\n\r\n\t// Week\r\n\tproto.week = proto.weeks = getSetWeek;\r\n\tproto.isoWeek = proto.isoWeeks = getSetISOWeek;\r\n\tproto.weeksInYear = getWeeksInYear;\r\n\tproto.isoWeeksInYear = getISOWeeksInYear;\r\n\r\n\t// Day\r\n\tproto.date = getSetDayOfMonth;\r\n\tproto.day = proto.days = getSetDayOfWeek;\r\n\tproto.weekday = getSetLocaleDayOfWeek;\r\n\tproto.isoWeekday = getSetISODayOfWeek;\r\n\tproto.dayOfYear = getSetDayOfYear;\r\n\r\n\t// Hour\r\n\tproto.hour = proto.hours = getSetHour;\r\n\r\n\t// Minute\r\n\tproto.minute = proto.minutes = getSetMinute;\r\n\r\n\t// Second\r\n\tproto.second = proto.seconds = getSetSecond;\r\n\r\n\t// Millisecond\r\n\tproto.millisecond = proto.milliseconds = getSetMillisecond;\r\n\r\n\t// Offset\r\n\tproto.utcOffset = getSetOffset;\r\n\tproto.utc = setOffsetToUTC;\r\n\tproto.local = setOffsetToLocal;\r\n\tproto.parseZone = setOffsetToParsedOffset;\r\n\tproto.hasAlignedHourOffset = hasAlignedHourOffset;\r\n\tproto.isDST = isDaylightSavingTime;\r\n\tproto.isLocal = isLocal;\r\n\tproto.isUtcOffset = isUtcOffset;\r\n\tproto.isUtc = isUtc;\r\n\tproto.isUTC = isUtc;\r\n\r\n\t// Timezone\r\n\tproto.zoneAbbr = getZoneAbbr;\r\n\tproto.zoneName = getZoneName;\r\n\r\n\t// Deprecations\r\n\tproto.dates = deprecate(\"dates accessor is deprecated. Use date instead.\", getSetDayOfMonth);\r\n\tproto.months = deprecate(\"months accessor is deprecated. Use month instead\", getSetMonth);\r\n\tproto.years = deprecate(\"years accessor is deprecated. Use year instead\", getSetYear);\r\n\tproto.zone = deprecate(\"moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/\", getSetZone);\r\n\tproto.isDSTShifted = deprecate(\"isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information\", isDaylightSavingTimeShifted);\r\n\r\n\tfunction createUnix(input) {\r\n\t\treturn createLocal(input * 1000);\r\n\t}\r\n\r\n\tfunction createInZone() {\r\n\t\treturn createLocal.apply(null, arguments).parseZone();\r\n\t}\r\n\r\n\tfunction preParsePostFormat(string) {\r\n\t\treturn string;\r\n\t}\r\n\r\n\tvar proto$1 = Locale.prototype;\r\n\r\n\tproto$1.calendar = calendar;\r\n\tproto$1.longDateFormat = longDateFormat;\r\n\tproto$1.invalidDate = invalidDate;\r\n\tproto$1.ordinal = ordinal;\r\n\tproto$1.preparse = preParsePostFormat;\r\n\tproto$1.postformat = preParsePostFormat;\r\n\tproto$1.relativeTime = relativeTime;\r\n\tproto$1.pastFuture = pastFuture;\r\n\tproto$1.set = set;\r\n\r\n\t// Month\r\n\tproto$1.months = localeMonths;\r\n\tproto$1.monthsShort = localeMonthsShort;\r\n\tproto$1.monthsParse = localeMonthsParse;\r\n\tproto$1.monthsRegex = monthsRegex;\r\n\tproto$1.monthsShortRegex = monthsShortRegex;\r\n\r\n\t// Week\r\n\tproto$1.week = localeWeek;\r\n\tproto$1.firstDayOfYear = localeFirstDayOfYear;\r\n\tproto$1.firstDayOfWeek = localeFirstDayOfWeek;\r\n\r\n\t// Day of Week\r\n\tproto$1.weekdays = localeWeekdays;\r\n\tproto$1.weekdaysMin = localeWeekdaysMin;\r\n\tproto$1.weekdaysShort = localeWeekdaysShort;\r\n\tproto$1.weekdaysParse = localeWeekdaysParse;\r\n\r\n\tproto$1.weekdaysRegex = weekdaysRegex;\r\n\tproto$1.weekdaysShortRegex = weekdaysShortRegex;\r\n\tproto$1.weekdaysMinRegex = weekdaysMinRegex;\r\n\r\n\t// Hours\r\n\tproto$1.isPM = localeIsPM;\r\n\tproto$1.meridiem = localeMeridiem;\r\n\r\n\tfunction get$1(format, index, field, setter) {\r\n\t\tvar locale = getLocale();\r\n\t\tvar utc = createUTC().set(setter, index);\r\n\t\treturn locale[field](utc, format);\r\n\t}\r\n\r\n\tfunction listMonthsImpl(format, index, field) {\r\n\t\tif (isNumber(format)) {\r\n\t\t\tindex = format;\r\n\t\t\tformat = undefined;\r\n\t\t}\r\n\r\n\t\tformat = format || \"\";\r\n\r\n\t\tif (index != null) {\r\n\t\t\treturn get$1(format, index, field, \"month\");\r\n\t\t}\r\n\r\n\t\tvar i;\r\n\t\tvar out = [];\r\n\t\tfor (i = 0; i < 12; i++) {\r\n\t\t\tout[i] = get$1(format, i, field, \"month\");\r\n\t\t}\r\n\t\treturn out;\r\n\t}\r\n\r\n\t// ()\r\n\t// (5)\r\n\t// (fmt, 5)\r\n\t// (fmt)\r\n\t// (true)\r\n\t// (true, 5)\r\n\t// (true, fmt, 5)\r\n\t// (true, fmt)\r\n\tfunction listWeekdaysImpl(localeSorted, format, index, field) {\r\n\t\tif (typeof localeSorted === \"boolean\") {\r\n\t\t\tif (isNumber(format)) {\r\n\t\t\t\tindex = format;\r\n\t\t\t\tformat = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tformat = format || \"\";\r\n\t\t} else {\r\n\t\t\tformat = localeSorted;\r\n\t\t\tindex = format;\r\n\t\t\tlocaleSorted = false;\r\n\r\n\t\t\tif (isNumber(format)) {\r\n\t\t\t\tindex = format;\r\n\t\t\t\tformat = undefined;\r\n\t\t\t}\r\n\r\n\t\t\tformat = format || \"\";\r\n\t\t}\r\n\r\n\t\tvar locale = getLocale(),\r\n\t\t\tshift = localeSorted ? locale._week.dow : 0;\r\n\r\n\t\tif (index != null) {\r\n\t\t\treturn get$1(format, (index + shift) % 7, field, \"day\");\r\n\t\t}\r\n\r\n\t\tvar i;\r\n\t\tvar out = [];\r\n\t\tfor (i = 0; i < 7; i++) {\r\n\t\t\tout[i] = get$1(format, (i + shift) % 7, field, \"day\");\r\n\t\t}\r\n\t\treturn out;\r\n\t}\r\n\r\n\tfunction listMonths(format, index) {\r\n\t\treturn listMonthsImpl(format, index, \"months\");\r\n\t}\r\n\r\n\tfunction listMonthsShort(format, index) {\r\n\t\treturn listMonthsImpl(format, index, \"monthsShort\");\r\n\t}\r\n\r\n\tfunction listWeekdays(localeSorted, format, index) {\r\n\t\treturn listWeekdaysImpl(localeSorted, format, index, \"weekdays\");\r\n\t}\r\n\r\n\tfunction listWeekdaysShort(localeSorted, format, index) {\r\n\t\treturn listWeekdaysImpl(localeSorted, format, index, \"weekdaysShort\");\r\n\t}\r\n\r\n\tfunction listWeekdaysMin(localeSorted, format, index) {\r\n\t\treturn listWeekdaysImpl(localeSorted, format, index, \"weekdaysMin\");\r\n\t}\r\n\r\n\tgetSetGlobalLocale(\"en\", {\r\n\t\tdayOfMonthOrdinalParse: /\\d{1,2}(th|st|nd|rd)/,\r\n\t\tordinal: function (number) {\r\n\t\t\tvar b = number % 10,\r\n\t\t\t\toutput = toInt((number % 100) / 10) === 1 ? \"th\" : b === 1 ? \"st\" : b === 2 ? \"nd\" : b === 3 ? \"rd\" : \"th\";\r\n\t\t\treturn number + output;\r\n\t\t},\r\n\t});\r\n\r\n\t// Side effect imports\r\n\thooks.lang = deprecate(\"moment.lang is deprecated. Use moment.locale instead.\", getSetGlobalLocale);\r\n\thooks.langData = deprecate(\"moment.langData is deprecated. Use moment.localeData instead.\", getLocale);\r\n\r\n\tvar mathAbs = Math.abs;\r\n\r\n\tfunction abs() {\r\n\t\tvar data = this._data;\r\n\r\n\t\tthis._milliseconds = mathAbs(this._milliseconds);\r\n\t\tthis._days = mathAbs(this._days);\r\n\t\tthis._months = mathAbs(this._months);\r\n\r\n\t\tdata.milliseconds = mathAbs(data.milliseconds);\r\n\t\tdata.seconds = mathAbs(data.seconds);\r\n\t\tdata.minutes = mathAbs(data.minutes);\r\n\t\tdata.hours = mathAbs(data.hours);\r\n\t\tdata.months = mathAbs(data.months);\r\n\t\tdata.years = mathAbs(data.years);\r\n\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction addSubtract$1(duration, input, value, direction) {\r\n\t\tvar other = createDuration(input, value);\r\n\r\n\t\tduration._milliseconds += direction * other._milliseconds;\r\n\t\tduration._days += direction * other._days;\r\n\t\tduration._months += direction * other._months;\r\n\r\n\t\treturn duration._bubble();\r\n\t}\r\n\r\n\t// supports only 2.0-style add(1, 's') or add(duration)\r\n\tfunction add$1(input, value) {\r\n\t\treturn addSubtract$1(this, input, value, 1);\r\n\t}\r\n\r\n\t// supports only 2.0-style subtract(1, 's') or subtract(duration)\r\n\tfunction subtract$1(input, value) {\r\n\t\treturn addSubtract$1(this, input, value, -1);\r\n\t}\r\n\r\n\tfunction absCeil(number) {\r\n\t\tif (number < 0) {\r\n\t\t\treturn Math.floor(number);\r\n\t\t} else {\r\n\t\t\treturn Math.ceil(number);\r\n\t\t}\r\n\t}\r\n\r\n\tfunction bubble() {\r\n\t\tvar milliseconds = this._milliseconds;\r\n\t\tvar days = this._days;\r\n\t\tvar months = this._months;\r\n\t\tvar data = this._data;\r\n\t\tvar seconds, minutes, hours, years, monthsFromDays;\r\n\r\n\t\t// if we have a mix of positive and negative values, bubble down first\r\n\t\t// check: https://github.com/moment/moment/issues/2166\r\n\t\tif (!((milliseconds >= 0 && days >= 0 && months >= 0) || (milliseconds <= 0 && days <= 0 && months <= 0))) {\r\n\t\t\tmilliseconds += absCeil(monthsToDays(months) + days) * 864e5;\r\n\t\t\tdays = 0;\r\n\t\t\tmonths = 0;\r\n\t\t}\r\n\r\n\t\t// The following code bubbles up values, see the tests for\r\n\t\t// examples of what that means.\r\n\t\tdata.milliseconds = milliseconds % 1000;\r\n\r\n\t\tseconds = absFloor(milliseconds / 1000);\r\n\t\tdata.seconds = seconds % 60;\r\n\r\n\t\tminutes = absFloor(seconds / 60);\r\n\t\tdata.minutes = minutes % 60;\r\n\r\n\t\thours = absFloor(minutes / 60);\r\n\t\tdata.hours = hours % 24;\r\n\r\n\t\tdays += absFloor(hours / 24);\r\n\r\n\t\t// convert days to months\r\n\t\tmonthsFromDays = absFloor(daysToMonths(days));\r\n\t\tmonths += monthsFromDays;\r\n\t\tdays -= absCeil(monthsToDays(monthsFromDays));\r\n\r\n\t\t// 12 months -> 1 year\r\n\t\tyears = absFloor(months / 12);\r\n\t\tmonths %= 12;\r\n\r\n\t\tdata.days = days;\r\n\t\tdata.months = months;\r\n\t\tdata.years = years;\r\n\r\n\t\treturn this;\r\n\t}\r\n\r\n\tfunction daysToMonths(days) {\r\n\t\t// 400 years have 146097 days (taking into account leap year rules)\r\n\t\t// 400 years have 12 months === 4800\r\n\t\treturn (days * 4800) / 146097;\r\n\t}\r\n\r\n\tfunction monthsToDays(months) {\r\n\t\t// the reverse of daysToMonths\r\n\t\treturn (months * 146097) / 4800;\r\n\t}\r\n\r\n\tfunction as(units) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn NaN;\r\n\t\t}\r\n\t\tvar days;\r\n\t\tvar months;\r\n\t\tvar milliseconds = this._milliseconds;\r\n\r\n\t\tunits = normalizeUnits(units);\r\n\r\n\t\tif (units === \"month\" || units === \"year\") {\r\n\t\t\tdays = this._days + milliseconds / 864e5;\r\n\t\t\tmonths = this._months + daysToMonths(days);\r\n\t\t\treturn units === \"month\" ? months : months / 12;\r\n\t\t} else {\r\n\t\t\t// handle milliseconds separately because of floating point math errors (issue #1867)\r\n\t\t\tdays = this._days + Math.round(monthsToDays(this._months));\r\n\t\t\tswitch (units) {\r\n\t\t\t\tcase \"week\":\r\n\t\t\t\t\treturn days / 7 + milliseconds / 6048e5;\r\n\t\t\t\tcase \"day\":\r\n\t\t\t\t\treturn days + milliseconds / 864e5;\r\n\t\t\t\tcase \"hour\":\r\n\t\t\t\t\treturn days * 24 + milliseconds / 36e5;\r\n\t\t\t\tcase \"minute\":\r\n\t\t\t\t\treturn days * 1440 + milliseconds / 6e4;\r\n\t\t\t\tcase \"second\":\r\n\t\t\t\t\treturn days * 86400 + milliseconds / 1000;\r\n\t\t\t\t// Math.floor prevents floating point math errors here\r\n\t\t\t\tcase \"millisecond\":\r\n\t\t\t\t\treturn Math.floor(days * 864e5) + milliseconds;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\tthrow new Error(\"Unknown unit \" + units);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// TODO: Use this.as('ms')?\r\n\tfunction valueOf$1() {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn NaN;\r\n\t\t}\r\n\t\treturn this._milliseconds + this._days * 864e5 + (this._months % 12) * 2592e6 + toInt(this._months / 12) * 31536e6;\r\n\t}\r\n\r\n\tfunction makeAs(alias) {\r\n\t\treturn function () {\r\n\t\t\treturn this.as(alias);\r\n\t\t};\r\n\t}\r\n\r\n\tvar asMilliseconds = makeAs(\"ms\");\r\n\tvar asSeconds = makeAs(\"s\");\r\n\tvar asMinutes = makeAs(\"m\");\r\n\tvar asHours = makeAs(\"h\");\r\n\tvar asDays = makeAs(\"d\");\r\n\tvar asWeeks = makeAs(\"w\");\r\n\tvar asMonths = makeAs(\"M\");\r\n\tvar asYears = makeAs(\"y\");\r\n\r\n\tfunction clone$1() {\r\n\t\treturn createDuration(this);\r\n\t}\r\n\r\n\tfunction get$2(units) {\r\n\t\tunits = normalizeUnits(units);\r\n\t\treturn this.isValid() ? this[units + \"s\"]() : NaN;\r\n\t}\r\n\r\n\tfunction makeGetter(name) {\r\n\t\treturn function () {\r\n\t\t\treturn this.isValid() ? this._data[name] : NaN;\r\n\t\t};\r\n\t}\r\n\r\n\tvar milliseconds = makeGetter(\"milliseconds\");\r\n\tvar seconds = makeGetter(\"seconds\");\r\n\tvar minutes = makeGetter(\"minutes\");\r\n\tvar hours = makeGetter(\"hours\");\r\n\tvar days = makeGetter(\"days\");\r\n\tvar months = makeGetter(\"months\");\r\n\tvar years = makeGetter(\"years\");\r\n\r\n\tfunction weeks() {\r\n\t\treturn absFloor(this.days() / 7);\r\n\t}\r\n\r\n\tvar round = Math.round;\r\n\tvar thresholds = {\r\n\t\tss: 44, // a few seconds to seconds\r\n\t\ts: 45, // seconds to minute\r\n\t\tm: 45, // minutes to hour\r\n\t\th: 22, // hours to day\r\n\t\td: 26, // days to month\r\n\t\tM: 11, // months to year\r\n\t};\r\n\r\n\t// helper function for moment.fn.from, moment.fn.fromNow, and moment.duration.fn.humanize\r\n\tfunction substituteTimeAgo(string, number, withoutSuffix, isFuture, locale) {\r\n\t\treturn locale.relativeTime(number || 1, !!withoutSuffix, string, isFuture);\r\n\t}\r\n\r\n\tfunction relativeTime$1(posNegDuration, withoutSuffix, locale) {\r\n\t\tvar duration = createDuration(posNegDuration).abs();\r\n\t\tvar seconds = round(duration.as(\"s\"));\r\n\t\tvar minutes = round(duration.as(\"m\"));\r\n\t\tvar hours = round(duration.as(\"h\"));\r\n\t\tvar days = round(duration.as(\"d\"));\r\n\t\tvar months = round(duration.as(\"M\"));\r\n\t\tvar years = round(duration.as(\"y\"));\r\n\r\n\t\tvar a = (seconds <= thresholds.ss && [\"s\", seconds]) || (seconds < thresholds.s && [\"ss\", seconds]) || (minutes <= 1 && [\"m\"]) || (minutes < thresholds.m && [\"mm\", minutes]) || (hours <= 1 && [\"h\"]) || (hours < thresholds.h && [\"hh\", hours]) || (days <= 1 && [\"d\"]) || (days < thresholds.d && [\"dd\", days]) || (months <= 1 && [\"M\"]) || (months < thresholds.M && [\"MM\", months]) || (years <= 1 && [\"y\"]) || [\"yy\", years];\r\n\r\n\t\ta[2] = withoutSuffix;\r\n\t\ta[3] = +posNegDuration > 0;\r\n\t\ta[4] = locale;\r\n\t\treturn substituteTimeAgo.apply(null, a);\r\n\t}\r\n\r\n\t// This function allows you to set the rounding function for relative time strings\r\n\tfunction getSetRelativeTimeRounding(roundingFunction) {\r\n\t\tif (roundingFunction === undefined) {\r\n\t\t\treturn round;\r\n\t\t}\r\n\t\tif (typeof roundingFunction === \"function\") {\r\n\t\t\tround = roundingFunction;\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\treturn false;\r\n\t}\r\n\r\n\t// This function allows you to set a threshold for relative time strings\r\n\tfunction getSetRelativeTimeThreshold(threshold, limit) {\r\n\t\tif (thresholds[threshold] === undefined) {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\tif (limit === undefined) {\r\n\t\t\treturn thresholds[threshold];\r\n\t\t}\r\n\t\tthresholds[threshold] = limit;\r\n\t\tif (threshold === \"s\") {\r\n\t\t\tthresholds.ss = limit - 1;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n\r\n\tfunction humanize(withSuffix) {\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn this.localeData().invalidDate();\r\n\t\t}\r\n\r\n\t\tvar locale = this.localeData();\r\n\t\tvar output = relativeTime$1(this, !withSuffix, locale);\r\n\r\n\t\tif (withSuffix) {\r\n\t\t\toutput = locale.pastFuture(+this, output);\r\n\t\t}\r\n\r\n\t\treturn locale.postformat(output);\r\n\t}\r\n\r\n\tvar abs$1 = Math.abs;\r\n\r\n\tfunction sign(x) {\r\n\t\treturn (x > 0) - (x < 0) || +x;\r\n\t}\r\n\r\n\tfunction toISOString$1() {\r\n\t\t// for ISO strings we do not use the normal bubbling rules:\r\n\t\t//  * milliseconds bubble up until they become hours\r\n\t\t//  * days do not bubble at all\r\n\t\t//  * months bubble up until they become years\r\n\t\t// This is because there is no context-free conversion between hours and days\r\n\t\t// (think of clock changes)\r\n\t\t// and also not between days and months (28-31 days per month)\r\n\t\tif (!this.isValid()) {\r\n\t\t\treturn this.localeData().invalidDate();\r\n\t\t}\r\n\r\n\t\tvar seconds = abs$1(this._milliseconds) / 1000;\r\n\t\tvar days = abs$1(this._days);\r\n\t\tvar months = abs$1(this._months);\r\n\t\tvar minutes, hours, years;\r\n\r\n\t\t// 3600 seconds -> 60 minutes -> 1 hour\r\n\t\tminutes = absFloor(seconds / 60);\r\n\t\thours = absFloor(minutes / 60);\r\n\t\tseconds %= 60;\r\n\t\tminutes %= 60;\r\n\r\n\t\t// 12 months -> 1 year\r\n\t\tyears = absFloor(months / 12);\r\n\t\tmonths %= 12;\r\n\r\n\t\t// inspired by https://github.com/dordille/moment-isoduration/blob/master/moment.isoduration.js\r\n\t\tvar Y = years;\r\n\t\tvar M = months;\r\n\t\tvar D = days;\r\n\t\tvar h = hours;\r\n\t\tvar m = minutes;\r\n\t\tvar s = seconds ? seconds.toFixed(3).replace(/\\.?0+$/, \"\") : \"\";\r\n\t\tvar total = this.asSeconds();\r\n\r\n\t\tif (!total) {\r\n\t\t\t// this is the same as C#'s (Noda) and python (isodate)...\r\n\t\t\t// but not other JS (goog.date)\r\n\t\t\treturn \"P0D\";\r\n\t\t}\r\n\r\n\t\tvar totalSign = total < 0 ? \"-\" : \"\";\r\n\t\tvar ymSign = sign(this._months) !== sign(total) ? \"-\" : \"\";\r\n\t\tvar daysSign = sign(this._days) !== sign(total) ? \"-\" : \"\";\r\n\t\tvar hmsSign = sign(this._milliseconds) !== sign(total) ? \"-\" : \"\";\r\n\r\n\t\treturn totalSign + \"P\" + (Y ? ymSign + Y + \"Y\" : \"\") + (M ? ymSign + M + \"M\" : \"\") + (D ? daysSign + D + \"D\" : \"\") + (h || m || s ? \"T\" : \"\") + (h ? hmsSign + h + \"H\" : \"\") + (m ? hmsSign + m + \"M\" : \"\") + (s ? hmsSign + s + \"S\" : \"\");\r\n\t}\r\n\r\n\tvar proto$2 = Duration.prototype;\r\n\r\n\tproto$2.isValid = isValid$1;\r\n\tproto$2.abs = abs;\r\n\tproto$2.add = add$1;\r\n\tproto$2.subtract = subtract$1;\r\n\tproto$2.as = as;\r\n\tproto$2.asMilliseconds = asMilliseconds;\r\n\tproto$2.asSeconds = asSeconds;\r\n\tproto$2.asMinutes = asMinutes;\r\n\tproto$2.asHours = asHours;\r\n\tproto$2.asDays = asDays;\r\n\tproto$2.asWeeks = asWeeks;\r\n\tproto$2.asMonths = asMonths;\r\n\tproto$2.asYears = asYears;\r\n\tproto$2.valueOf = valueOf$1;\r\n\tproto$2._bubble = bubble;\r\n\tproto$2.clone = clone$1;\r\n\tproto$2.get = get$2;\r\n\tproto$2.milliseconds = milliseconds;\r\n\tproto$2.seconds = seconds;\r\n\tproto$2.minutes = minutes;\r\n\tproto$2.hours = hours;\r\n\tproto$2.days = days;\r\n\tproto$2.weeks = weeks;\r\n\tproto$2.months = months;\r\n\tproto$2.years = years;\r\n\tproto$2.humanize = humanize;\r\n\tproto$2.toISOString = toISOString$1;\r\n\tproto$2.toString = toISOString$1;\r\n\tproto$2.toJSON = toISOString$1;\r\n\tproto$2.locale = locale;\r\n\tproto$2.localeData = localeData;\r\n\r\n\t// Deprecations\r\n\tproto$2.toIsoString = deprecate(\"toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)\", toISOString$1);\r\n\tproto$2.lang = lang;\r\n\r\n\t// Side effect imports\r\n\r\n\t// FORMATTING\r\n\r\n\taddFormatToken(\"X\", 0, 0, \"unix\");\r\n\taddFormatToken(\"x\", 0, 0, \"valueOf\");\r\n\r\n\t// PARSING\r\n\r\n\taddRegexToken(\"x\", matchSigned);\r\n\taddRegexToken(\"X\", matchTimestamp);\r\n\taddParseToken(\"X\", function (input, array, config) {\r\n\t\tconfig._d = new Date(parseFloat(input, 10) * 1000);\r\n\t});\r\n\taddParseToken(\"x\", function (input, array, config) {\r\n\t\tconfig._d = new Date(toInt(input));\r\n\t});\r\n\r\n\t// Side effect imports\r\n\r\n\thooks.version = \"2.20.1\";\r\n\r\n\tsetHookCallback(createLocal);\r\n\r\n\thooks.fn = proto;\r\n\thooks.min = min;\r\n\thooks.max = max;\r\n\thooks.now = now;\r\n\thooks.utc = createUTC;\r\n\thooks.unix = createUnix;\r\n\thooks.months = listMonths;\r\n\thooks.isDate = isDate;\r\n\thooks.locale = getSetGlobalLocale;\r\n\thooks.invalid = createInvalid;\r\n\thooks.duration = createDuration;\r\n\thooks.isMoment = isMoment;\r\n\thooks.weekdays = listWeekdays;\r\n\thooks.parseZone = createInZone;\r\n\thooks.localeData = getLocale;\r\n\thooks.isDuration = isDuration;\r\n\thooks.monthsShort = listMonthsShort;\r\n\thooks.weekdaysMin = listWeekdaysMin;\r\n\thooks.defineLocale = defineLocale;\r\n\thooks.updateLocale = updateLocale;\r\n\thooks.locales = listLocales;\r\n\thooks.weekdaysShort = listWeekdaysShort;\r\n\thooks.normalizeUnits = normalizeUnits;\r\n\thooks.relativeTimeRounding = getSetRelativeTimeRounding;\r\n\thooks.relativeTimeThreshold = getSetRelativeTimeThreshold;\r\n\thooks.calendarFormat = getCalendarFormat;\r\n\thooks.prototype = proto;\r\n\r\n\t// currently HTML5 input type only supports 24-hour formats\r\n\thooks.HTML5_FMT = {\r\n\t\tDATETIME_LOCAL: \"YYYY-MM-DDTHH:mm\", // <input type=\"datetime-local\" />\r\n\t\tDATETIME_LOCAL_SECONDS: \"YYYY-MM-DDTHH:mm:ss\", // <input type=\"datetime-local\" step=\"1\" />\r\n\t\tDATETIME_LOCAL_MS: \"YYYY-MM-DDTHH:mm:ss.SSS\", // <input type=\"datetime-local\" step=\"0.001\" />\r\n\t\tDATE: \"YYYY-MM-DD\", // <input type=\"date\" />\r\n\t\tTIME: \"HH:mm\", // <input type=\"time\" />\r\n\t\tTIME_SECONDS: \"HH:mm:ss\", // <input type=\"time\" step=\"1\" />\r\n\t\tTIME_MS: \"HH:mm:ss.SSS\", // <input type=\"time\" step=\"0.001\" />\r\n\t\tWEEK: \"YYYY-[W]WW\", // <input type=\"week\" />\r\n\t\tMONTH: \"YYYY-MM\", // <input type=\"month\" />\r\n\t};\r\n\r\n\treturn hooks;\r\n});\r\n"], "names": ["global", "factory", "exports", "module", "define", "amd", "moment", "this", "<PERSON><PERSON><PERSON><PERSON>", "hooks", "apply", "arguments", "isArray", "input", "Array", "Object", "prototype", "toString", "call", "isObject", "isUndefined", "isNumber", "isDate", "Date", "map", "arr", "fn", "res", "i", "length", "push", "hasOwnProp", "a", "b", "hasOwnProperty", "extend", "valueOf", "createUTC", "format", "locale", "strict", "createLocalOrUTC", "utc", "getParsingFlags", "m", "_pf", "empty", "unusedTokens", "unusedInput", "overflow", "charsLeftOver", "nullInput", "<PERSON><PERSON><PERSON><PERSON>", "invalidFormat", "userInvalidated", "iso", "parsedDateParts", "meridiem", "rfc2822", "weekdayMismatch", "<PERSON><PERSON><PERSON><PERSON>", "_isValid", "flags", "parsedParts", "some", "isNowValid", "isNaN", "_d", "getTime", "invalidWeekday", "_strict", "undefined", "bigHour", "isFrozen", "createInvalid", "NaN", "fun", "t", "len", "momentProperties", "copyConfig", "to", "from", "prop", "val", "_isAMomentObject", "_i", "_f", "_l", "_tzm", "_isUTC", "_offset", "_locale", "updateInProgress", "Moment", "config", "updateOffset", "isMoment", "obj", "absFloor", "number", "Math", "ceil", "floor", "toInt", "argumentForCoercion", "coerced<PERSON>umber", "value", "isFinite", "compareArrays", "array1", "array2", "dont<PERSON><PERSON><PERSON>", "min", "lengthDiff", "abs", "diffs", "warn", "msg", "suppressDeprecationWarnings", "console", "deprecate", "firstTime", "depre<PERSON><PERSON><PERSON><PERSON>", "arg", "args", "key", "slice", "join", "Error", "stack", "deprecations", "deprecateSimple", "name", "isFunction", "Function", "mergeConfigs", "parentConfig", "childConfig", "Locale", "set", "keys", "aliases", "addUnitAlias", "unit", "shorthand", "lowerCase", "toLowerCase", "normalizeUnits", "units", "normalizeObjectUnits", "inputObject", "normalizedProp", "normalizedInput", "priorities", "addUnitPriority", "priority", "zeroFill", "targetLength", "forceSign", "absNumber", "pow", "max", "substr", "formattingTokens", "localFormattingTokens", "formatFunctions", "formatTokenFunctions", "addFormatToken", "token", "padded", "ordinal", "callback", "func", "localeData", "formatMoment", "expandFormat", "array", "match", "replace", "mom", "output", "invalidDate", "replaceLongDateFormatTokens", "longDateFormat", "lastIndex", "test", "match1", "match2", "match3", "match4", "match6", "match1to2", "match3to4", "match5to6", "match1to3", "match1to4", "match1to6", "matchUnsigned", "matchSigned", "matchOffset", "matchShortOffset", "matchWord", "regexes", "addRegexToken", "regex", "strictRegex", "isStrict", "getParseRegexForToken", "RegExp", "regexEscape", "matched", "p1", "p2", "p3", "p4", "s", "tokens", "addParseToken", "addWeekParseToken", "_w", "YEAR", "MONTH", "DATE", "HOUR", "MINUTE", "SECOND", "MILLISECOND", "WEEK", "WEEKDAY", "daysInYear", "year", "isLeapYear", "y", "parseTwoDigitYear", "parseInt", "indexOf", "getSetYear", "makeGetSet", "keepTime", "set$1", "get", "month", "date", "daysInMonth", "x", "mod<PERSON>onth", "o", "monthsShort", "months", "monthsShortRegex", "monthsRegex", "<PERSON><PERSON><PERSON>e", "MONTHS_IN_FORMAT", "defaultLocaleMonths", "split", "defaultLocaleMonthsShort", "setMonth", "dayOfMonth", "getSetMonth", "defaultMonthsShortRegex", "defaultMonthsRegex", "computeMonthsParse", "cmpLenRev", "shortPieces", "long<PERSON><PERSON><PERSON>", "mixedPieces", "sort", "_monthsRegex", "_monthsShortRegex", "_monthsStrictRegex", "_monthsShortStrictRegex", "createDate", "d", "h", "M", "ms", "getFullYear", "setFullYear", "createUTCDate", "UTC", "getUTCFullYear", "setUTCFullYear", "firstWeekOffset", "dow", "doy", "fwd", "getUTCDay", "dayOfYearFromWeeks", "week", "weekday", "resYear", "dayOfYear", "resDayOfYear", "weekOfYear", "resWeek", "weekOffset", "weeksInYear", "weekOffsetNext", "weekdaysMin", "weekdaysShort", "weekdays", "weekdaysMinRegex", "weekdaysShortRegex", "weekdaysRegex", "weekdaysParse", "defaultLocaleWeekdays", "defaultLocaleWeekdaysShort", "defaultLocaleWeekdaysMin", "defaultWeekdaysRegex", "defaultWeekdaysShortRegex", "defaultWeekdaysMinRegex", "computeWeekdaysParse", "minp", "shortp", "longp", "min<PERSON><PERSON>ces", "day", "_weekdaysRegex", "_weekdaysShortRegex", "_weekdaysMinRegex", "_weekdaysStrictRegex", "_weekdaysShortStrictRegex", "_weekdaysMinStrictRegex", "hFormat", "hours", "lowercase", "minutes", "matchMeridiem", "_meridiemParse", "seconds", "kInput", "_isPm", "isPM", "_meridiem", "pos", "pos1", "pos2", "globalLocale", "getSetHour", "baseConfig", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "LTS", "LT", "L", "LL", "LLL", "LLLL", "dayOfMonthOrdinalParse", "relativeTime", "future", "past", "ss", "mm", "hh", "dd", "MM", "yy", "meridiemParse", "locales", "localeFamilies", "normalizeLocale", "loadLocale", "oldLocale", "_abbr", "require", "getSetGlobalLocale", "e", "values", "data", "getLocale", "defineLocale", "abbr", "_config", "parentLocale", "for<PERSON>ach", "chooseLocale", "j", "next", "names", "checkOverflow", "_a", "_overflowDayOfYear", "_overflowWeeks", "_overflowWeekday", "defaults", "c", "config<PERSON><PERSON><PERSON><PERSON><PERSON>", "currentDate", "w", "weekYear", "weekdayOverflow", "curWeek", "nowValue", "now", "_useUTC", "getUTCMonth", "getUTCDate", "getMonth", "getDate", "GG", "W", "E", "createLocal", "_week", "gg", "temp", "_dayOfYear", "yearToUse", "_nextDay", "expectedWeekday", "getDay", "setUTCMinutes", "getUTCMinutes", "extendedIsoRegex", "basicIsoRegex", "tzRegex", "isoDates", "isoTimes", "aspNetJsonRegex", "configFromISO", "l", "allowTime", "dateFormat", "timeFormat", "tzFormat", "string", "exec", "configFromStringAndFormat", "extractFromRFC2822Strings", "yearStr", "monthStr", "dayStr", "hourStr", "minuteStr", "secondStr", "result", "obsOffsets", "UT", "GMT", "EDT", "EST", "CDT", "CST", "MDT", "MST", "PDT", "PST", "configFromRFC2822", "obsOffset", "militaryOffset", "trim", "parsed<PERSON><PERSON><PERSON>", "weekdayStr", "parsedInput", "numOffset", "hm", "ISO_8601", "RFC_2822", "stringLength", "totalParsedInputLength", "skipped", "hour", "meridiemHour", "isPm", "prepareConfig", "preparse", "configFromStringAndArray", "tempConfig", "bestMoment", "scoreToBeat", "currentScore", "score", "createFromInputFallback", "minute", "second", "millisecond", "isUTC", "getOwnPropertyNames", "k", "add", "prototypeMin", "other", "prototypeMax", "pickBy", "moments", "ordering", "Duration", "duration", "years", "quarters", "quarter", "weeks", "days", "milliseconds", "unitHasDecimal", "parseFloat", "_milliseconds", "_days", "_months", "_data", "_bubble", "isDuration", "absRound", "round", "offset", "separator", "utcOffset", "sign", "offsetFromString", "chunkOffset", "matcher", "matches", "parts", "cloneWithOffset", "model", "diff", "clone", "setTime", "local", "getDateOffset", "getTimezoneOffset", "isUtc", "aspNetRegex", "isoRegex", "createDuration", "ret", "parseIso", "diffRes", "base", "isBefore", "positiveMomentsDifference", "inp", "isAfter", "createAdder", "direction", "period", "tmp", "addSubtract", "isAdding", "invalid", "subtract", "monthDiff", "wholeMonthDiff", "anchor", "adjust", "newLocaleData", "defaultFormat", "defaultFormatUtc", "lang", "addWeekYearFormatToken", "getter", "getSetWeekYearHelper", "<PERSON><PERSON><PERSON><PERSON>", "dayOfYearData", "isoWeekYear", "_dayOfMonthOrdinalParse", "_ordinalParse", "_dayOfMonthOrdinalParseLenient", "getSetDayOfMonth", "getSetMinute", "getSetSecond", "parseMs", "getSetMillisecond", "proto", "preParsePostFormat", "time", "formats", "sod", "startOf", "calendarFormat", "asFloat", "that", "zoneDelta", "endOf", "inputString", "postformat", "withoutSuffix", "humanize", "fromNow", "toNow", "invalidAt", "localInput", "isBetween", "inclusivity", "isSame", "inputMs", "isSameOrAfter", "isSameOrBefore", "parsingFlags", "prioritized", "unitsObj", "u", "isoWeekday", "toArray", "toObject", "toDate", "toISOString", "keepOffset", "inspect", "zone", "prefix", "isLocal", "toJSON", "unix", "creationData", "isoWeek", "isoWeeks", "weekInfo", "isoWeeksInYear", "keepLocalTime", "keepMinutes", "localAdjust", "_changeInProgress", "parseZone", "tZone", "hasAlignedHourOffset", "isDST", "isUtcOffset", "zoneAbbr", "zoneName", "dates", "isDSTShifted", "_isDSTShifted", "proto$1", "get$1", "index", "field", "setter", "listMonthsImpl", "out", "listWeekdaysImpl", "localeSorted", "shift", "_calendar", "_longDateFormat", "formatUpper", "toUpperCase", "_invalidDate", "_ordinal", "isFuture", "_relativeTime", "pastFuture", "source", "isFormat", "_monthsShort", "monthName", "_monthsParseExact", "ii", "llc", "toLocaleLowerCase", "_monthsParse", "_longMonthsParse", "_shortMonthsParse", "firstDayOfYear", "firstDayOfWeek", "_weekdays", "_weekdaysMin", "_weekdaysShort", "weekdayName", "_weekdaysParseExact", "_weekdaysParse", "_shortWeekdaysParse", "_minWeekdaysParse", "_fullWeekdaysParse", "char<PERSON>t", "isLower", "langData", "mathAbs", "addSubtract$1", "absCeil", "daysToMonths", "monthsToDays", "makeAs", "alias", "as", "asMilliseconds", "asSeconds", "asMinutes", "asHours", "asDays", "asWeeks", "asMonths", "as<PERSON><PERSON>s", "makeGetter", "thresholds", "relativeTime$1", "posNegDuration", "abs$1", "toISOString$1", "Y", "D", "total", "ymSign", "daysSign", "hmsSign", "toFixed", "proto$2", "monthsFromDays", "withSuffix", "toIsoString", "version", "updateLocale", "tmpLocale", "relativeTimeRounding", "roundingFunction", "relativeTimeThreshold", "threshold", "limit", "myMoment", "HTML5_FMT", "DATETIME_LOCAL", "DATETIME_LOCAL_SECONDS", "DATETIME_LOCAL_MS", "TIME", "TIME_SECONDS", "TIME_MS"], "mappings": "AAMA,CAAA,SAAWA,EAAQC,GACC,UAAnB,OAAOC,SAA0C,aAAlB,OAAOC,OAA0BA,OAAOD,QAAUD,EAAQ,EAAuB,YAAlB,OAAOG,QAAyBA,OAAOC,IAAMD,OAAOH,CAAO,EAAKD,EAAOM,OAASL,EAAQ,CACtL,EAAEM,KAAM,WACR,aAEA,IAAIC,EAEJ,SAASC,IACR,OAAOD,EAAaE,MAAM,KAAMC,SAAS,CAC1C,CAQA,SAASC,EAAQC,GAChB,OAAOA,aAAiBC,OAAmD,mBAA1CC,OAAOC,UAAUC,SAASC,KAAKL,CAAK,CACtE,CAEA,SAASM,EAASN,GAGjB,OAAgB,MAATA,GAA2D,oBAA1CE,OAAOC,UAAUC,SAASC,KAAKL,CAAK,CAC7D,CAgBA,SAASO,EAAYP,GACpB,OAAiB,KAAA,IAAVA,CACR,CAEA,SAASQ,EAASR,GACjB,MAAwB,UAAjB,OAAOA,GAAgE,oBAA1CE,OAAOC,UAAUC,SAASC,KAAKL,CAAK,CACzE,CAEA,SAASS,EAAOT,GACf,OAAOA,aAAiBU,MAAkD,kBAA1CR,OAAOC,UAAUC,SAASC,KAAKL,CAAK,CACrE,CAEA,SAASW,EAAIC,EAAKC,GAGjB,IAFA,IAAIC,EAAM,GAELC,EAAI,EAAGA,EAAIH,EAAII,OAAQ,EAAED,EAC7BD,EAAIG,KAAKJ,EAAGD,EAAIG,GAAIA,CAAC,CAAC,EAEvB,OAAOD,CACR,CAEA,SAASI,EAAWC,EAAGC,GACtB,OAAOlB,OAAOC,UAAUkB,eAAehB,KAAKc,EAAGC,CAAC,CACjD,CAEA,SAASE,EAAOH,EAAGC,GAClB,IAAK,IAAIL,KAAKK,EACTF,EAAWE,EAAGL,CAAC,IAClBI,EAAEJ,GAAKK,EAAEL,IAYX,OARIG,EAAWE,EAAG,UAAU,IAC3BD,EAAEf,SAAWgB,EAAEhB,UAGZc,EAAWE,EAAG,SAAS,IAC1BD,EAAEI,QAAUH,EAAEG,SAGRJ,CACR,CAEA,SAASK,EAAUxB,EAAOyB,EAAQC,EAAQC,GACzC,OAAOC,GAAiB5B,EAAOyB,EAAQC,EAAQC,EAAQ,CAAA,CAAI,EAAEE,IAAI,CAClE,CAsBA,SAASC,EAAgBC,GAIxB,OAHa,MAATA,EAAEC,MACLD,EAAEC,IApBI,CACNC,MAAO,CAAA,EACPC,aAAc,GACdC,YAAa,GACbC,SAAU,CAAC,EACXC,cAAe,EACfC,UAAW,CAAA,EACXC,aAAc,KACdC,cAAe,CAAA,EACfC,gBAAiB,CAAA,EACjBC,IAAK,CAAA,EACLC,gBAAiB,GACjBC,SAAU,KACVC,QAAS,CAAA,EACTC,gBAAiB,CAAA,CAClB,GAOOf,EAAEC,GACV,CAoBA,SAASe,EAAQhB,GAChB,GAAkB,MAAdA,EAAEiB,SAAkB,CACvB,IAAIC,EAAQnB,EAAgBC,CAAC,EACzBmB,EAAcC,EAAK9C,KAAK4C,EAAMN,gBAAiB,SAAU5B,GAC5D,OAAY,MAALA,CACR,CAAC,EACGqC,EAAa,CAACC,MAAMtB,EAAEuB,GAAGC,QAAQ,CAAC,GAAKN,EAAMb,SAAW,GAAK,CAACa,EAAMhB,OAAS,CAACgB,EAAMV,cAAgB,CAACU,EAAMO,gBAAkB,CAACP,EAAMH,iBAAmB,CAACG,EAAMX,WAAa,CAACW,EAAMT,eAAiB,CAACS,EAAMR,kBAAoB,CAACQ,EAAML,UAAaK,EAAML,UAAYM,GAMxQ,GAJInB,EAAE0B,UACLL,EAAaA,GAAsC,IAAxBH,EAAMZ,eAAqD,IAA9BY,EAAMf,aAAalB,QAAkC0C,KAAAA,IAAlBT,EAAMU,SAG3E,MAAnBzD,OAAO0D,UAAqB1D,OAAO0D,SAAS7B,CAAC,EAGhD,OAAOqB,EAFPrB,EAAEiB,SAAWI,CAIf,CACA,OAAOrB,EAAEiB,QACV,CAEA,SAASa,EAAcZ,GACtB,IAAIlB,EAAIP,EAAUsC,GAAG,EAOrB,OANa,MAATb,EACH3B,EAAOQ,EAAgBC,CAAC,EAAGkB,CAAK,EAEhCnB,EAAgBC,CAAC,EAAEU,gBAAkB,CAAA,EAG/BV,CACR,CAIA,IAlDCoB,EADGlD,MAAME,UAAUgD,MAGZ,SAAUY,GAIhB,IAHA,IAAIC,EAAI9D,OAAOR,IAAI,EACfuE,EAAMD,EAAEhD,SAAW,EAEdD,EAAI,EAAGA,EAAIkD,EAAKlD,CAAC,GACzB,GAAIA,KAAKiD,GAAKD,EAAI1D,KAAKX,KAAMsE,EAAEjD,GAAIA,EAAGiD,CAAC,EACtC,MAAO,CAAA,EAIT,MAAO,CAAA,CACR,EAqCGE,EAAoBtE,EAAMsE,iBAAmB,GAEjD,SAASC,EAAWC,EAAIC,GACvB,IAAItD,EAAGuD,EAAMC,EAiCb,GA/BKhE,EAAY8D,EAAKG,gBAAgB,IACrCJ,EAAGI,iBAAmBH,EAAKG,kBAEvBjE,EAAY8D,EAAKI,EAAE,IACvBL,EAAGK,GAAKJ,EAAKI,IAETlE,EAAY8D,EAAKK,EAAE,IACvBN,EAAGM,GAAKL,EAAKK,IAETnE,EAAY8D,EAAKM,EAAE,IACvBP,EAAGO,GAAKN,EAAKM,IAETpE,EAAY8D,EAAKZ,OAAO,IAC5BW,EAAGX,QAAUY,EAAKZ,SAEdlD,EAAY8D,EAAKO,IAAI,IACzBR,EAAGQ,KAAOP,EAAKO,MAEXrE,EAAY8D,EAAKQ,MAAM,IAC3BT,EAAGS,OAASR,EAAKQ,QAEbtE,EAAY8D,EAAKS,OAAO,IAC5BV,EAAGU,QAAUT,EAAKS,SAEdvE,EAAY8D,EAAKrC,GAAG,IACxBoC,EAAGpC,IAAMF,EAAgBuC,CAAI,GAEzB9D,EAAY8D,EAAKU,OAAO,IAC5BX,EAAGW,QAAUV,EAAKU,SAGW,EAA1Bb,EAAiBlD,OACpB,IAAKD,EAAI,EAAGA,EAAImD,EAAiBlD,OAAQD,CAAC,GAGpCR,EADLgE,EAAMF,EADNC,EAAOJ,EAAiBnD,GAEJ,IACnBqD,EAAGE,GAAQC,GAKd,OAAOH,CACR,CAEA,IAAIY,EAAmB,CAAA,EAGvB,SAASC,EAAOC,GACff,EAAWzE,KAAMwF,CAAM,EACvBxF,KAAK4D,GAAK,IAAI5C,KAAkB,MAAbwE,EAAO5B,GAAa4B,EAAO5B,GAAGC,QAAQ,EAAIO,GAAG,EAC3DpE,KAAKqD,QAAQ,IACjBrD,KAAK4D,GAAK,IAAI5C,KAAKoD,GAAG,GAIE,CAAA,IAArBkB,IACHA,EAAmB,CAAA,EACnBpF,EAAMuF,aAAazF,IAAI,EACvBsF,EAAmB,CAAA,EAErB,CAEA,SAASI,EAASC,GACjB,OAAOA,aAAeJ,GAAkB,MAAPI,GAAuC,MAAxBA,EAAIb,gBACrD,CAEA,SAASc,EAASC,GACjB,OAAIA,EAAS,EAELC,KAAKC,KAAKF,CAAM,GAAK,EAErBC,KAAKE,MAAMH,CAAM,CAE1B,CAEA,SAASI,EAAMC,GACd,IAAIC,EAAgB,CAACD,EACpBE,EAAQ,EAMT,OAHCA,EADqB,GAAlBD,GAAuBE,SAASF,CAAa,EACxCP,EAASO,CAAa,EAGxBC,CACR,CAGA,SAASE,EAAcC,EAAQC,EAAQC,GAKtC,IAJA,IAAIlC,EAAMuB,KAAKY,IAAIH,EAAOjF,OAAQkF,EAAOlF,MAAM,EAC9CqF,EAAab,KAAKc,IAAIL,EAAOjF,OAASkF,EAAOlF,MAAM,EACnDuF,EAAQ,EAEJxF,EAAI,EAAGA,EAAIkD,EAAKlD,CAAC,IAChBoF,GAAeF,EAAOlF,KAAOmF,EAAOnF,IAAQ,CAACoF,GAAeR,EAAMM,EAAOlF,EAAE,IAAM4E,EAAMO,EAAOnF,EAAE,IACpGwF,CAAK,GAGP,OAAOA,EAAQF,CAChB,CAEA,SAASG,EAAKC,GAC6B,CAAA,IAAtC7G,EAAM8G,6BAA4D,aAAnB,OAAOC,SAA2BA,QAAQH,MAC5FG,QAAQH,KAAK,wBAA0BC,CAAG,CAE5C,CAEA,SAASG,EAAUH,EAAK5F,GACvB,IAAIgG,EAAY,CAAA,EAEhB,OAAOvF,EAAO,WAIb,GAHgC,MAA5B1B,EAAMkH,oBACTlH,EAAMkH,mBAAmB,KAAML,CAAG,EAE/BI,EAAW,CAGd,IAFA,IACIE,EADAC,EAAO,GAEFjG,EAAI,EAAGA,EAAIjB,UAAUkB,OAAQD,CAAC,GAAI,CAE1C,GADAgG,EAAM,GACsB,UAAxB,OAAOjH,UAAUiB,GAAiB,CAErC,IAAK,IAAIkG,KADTF,GAAO,MAAQhG,EAAI,KACHjB,UAAU,GACzBiH,GAAOE,EAAM,KAAOnH,UAAU,GAAGmH,GAAO,KAEzCF,EAAMA,EAAIG,MAAM,EAAG,CAAC,CAAC,CACtB,MACCH,EAAMjH,UAAUiB,GAEjBiG,EAAK/F,KAAK8F,CAAG,CACd,CACAP,EAAKC,EAAM,gBAAkBxG,MAAME,UAAU+G,MAAM7G,KAAK2G,CAAI,EAAEG,KAAK,EAAE,EAAI,MAAO,IAAIC,OAAQC,KAAK,EACjGR,EAAY,CAAA,CACb,CACA,OAAOhG,EAAGhB,MAAMH,KAAMI,SAAS,CAChC,EAAGe,CAAE,CACN,CAEA,IAAIyG,EAAe,GAEnB,SAASC,EAAgBC,EAAMf,GACE,MAA5B7G,EAAMkH,oBACTlH,EAAMkH,mBAAmBU,EAAMf,CAAG,EAE9Ba,EAAaE,KACjBhB,EAAKC,CAAG,EACRa,EAAaE,GAAQ,CAAA,EAEvB,CAKA,SAASC,EAAWzH,GACnB,OAAOA,aAAiB0H,UAAsD,sBAA1CxH,OAAOC,UAAUC,SAASC,KAAKL,CAAK,CACzE,CAmBA,SAAS2H,EAAaC,EAAcC,GACnC,IACCvD,EADGxD,EAAMQ,EAAO,GAAIsG,CAAY,EAEjC,IAAKtD,KAAQuD,EACR3G,EAAW2G,EAAavD,CAAI,IAC3BhE,EAASsH,EAAatD,EAAK,GAAKhE,EAASuH,EAAYvD,EAAK,GAC7DxD,EAAIwD,GAAQ,GACZhD,EAAOR,EAAIwD,GAAOsD,EAAatD,EAAK,EACpChD,EAAOR,EAAIwD,GAAOuD,EAAYvD,EAAK,GACJ,MAArBuD,EAAYvD,GACtBxD,EAAIwD,GAAQuD,EAAYvD,GAExB,OAAOxD,EAAIwD,IAId,IAAKA,KAAQsD,EACR1G,EAAW0G,EAActD,CAAI,GAAK,CAACpD,EAAW2G,EAAavD,CAAI,GAAKhE,EAASsH,EAAatD,EAAK,IAElGxD,EAAIwD,GAAQhD,EAAO,GAAIR,EAAIwD,EAAK,GAGlC,OAAOxD,CACR,CAEA,SAASgH,EAAO5C,GACD,MAAVA,GACHxF,KAAKqI,IAAI7C,CAAM,CAEjB,CArDAtF,EAAM8G,4BAA8B,CAAA,EACpC9G,EAAMkH,mBAAqB,KAuE3B,IAdCkB,GADG9H,OAAO8H,MAGH,SAAU3C,GAChB,IAAItE,EACHD,EAAM,GACP,IAAKC,KAAKsE,EACLnE,EAAWmE,EAAKtE,CAAC,GACpBD,EAAIG,KAAKF,CAAC,EAGZ,OAAOD,CACR,EAiFD,IAAImH,GAAU,GAEd,SAASC,EAAaC,EAAMC,GAC3B,IAAIC,EAAYF,EAAKG,YAAY,EACjCL,GAAQI,GAAaJ,GAAQI,EAAY,KAAOJ,GAAQG,GAAaD,CACtE,CAEA,SAASI,EAAeC,GACvB,MAAwB,UAAjB,OAAOA,EAAqBP,GAAQO,IAAUP,GAAQO,EAAMF,YAAY,GAAK5E,KAAAA,CACrF,CAEA,SAAS+E,GAAqBC,GAC7B,IACCC,EACArE,EAFGsE,EAAkB,GAItB,IAAKtE,KAAQoE,EACRxH,EAAWwH,EAAapE,CAAI,IAC/BqE,EAAiBJ,EAAejE,CAAI,KAEnCsE,EAAgBD,GAAkBD,EAAYpE,IAKjD,OAAOsE,CACR,CAEA,IAAIC,GAAa,GAEjB,SAASC,EAAgBX,EAAMY,GAC9BF,GAAWV,GAAQY,CACpB,CAaA,SAASC,EAASzD,EAAQ0D,EAAcC,GACvC,IAAIC,EAAY,GAAK3D,KAAKc,IAAIf,CAAM,EAGpC,OADkB,GAAVA,EACQ2D,EAAY,IAAM,GAAM,KAAO1D,KAAK4D,IAAI,GAAI5D,KAAK6D,IAAI,EAFtDJ,EAAeE,EAAUnI,MAE2C,CAAC,EAAEZ,SAAS,EAAEkJ,OAAO,CAAC,EAAIH,CAC9G,CAEA,IAAII,GAAmB,uLAEnBC,GAAwB,6CAExBC,GAAkB,GAElBC,GAAuB,GAM3B,SAASC,EAAeC,EAAOC,EAAQC,EAASC,GAC/C,IAAIC,EACoB,UAApB,OAAOD,EACH,WACN,OAAOrK,KAAKqK,GAAU,CACvB,EAJUA,EAMPH,IACHF,GAAqBE,GAASI,GAE3BH,IACHH,GAAqBG,EAAO,IAAM,WACjC,OAAOb,EAASgB,EAAKnK,MAAMH,KAAMI,SAAS,EAAG+J,EAAO,GAAIA,EAAO,EAAE,CAClE,GAEGC,IACHJ,GAAqBI,GAAW,WAC/B,OAAOpK,KAAKuK,WAAW,EAAEH,QAAQE,EAAKnK,MAAMH,KAAMI,SAAS,EAAG8J,CAAK,CACpE,EAEF,CAiCA,SAASM,GAAanI,EAAGN,GACxB,OAAKM,EAAEgB,QAAQ,GAIftB,EAAS0I,GAAa1I,EAAQM,EAAEkI,WAAW,CAAC,EAC5CR,GAAgBhI,GAAUgI,GAAgBhI,IA9B3C,SAA4BA,GAK3B,IAJA,IAR+BzB,EAQ3BoK,EAAQ3I,EAAO4I,MAAMd,EAAgB,EAIpCxI,EAAI,EAAGC,EAASoJ,EAAMpJ,OAAQD,EAAIC,EAAQD,CAAC,GAC3C2I,GAAqBU,EAAMrJ,IAC9BqJ,EAAMrJ,GAAK2I,GAAqBU,EAAMrJ,IAEtCqJ,EAAMrJ,IAhBuBf,EAgBKoK,EAAMrJ,IAfhCsJ,MAAM,UAAU,EAClBrK,EAAMsK,QAAQ,WAAY,EAAE,EAE7BtK,EAAMsK,QAAQ,MAAO,EAAE,EAgB9B,OAAO,SAAUC,GAGhB,IAFA,IAAIC,EAAS,GAERzJ,EAAI,EAAGA,EAAIC,EAAQD,CAAC,GACxByJ,GAAU/C,EAAW2C,EAAMrJ,EAAE,EAAIqJ,EAAMrJ,GAAGV,KAAKkK,EAAK9I,CAAM,EAAI2I,EAAMrJ,GAErE,OAAOyJ,CACR,CACD,EASyE/I,CAAM,EAEvEgI,GAAgBhI,GAAQM,CAAC,GANxBA,EAAEkI,WAAW,EAAEQ,YAAY,CAOpC,CAEA,SAASN,GAAa1I,EAAQC,GAC7B,IAAIX,EAAI,EAER,SAAS2J,EAA4B1K,GACpC,OAAO0B,EAAOiJ,eAAe3K,CAAK,GAAKA,CACxC,CAGA,IADAwJ,GAAsBoB,UAAY,EACtB,GAAL7J,GAAUyI,GAAsBqB,KAAKpJ,CAAM,GACjDA,EAASA,EAAO6I,QAAQd,GAAuBkB,CAA2B,EAC1ElB,GAAsBoB,UAAY,EAClC7J,EAAAA,EAGD,OAAOU,CACR,CAEA,IAAIqJ,EAAS,KACTC,EAAS,OACTC,GAAS,QACTC,GAAS,QACTC,GAAS,aACTC,EAAY,QACZC,GAAY,YACZC,GAAY,gBACZC,GAAY,UACZC,GAAY,UACZC,GAAY,eAEZC,GAAgB,MAChBC,GAAc,WAEdC,GAAc,qBACdC,GAAmB,0BAMnBC,EAAY,wJAEZC,GAAU,GAEd,SAASC,EAAcnC,EAAOoC,EAAOC,GACpCH,GAAQlC,GAASnC,EAAWuE,CAAK,EAC9BA,EACA,SAAUE,EAAUjC,GACpB,OAAOiC,GAAYD,EAAcA,EAAcD,CAC/C,CACJ,CAEA,SAASG,GAAsBvC,EAAO1E,GACrC,OAAKhE,EAAW4K,GAASlC,CAAK,EAIvBkC,GAAQlC,GAAO1E,EAAOzB,QAASyB,EAAOH,OAAO,EAH5C,IAAIqH,OAQLC,GAR2BzC,EAS/BU,QAAQ,KAAM,EAAE,EAAEA,QAAQ,sCAAuC,SAAUgC,EAASC,EAAIC,EAAIC,EAAIC,GACjG,OAAOH,GAAMC,GAAMC,GAAMC,CAC1B,CAAC,CACF,CAZwC,CAIzC,CAWA,SAASL,GAAYM,GACpB,OAAOA,EAAErC,QAAQ,yBAA0B,MAAM,CAClD,CAEA,IAAIsC,GAAS,GAEb,SAASC,EAAcjD,EAAOG,GAC7B,IAAIhJ,EACHiJ,EAAOD,EASR,IARqB,UAAjB,OAAOH,IACVA,EAAQ,CAACA,IAENpJ,EAASuJ,CAAQ,IACpBC,EAAO,SAAUhK,EAAOoK,GACvBA,EAAML,GAAYpE,EAAM3F,CAAK,CAC9B,GAEIe,EAAI,EAAGA,EAAI6I,EAAM5I,OAAQD,CAAC,GAC9B6L,GAAOhD,EAAM7I,IAAMiJ,CAErB,CAEA,SAAS8C,GAAkBlD,EAAOG,GACjC8C,EAAcjD,EAAO,SAAU5J,EAAOoK,EAAOlF,EAAQ0E,GACpD1E,EAAO6H,GAAK7H,EAAO6H,IAAM,GACzBhD,EAAS/J,EAAOkF,EAAO6H,GAAI7H,EAAQ0E,CAAK,CACzC,CAAC,CACF,CAQA,IAAIoD,EAAO,EACPC,EAAQ,EACRC,EAAO,EACPC,EAAO,EACPC,EAAS,EACTC,EAAS,EACTC,GAAc,EACdC,GAAO,EACPC,GAAU,EA8Cd,SAASC,GAAWC,GACnB,OAAOC,GAAWD,CAAI,EAAI,IAAM,GACjC,CAEA,SAASC,GAAWD,GACnB,OAAQA,EAAO,GAAM,GAAKA,EAAO,KAAQ,GAAMA,EAAO,KAAQ,CAC/D,CAhDA/D,EAAe,IAAK,EAAG,EAAG,WACzB,IAAIiE,EAAIlO,KAAKgO,KAAK,EAClB,OAAOE,GAAK,KAAO,GAAKA,EAAI,IAAMA,CACnC,CAAC,EAEDjE,EAAe,EAAG,CAAC,KAAM,GAAI,EAAG,WAC/B,OAAOjK,KAAKgO,KAAK,EAAI,GACtB,CAAC,EAED/D,EAAe,EAAG,CAAC,OAAQ,GAAI,EAAG,MAAM,EACxCA,EAAe,EAAG,CAAC,QAAS,GAAI,EAAG,MAAM,EACzCA,EAAe,EAAG,CAAC,SAAU,EAAG,CAAA,GAAO,EAAG,MAAM,EAIhDzB,EAAa,OAAQ,GAAG,EAIxBY,EAAgB,OAAQ,CAAC,EAIzBiD,EAAc,IAAKL,EAAW,EAC9BK,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,OAAQR,GAAWN,EAAM,EACvCc,EAAc,QAASP,GAAWN,EAAM,EACxCa,EAAc,SAAUP,GAAWN,EAAM,EAEzC2B,EAAc,CAAC,QAAS,UAAWG,CAAI,EACvCH,EAAc,OAAQ,SAAU7M,EAAOoK,GACtCA,EAAM4C,GAAyB,IAAjBhN,EAAMgB,OAAepB,EAAMiO,kBAAkB7N,CAAK,EAAI2F,EAAM3F,CAAK,CAChF,CAAC,EACD6M,EAAc,KAAM,SAAU7M,EAAOoK,GACpCA,EAAM4C,GAAQpN,EAAMiO,kBAAkB7N,CAAK,CAC5C,CAAC,EACD6M,EAAc,IAAK,SAAU7M,EAAOoK,GACnCA,EAAM4C,GAAQc,SAAS9N,EAAO,EAAE,CACjC,CAAC,EAcDJ,EAAMiO,kBAAoB,SAAU7N,GACnC,OAAO2F,EAAM3F,CAAK,GAAoB,GAAf2F,EAAM3F,CAAK,EAAS,KAAO,IACnD,EAIA,IA8DI+N,EA9DAC,GAAaC,GAAW,WAAY,CAAA,CAAI,EAM5C,SAASA,GAAW9F,EAAM+F,GACzB,OAAO,SAAUpI,GAChB,OAAa,MAATA,GACHqI,GAAMzO,KAAMyI,EAAMrC,CAAK,EACvBlG,EAAMuF,aAAazF,KAAMwO,CAAQ,EAC1BxO,MAEA0O,GAAI1O,KAAMyI,CAAI,CAEvB,CACD,CAEA,SAASiG,GAAI7D,EAAKpC,GACjB,OAAOoC,EAAIxH,QAAQ,EAAIwH,EAAIjH,GAAG,OAASiH,EAAI1F,OAAS,MAAQ,IAAMsD,GAAM,EAAIrE,GAC7E,CAEA,SAASqK,GAAM5D,EAAKpC,EAAMrC,GACrByE,EAAIxH,QAAQ,GAAK,CAACM,MAAMyC,CAAK,IACnB,aAATqC,GAAuBwF,GAAWpD,EAAImD,KAAK,CAAC,GAAqB,IAAhBnD,EAAI8D,MAAM,GAA0B,KAAf9D,EAAI+D,KAAK,EAClF/D,EAAIjH,GAAG,OAASiH,EAAI1F,OAAS,MAAQ,IAAMsD,GAAMrC,EAAOyE,EAAI8D,MAAM,EAAGE,GAAYzI,EAAOyE,EAAI8D,MAAM,CAAC,CAAC,EAEpG9D,EAAIjH,GAAG,OAASiH,EAAI1F,OAAS,MAAQ,IAAMsD,GAAMrC,CAAK,EAGzD,CAiDA,SAASyI,GAAYb,EAAMW,GAC1B,IAtBeG,EAsBf,OAAInL,MAAMqK,CAAI,GAAKrK,MAAMgL,CAAK,EACtBvK,KAEJ2K,GAAeJ,GAzBJG,EAyBW,IAxBRA,GAAKA,EAyBvBd,IAASW,EAAQI,GAAY,GACT,GAAbA,EAAkBd,GAAWD,CAAI,EAAI,GAAK,GAAM,GAAOe,EAAW,EAAK,EAC/E,CArBCV,EADG9N,MAAME,UAAU4N,SAGT,SAAUW,GAGnB,IADA,IACK3N,EAAI,EAAGA,EAAIrB,KAAKsB,OAAQ,EAAED,EAC9B,GAAIrB,KAAKqB,KAAO2N,EACf,OAAO3N,EAGT,MAAO,CAAC,CACT,EAcD4I,EAAe,IAAK,CAAC,KAAM,GAAI,KAAM,WACpC,OAAOjK,KAAK2O,MAAM,EAAI,CACvB,CAAC,EAED1E,EAAe,MAAO,EAAG,EAAG,SAAUlI,GACrC,OAAO/B,KAAKuK,WAAW,EAAE0E,YAAYjP,KAAM+B,CAAM,CAClD,CAAC,EAEDkI,EAAe,OAAQ,EAAG,EAAG,SAAUlI,GACtC,OAAO/B,KAAKuK,WAAW,EAAE2E,OAAOlP,KAAM+B,CAAM,CAC7C,CAAC,EAIDyG,EAAa,QAAS,GAAG,EAIzBY,EAAgB,QAAS,CAAC,EAI1BiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,MAAO,SAAUG,EAAUxK,GACxC,OAAOA,EAAOmN,iBAAiB3C,CAAQ,CACxC,CAAC,EACDH,EAAc,OAAQ,SAAUG,EAAUxK,GACzC,OAAOA,EAAOoN,YAAY5C,CAAQ,CACnC,CAAC,EAEDW,EAAc,CAAC,IAAK,MAAO,SAAU7M,EAAOoK,GAC3CA,EAAM6C,GAAStH,EAAM3F,CAAK,EAAI,CAC/B,CAAC,EAED6M,EAAc,CAAC,MAAO,QAAS,SAAU7M,EAAOoK,EAAOlF,EAAQ0E,GAC1DyE,EAAQnJ,EAAOH,QAAQgK,YAAY/O,EAAO4J,EAAO1E,EAAOzB,OAAO,EAEtD,MAAT4K,EACHjE,EAAM6C,GAASoB,EAEfvM,EAAgBoD,CAAM,EAAE3C,aAAevC,CAEzC,CAAC,EAID,IAAIgP,GAAmB,gCACnBC,GAAsB,wFAAwFC,MAAM,GAAG,EAQ3H,IAAIC,GAA2B,kDAAkDD,MAAM,GAAG,EA4F1F,SAASE,GAAS7E,EAAKzE,GACtB,IAAIuJ,EAEJ,GAAK9E,EAAIxH,QAAQ,EAAjB,CAKA,GAAqB,UAAjB,OAAO+C,EACV,GAAI,QAAQ+E,KAAK/E,CAAK,EACrBA,EAAQH,EAAMG,CAAK,OAInB,GAAI,CAACtF,EAFLsF,EAAQyE,EAAIN,WAAW,EAAE8E,YAAYjJ,CAAK,CAEvB,EAClB,OAKHuJ,EAAa7J,KAAKY,IAAImE,EAAI+D,KAAK,EAAGC,GAAYhE,EAAImD,KAAK,EAAG5H,CAAK,CAAC,EAChEyE,EAAIjH,GAAG,OAASiH,EAAI1F,OAAS,MAAQ,IAAM,SAASiB,EAAOuJ,CAAU,CAfrE,CAiBD,CAEA,SAASC,GAAYxJ,GACpB,OAAa,MAATA,GACHsJ,GAAS1P,KAAMoG,CAAK,EACpBlG,EAAMuF,aAAazF,KAAM,CAAA,CAAI,EACtBA,MAEA0O,GAAI1O,KAAM,OAAO,CAE1B,CAMA,IAAI6P,GAA0B1D,EAmB9B,IAAI2D,GAAqB3D,EAmBzB,SAAS4D,KACR,SAASC,EAAUvO,EAAGC,GACrB,OAAOA,EAAEJ,OAASG,EAAEH,MACrB,CAOA,IALA,IAICuJ,EAJGoF,EAAc,GACjBC,EAAa,GACbC,EAAc,GAGV9O,EAAI,EAAGA,EAAI,GAAIA,CAAC,GAEpBwJ,EAAM/I,EAAU,CAAC,IAAMT,EAAE,EACzB4O,EAAY1O,KAAKvB,KAAKiP,YAAYpE,EAAK,EAAE,CAAC,EAC1CqF,EAAW3O,KAAKvB,KAAKkP,OAAOrE,EAAK,EAAE,CAAC,EACpCsF,EAAY5O,KAAKvB,KAAKkP,OAAOrE,EAAK,EAAE,CAAC,EACrCsF,EAAY5O,KAAKvB,KAAKiP,YAAYpE,EAAK,EAAE,CAAC,EAO3C,IAHAoF,EAAYG,KAAKJ,CAAS,EAC1BE,EAAWE,KAAKJ,CAAS,EACzBG,EAAYC,KAAKJ,CAAS,EACrB3O,EAAI,EAAGA,EAAI,GAAIA,CAAC,GACpB4O,EAAY5O,GAAKsL,GAAYsD,EAAY5O,EAAE,EAC3C6O,EAAW7O,GAAKsL,GAAYuD,EAAW7O,EAAE,EAE1C,IAAKA,EAAI,EAAGA,EAAI,GAAIA,CAAC,GACpB8O,EAAY9O,GAAKsL,GAAYwD,EAAY9O,EAAE,EAG5CrB,KAAKqQ,aAAe,IAAI3D,OAAO,KAAOyD,EAAY1I,KAAK,GAAG,EAAI,IAAK,GAAG,EACtEzH,KAAKsQ,kBAAoBtQ,KAAKqQ,aAC9BrQ,KAAKuQ,mBAAqB,IAAI7D,OAAO,KAAOwD,EAAWzI,KAAK,GAAG,EAAI,IAAK,GAAG,EAC3EzH,KAAKwQ,wBAA0B,IAAI9D,OAAO,KAAOuD,EAAYxI,KAAK,GAAG,EAAI,IAAK,GAAG,CAClF,CAEA,SAASgJ,GAAWvC,EAAG7L,EAAGqO,EAAGC,EAAGC,EAAG3D,EAAG4D,GAGjCjC,EAAO,IAAI5N,KAAKkN,EAAG7L,EAAGqO,EAAGC,EAAGC,EAAG3D,EAAG4D,CAAE,EAMxC,OAHI3C,EAAI,KAAY,GAALA,GAAU7H,SAASuI,EAAKkC,YAAY,CAAC,GACnDlC,EAAKmC,YAAY7C,CAAC,EAEZU,CACR,CAEA,SAASoC,GAAc9C,GACtB,IAAIU,EAAO,IAAI5N,KAAKA,KAAKiQ,IAAI9Q,MAAM,KAAMC,SAAS,CAAC,EAMnD,OAHI8N,EAAI,KAAY,GAALA,GAAU7H,SAASuI,EAAKsC,eAAe,CAAC,GACtDtC,EAAKuC,eAAejD,CAAC,EAEfU,CACR,CAGA,SAASwC,GAAgBpD,EAAMqD,EAAKC,GAElCC,EAAM,EAAIF,EAAMC,EAIjB,OAAgBC,GAFN,EAAIP,GAAchD,EAAM,EAAGuD,CAAG,EAAEC,UAAU,EAAIH,GAAO,EAEzC,CACvB,CAGA,SAASI,GAAmBzD,EAAM0D,EAAMC,EAASN,EAAKC,GACrD,IAGCM,EADAC,EAAY,EAAI,GAAKH,EAAO,IAFT,EAAIC,EAAUN,GAAO,EAC3BD,GAAgBpD,EAAMqD,EAAKC,CAAG,EAO3CQ,EAFGD,GAAa,EAED9D,GADf6D,EAAU5D,EAAO,CACgB,EAAI6D,EAC3BA,EAAY9D,GAAWC,CAAI,GACrC4D,EAAU5D,EAAO,EACF6D,EAAY9D,GAAWC,CAAI,IAE1C4D,EAAU5D,EACK6D,GAGhB,MAAO,CACN7D,KAAM4D,EACNC,UAAWC,CACZ,CACD,CAEA,SAASC,GAAWlH,EAAKwG,EAAKC,GAC7B,IAECU,EACAJ,EAHGK,EAAab,GAAgBvG,EAAImD,KAAK,EAAGqD,EAAKC,CAAG,EACpDI,EAAO5L,KAAKE,OAAO6E,EAAIgH,UAAU,EAAII,EAAa,GAAK,CAAC,EAAI,EAe7D,OAXIP,EAAO,EAEVM,EAAUN,EAAOQ,GADjBN,EAAU/G,EAAImD,KAAK,EAAI,EACeqD,EAAKC,CAAG,EACpCI,EAAOQ,GAAYrH,EAAImD,KAAK,EAAGqD,EAAKC,CAAG,GACjDU,EAAUN,EAAOQ,GAAYrH,EAAImD,KAAK,EAAGqD,EAAKC,CAAG,EACjDM,EAAU/G,EAAImD,KAAK,EAAI,IAEvB4D,EAAU/G,EAAImD,KAAK,EACnBgE,EAAUN,GAGJ,CACNA,KAAMM,EACNhE,KAAM4D,CACP,CACD,CAEA,SAASM,GAAYlE,EAAMqD,EAAKC,GAC/B,IAAIW,EAAab,GAAgBpD,EAAMqD,EAAKC,CAAG,EAC9Ca,EAAiBf,GAAgBpD,EAAO,EAAGqD,EAAKC,CAAG,EACpD,OAAQvD,GAAWC,CAAI,EAAIiE,EAAaE,GAAkB,CAC3D,CAIAlI,EAAe,IAAK,CAAC,KAAM,GAAI,KAAM,MAAM,EAC3CA,EAAe,IAAK,CAAC,KAAM,GAAI,KAAM,SAAS,EAI9CzB,EAAa,OAAQ,GAAG,EACxBA,EAAa,UAAW,GAAG,EAI3BY,EAAgB,OAAQ,CAAC,EACzBA,EAAgB,UAAW,CAAC,EAI5BiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EAErC+B,GAAkB,CAAC,IAAK,KAAM,IAAK,MAAO,SAAU9M,EAAOoR,EAAMlM,EAAQ0E,GACxEwH,EAAKxH,EAAMN,OAAO,EAAG,CAAC,GAAK3D,EAAM3F,CAAK,CACvC,CAAC,EAqCD2J,EAAe,IAAK,EAAG,KAAM,KAAK,EAElCA,EAAe,KAAM,EAAG,EAAG,SAAUlI,GACpC,OAAO/B,KAAKuK,WAAW,EAAE6H,YAAYpS,KAAM+B,CAAM,CAClD,CAAC,EAEDkI,EAAe,MAAO,EAAG,EAAG,SAAUlI,GACrC,OAAO/B,KAAKuK,WAAW,EAAE8H,cAAcrS,KAAM+B,CAAM,CACpD,CAAC,EAEDkI,EAAe,OAAQ,EAAG,EAAG,SAAUlI,GACtC,OAAO/B,KAAKuK,WAAW,EAAE+H,SAAStS,KAAM+B,CAAM,CAC/C,CAAC,EAEDkI,EAAe,IAAK,EAAG,EAAG,SAAS,EACnCA,EAAe,IAAK,EAAG,EAAG,YAAY,EAItCzB,EAAa,MAAO,GAAG,EACvBA,EAAa,UAAW,GAAG,EAC3BA,EAAa,aAAc,GAAG,EAG9BY,EAAgB,MAAO,EAAE,EACzBA,EAAgB,UAAW,EAAE,EAC7BA,EAAgB,aAAc,EAAE,EAIhCiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAM,SAAUG,EAAUxK,GACvC,OAAOA,EAAOuQ,iBAAiB/F,CAAQ,CACxC,CAAC,EACDH,EAAc,MAAO,SAAUG,EAAUxK,GACxC,OAAOA,EAAOwQ,mBAAmBhG,CAAQ,CAC1C,CAAC,EACDH,EAAc,OAAQ,SAAUG,EAAUxK,GACzC,OAAOA,EAAOyQ,cAAcjG,CAAQ,CACrC,CAAC,EAEDY,GAAkB,CAAC,KAAM,MAAO,QAAS,SAAU9M,EAAOoR,EAAMlM,EAAQ0E,GACnEyH,EAAUnM,EAAOH,QAAQqN,cAAcpS,EAAO4J,EAAO1E,EAAOzB,OAAO,EAExD,MAAX4N,EACHD,EAAKhB,EAAIiB,EAETvP,EAAgBoD,CAAM,EAAE1B,eAAiBxD,CAE3C,CAAC,EAED8M,GAAkB,CAAC,IAAK,IAAK,KAAM,SAAU9M,EAAOoR,EAAMlM,EAAQ0E,GACjEwH,EAAKxH,GAASjE,EAAM3F,CAAK,CAC1B,CAAC,EA8BD,IAAIqS,GAAwB,2DAA2DnD,MAAM,GAAG,EAQhG,IAAIoD,GAA6B,8BAA8BpD,MAAM,GAAG,EAKxE,IAAIqD,GAA2B,uBAAuBrD,MAAM,GAAG,EAwJ/D,IAAIsD,GAAuB3G,EAmB3B,IAAI4G,GAA4B5G,EAmBhC,IAAI6G,GAA0B7G,EAmB9B,SAAS8G,KACR,SAASjD,EAAUvO,EAAGC,GACrB,OAAOA,EAAEJ,OAASG,EAAEH,MACrB,CAWA,IATA,IAMC4R,EACAC,EACAC,EARGC,EAAY,GACfpD,EAAc,GACdC,EAAa,GACbC,EAAc,GAMV9O,EAAI,EAAGA,EAAI,EAAGA,CAAC,GAEnBwJ,EAAM/I,EAAU,CAAC,IAAM,EAAE,EAAEwR,IAAIjS,CAAC,EAChC6R,EAAOlT,KAAKoS,YAAYvH,EAAK,EAAE,EAC/BsI,EAASnT,KAAKqS,cAAcxH,EAAK,EAAE,EACnCuI,EAAQpT,KAAKsS,SAASzH,EAAK,EAAE,EAC7BwI,EAAU9R,KAAK2R,CAAI,EACnBjD,EAAY1O,KAAK4R,CAAM,EACvBjD,EAAW3O,KAAK6R,CAAK,EACrBjD,EAAY5O,KAAK2R,CAAI,EACrB/C,EAAY5O,KAAK4R,CAAM,EACvBhD,EAAY5O,KAAK6R,CAAK,EAQvB,IAJAC,EAAUjD,KAAKJ,CAAS,EACxBC,EAAYG,KAAKJ,CAAS,EAC1BE,EAAWE,KAAKJ,CAAS,EACzBG,EAAYC,KAAKJ,CAAS,EACrB3O,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACnB4O,EAAY5O,GAAKsL,GAAYsD,EAAY5O,EAAE,EAC3C6O,EAAW7O,GAAKsL,GAAYuD,EAAW7O,EAAE,EACzC8O,EAAY9O,GAAKsL,GAAYwD,EAAY9O,EAAE,EAG5CrB,KAAKuT,eAAiB,IAAI7G,OAAO,KAAOyD,EAAY1I,KAAK,GAAG,EAAI,IAAK,GAAG,EACxEzH,KAAKwT,oBAAsBxT,KAAKuT,eAChCvT,KAAKyT,kBAAoBzT,KAAKuT,eAE9BvT,KAAK0T,qBAAuB,IAAIhH,OAAO,KAAOwD,EAAWzI,KAAK,GAAG,EAAI,IAAK,GAAG,EAC7EzH,KAAK2T,0BAA4B,IAAIjH,OAAO,KAAOuD,EAAYxI,KAAK,GAAG,EAAI,IAAK,GAAG,EACnFzH,KAAK4T,wBAA0B,IAAIlH,OAAO,KAAO2G,EAAU5L,KAAK,GAAG,EAAI,IAAK,GAAG,CAChF,CAIA,SAASoM,KACR,OAAO7T,KAAK8T,MAAM,EAAI,IAAM,EAC7B,CA0BA,SAAS5Q,GAASgH,EAAO6J,GACxB9J,EAAeC,EAAO,EAAG,EAAG,WAC3B,OAAOlK,KAAKuK,WAAW,EAAErH,SAASlD,KAAK8T,MAAM,EAAG9T,KAAKgU,QAAQ,EAAGD,CAAS,CAC1E,CAAC,CACF,CAcA,SAASE,GAAczH,EAAUxK,GAChC,OAAOA,EAAOkS,cACf,CAxCAjK,EAAe,IAAK,CAAC,KAAM,GAAI,EAAG,MAAM,EACxCA,EAAe,IAAK,CAAC,KAAM,GAAI,EAAG4J,EAAO,EACzC5J,EAAe,IAAK,CAAC,KAAM,GAAI,EAN/B,WACC,OAAOjK,KAAK8T,MAAM,GAAK,EACxB,CAIyC,EAEzC7J,EAAe,MAAO,EAAG,EAAG,WAC3B,MAAO,GAAK4J,GAAQ1T,MAAMH,IAAI,EAAIsJ,EAAStJ,KAAKgU,QAAQ,EAAG,CAAC,CAC7D,CAAC,EAED/J,EAAe,QAAS,EAAG,EAAG,WAC7B,MAAO,GAAK4J,GAAQ1T,MAAMH,IAAI,EAAIsJ,EAAStJ,KAAKgU,QAAQ,EAAG,CAAC,EAAI1K,EAAStJ,KAAKmU,QAAQ,EAAG,CAAC,CAC3F,CAAC,EAEDlK,EAAe,MAAO,EAAG,EAAG,WAC3B,MAAO,GAAKjK,KAAK8T,MAAM,EAAIxK,EAAStJ,KAAKgU,QAAQ,EAAG,CAAC,CACtD,CAAC,EAED/J,EAAe,QAAS,EAAG,EAAG,WAC7B,MAAO,GAAKjK,KAAK8T,MAAM,EAAIxK,EAAStJ,KAAKgU,QAAQ,EAAG,CAAC,EAAI1K,EAAStJ,KAAKmU,QAAQ,EAAG,CAAC,CACpF,CAAC,EAQDjR,GAAS,IAAK,CAAA,CAAI,EAClBA,GAAS,IAAK,CAAA,CAAK,EAInBsF,EAAa,OAAQ,GAAG,EAGxBY,EAAgB,OAAQ,EAAE,EAQ1BiD,EAAc,IAAK4H,EAAa,EAChC5H,EAAc,IAAK4H,EAAa,EAChC5H,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,KAAMZ,EAAWJ,CAAM,EAErCgB,EAAc,MAAOX,EAAS,EAC9BW,EAAc,QAASV,EAAS,EAChCU,EAAc,MAAOX,EAAS,EAC9BW,EAAc,QAASV,EAAS,EAEhCwB,EAAc,CAAC,IAAK,MAAOM,CAAI,EAC/BN,EAAc,CAAC,IAAK,MAAO,SAAU7M,EAAOoK,EAAOlF,GAC9C4O,EAASnO,EAAM3F,CAAK,EACxBoK,EAAM+C,GAAmB,KAAX2G,EAAgB,EAAIA,CACnC,CAAC,EACDjH,EAAc,CAAC,IAAK,KAAM,SAAU7M,EAAOoK,EAAOlF,GACjDA,EAAO6O,MAAQ7O,EAAOH,QAAQiP,KAAKhU,CAAK,EACxCkF,EAAO+O,UAAYjU,CACpB,CAAC,EACD6M,EAAc,CAAC,IAAK,MAAO,SAAU7M,EAAOoK,EAAOlF,GAClDkF,EAAM+C,GAAQxH,EAAM3F,CAAK,EACzB8B,EAAgBoD,CAAM,EAAEvB,QAAU,CAAA,CACnC,CAAC,EACDkJ,EAAc,MAAO,SAAU7M,EAAOoK,EAAOlF,GAC5C,IAAIgP,EAAMlU,EAAMgB,OAAS,EACzBoJ,EAAM+C,GAAQxH,EAAM3F,EAAMsJ,OAAO,EAAG4K,CAAG,CAAC,EACxC9J,EAAMgD,GAAUzH,EAAM3F,EAAMsJ,OAAO4K,CAAG,CAAC,EACvCpS,EAAgBoD,CAAM,EAAEvB,QAAU,CAAA,CACnC,CAAC,EACDkJ,EAAc,QAAS,SAAU7M,EAAOoK,EAAOlF,GAC9C,IAAIiP,EAAOnU,EAAMgB,OAAS,EACtBoT,EAAOpU,EAAMgB,OAAS,EAC1BoJ,EAAM+C,GAAQxH,EAAM3F,EAAMsJ,OAAO,EAAG6K,CAAI,CAAC,EACzC/J,EAAMgD,GAAUzH,EAAM3F,EAAMsJ,OAAO6K,EAAM,CAAC,CAAC,EAC3C/J,EAAMiD,GAAU1H,EAAM3F,EAAMsJ,OAAO8K,CAAI,CAAC,EACxCtS,EAAgBoD,CAAM,EAAEvB,QAAU,CAAA,CACnC,CAAC,EACDkJ,EAAc,MAAO,SAAU7M,EAAOoK,EAAOlF,GAC5C,IAAIgP,EAAMlU,EAAMgB,OAAS,EACzBoJ,EAAM+C,GAAQxH,EAAM3F,EAAMsJ,OAAO,EAAG4K,CAAG,CAAC,EACxC9J,EAAMgD,GAAUzH,EAAM3F,EAAMsJ,OAAO4K,CAAG,CAAC,CACxC,CAAC,EACDrH,EAAc,QAAS,SAAU7M,EAAOoK,EAAOlF,GAC9C,IAAIiP,EAAOnU,EAAMgB,OAAS,EACtBoT,EAAOpU,EAAMgB,OAAS,EAC1BoJ,EAAM+C,GAAQxH,EAAM3F,EAAMsJ,OAAO,EAAG6K,CAAI,CAAC,EACzC/J,EAAMgD,GAAUzH,EAAM3F,EAAMsJ,OAAO6K,EAAM,CAAC,CAAC,EAC3C/J,EAAMiD,GAAU1H,EAAM3F,EAAMsJ,OAAO8K,CAAI,CAAC,CACzC,CAAC,EAyBD,IA6BIC,GA7BAC,EAAarG,GAAW,QAAS,CAAA,CAAI,EAMrCsG,GAAa,CAChBC,SAz1CqB,CACrBC,QAAS,gBACTC,QAAS,mBACTC,SAAU,eACVC,QAAS,oBACTC,SAAU,sBACVC,SAAU,GACX,EAm1CCnK,eA50C2B,CAC3BoK,IAAK,YACLC,GAAI,SACJC,EAAG,aACHC,GAAI,eACJC,IAAK,sBACLC,KAAM,2BACP,EAs0CC3K,YArzCwB,eAszCxBX,QAhzCoB,KAizCpBuL,uBAhzCmC,UAizCnCC,aA3yCyB,CACzBC,OAAQ,QACRC,KAAM,SACN7I,EAAG,gBACH8I,GAAI,aACJ1T,EAAG,WACH2T,GAAI,aACJrF,EAAG,UACHsF,GAAI,WACJvF,EAAG,QACHwF,GAAI,UACJtF,EAAG,UACHuF,GAAI,YACJjI,EAAG,SACHkI,GAAI,UACL,EA8xCClH,OAAQK,GACRN,YAAaQ,GAEbiC,KAhhBuB,CACvBL,IAAK,EACLC,IAAK,CACN,EA+gBCgB,SAAUK,GACVP,YAAaS,GACbR,cAAeO,GAEfyD,cAtCgC,eAuCjC,EAGIC,EAAU,GACVC,GAAiB,GAGrB,SAASC,GAAgBjP,GACxB,OAAOA,GAAMA,EAAIqB,YAAY,EAAEgC,QAAQ,IAAK,GAAG,CAChD,CAiCA,SAAS6L,GAAW3O,GAGnB,GAAI,CAACwO,EAAQxO,IAA2B,aAAlB,OAAOlI,QAA0BA,QAAUA,OAAOD,QACvE,IAEC,IADA+W,EAAY/B,GAAagC,MACJC,QACN,YAAc9O,CAAI,EACjC+O,GAAmBH,CAAS,CAChB,CAAX,MAAOI,IAEV,OAAOR,EAAQxO,EAChB,CAKA,SAAS+O,GAAmBtP,EAAKwP,GAehC,OAJEpC,GATEpN,IAEFyP,EADGnW,EAAYkW,CAAM,EACdE,GAAU1P,CAAG,EAEb2P,GAAa3P,EAAKwP,CAAM,GAKhBC,EAIVrC,IAAagC,KACrB,CAEA,SAASO,GAAapP,EAAMtC,GAC3B,GAAe,OAAXA,EAqCH,OADA,OAAO8Q,EAAQxO,GACR,KApCP,IAAII,EAAe2M,GAEnB,GADArP,EAAO2R,KAAOrP,EACO,MAAjBwO,EAAQxO,GACXD,EAAgB,uBAAwB,yOAAwP,EAChSK,EAAeoO,EAAQxO,GAAMsP,aACvB,GAA2B,MAAvB5R,EAAO6R,aAAsB,CACvC,GAAoC,MAAhCf,EAAQ9Q,EAAO6R,cAUlB,OAPKd,GAAe/Q,EAAO6R,gBAC1Bd,GAAe/Q,EAAO6R,cAAgB,IAEvCd,GAAe/Q,EAAO6R,cAAc9V,KAAK,CACxCuG,KAAMA,EACNtC,OAAQA,CACT,CAAC,EACM,KATP0C,EAAeoO,EAAQ9Q,EAAO6R,cAAcD,OAW9C,CAcA,OAbAd,EAAQxO,GAAQ,IAAIM,EAAOH,EAAaC,EAAc1C,CAAM,CAAC,EAEzD+Q,GAAezO,IAClByO,GAAezO,GAAMwP,QAAQ,SAAUxI,GACtCoI,GAAapI,EAAEhH,KAAMgH,EAAEtJ,MAAM,CAC9B,CAAC,EAMFqR,GAAmB/O,CAAI,EAEhBwO,EAAQxO,EAMjB,CAiCA,SAASmP,GAAU1P,GAClB,IAAIvF,EAMJ,GAAI,EAHHuF,EADGA,GAAOA,EAAIlC,SAAWkC,EAAIlC,QAAQsR,MAC/BpP,EAAIlC,QAAQsR,MAGdpP,GACJ,OAAOoN,GAGR,GAAI,CAACtU,EAAQkH,CAAG,EAAG,CAGlB,GADAvF,EAASyU,GAAWlP,CAAG,EAEtB,OAAOvF,EAERuF,EAAM,CAACA,EACR,CAnJA,IAqJOgQ,IA1JNC,EACAC,EACAzV,EACAwN,EALoBkI,EA4JDnQ,EA3JhBlG,EAAI,EAMDA,EAAIqW,EAAMpW,QAAQ,CAKxB,IAHAkW,GADAhI,EAAQgH,GAAgBkB,EAAMrW,EAAE,EAAEmO,MAAM,GAAG,GACjClO,OAEVmW,GADAA,EAAOjB,GAAgBkB,EAAMrW,EAAI,EAAE,GACrBoW,EAAKjI,MAAM,GAAG,EAAI,KACrB,EAAJgI,GAAO,CAEb,GADAxV,EAASyU,GAAWjH,EAAMhI,MAAM,EAAGgQ,CAAC,EAAE/P,KAAK,GAAG,CAAC,EAE9C,OAAOzF,EAER,GAAIyV,GAAQA,EAAKnW,QAAUkW,GAAKlR,EAAckJ,EAAOiI,EAAM,CAAA,CAAI,GAAKD,EAAI,EAEvE,MAEDA,CAAC,EACF,CACAnW,CAAC,EACF,CACA,OAAO,IAoIR,CAMA,SAASsW,GAActV,GACtB,IACIZ,EAAIY,EAAEuV,GAkBV,OAhBInW,GAAqC,CAAC,IAAjCW,EAAgBC,CAAC,EAAEK,WAC3BA,EAAWjB,EAAE8L,GAAS,GAAgB,GAAX9L,EAAE8L,GAAcA,EAAQ9L,EAAE+L,GAAQ,GAAK/L,EAAE+L,GAAQqB,GAAYpN,EAAE6L,GAAO7L,EAAE8L,EAAM,EAAIC,EAAO/L,EAAEgM,GAAQ,GAAe,GAAVhM,EAAEgM,IAA2B,KAAZhM,EAAEgM,KAA+B,IAAdhM,EAAEiM,IAA+B,IAAdjM,EAAEkM,IAAoC,IAAnBlM,EAAEmM,KAAuBH,EAAOhM,EAAEiM,GAAU,GAAiB,GAAZjM,EAAEiM,GAAeA,EAASjM,EAAEkM,GAAU,GAAiB,GAAZlM,EAAEkM,GAAeA,EAASlM,EAAEmM,IAAe,GAAsB,IAAjBnM,EAAEmM,IAAqBA,GAAc,CAAC,EAE3XxL,EAAgBC,CAAC,EAAEwV,qBAAuBnV,EAAW4K,GAAmBE,EAAX9K,KAChEA,EAAW8K,GAERpL,EAAgBC,CAAC,EAAEyV,gBAA+B,CAAC,IAAdpV,IACxCA,EAAWmL,IAERzL,EAAgBC,CAAC,EAAE0V,kBAAiC,CAAC,IAAdrV,IAC1CA,EAAWoL,IAGZ1L,EAAgBC,CAAC,EAAEK,SAAWA,GAGxBL,CACR,CAGA,SAAS2V,GAASvW,EAAGC,EAAGuW,GACvB,OAAS,MAALxW,EACIA,EAEC,MAALC,EACIA,EAEDuW,CACR,CAeA,SAASC,GAAgB1S,GACxB,IAAInE,EAGH8W,EAmE6B3S,EAC1B4S,EAAGC,EAAU3G,EAAMC,EAASN,EAAKC,EAAWgH,EAqB3CC,EA1FJjY,EAAQ,GAKT,GAAIkF,CAAAA,EAAO5B,GAAX,CA6BA,IAlDyB4B,EAyBMA,EAvB3BgT,EAAW,IAAIxX,KAAKd,EAAMuY,IAAI,CAAC,EAuBnCN,EAtBI3S,EAAOkT,QACH,CAACF,EAAStH,eAAe,EAAGsH,EAASG,YAAY,EAAGH,EAASI,WAAW,GAEzE,CAACJ,EAAS1H,YAAY,EAAG0H,EAASK,SAAS,EAAGL,EAASM,QAAQ,GAsBlEtT,EAAO6H,IAAyB,MAAnB7H,EAAOoS,GAAGpK,IAAqC,MAApBhI,EAAOoS,GAAGrK,KA4D1C,OADZ6K,GAH8B5S,EAvDPA,GA0DZ6H,IACL0L,IAAqB,MAAPX,EAAEY,GAAoB,MAAPZ,EAAEa,GACpC5H,EAAM,EACNC,EAAM,EAMN+G,EAAWL,GAASI,EAAEW,GAAIvT,EAAOoS,GAAGtK,GAAOyE,GAAWmH,EAAY,EAAG,EAAG,CAAC,EAAElL,IAAI,EAC/E0D,EAAOsG,GAASI,EAAEY,EAAG,CAAC,IACtBrH,EAAUqG,GAASI,EAAEa,EAAG,CAAC,GACX,GAAe,EAAVtH,KAClB2G,EAAkB,CAAA,KAGnBjH,EAAM7L,EAAOH,QAAQ8T,MAAM9H,IAC3BC,EAAM9L,EAAOH,QAAQ8T,MAAM7H,IAEvBiH,EAAUxG,GAAWmH,EAAY,EAAG7H,EAAKC,CAAG,EAEhD+G,EAAWL,GAASI,EAAEgB,GAAI5T,EAAOoS,GAAGtK,GAAOiL,EAAQvK,IAAI,EAGvD0D,EAAOsG,GAASI,EAAEA,EAAGG,EAAQ7G,IAAI,EAEtB,MAAP0G,EAAE1H,IAELiB,EAAUyG,EAAE1H,GACE,GAAe,EAAViB,KAClB2G,EAAkB,CAAA,GAEF,MAAPF,EAAEtB,GAEZnF,EAAUyG,EAAEtB,EAAIzF,GACZ+G,EAAEtB,EAAI,GAAW,EAANsB,EAAEtB,KAChBwB,EAAkB,CAAA,IAInB3G,EAAUN,GAGRK,EAAO,GAAKA,EAAOQ,GAAYmG,EAAUhH,EAAKC,CAAG,EACpDlP,EAAgBoD,CAAM,EAAEsS,eAAiB,CAAA,EACZ,MAAnBQ,EACVlW,EAAgBoD,CAAM,EAAEuS,iBAAmB,CAAA,GAE3CsB,EAAO5H,GAAmB4G,EAAU3G,EAAMC,EAASN,EAAKC,CAAG,EAC3D9L,EAAOoS,GAAGtK,GAAQ+L,EAAKrL,KACvBxI,EAAO8T,WAAaD,EAAKxH,YAxGD,MAArBrM,EAAO8T,aACVC,EAAYvB,GAASxS,EAAOoS,GAAGtK,GAAO6K,EAAY7K,EAAK,GAEnD9H,EAAO8T,WAAavL,GAAWwL,CAAS,GAA2B,IAAtB/T,EAAO8T,cACvDlX,EAAgBoD,CAAM,EAAEqS,mBAAqB,CAAA,GAG9CjJ,EAAOoC,GAAcuI,EAAW,EAAG/T,EAAO8T,UAAU,EACpD9T,EAAOoS,GAAGrK,GAASqB,EAAK+J,YAAY,EACpCnT,EAAOoS,GAAGpK,GAAQoB,EAAKgK,WAAW,GAQ9BvX,EAAI,EAAGA,EAAI,GAAqB,MAAhBmE,EAAOoS,GAAGvW,GAAY,EAAEA,EAC5CmE,EAAOoS,GAAGvW,GAAKf,EAAMe,GAAK8W,EAAY9W,GAIvC,KAAOA,EAAI,EAAGA,CAAC,GACdmE,EAAOoS,GAAGvW,GAAKf,EAAMe,GAAqB,MAAhBmE,EAAOoS,GAAGvW,GAAoB,IAANA,EAAU,EAAI,EAAKmE,EAAOoS,GAAGvW,GAIxD,KAApBmE,EAAOoS,GAAGnK,IAAsC,IAAtBjI,EAAOoS,GAAGlK,IAAuC,IAAtBlI,EAAOoS,GAAGjK,IAA4C,IAA3BnI,EAAOoS,GAAGhK,MAC7FpI,EAAOgU,SAAW,CAAA,EAClBhU,EAAOoS,GAAGnK,GAAQ,GAGnBjI,EAAO5B,IAAM4B,EAAOkT,QAAU1H,GAAgBP,IAAYtQ,MAAM,KAAMG,CAAK,EAC3EmZ,EAAkBjU,EAAOkT,QAAUlT,EAAO5B,GAAG4N,UAAU,EAAIhM,EAAO5B,GAAG8V,OAAO,EAIzD,MAAflU,EAAON,MACVM,EAAO5B,GAAG+V,cAAcnU,EAAO5B,GAAGgW,cAAc,EAAIpU,EAAON,IAAI,EAG5DM,EAAOgU,WACVhU,EAAOoS,GAAGnK,GAAQ,IAIfjI,EAAO6H,IAA6B,KAAA,IAAhB7H,EAAO6H,GAAGqD,GAAqBlL,EAAO6H,GAAGqD,IAAM+I,IACtErX,EAAgBoD,CAAM,EAAEpC,gBAAkB,CAAA,EAzD3C,CA2DD,CA6DA,IAAIyW,GAAmB,mJACnBC,GAAgB,8IAEhBC,GAAU,wBAEVC,GAAW,CACd,CAAC,eAAgB,uBACjB,CAAC,aAAc,mBACf,CAAC,eAAgB,kBACjB,CAAC,aAAc,cAAe,CAAA,GAC9B,CAAC,WAAY,eACb,CAAC,UAAW,aAAc,CAAA,GAC1B,CAAC,aAAc,cACf,CAAC,WAAY,SAEb,CAAC,aAAc,eACf,CAAC,YAAa,cAAe,CAAA,GAC7B,CAAC,UAAW,UAITC,GAAW,CACd,CAAC,gBAAiB,uBAClB,CAAC,gBAAiB,sBAClB,CAAC,WAAY,kBACb,CAAC,QAAS,aACV,CAAC,cAAe,qBAChB,CAAC,cAAe,oBAChB,CAAC,SAAU,gBACX,CAAC,OAAQ,YACT,CAAC,KAAM,SAGJC,GAAkB,sBAGtB,SAASC,GAAc3U,GACtB,IAAInE,EACH+Y,EAGAC,EACAC,EACAC,EACAC,EALAC,EAASjV,EAAOT,GAChB4F,EAAQkP,GAAiBa,KAAKD,CAAM,GAAKX,GAAcY,KAAKD,CAAM,EAMnE,GAAI9P,EAAO,CAGV,IAFAvI,EAAgBoD,CAAM,EAAExC,IAAM,CAAA,EAEzB3B,EAAI,EAAG+Y,EAAIJ,GAAS1Y,OAAQD,EAAI+Y,EAAG/Y,CAAC,GACxC,GAAI2Y,GAAS3Y,GAAG,GAAGqZ,KAAK/P,EAAM,EAAE,EAAG,CAClC2P,EAAaN,GAAS3Y,GAAG,GACzBgZ,EAA+B,CAAA,IAAnBL,GAAS3Y,GAAG,GACxB,KACD,CAED,GAAkB,MAAdiZ,EACH9U,EAAOlC,SAAW,CAAA,MADnB,CAIA,GAAIqH,EAAM,GAAI,CACb,IAAKtJ,EAAI,EAAG+Y,EAAIH,GAAS3Y,OAAQD,EAAI+Y,EAAG/Y,CAAC,GACxC,GAAI4Y,GAAS5Y,GAAG,GAAGqZ,KAAK/P,EAAM,EAAE,EAAG,CAElC4P,GAAc5P,EAAM,IAAM,KAAOsP,GAAS5Y,GAAG,GAC7C,KACD,CAED,GAAkB,MAAdkZ,EAEH,OADA/U,KAAAA,EAAOlC,SAAW,CAAA,EAGpB,CACA,GAAK+W,GAA2B,MAAdE,EAAlB,CAIA,GAAI5P,EAAM,GAAI,CACb,GAAIoP,CAAAA,GAAQW,KAAK/P,EAAM,EAAE,EAIxB,OADAnF,KAAAA,EAAOlC,SAAW,CAAA,GAFlBkX,EAAW,GAKb,CACAhV,EAAOR,GAAKsV,GAAcC,GAAc,KAAOC,GAAY,IAC3DG,GAA0BnV,CAAM,CAVhC,MAFCA,EAAOlC,SAAW,CAAA,CAfnB,CA4BD,MACCkC,EAAOlC,SAAW,CAAA,CAEpB,CAGA,IAAIH,GAAU,0LAEd,SAASyX,GAA0BC,EAASC,EAAUC,EAAQC,EAASC,EAAWC,GAC7EC,EAAS,CASd,SAAwBN,GACnB7M,EAAOI,SAASyM,EAAS,EAAE,EAC/B,CAAA,GAAI7M,GAAQ,GACX,OAAO,IAAOA,EACR,GAAIA,GAAQ,IAClB,OAAO,KAAOA,CACf,CACA,OAAOA,CACR,EAjB8B6M,CAAO,EAAGpL,GAAyBpB,QAAQyM,CAAQ,EAAG1M,SAAS2M,EAAQ,EAAE,EAAG3M,SAAS4M,EAAS,EAAE,EAAG5M,SAAS6M,EAAW,EAAE,GAMtJ,OAJIC,GACHC,EAAO5Z,KAAK6M,SAAS8M,EAAW,EAAE,CAAC,EAG7BC,CACR,CAkCA,IAAIC,GAAa,CAChBC,GAAI,EACJC,IAAK,EACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,IACLC,IAAK,CAAA,GACN,EAiBA,SAASC,GAAkBvW,GAC1B,IAhBwBwW,EAAWC,EAgB/BtR,EAAQxH,GAAQuX,KAAuBlV,EAAOT,GAhDhD6F,QAAQ,oBAAqB,GAAG,EAChCA,QAAQ,WAAY,GAAG,EACvBsR,KAAK,CA8C8C,EACjDvR,GACCwR,EAAcvB,GAA0BjQ,EAAM,GAAIA,EAAM,GAAIA,EAAM,GAAIA,EAAM,GAAIA,EAAM,GAAIA,EAAM,EAAE,EA7CxG,SAAsByR,EAAYC,EAAa7W,GAC9C,GAAI4W,CAAAA,GAEmBxJ,GAA2BvE,QAAQ+N,CAAU,IAClD,IAAIpb,KAAKqb,EAAY,GAAIA,EAAY,GAAIA,EAAY,EAAE,EAAE3C,OAAO,EAOlF,OAAO,EALLtX,EAAgBoD,CAAM,EAAEpC,gBAAkB,CAAA,EAC1CoC,EAAOlC,SAAW,CAAA,CAKrB,EAkCoBqH,EAAM,GAAIwR,EAAa3W,CAAM,IAI/CA,EAAOoS,GAAKuE,EACZ3W,EAAON,MAxBgB8W,EAwBOrR,EAAM,GAxBFsR,EAwBMtR,EAAM,GAxBI2R,EAwBA3R,EAAM,IAvBrDqR,EACIZ,GAAWY,GACRC,EAEH,EAKI,MAHPM,EAAKnO,SAASkO,EAAW,EAAE,IAC3Bja,EAAIka,EAAK,MACG,KACAla,GAgBhBmD,EAAO5B,GAAKoN,GAAc7Q,MAAM,KAAMqF,EAAOoS,EAAE,EAC/CpS,EAAO5B,GAAG+V,cAAcnU,EAAO5B,GAAGgW,cAAc,EAAIpU,EAAON,IAAI,EAE/D9C,EAAgBoD,CAAM,EAAErC,QAAU,CAAA,IAElCqC,EAAOlC,SAAW,CAAA,CAEpB,CAwCA,SAASqX,GAA0BnV,GAElC,GAAIA,EAAOR,KAAO9E,EAAMsc,SACvBrC,GAAc3U,CAAM,OAGrB,GAAIA,EAAOR,KAAO9E,EAAMuc,SACvBV,GAAkBvW,CAAM,MADzB,CAIAA,EAAOoS,GAAK,GACZxV,EAAgBoD,CAAM,EAAEjD,MAAQ,CAAA,EAchC,IAXA,IAEC8Z,EAEAnS,EA9oD+BA,EAAO5J,EAAOkF,EA0oD1CiV,EAAS,GAAKjV,EAAOT,GAMxB2X,EAAejC,EAAOnZ,OACtBqb,EAAyB,EAE1BzP,EAASzC,GAAajF,EAAOR,GAAIQ,EAAOH,OAAO,EAAEsF,MAAMd,EAAgB,GAAK,GAEvExI,EAAI,EAAGA,EAAI6L,EAAO5L,OAAQD,CAAC,GAC/B6I,EAAQgD,EAAO7L,IACfgb,GAAe5B,EAAO9P,MAAM8B,GAAsBvC,EAAO1E,CAAM,CAAC,GAAK,IAAI,MAKnD,GADrBoX,EAAUnC,EAAO7Q,OAAO,EAAG6Q,EAAOpM,QAAQgO,CAAW,CAAC,GAC1C/a,QACXc,EAAgBoD,CAAM,EAAE/C,YAAYlB,KAAKqb,CAAO,EAEjDnC,EAASA,EAAOjT,MAAMiT,EAAOpM,QAAQgO,CAAW,EAAIA,EAAY/a,MAAM,EACtEqb,GAA0BN,EAAY/a,QAGnC0I,GAAqBE,IACpBmS,EACHja,EAAgBoD,CAAM,EAAEjD,MAAQ,CAAA,EAEhCH,EAAgBoD,CAAM,EAAEhD,aAAajB,KAAK2I,CAAK,EAvqDlBA,EAyqDNA,EAzqDoB1E,EAyqDAA,EAxqDjC,OAD0BlF,EAyqDN+b,IAxqDZ7a,EAAW0L,GAAQhD,CAAK,GAC5CgD,GAAOhD,GAAO5J,EAAOkF,EAAOoS,GAAIpS,EAAQ0E,CAAK,GAwqDlC1E,EAAOzB,SAAW,CAACsY,GAC7Bja,EAAgBoD,CAAM,EAAEhD,aAAajB,KAAK2I,CAAK,EAKjD9H,EAAgBoD,CAAM,EAAE7C,cAAgB+Z,EAAeC,EACnC,EAAhBlC,EAAOnZ,QACVc,EAAgBoD,CAAM,EAAE/C,YAAYlB,KAAKkZ,CAAM,EAI5CjV,EAAOoS,GAAGnK,IAAS,IAA0C,CAAA,IAApCrL,EAAgBoD,CAAM,EAAEvB,SAAsC,EAAlBuB,EAAOoS,GAAGnK,KAClFrL,EAAgBoD,CAAM,EAAEvB,QAAUD,KAAAA,GAGnC5B,EAAgBoD,CAAM,EAAEvC,gBAAkBuC,EAAOoS,GAAGpQ,MAAM,CAAC,EAC3DpF,EAAgBoD,CAAM,EAAEtC,SAAWsC,EAAO+O,UAE1C/O,EAAOoS,GAAGnK,GAMX,SAAyBzL,EAAQ6a,EAAM3Z,GAGtC,GAAgB,MAAZA,EAEH,OAAO2Z,EAER,OAA2B,MAAvB7a,EAAO8a,aACH9a,EAAO8a,aAAaD,EAAM3Z,CAAQ,EAChB,MAAflB,EAAOsS,OAEjByI,EAAO/a,EAAOsS,KAAKpR,CAAQ,IACf2Z,EAAO,KAClBA,GAAQ,IAGRA,EADIE,GAAiB,KAATF,EAGNA,EAFC,GAKDA,CAET,EA7BmCrX,EAAOH,QAASG,EAAOoS,GAAGnK,GAAOjI,EAAO+O,SAAS,EAEnF2D,GAAgB1S,CAAM,EACtBmS,GAAcnS,CAAM,CA3DpB,CA4DD,CA2FA,SAASwX,GAAcxX,GACtB,IAzByBA,EAKrBnE,EAoBAf,EAAQkF,EAAOT,GAClBhD,EAASyD,EAAOR,GAIjB,GAFAQ,EAAOH,QAAUG,EAAOH,SAAW4R,GAAUzR,EAAOP,EAAE,EAExC,OAAV3E,GAA8B0D,KAAAA,IAAXjC,GAAkC,KAAVzB,EAC9C,OAAO6D,EAAc,CAAEvB,UAAW,CAAA,CAAK,CAAC,EAOzC,GAJqB,UAAjB,OAAOtC,IACVkF,EAAOT,GAAKzE,EAAQkF,EAAOH,QAAQ4X,SAAS3c,CAAK,GAG9CoF,EAASpF,CAAK,EACjB,OAAO,IAAIiF,EAAOoS,GAAcrX,CAAK,CAAC,EAChC,GAAIS,EAAOT,CAAK,EACtBkF,EAAO5B,GAAKtD,OACN,GAAID,EAAQ0B,CAAM,EAAG,CAC3Bmb,IAjFGC,EAAYC,EAAYC,EAAahc,EAAGic,EADX9X,EAkFPA,EA/E1B,GAAyB,IAArBA,EAAOR,GAAG1D,OACbc,EAAgBoD,CAAM,EAAE1C,cAAgB,CAAA,EACxC0C,EAAO5B,GAAK,IAAI5C,KAAKoD,GAAG,MAFzB,CAMA,IAAK/C,EAAI,EAAGA,EAAImE,EAAOR,GAAG1D,OAAQD,CAAC,GAClCic,EAAe,EACfH,EAAa1Y,EAAW,GAAIe,CAAM,EACZ,MAAlBA,EAAOkT,UACVyE,EAAWzE,QAAUlT,EAAOkT,SAE7ByE,EAAWnY,GAAKQ,EAAOR,GAAG3D,GAC1BsZ,GAA0BwC,CAAU,EAE/B9Z,EAAQ8Z,CAAU,IAQvBG,GAHAA,GAAgBlb,EAAgB+a,CAAU,EAAExa,eAGsB,GAAlDP,EAAgB+a,CAAU,EAAE3a,aAAalB,OAEzDc,EAAgB+a,CAAU,EAAEI,MAAQD,EAEjB,MAAfD,GAAuBC,EAAeD,KACzCA,EAAcC,EACdF,EAAaD,GAIfvb,EAAO4D,EAAQ4X,GAAcD,CAAU,CA7BvC,CA4EA,MAAO,GAAIpb,EACV4Y,GAA0BnV,CAAM,OAcjC,GAAI3E,EADAP,GADoBkF,EAVPA,GAWET,EACE,EACpBS,EAAO5B,GAAK,IAAI5C,KAAKd,EAAMuY,IAAI,CAAC,OACtB1X,EAAOT,CAAK,EACtBkF,EAAO5B,GAAK,IAAI5C,KAAKV,EAAMuB,QAAQ,CAAC,EACT,UAAjB,OAAOvB,GA5OOkF,EA6OPA,EA1OF,QAFZoH,EAAUsN,GAAgBQ,KAAKlV,EAAOT,EAAE,GAG3CS,EAAO5B,GAAK,IAAI5C,KAAK,CAAC4L,EAAQ,EAAE,GAIjCuN,GAAc3U,CAAM,EACI,CAAA,IAApBA,EAAOlC,WACV,OAAOkC,EAAOlC,SAKfyY,GAAkBvW,CAAM,EACA,CAAA,IAApBA,EAAOlC,YACV,OAAOkC,EAAOlC,SAMfpD,EAAMsd,wBAAwBhY,CAAM,KAuNzBnF,EAAQC,CAAK,GACvBkF,EAAOoS,GAAK3W,EAAIX,EAAMkH,MAAM,CAAC,EAAG,SAAU7B,GACzC,OAAOyI,SAASzI,EAAK,EAAE,CACxB,CAAC,EACDuS,GAAgB1S,CAAM,GACZ5E,EAASN,CAAK,GAtEAkF,EAuEPA,GAtEP5B,KAIPvC,EAAI0H,GAAqBvD,EAAOT,EAAE,EACtCS,EAAOoS,GAAK3W,EAAI,CAACI,EAAE2M,KAAM3M,EAAEsN,MAAOtN,EAAEiS,KAAOjS,EAAEuN,KAAMvN,EAAEwb,KAAMxb,EAAEoc,OAAQpc,EAAEqc,OAAQrc,EAAEsc,aAAc,SAAUhY,GACxG,OAAOA,GAAOyI,SAASzI,EAAK,EAAE,CAC/B,CAAC,EAEDuS,GAAgB1S,CAAM,GA8DX1E,EAASR,CAAK,EAExBkF,EAAO5B,GAAK,IAAI5C,KAAKV,CAAK,EAE1BJ,EAAMsd,wBAAwBhY,CAAM,EAtBrC,OAJKnC,EAAQmC,CAAM,IAClBA,EAAO5B,GAAK,MAGN4B,CACR,CAyBA,SAAStD,GAAiB5B,EAAOyB,EAAQC,EAAQC,EAAQ2b,GACxD,IAAI3F,EAAI,GAmBR,MAjBe,CAAA,IAAXjW,GAA8B,CAAA,IAAXA,IACtBC,EAASD,EACTA,EAASgC,KAAAA,IAGLpD,EAASN,CAAK,GAx/EpB,SAAuBqF,GACtB,GAAInF,OAAOqd,oBACV,OAAkD,IAA3Crd,OAAOqd,oBAAoBlY,CAAG,EAAErE,OAGvC,IADA,IAAIwc,KACMnY,EACT,GAAIA,EAAIhE,eAAemc,CAAC,EACvB,OAGF,OAAO,CAET,EA4+EuCxd,CAAK,GAAOD,EAAQC,CAAK,GAAsB,IAAjBA,EAAMgB,UACzEhB,EAAQ0D,KAAAA,GAITiU,EAAEnT,iBAAmB,CAAA,EACrBmT,EAAES,QAAUT,EAAE9S,OAASyY,EACvB3F,EAAEhT,GAAKjD,EACPiW,EAAElT,GAAKzE,EACP2X,EAAEjT,GAAKjD,EACPkW,EAAElU,QAAU9B,GApFRb,EAAM,IAAImE,EAAOoS,GAAcqF,GADVxX,EAuFDyS,CAtF+B,CAAC,CAAC,GACjDuB,WAEPpY,EAAI2c,IAAI,EAAG,GAAG,EACd3c,EAAIoY,SAAWxV,KAAAA,GAGT5C,CAgFR,CAEA,SAAS8X,EAAY5Y,EAAOyB,EAAQC,EAAQC,GAC3C,OAAOC,GAAiB5B,EAAOyB,EAAQC,EAAQC,EAAQ,CAAA,CAAK,CAC7D,CA5PA/B,EAAMsd,wBAA0BtW,EAAU,iVAAiW,SAAU1B,GACpZA,EAAO5B,GAAK,IAAI5C,KAAKwE,EAAOT,IAAMS,EAAOkT,QAAU,OAAS,GAAG,CAChE,CAAC,EAGDxY,EAAMsc,SAAW,aAGjBtc,EAAMuc,SAAW,aAsPbuB,GAAe9W,EAAU,qGAAsG,WAClI,IAAI+W,EAAQ/E,EAAY/Y,MAAM,KAAMC,SAAS,EAC7C,OAAIJ,KAAKqD,QAAQ,GAAK4a,EAAM5a,QAAQ,EAC5B4a,EAAQje,KAAOA,KAAOie,EAEtB9Z,EAAc,CAEvB,CAAC,EAEG+Z,GAAehX,EAAU,qGAAsG,WAClI,IAAI+W,EAAQ/E,EAAY/Y,MAAM,KAAMC,SAAS,EAC7C,OAAIJ,KAAKqD,QAAQ,GAAK4a,EAAM5a,QAAQ,EACpBrD,KAARie,EAAeje,KAAOie,EAEtB9Z,EAAc,CAEvB,CAAC,EAOD,SAASga,GAAOhd,EAAIid,GACnB,IAAIhd,EAAKC,EAIT,GAAI,EAFH+c,EADsB,IAAnBA,EAAQ9c,QAAgBjB,EAAQ+d,EAAQ,EAAE,EACnCA,EAAQ,GAEdA,GAAQ9c,OACZ,OAAO4X,EAAY,EAGpB,IADA9X,EAAMgd,EAAQ,GACT/c,EAAI,EAAGA,EAAI+c,EAAQ9c,OAAQ,EAAED,EAC5B+c,EAAQ/c,GAAGgC,QAAQ,GAAK+a,CAAAA,EAAQ/c,GAAGF,GAAIC,CAAG,IAC9CA,EAAMgd,EAAQ/c,IAGhB,OAAOD,CACR,CAeA,IAIIid,GAAW,CAAC,OAAQ,UAAW,QAAS,OAAQ,MAAO,OAAQ,SAAU,SAAU,eAgCvF,SAASC,GAASC,GACjB,IAAIrV,EAAkBH,GAAqBwV,CAAQ,EAClDC,EAAQtV,EAAgB8E,MAAQ,EAChCyQ,EAAWvV,EAAgBwV,SAAW,EACtCxP,EAAShG,EAAgByF,OAAS,EAClCgQ,EAAQzV,EAAgBwI,MAAQ,EAChCkN,EAAO1V,EAAgBoK,KAAO,EAC9BQ,EAAQ5K,EAAgB2T,MAAQ,EAChC7I,EAAU9K,EAAgBuU,QAAU,EACpCtJ,EAAUjL,EAAgBwU,QAAU,EACpCmB,EAAe3V,EAAgByU,aAAe,EAE/C3d,KAAKsD,SA1CN,SAAyBjB,GACxB,IAAK,IAAIkF,KAAOlF,EACf,GAAsC,CAAC,IAAjCgM,EAAQ1N,KAAK0d,GAAU9W,CAAG,GAAuB,MAAVlF,EAAEkF,IAAiB5D,MAAMtB,EAAEkF,EAAI,EAC3E,MAAO,CAAA,EAKT,IADA,IAAIuX,EAAiB,CAAA,EACZzd,EAAI,EAAGA,EAAIgd,GAAS/c,OAAQ,EAAED,EACtC,GAAIgB,EAAEgc,GAAShd,IAAK,CACnB,GAAIyd,EACH,MAAO,CAAA,EAEJC,WAAW1c,EAAEgc,GAAShd,GAAG,IAAM4E,EAAM5D,EAAEgc,GAAShd,GAAG,IACtDyd,EAAiB,CAAA,EAEnB,CAGD,MAAO,CAAA,CACR,EAsBiC5V,CAAe,EAG/ClJ,KAAKgf,cACJ,CAACH,EACS,IAAV1K,EACU,IAAVH,EACQ,IAARF,EAAe,GAAK,GAGrB9T,KAAKif,MAAQ,CAACL,EAAe,EAARD,EAIrB3e,KAAKkf,QAAU,CAAChQ,EAAoB,EAAXuP,EAAuB,GAARD,EAExCxe,KAAKmf,MAAQ,GAEbnf,KAAKqF,QAAU4R,GAAU,EAEzBjX,KAAKof,QAAQ,CACd,CAEA,SAASC,GAAW1Z,GACnB,OAAOA,aAAe2Y,EACvB,CAEA,SAASgB,GAASzZ,GACjB,OAAIA,EAAS,EACqB,CAAC,EAA3BC,KAAKyZ,MAAM,CAAC,EAAI1Z,CAAM,EAEtBC,KAAKyZ,MAAM1Z,CAAM,CAE1B,CAIA,SAAS2Z,GAAOtV,EAAOuV,GACtBxV,EAAeC,EAAO,EAAG,EAAG,WAC3B,IAAIsV,EAASxf,KAAK0f,UAAU,EACxBC,EAAO,IAKX,OAJIH,EAAS,IACZA,EAAS,CAACA,EACVG,EAAO,KAEDA,EAAOrW,EAAS,CAAC,EAAEkW,EAAS,IAAK,CAAC,EAAIC,EAAYnW,EAAS,CAAC,CAACkW,EAAS,GAAI,CAAC,CACnF,CAAC,CACF,CAEAA,GAAO,IAAK,GAAG,EACfA,GAAO,KAAM,EAAE,EAIfnT,EAAc,IAAKH,EAAgB,EACnCG,EAAc,KAAMH,EAAgB,EACpCiB,EAAc,CAAC,IAAK,MAAO,SAAU7M,EAAOoK,EAAOlF,GAClDA,EAAOkT,QAAU,CAAA,EACjBlT,EAAON,KAAO0a,GAAiB1T,GAAkB5L,CAAK,CACvD,CAAC,EAOD,IAAIuf,GAAc,kBAElB,SAASD,GAAiBE,EAASrF,GAClC,IAAIsF,GAAWtF,GAAU,IAAI9P,MAAMmV,CAAO,EAE1C,OAAgB,OAAZC,EACI,KAOW,KAFf/L,EAAuB,IADvBgM,IADQD,EAAQA,EAAQze,OAAS,IAAM,IACtB,IAAIqJ,MAAMkV,EAAW,GAAK,CAAC,IAAK,EAAG,IAClC,GAAW5Z,EAAM+Z,EAAM,EAAE,GAExB,EAAiB,MAAbA,EAAM,GAAahM,EAAU,CAACA,CAC1D,CAGA,SAASiM,GAAgB3f,EAAO4f,GAC/B,IAASC,EACT,OAAID,EAAM/a,QACT/D,EAAM8e,EAAME,MAAM,EAClBD,GAAQza,EAASpF,CAAK,GAAKS,EAAOT,CAAK,EAAIA,EAAkB4Y,EAAY5Y,CAAK,GAA7BuB,QAAQ,EAAoCT,EAAIS,QAAQ,EAEzGT,EAAIwC,GAAGyc,QAAQjf,EAAIwC,GAAG/B,QAAQ,EAAIse,CAAI,EACtCjgB,EAAMuF,aAAarE,EAAK,CAAA,CAAK,EACtBA,GAEA8X,EAAY5Y,CAAK,EAAEggB,MAAM,CAElC,CAEA,SAASC,GAAcle,GAGtB,OAAoD,GAA7C,CAACyD,KAAKyZ,MAAMld,EAAEuB,GAAG4c,kBAAkB,EAAI,EAAE,CACjD,CA+IA,SAASC,KACR,MAAOzgB,CAAAA,CAAAA,KAAKqD,QAAQ,GAAIrD,KAAKmF,QAA2B,IAAjBnF,KAAKoF,OAC7C,CA3IAlF,EAAMuF,aAAe,aA8IrB,IAAIib,GAAc,2DAKdC,GAAW,sKAEf,SAASC,EAAetgB,EAAOiH,GAC9B,IAICsZ,EAJGtC,EAAWje,EA0Df,OAnDI+e,GAAW/e,CAAK,EACnBie,EAAW,CACV1N,GAAIvQ,EAAM0e,cACVtO,EAAGpQ,EAAM2e,MACTrO,EAAGtQ,EAAM4e,OACV,EACUpe,EAASR,CAAK,GACxBie,EAAW,GACPhX,EACHgX,EAAShX,GAAOjH,EAEhBie,EAASM,aAAeve,IAEZqK,EAAQ+V,GAAYhG,KAAKpa,CAAK,IAC3Cqf,EAAoB,MAAbhV,EAAM,GAAa,CAAC,EAAI,EAC/B4T,EAAW,CACVrQ,EAAG,EACHwC,EAAGzK,EAAM0E,EAAM6C,EAAK,EAAImS,EACxBhP,EAAG1K,EAAM0E,EAAM8C,EAAK,EAAIkS,EACxBtd,EAAG4D,EAAM0E,EAAM+C,EAAO,EAAIiS,EAC1B1S,EAAGhH,EAAM0E,EAAMgD,EAAO,EAAIgS,EAC1B9O,GAAI5K,EAAMqZ,GAA8B,IAArB3U,EAAMiD,GAAmB,CAAC,EAAI+R,CAClD,IACahV,EAAQgW,GAASjG,KAAKpa,CAAK,IACxCqf,EAAoB,MAAbhV,EAAM,GAAa,CAAC,GAAIA,EAAM,GAAa,GAClD4T,EAAW,CACVrQ,EAAG4S,GAASnW,EAAM,GAAIgV,CAAI,EAC1B/O,EAAGkQ,GAASnW,EAAM,GAAIgV,CAAI,EAC1BvH,EAAG0I,GAASnW,EAAM,GAAIgV,CAAI,EAC1BjP,EAAGoQ,GAASnW,EAAM,GAAIgV,CAAI,EAC1BhP,EAAGmQ,GAASnW,EAAM,GAAIgV,CAAI,EAC1Btd,EAAGye,GAASnW,EAAM,GAAIgV,CAAI,EAC1B1S,EAAG6T,GAASnW,EAAM,GAAIgV,CAAI,CAC3B,GACsB,MAAZpB,EAEVA,EAAW,GACmB,UAApB,OAAOA,IAA0B,SAAUA,GAAY,OAAQA,KACzEwC,EAyCF,SAA2BC,EAAM/C,GAChC,IAAI7c,EACJ,GAAM4f,CAAAA,EAAK3d,QAAQ,GAAK4a,CAAAA,EAAM5a,QAAQ,EACrC,MAAO,CAAEwb,aAAc,EAAG3P,OAAQ,CAAE,EAGrC+O,EAAQgC,GAAgBhC,EAAO+C,CAAI,EAC/BA,EAAKC,SAAShD,CAAK,EACtB7c,EAAM8f,GAA0BF,EAAM/C,CAAK,IAE3C7c,EAAM8f,GAA0BjD,EAAO+C,CAAI,GACvCnC,aAAe,CAACzd,EAAIyd,aACxBzd,EAAI8N,OAAS,CAAC9N,EAAI8N,QAGnB,OAAO9N,CACR,EAzD8B8X,EAAYqF,EAAS5Z,IAAI,EAAGuU,EAAYqF,EAAS7Z,EAAE,CAAC,GAEhF6Z,EAAW,IACF1N,GAAKkQ,EAAQlC,aACtBN,EAAS3N,EAAImQ,EAAQ7R,QAGtB2R,EAAM,IAAIvC,GAASC,CAAQ,EAEvBc,GAAW/e,CAAK,GAAKkB,EAAWlB,EAAO,SAAS,IACnDugB,EAAIxb,QAAU/E,EAAM+E,SAGdwb,CACR,CAKA,SAASC,GAASK,EAAKxB,GAIlBve,EAAM+f,GAAOpC,WAAWoC,EAAIvW,QAAQ,IAAK,GAAG,CAAC,EAEjD,OAAQjH,MAAMvC,CAAG,EAAI,EAAIA,GAAOue,CACjC,CAEA,SAASuB,GAA0BF,EAAM/C,GACxC,IAAI7c,EAAM,CAAEyd,aAAc,EAAG3P,OAAQ,CAAE,EASvC,OAPA9N,EAAI8N,OAAS+O,EAAMtP,MAAM,EAAIqS,EAAKrS,MAAM,EAAmC,IAA9BsP,EAAMjQ,KAAK,EAAIgT,EAAKhT,KAAK,GAClEgT,EAAKZ,MAAM,EAAErC,IAAI3c,EAAI8N,OAAQ,GAAG,EAAEkS,QAAQnD,CAAK,GAClD,EAAE7c,EAAI8N,OAGP9N,EAAIyd,aAAe,CAACZ,EAAQ,CAAC+C,EAAKZ,MAAM,EAAErC,IAAI3c,EAAI8N,OAAQ,GAAG,EAEtD9N,CACR,CAqBA,SAASigB,GAAYC,EAAWxZ,GAC/B,OAAO,SAAUjD,EAAK0c,GACrB,IAASC,EAYT,OAVe,OAAXD,GAAoB5d,MAAM,CAAC4d,CAAM,IACpC1Z,EAAgBC,EAAM,YAAcA,EAAO,uDAAyDA,EAA8B,gGAA8E,EAChN0Z,EAAM3c,EACNA,EAAM0c,EACNA,EAASC,GAKVC,GAAYzhB,KADN4gB,EADN/b,EAAqB,UAAf,OAAOA,EAAmB,CAACA,EAAMA,EACb0c,CAAM,EACTD,CAAS,EACzBthB,IACR,CACD,CAEA,SAASyhB,GAAY5W,EAAK0T,EAAUmD,EAAUjc,GAC7C,IAAIoZ,EAAeN,EAASS,cAC3BJ,EAAOU,GAASf,EAASU,KAAK,EAC9B/P,EAASoQ,GAASf,EAASW,OAAO,EAE9BrU,EAAIxH,QAAQ,IAKjBoC,EAA+B,MAAhBA,GAA8BA,EAEzCyJ,GACHQ,GAAS7E,EAAK6D,GAAI7D,EAAK,OAAO,EAAIqE,EAASwS,CAAQ,EAEhD9C,GACHnQ,GAAM5D,EAAK,OAAQ6D,GAAI7D,EAAK,MAAM,EAAI+T,EAAO8C,CAAQ,EAElD7C,GACHhU,EAAIjH,GAAGyc,QAAQxV,EAAIjH,GAAG/B,QAAQ,EAAIgd,EAAe6C,CAAQ,EAEtDjc,IACHvF,EAAMuF,aAAaoF,EAAK+T,GAAQ1P,CAAM,CAExC,CAtFA0R,EAAezf,GAAKmd,GAAS7d,UAC7BmgB,EAAee,QA/Uf,WACC,OAAOf,EAAexc,GAAG,CAC1B,EAoaI2Z,GAAMsD,GAAY,EAAG,KAAK,EAC1BO,GAAWP,GAAY,CAAC,EAAG,UAAU,EA8HzC,SAASQ,GAAUpgB,EAAGC,GAErB,IAAIogB,EAAyC,IAAvBpgB,EAAEsM,KAAK,EAAIvM,EAAEuM,KAAK,IAAWtM,EAAEiN,MAAM,EAAIlN,EAAEkN,MAAM,GAEtEoT,EAAStgB,EAAE2e,MAAM,EAAErC,IAAI+D,EAAgB,QAAQ,EAO/CE,EAHGtgB,EAAIqgB,EAAS,GAGNrgB,EAAIqgB,IAAWA,EAFftgB,EAAE2e,MAAM,EAAErC,IAAI+D,EAAiB,EAAG,QAAQ,IAM1CpgB,EAAIqgB,IAFJtgB,EAAE2e,MAAM,EAAErC,IAAqB,EAAjB+D,EAAoB,QAAQ,EAEjBC,GAIpC,MAAO,EAAED,EAAiBE,IAAW,CACtC,CAwFA,SAAShgB,GAAOuF,GAGf,OAAYvD,KAAAA,IAARuD,EACIvH,KAAKqF,QAAQsR,OAGC,OADrBsL,EAAgBhL,GAAU1P,CAAG,KAE5BvH,KAAKqF,QAAU4c,GAETjiB,KAET,CAlGAE,EAAMgiB,cAAgB,uBACtBhiB,EAAMiiB,iBAAmB,yBAmGrBC,GAAOlb,EAAU,kJAAmJ,SAAUK,GACjL,OAAYvD,KAAAA,IAARuD,EACIvH,KAAKuK,WAAW,EAEhBvK,KAAKgC,OAAOuF,CAAG,CAExB,CAAC,EAED,SAASgD,KACR,OAAOvK,KAAKqF,OACb,CAiIA,SAASgd,GAAuBnY,EAAOoY,GACtCrY,EAAe,EAAG,CAACC,EAAOA,EAAM5I,QAAS,EAAGghB,CAAM,CACnD,CAuDA,SAASC,GAAqBjiB,EAAOoR,EAAMC,EAASN,EAAKC,GACxD,IAAIkR,EACJ,OAAa,MAATliB,EACIyR,GAAW/R,KAAMqR,EAAKC,CAAG,EAAEtD,MAElCwU,EAActQ,GAAY5R,EAAO+Q,EAAKC,CAAG,EAQ3C,SAAoB+G,EAAU3G,EAAMC,EAASN,EAAKC,GAC7CmR,EAAgBhR,GAAmB4G,EAAU3G,EAAMC,EAASN,EAAKC,CAAG,EACvE1C,EAAOoC,GAAcyR,EAAczU,KAAM,EAAGyU,EAAc5Q,SAAS,EAKpE,OAHA7R,KAAKgO,KAAKY,EAAKsC,eAAe,CAAC,EAC/BlR,KAAK2O,MAAMC,EAAK+J,YAAY,CAAC,EAC7B3Y,KAAK4O,KAAKA,EAAKgK,WAAW,CAAC,EACpB5Y,IACR,EAZoBW,KAAKX,KAAMM,EAF5BoR,EADU8Q,EAAP9Q,EACI8Q,EAE4B9Q,EAAMC,EAASN,EAAKC,CAAG,EAE7D,CA5EArH,EAAe,EAAG,CAAC,KAAM,GAAI,EAAG,WAC/B,OAAOjK,KAAKqY,SAAS,EAAI,GAC1B,CAAC,EAEDpO,EAAe,EAAG,CAAC,KAAM,GAAI,EAAG,WAC/B,OAAOjK,KAAK0iB,YAAY,EAAI,GAC7B,CAAC,EAMDL,GAAuB,OAAQ,UAAU,EACzCA,GAAuB,QAAS,UAAU,EAC1CA,GAAuB,OAAQ,aAAa,EAC5CA,GAAuB,QAAS,aAAa,EAI7C7Z,EAAa,WAAY,IAAI,EAC7BA,EAAa,cAAe,IAAI,EAIhCY,EAAgB,WAAY,CAAC,EAC7BA,EAAgB,cAAe,CAAC,EAIhCiD,EAAc,IAAKL,EAAW,EAC9BK,EAAc,IAAKL,EAAW,EAC9BK,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,OAAQR,GAAWN,EAAM,EACvCc,EAAc,OAAQR,GAAWN,EAAM,EACvCc,EAAc,QAASP,GAAWN,EAAM,EACxCa,EAAc,QAASP,GAAWN,EAAM,EAExC4B,GAAkB,CAAC,OAAQ,QAAS,OAAQ,SAAU,SAAU9M,EAAOoR,EAAMlM,EAAQ0E,GACpFwH,EAAKxH,EAAMN,OAAO,EAAG,CAAC,GAAK3D,EAAM3F,CAAK,CACvC,CAAC,EAED8M,GAAkB,CAAC,KAAM,MAAO,SAAU9M,EAAOoR,EAAMlM,EAAQ0E,GAC9DwH,EAAKxH,GAAShK,EAAMiO,kBAAkB7N,CAAK,CAC5C,CAAC,EA8CD2J,EAAe,IAAK,EAAG,KAAM,SAAS,EAItCzB,EAAa,UAAW,GAAG,EAI3BY,EAAgB,UAAW,CAAC,EAI5BiD,EAAc,IAAKjB,CAAM,EACzB+B,EAAc,IAAK,SAAU7M,EAAOoK,GACnCA,EAAM6C,GAA8B,GAApBtH,EAAM3F,CAAK,EAAI,EAChC,CAAC,EAUD2J,EAAe,IAAK,CAAC,KAAM,GAAI,KAAM,MAAM,EAI3CzB,EAAa,OAAQ,GAAG,EAGxBY,EAAgB,OAAQ,CAAC,EAIzBiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrCgB,EAAc,KAAM,SAAUG,EAAUxK,GAEvC,OAAOwK,EAAWxK,EAAO2gB,yBAA2B3gB,EAAO4gB,cAAgB5gB,EAAO6gB,8BACnF,CAAC,EAED1V,EAAc,CAAC,IAAK,MAAOK,CAAI,EAC/BL,EAAc,KAAM,SAAU7M,EAAOoK,GACpCA,EAAM8C,GAAQvH,EAAM3F,EAAMqK,MAAMc,CAAS,EAAE,EAAE,CAC9C,CAAC,EAIGqX,GAAmBvU,GAAW,OAAQ,CAAA,CAAI,EAI9CtE,EAAe,MAAO,CAAC,OAAQ,GAAI,OAAQ,WAAW,EAItDzB,EAAa,YAAa,KAAK,EAG/BY,EAAgB,YAAa,CAAC,EAI9BiD,EAAc,MAAOT,EAAS,EAC9BS,EAAc,OAAQf,EAAM,EAC5B6B,EAAc,CAAC,MAAO,QAAS,SAAU7M,EAAOoK,EAAOlF,GACtDA,EAAO8T,WAAarT,EAAM3F,CAAK,CAChC,CAAC,EAaD2J,EAAe,IAAK,CAAC,KAAM,GAAI,EAAG,QAAQ,EAI1CzB,EAAa,SAAU,GAAG,EAI1BY,EAAgB,SAAU,EAAE,EAI5BiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrC8B,EAAc,CAAC,IAAK,MAAOO,CAAM,EAIjC,IAoEIxD,GApEA6Y,GAAexU,GAAW,UAAW,CAAA,CAAK,EAsB1CyU,IAlBJ/Y,EAAe,IAAK,CAAC,KAAM,GAAI,EAAG,QAAQ,EAI1CzB,EAAa,SAAU,GAAG,EAI1BY,EAAgB,SAAU,EAAE,EAI5BiD,EAAc,IAAKZ,CAAS,EAC5BY,EAAc,KAAMZ,EAAWJ,CAAM,EACrC8B,EAAc,CAAC,IAAK,MAAOQ,CAAM,EAIdY,GAAW,UAAW,CAAA,CAAK,GA+C9C,IA3CAtE,EAAe,IAAK,EAAG,EAAG,WACzB,MAAO,CAAC,EAAEjK,KAAK2d,YAAY,EAAI,IAChC,CAAC,EAED1T,EAAe,EAAG,CAAC,KAAM,GAAI,EAAG,WAC/B,MAAO,CAAC,EAAEjK,KAAK2d,YAAY,EAAI,GAChC,CAAC,EAED1T,EAAe,EAAG,CAAC,MAAO,GAAI,EAAG,aAAa,EAC9CA,EAAe,EAAG,CAAC,OAAQ,GAAI,EAAG,WACjC,OAA4B,GAArBjK,KAAK2d,YAAY,CACzB,CAAC,EACD1T,EAAe,EAAG,CAAC,QAAS,GAAI,EAAG,WAClC,OAA4B,IAArBjK,KAAK2d,YAAY,CACzB,CAAC,EACD1T,EAAe,EAAG,CAAC,SAAU,GAAI,EAAG,WACnC,OAA4B,IAArBjK,KAAK2d,YAAY,CACzB,CAAC,EACD1T,EAAe,EAAG,CAAC,UAAW,GAAI,EAAG,WACpC,OAA4B,IAArBjK,KAAK2d,YAAY,CACzB,CAAC,EACD1T,EAAe,EAAG,CAAC,WAAY,GAAI,EAAG,WACrC,OAA4B,IAArBjK,KAAK2d,YAAY,CACzB,CAAC,EACD1T,EAAe,EAAG,CAAC,YAAa,GAAI,EAAG,WACtC,OAA4B,IAArBjK,KAAK2d,YAAY,CACzB,CAAC,EAIDnV,EAAa,cAAe,IAAI,EAIhCY,EAAgB,cAAe,EAAE,EAIjCiD,EAAc,IAAKT,GAAWR,CAAM,EACpCiB,EAAc,KAAMT,GAAWP,CAAM,EACrCgB,EAAc,MAAOT,GAAWN,EAAM,EAGjCpB,GAAQ,OAAQA,GAAM5I,QAAU,EAAG4I,IAAS,IAChDmC,EAAcnC,GAAO6B,EAAa,EAGnC,SAASkX,GAAQ3iB,EAAOoK,GACvBA,EAAMkD,IAAe3H,EAAuB,KAAhB,KAAO3F,EAAa,CACjD,CAEA,IAAK4J,GAAQ,IAAKA,GAAM5I,QAAU,EAAG4I,IAAS,IAC7CiD,EAAcjD,GAAO+Y,EAAO,EAIzBC,GAAoB3U,GAAW,eAAgB,CAAA,CAAK,EAIxDtE,EAAe,IAAK,EAAG,EAAG,UAAU,EACpCA,EAAe,KAAM,EAAG,EAAG,UAAU,EAYjCkZ,EAAQ5d,EAAO9E,UAgHnB,SAAS2iB,GAAmB3I,GAC3B,OAAOA,CACR,CAhHA0I,EAAMpF,IAAMA,GACZoF,EAAMrO,SAxpBN,SAAoBuO,EAAMC,GAGzB,IACCC,EAAMtD,GAAgBxH,EADb4K,GAAQnK,EAAY,EACFlZ,IAAI,EAAEwjB,QAAQ,KAAK,EAC9CzhB,EAAS7B,EAAMujB,eAAezjB,KAAMujB,CAAG,GAAK,WAEzCzY,EAASwY,IAAYvb,EAAWub,EAAQvhB,EAAO,EAAIuhB,EAAQvhB,GAAQpB,KAAKX,KAAMyY,CAAG,EAAI6K,EAAQvhB,IAEjG,OAAO/B,KAAK+B,OAAO+I,GAAU9K,KAAKuK,WAAW,EAAEuK,SAAS/S,EAAQ/B,KAAMkZ,EAAYT,CAAG,CAAC,CAAC,CACxF,EA+oBA0K,EAAM/C,MA7oBN,WACC,OAAO,IAAI7a,EAAOvF,IAAI,CACvB,EA4oBAmjB,EAAMhD,KAplBN,SAAc7f,EAAOwI,EAAO4a,GAC3B,IAAIC,EAAMC,EAAkB9Y,EAE5B,GAAI,CAAC9K,KAAKqD,QAAQ,EACjB,OAAOe,IAKR,GAAI,EAFJuf,EAAO1D,GAAgB3f,EAAON,IAAI,GAExBqD,QAAQ,EACjB,OAAOe,IAOR,OAJAwf,EAAoD,KAAvCD,EAAKjE,UAAU,EAAI1f,KAAK0f,UAAU,GAE/C5W,EAAQD,EAAeC,CAAK,GAG3B,IAAK,OACJgC,EAAS+W,GAAU7hB,KAAM2jB,CAAI,EAAI,GACjC,MACD,IAAK,QACJ7Y,EAAS+W,GAAU7hB,KAAM2jB,CAAI,EAC7B,MACD,IAAK,UACJ7Y,EAAS+W,GAAU7hB,KAAM2jB,CAAI,EAAI,EACjC,MACD,IAAK,SACJ7Y,GAAU9K,KAAO2jB,GAAQ,IACzB,MACD,IAAK,SACJ7Y,GAAU9K,KAAO2jB,GAAQ,IACzB,MACD,IAAK,OACJ7Y,GAAU9K,KAAO2jB,GAAQ,KACzB,MACD,IAAK,MACJ7Y,GAAU9K,KAAO2jB,EAAOC,GAAa,MACrC,MACD,IAAK,OACJ9Y,GAAU9K,KAAO2jB,EAAOC,GAAa,OACrC,MACD,QACC9Y,EAAS9K,KAAO2jB,CAClB,CAEA,OAAOD,EAAU5Y,EAASlF,EAASkF,CAAM,CAC1C,EAsiBAqY,EAAMU,MAlXN,SAAe/a,GAEd,OAAc9E,KAAAA,KADd8E,EAAQD,EAAeC,CAAK,IACS,gBAAVA,EACnB9I,KAQDA,KAAKwjB,QAHX1a,EADa,SAAVA,EACK,MAGWA,CAAK,EACvBiV,IAAI,EAAa,YAAVjV,EAAsB,OAASA,CAAK,EAC3C8Y,SAAS,EAAG,IAAI,CACnB,EAqWAuB,EAAMphB,OA5dN,SAAgB+hB,GAKf,OAJKA,EAAAA,IACU9jB,KAAKygB,MAAM,EAAIvgB,EAAMiiB,iBAAmBjiB,EAAMgiB,eAEzDpX,EAASN,GAAaxK,KAAM8jB,CAAW,EACpC9jB,KAAKuK,WAAW,EAAEwZ,WAAWjZ,CAAM,CAC3C,EAudAqY,EAAMxe,KArdN,SAAc0e,EAAMW,GACnB,OAAIhkB,KAAKqD,QAAQ,IAAOqC,EAAS2d,CAAI,GAAKA,EAAKhgB,QAAQ,GAAM6V,EAAYmK,CAAI,EAAEhgB,QAAQ,GAC/Eud,EAAe,CAAElc,GAAI1E,KAAM2E,KAAM0e,CAAK,CAAC,EAAErhB,OAAOhC,KAAKgC,OAAO,CAAC,EAAEiiB,SAAS,CAACD,CAAa,EAEtFhkB,KAAKuK,WAAW,EAAEQ,YAAY,CAEvC,EAgdAoY,EAAMe,QA9cN,SAAiBF,GAChB,OAAOhkB,KAAK2E,KAAKuU,EAAY,EAAG8K,CAAa,CAC9C,EA6cAb,EAAMze,GA3cN,SAAY2e,EAAMW,GACjB,OAAIhkB,KAAKqD,QAAQ,IAAOqC,EAAS2d,CAAI,GAAKA,EAAKhgB,QAAQ,GAAM6V,EAAYmK,CAAI,EAAEhgB,QAAQ,GAC/Eud,EAAe,CAAEjc,KAAM3E,KAAM0E,GAAI2e,CAAK,CAAC,EAAErhB,OAAOhC,KAAKgC,OAAO,CAAC,EAAEiiB,SAAS,CAACD,CAAa,EAEtFhkB,KAAKuK,WAAW,EAAEQ,YAAY,CAEvC,EAscAoY,EAAMgB,MApcN,SAAeH,GACd,OAAOhkB,KAAK0E,GAAGwU,EAAY,EAAG8K,CAAa,CAC5C,EAmcAb,EAAMzU,IAr6FN,SAAmB5F,GAElB,OAAIf,EAAW/H,KADf8I,EAAQD,EAAeC,CAAK,EACF,EAClB9I,KAAK8I,GAAO,EAEb9I,IACR,EAg6FAmjB,EAAMiB,UA9TN,WACC,OAAOhiB,EAAgBpC,IAAI,EAAE0C,QAC9B,EA6TAygB,EAAM/B,QAnpBN,SAAiB9gB,EAAOwI,GAEvB,OADIub,EAAa3e,EAASpF,CAAK,EAAIA,EAAQ4Y,EAAY5Y,CAAK,EACvD,EAACN,CAAAA,KAAKqD,QAAQ,GAAKghB,CAAAA,EAAWhhB,QAAQ,KAI7B,iBADdyF,EAAQD,EAAgBhI,EAAYiI,CAAK,EAAY,cAARA,CAAqB,GAE1D9I,KAAK6B,QAAQ,EAAIwiB,EAAWxiB,QAAQ,EAEpCwiB,EAAWxiB,QAAQ,EAAI7B,KAAKogB,MAAM,EAAEoD,QAAQ1a,CAAK,EAAEjH,QAAQ,EAEpE,EAyoBAshB,EAAMlC,SAvoBN,SAAkB3gB,EAAOwI,GAExB,OADIub,EAAa3e,EAASpF,CAAK,EAAIA,EAAQ4Y,EAAY5Y,CAAK,EACvD,EAACN,CAAAA,KAAKqD,QAAQ,GAAKghB,CAAAA,EAAWhhB,QAAQ,KAI7B,iBADdyF,EAAQD,EAAgBhI,EAAYiI,CAAK,EAAY,cAARA,CAAqB,GAE1D9I,KAAK6B,QAAQ,EAAIwiB,EAAWxiB,QAAQ,EAEpC7B,KAAKogB,MAAM,EAAEyD,MAAM/a,CAAK,EAAEjH,QAAQ,EAAIwiB,EAAWxiB,QAAQ,EAElE,EA6nBAshB,EAAMmB,UA3nBN,SAAmB3f,EAAMD,EAAIoE,EAAOyb,GAEnC,OAA2B,OAD3BA,EAAcA,GAAe,MACT,GAAavkB,KAAKohB,QAAQzc,EAAMmE,CAAK,EAAI,CAAC9I,KAAKihB,SAAStc,EAAMmE,CAAK,KAA0B,MAAnByb,EAAY,GAAavkB,KAAKihB,SAASvc,EAAIoE,CAAK,EAAI,CAAC9I,KAAKohB,QAAQ1c,EAAIoE,CAAK,EAC1K,EAynBAqa,EAAMqB,OAvnBN,SAAgBlkB,EAAOwI,GACtB,IAAIub,EAAa3e,EAASpF,CAAK,EAAIA,EAAQ4Y,EAAY5Y,CAAK,EAE5D,MAAK,EAACN,CAAAA,KAAKqD,QAAQ,GAAKghB,CAAAA,EAAWhhB,QAAQ,KAI7B,iBADdyF,EAAQD,EAAeC,GAAS,aAAa,GAErC9I,KAAK6B,QAAQ,IAAMwiB,EAAWxiB,QAAQ,GAE7C4iB,EAAUJ,EAAWxiB,QAAQ,EACtB7B,KAAKogB,MAAM,EAAEoD,QAAQ1a,CAAK,EAAEjH,QAAQ,GAAK4iB,GAAWA,GAAWzkB,KAAKogB,MAAM,EAAEyD,MAAM/a,CAAK,EAAEjH,QAAQ,GAE1G,EA2mBAshB,EAAMuB,cAzmBN,SAAuBpkB,EAAOwI,GAC7B,OAAO9I,KAAKwkB,OAAOlkB,EAAOwI,CAAK,GAAK9I,KAAKohB,QAAQ9gB,EAAOwI,CAAK,CAC9D,EAwmBAqa,EAAMwB,eAtmBN,SAAwBrkB,EAAOwI,GAC9B,OAAO9I,KAAKwkB,OAAOlkB,EAAOwI,CAAK,GAAK9I,KAAKihB,SAAS3gB,EAAOwI,CAAK,CAC/D,EAqmBAqa,EAAM9f,QA7UN,WACC,OAAOA,EAAQrD,IAAI,CACpB,EA4UAmjB,EAAMf,KAAOA,GACbe,EAAMnhB,OAASA,GACfmhB,EAAM5Y,WAAaA,GACnB4Y,EAAMxZ,IAAMuU,GACZiF,EAAMzc,IAAMsX,GACZmF,EAAMyB,aA/UN,WACC,OAAOhjB,EAAO,GAAIQ,EAAgBpC,IAAI,CAAC,CACxC,EA8UAmjB,EAAM9a,IA56FN,SAAmBS,EAAO1C,GACzB,GAAqB,UAAjB,OAAO0C,EAGV,IADA,IAAI+b,EAxTN,SAA6BC,GAC5B,IACSC,EADLjc,EAAQ,GACZ,IAASic,KAAKD,EACbhc,EAAMvH,KAAK,CAAEkH,KAAMsc,EAAG1b,SAAUF,GAAW4b,EAAG,CAAC,EAKhD,OAHAjc,EAAMsH,KAAK,SAAU3O,EAAGC,GACvB,OAAOD,EAAE4H,SAAW3H,EAAE2H,QACvB,CAAC,EACMP,CACR,EA8SEA,EAAQC,GAAqBD,CAAK,CACS,EAClCzH,EAAI,EAAGA,EAAIwjB,EAAYvjB,OAAQD,CAAC,GACxCrB,KAAK6kB,EAAYxjB,GAAGoH,MAAMK,EAAM+b,EAAYxjB,GAAGoH,KAAK,OAIrD,GAAIV,EAAW/H,KADf8I,EAAQD,EAAeC,CAAK,EACF,EACzB,OAAO9I,KAAK8I,GAAO1C,CAAK,EAG1B,OAAOpG,IACR,EA+5FAmjB,EAAMK,QApbN,SAAiB1a,GAIhB,OAHAA,EAAQD,EAAeC,CAAK,GAI3B,IAAK,OACJ9I,KAAK2O,MAAM,CAAC,EAEb,IAAK,UACL,IAAK,QACJ3O,KAAK4O,KAAK,CAAC,EAEZ,IAAK,OACL,IAAK,UACL,IAAK,MACL,IAAK,OACJ5O,KAAK8T,MAAM,CAAC,EAEb,IAAK,OACJ9T,KAAKgU,QAAQ,CAAC,EAEf,IAAK,SACJhU,KAAKmU,QAAQ,CAAC,EAEf,IAAK,SACJnU,KAAK6e,aAAa,CAAC,CACrB,CAeA,MAZc,SAAV/V,GACH9I,KAAK2R,QAAQ,CAAC,EAED,YAAV7I,GACH9I,KAAKglB,WAAW,CAAC,EAIJ,YAAVlc,GACH9I,KAAK2O,MAAqC,EAA/B7I,KAAKE,MAAMhG,KAAK2O,MAAM,EAAI,CAAC,CAAK,EAGrC3O,IACR,EA2YAmjB,EAAMvB,SAAWA,GACjBuB,EAAM8B,QA9WN,WACC,IAAI5iB,EAAIrC,KACR,MAAO,CAACqC,EAAE2L,KAAK,EAAG3L,EAAEsM,MAAM,EAAGtM,EAAEuM,KAAK,EAAGvM,EAAEwa,KAAK,EAAGxa,EAAEob,OAAO,EAAGpb,EAAEqb,OAAO,EAAGrb,EAAEsb,YAAY,EACxF,EA4WAwF,EAAM+B,SA1WN,WACC,IAAI7iB,EAAIrC,KACR,MAAO,CACNwe,MAAOnc,EAAE2L,KAAK,EACdkB,OAAQ7M,EAAEsM,MAAM,EAChBC,KAAMvM,EAAEuM,KAAK,EACbkF,MAAOzR,EAAEyR,MAAM,EACfE,QAAS3R,EAAE2R,QAAQ,EACnBG,QAAS9R,EAAE8R,QAAQ,EACnB0K,aAAcxc,EAAEwc,aAAa,CAC9B,CACD,EAgWAsE,EAAMgC,OApXN,WACC,OAAO,IAAInkB,KAAKhB,KAAK6B,QAAQ,CAAC,CAC/B,EAmXAshB,EAAMiC,YAliBN,SAAqBC,GACpB,IAIIhjB,EAJJ,OAAKrC,KAAKqD,QAAQ,GAIdhB,GADAF,EAAqB,CAAA,IAAfkjB,GACIrlB,KAAKogB,MAAM,EAAEje,IAAI,EAAInC,MAC7BgO,KAAK,EAAI,GAAgB,KAAX3L,EAAE2L,KAAK,EACnBxD,GAAanI,EAAGF,EAAM,iCAAmC,8BAA8B,EAE3F4F,EAAW/G,KAAKP,UAAU2kB,WAAW,EAEpCjjB,EACInC,KAAKmlB,OAAO,EAAEC,YAAY,EAE1B,IAAIpkB,KAAKhB,KAAK4D,GAAG/B,QAAQ,CAAC,EAAEujB,YAAY,EAAExa,QAAQ,IAAKJ,GAAanI,EAAG,GAAG,CAAC,EAG7EmI,GAAanI,EAAGF,EAAM,+BAAiC,4BAA4B,EAflF,IAgBT,EAihBAghB,EAAMmC,QAzgBN,WACC,IAIIC,EAKAC,EACAxX,EAVJ,OAAKhO,KAAKqD,QAAQ,GAGdiH,EAAO,SACPib,EAAO,GACNvlB,KAAKylB,QAAQ,IACjBnb,EAA4B,IAArBtK,KAAK0f,UAAU,EAAU,aAAe,mBAC/C6F,EAAO,KAEJC,EAAS,IAAMlb,EAAO,MACtB0D,EAAO,GAAKhO,KAAKgO,KAAK,GAAKhO,KAAKgO,KAAK,GAAK,KAAO,OAAS,SAIvDhO,KAAK+B,OAAOyjB,EAASxX,EAHb,yBACFuX,EAAO,OAEgC,GAb5C,qBAAuBvlB,KAAK+E,GAAK,MAc1C,EA0fAoe,EAAMuC,OAjWN,WAEC,OAAO1lB,KAAKqD,QAAQ,EAAIrD,KAAKolB,YAAY,EAAI,IAC9C,EA+VAjC,EAAMziB,SAziBN,WACC,OAAOV,KAAKogB,MAAM,EAAEpe,OAAO,IAAI,EAAED,OAAO,kCAAkC,CAC3E,EAwiBAohB,EAAMwC,KA7XN,WACC,OAAO7f,KAAKE,MAAMhG,KAAK6B,QAAQ,EAAI,GAAI,CACxC,EA4XAshB,EAAMthB,QAlYN,WACC,OAAO7B,KAAK4D,GAAG/B,QAAQ,EAA0B,KAArB7B,KAAKoF,SAAW,EAC7C,EAiYA+d,EAAMyC,aApVN,WACC,MAAO,CACNtlB,MAAON,KAAK+E,GACZhD,OAAQ/B,KAAKgF,GACbhD,OAAQhC,KAAKqF,QACbuY,MAAO5d,KAAKmF,OACZlD,OAAQjC,KAAK+D,OACd,CACD,EA+UAof,EAAMnV,KAAOM,GACb6U,EAAMlV,WAp+FN,WACC,OAAOA,GAAWjO,KAAKgO,KAAK,CAAC,CAC9B,EAq+FAmV,EAAM9K,SA/RN,SAAwB/X,GACvB,OAAOiiB,GAAqB5hB,KAAKX,KAAMM,EAAON,KAAK0R,KAAK,EAAG1R,KAAK2R,QAAQ,EAAG3R,KAAKuK,WAAW,EAAE4O,MAAM9H,IAAKrR,KAAKuK,WAAW,EAAE4O,MAAM7H,GAAG,CACpI,EA8RA6R,EAAMT,YA5RN,SAA2BpiB,GAC1B,OAAOiiB,GAAqB5hB,KAAKX,KAAMM,EAAON,KAAK6lB,QAAQ,EAAG7lB,KAAKglB,WAAW,EAAG,EAAG,CAAC,CACtF,EA6RA7B,EAAMzE,QAAUyE,EAAM1E,SAtOtB,SAAuBne,GACtB,OAAgB,MAATA,EAAgBwF,KAAKC,MAAM/F,KAAK2O,MAAM,EAAI,GAAK,CAAC,EAAI3O,KAAK2O,MAAoB,GAAbrO,EAAQ,GAAUN,KAAK2O,MAAM,EAAI,CAAE,CAC3G,EAuOAwU,EAAMxU,MAAQiB,GACduT,EAAMtU,YAhuFN,WACC,OAAOA,GAAY7O,KAAKgO,KAAK,EAAGhO,KAAK2O,MAAM,CAAC,CAC7C,EAiuFAwU,EAAMzR,KAAOyR,EAAMxE,MA7gFnB,SAAoBre,GACnB,IAAIoR,EAAO1R,KAAKuK,WAAW,EAAEmH,KAAK1R,IAAI,EACtC,OAAgB,MAATM,EAAgBoR,EAAO1R,KAAK+d,IAAqB,GAAhBzd,EAAQoR,GAAW,GAAG,CAC/D,EA2gFAyR,EAAM0C,QAAU1C,EAAM2C,SAzgFtB,SAAuBxlB,GACtB,IAAIoR,EAAOK,GAAW/R,KAAM,EAAG,CAAC,EAAE0R,KAClC,OAAgB,MAATpR,EAAgBoR,EAAO1R,KAAK+d,IAAqB,GAAhBzd,EAAQoR,GAAW,GAAG,CAC/D,EAugFAyR,EAAMjR,YAhSN,WACC,IAAI6T,EAAW/lB,KAAKuK,WAAW,EAAE4O,MACjC,OAAOjH,GAAYlS,KAAKgO,KAAK,EAAG+X,EAAS1U,IAAK0U,EAASzU,GAAG,CAC3D,EA8RA6R,EAAM6C,eArSN,WACC,OAAO9T,GAAYlS,KAAKgO,KAAK,EAAG,EAAG,CAAC,CACrC,EAsSAmV,EAAMvU,KAAOkU,GACbK,EAAM7P,IAAM6P,EAAMvE,KApzElB,SAAyBte,GACxB,IAGIgT,EA7JiBhT,EAAO0B,EA0J5B,OAAKhC,KAAKqD,QAAQ,GAGdiQ,EAAMtT,KAAKmF,OAASnF,KAAK4D,GAAG4N,UAAU,EAAIxR,KAAK4D,GAAG8V,OAAO,EAChD,MAATpZ,GA9JiBA,EA+JCA,EA/JM0B,EA+JChC,KAAKuK,WAAW,EAA5CjK,EA9JoB,UAAjB,OAAOA,EACHA,EAGHqD,MAAMrD,CAAK,EAKK,UAAjB,OADJA,EAAQ0B,EAAO0Q,cAAcpS,CAAK,GAE1BA,EAGD,KARC8N,SAAS9N,EAAO,EAAE,EA0JlBN,KAAK+d,IAAIzd,EAAQgT,EAAK,GAAG,GAEzBA,GAPS,MAAThT,EAAgBN,KAAOoE,GAShC,EA0yEA+e,EAAMxR,QAxyEN,SAA+BrR,GAC9B,IAGIqR,EAHJ,OAAK3R,KAAKqD,QAAQ,GAGdsO,GAAW3R,KAAKsT,IAAI,EAAI,EAAItT,KAAKuK,WAAW,EAAE4O,MAAM9H,KAAO,EAC/C,MAAT/Q,EAAgBqR,EAAU3R,KAAK+d,IAAIzd,EAAQqR,EAAS,GAAG,GAH7C,MAATrR,EAAgBN,KAAOoE,GAIhC,EAmyEA+e,EAAM6B,WAjyEN,SAA4B1kB,GAC3B,IA9JwBA,EAAO0B,EA8J/B,OAAKhC,KAAKqD,QAAQ,EAQL,MAAT/C,GAtKoBA,EAuKOA,EAvKA0B,EAuKOhC,KAAKuK,WAAW,EAAjDoH,EAtKgB,UAAjB,OAAOrR,EACH0B,EAAO0Q,cAAcpS,CAAK,EAAI,GAAK,EAEpCqD,MAAMrD,CAAK,EAAI,KAAOA,EAoKrBN,KAAKsT,IAAItT,KAAKsT,IAAI,EAAI,EAAI3B,EAAUA,EAAU,CAAC,GAE/C3R,KAAKsT,IAAI,GAAK,EAXL,MAAThT,EAAgBN,KAAOoE,GAahC,EAmxEA+e,EAAMtR,UA/LN,SAAyBvR,GACxB,IAAIuR,EAAY/L,KAAKyZ,OAAOvf,KAAKogB,MAAM,EAAEoD,QAAQ,KAAK,EAAIxjB,KAAKogB,MAAM,EAAEoD,QAAQ,MAAM,GAAK,KAAK,EAAI,EACnG,OAAgB,MAATljB,EAAgBuR,EAAY7R,KAAK+d,IAAIzd,EAAQuR,EAAW,GAAG,CACnE,EA+LAsR,EAAMtG,KAAOsG,EAAMrP,MAAQc,EAG3BuO,EAAM1F,OAAS0F,EAAMnP,QAAU+O,GAG/BI,EAAMzF,OAASyF,EAAMhP,QAAU6O,GAG/BG,EAAMxF,YAAcwF,EAAMtE,aAAeqE,GAGzCC,EAAMzD,UA5gCN,SAAsBpf,EAAO2lB,EAAeC,GAC3C,IACCC,EADG3G,EAASxf,KAAKoF,SAAW,EAE7B,GAAI,CAACpF,KAAKqD,QAAQ,EACjB,OAAgB,MAAT/C,EAAgBN,KAAOoE,IAE/B,GAAa,MAAT9D,EA4BH,OAAON,KAAKmF,OAASqa,EAASe,GAAcvgB,IAAI,EA3BhD,GAAqB,UAAjB,OAAOM,GAEV,GAAc,QADdA,EAAQsf,GAAiB1T,GAAkB5L,CAAK,GAE/C,OAAON,IACR,MACU8F,KAAKc,IAAItG,CAAK,EAAI,IAAM,CAAC4lB,IACnC5lB,GAAgB,IAmBjB,MAjBI,CAACN,KAAKmF,QAAU8gB,IACnBE,EAAc5F,GAAcvgB,IAAI,GAEjCA,KAAKoF,QAAU9E,EACfN,KAAKmF,OAAS,CAAA,EACK,MAAfghB,GACHnmB,KAAK+d,IAAIoI,EAAa,GAAG,EAEtB3G,IAAWlf,IACV,CAAC2lB,GAAiBjmB,KAAKomB,kBAC1B3E,GAAYzhB,KAAM4gB,EAAetgB,EAAQkf,EAAQ,GAAG,EAAG,EAAG,CAAA,CAAK,EACpDxf,KAAKomB,oBAChBpmB,KAAKomB,kBAAoB,CAAA,EACzBlmB,EAAMuF,aAAazF,KAAM,CAAA,CAAI,EAC7BA,KAAKomB,kBAAoB,OAGpBpmB,IAIT,EAy+BAmjB,EAAMhhB,IAz9BN,SAAwB8jB,GACvB,OAAOjmB,KAAK0f,UAAU,EAAGuG,CAAa,CACvC,EAw9BA9C,EAAM7C,MAt9BN,SAA0B2F,GASzB,OARIjmB,KAAKmF,SACRnF,KAAK0f,UAAU,EAAGuG,CAAa,EAC/BjmB,KAAKmF,OAAS,CAAA,EAEV8gB,IACHjmB,KAAK4hB,SAASrB,GAAcvgB,IAAI,EAAG,GAAG,EAGjCA,IACR,EA68BAmjB,EAAMkD,UA38BN,WACC,IAGKC,EAOL,OAViB,MAAbtmB,KAAKkF,KACRlF,KAAK0f,UAAU1f,KAAKkF,KAAM,CAAA,EAAO,CAAA,CAAI,EACR,UAAnB,OAAOlF,KAAK+E,KAET,OADTuhB,EAAQ1G,GAAiB3T,GAAajM,KAAK+E,EAAE,GAEhD/E,KAAK0f,UAAU4G,CAAK,EAEpBtmB,KAAK0f,UAAU,EAAG,CAAA,CAAI,GAGjB1f,IACR,EAg8BAmjB,EAAMoD,qBA97BN,SAA8BjmB,GAC7B,MAAKN,CAAAA,CAAAA,KAAKqD,QAAQ,IAGlB/C,EAAQA,EAAQ4Y,EAAY5Y,CAAK,EAAEof,UAAU,EAAI,GAEzC1f,KAAK0f,UAAU,EAAIpf,GAAS,IAAO,EAC5C,EAw7BA6iB,EAAMqD,MAt7BN,WACC,OAAOxmB,KAAK0f,UAAU,EAAI1f,KAAKogB,MAAM,EAAEzR,MAAM,CAAC,EAAE+Q,UAAU,GAAK1f,KAAK0f,UAAU,EAAI1f,KAAKogB,MAAM,EAAEzR,MAAM,CAAC,EAAE+Q,UAAU,CACnH,EAq7BAyD,EAAMsC,QA/5BN,WACC,MAAOzlB,CAAAA,CAAAA,KAAKqD,QAAQ,GAAI,CAACrD,KAAKmF,MAC/B,EA85BAge,EAAMsD,YA55BN,WACC,MAAOzmB,CAAAA,CAAAA,KAAKqD,QAAQ,GAAIrD,KAAKmF,MAC9B,EA25BAge,EAAM1C,MAAQA,GACd0C,EAAMvF,MAAQ6C,GAGd0C,EAAMuD,SAtGN,WACC,OAAO1mB,KAAKmF,OAAS,MAAQ,EAC9B,EAqGAge,EAAMwD,SAnGN,WACC,OAAO3mB,KAAKmF,OAAS,6BAA+B,EACrD,EAoGAge,EAAMyD,MAAQ1f,EAAU,kDAAmD4b,EAAgB,EAC3FK,EAAMjU,OAAShI,EAAU,mDAAoD0I,EAAW,EACxFuT,EAAM3E,MAAQtX,EAAU,iDAAkDoH,EAAU,EACpF6U,EAAMoC,KAAOre,EAAU,2GAz/BvB,SAAoB5G,EAAO2lB,GAC1B,OAAa,MAAT3lB,GAKHN,KAAK0f,UAHJpf,EADoB,UAAjB,OAAOA,EACF,CAACA,EAGKA,EAAO2lB,CAAa,EAE5BjmB,MAEA,CAACA,KAAK0f,UAAU,CAEzB,CA6+B6I,EAC7IyD,EAAM0D,aAAe3f,EAAU,0GAj8B/B,WACC,IAII+Q,EAMCgG,EAML,OAhBKpd,EAAYb,KAAK8mB,aAAa,IAMnCriB,EAFIwT,EAAI,GAEMjY,IAAI,GAClBiY,EAAI+E,GAAc/E,CAAC,GAEbL,IACDqG,GAAQhG,EAAE9S,OAASrD,EAAkBoX,GAARjB,EAAEL,EAAE,EACrC5X,KAAK8mB,cAAgB9mB,KAAKqD,QAAQ,GAA4C,EAAvCiD,EAAc2R,EAAEL,GAAIqG,EAAMgH,QAAQ,CAAC,GAE1EjlB,KAAK8mB,cAAgB,CAAA,GAGf9mB,KAAK8mB,aACb,CA+6BqK,EAcjKC,EAAU3e,EAAO3H,UAsCrB,SAASumB,GAAMjlB,EAAQklB,EAAOC,EAAOC,GACpC,IAAInlB,EAASiV,GAAU,EACnB9U,EAAML,EAAU,EAAEuG,IAAI8e,EAAQF,CAAK,EACvC,OAAOjlB,EAAOklB,GAAO/kB,EAAKJ,CAAM,CACjC,CAEA,SAASqlB,GAAerlB,EAAQklB,EAAOC,GAQtC,GAPIpmB,EAASiB,CAAM,IAClBklB,EAAQllB,EACRA,EAASiC,KAAAA,GAGVjC,EAASA,GAAU,GAEN,MAATklB,EACH,OAAOD,GAAMjlB,EAAQklB,EAAOC,EAAO,OAAO,EAK3C,IAFA,IACIG,EAAM,GACLhmB,EAAI,EAAGA,EAAI,GAAIA,CAAC,GACpBgmB,EAAIhmB,GAAK2lB,GAAMjlB,EAAQV,EAAG6lB,EAAO,OAAO,EAEzC,OAAOG,CACR,CAUA,SAASC,GAAiBC,EAAcxlB,EAAQklB,EAAOC,GAOrDnlB,GAN2B,WAAxB,OAAOwlB,EACNzmB,EAASiB,CAAM,IAClBklB,EAAQllB,EACRA,EAASiC,KAAAA,IAKVjC,EAASwlB,EAETA,EAAe,CAAA,EAEXzmB,EAHJmmB,EAAQllB,CAGW,IAClBklB,EAAQllB,EACRA,EAASiC,KAAAA,IAGDjC,GAAU,IAGpB,IAAIC,EAASiV,GAAU,EACtBuQ,EAAQD,EAAevlB,EAAOmX,MAAM9H,IAAM,EAE3C,GAAa,MAAT4V,EACH,OAAOD,GAAMjlB,GAASklB,EAAQO,GAAS,EAAGN,EAAO,KAAK,EAKvD,IADA,IAAIG,EAAM,GACLhmB,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACnBgmB,EAAIhmB,GAAK2lB,GAAMjlB,GAASV,EAAImmB,GAAS,EAAGN,EAAO,KAAK,EAErD,OAAOG,CACR,CAxGAN,EAAQjS,SAn6GR,SAAkBvN,EAAKsD,EAAK4N,GAE3B,OAAO1Q,EADH+C,EAAS9K,KAAKynB,UAAUlgB,IAAQvH,KAAKynB,UAAoB,QACrC,EAAI3c,EAAOnK,KAAKkK,EAAK4N,CAAG,EAAI3N,CACrD,EAi6GAic,EAAQ9b,eAt5GR,SAAwB1D,GACvB,IAAIxF,EAAS/B,KAAK0nB,gBAAgBngB,GACjCogB,EAAc3nB,KAAK0nB,gBAAgBngB,EAAIqgB,YAAY,GAEpD,OAAI7lB,GAAU,CAAC4lB,EACP5lB,GAGR/B,KAAK0nB,gBAAgBngB,GAAOogB,EAAY/c,QAAQ,mBAAoB,SAAU/F,GAC7E,OAAOA,EAAI2C,MAAM,CAAC,CACnB,CAAC,EAEMxH,KAAK0nB,gBAAgBngB,GAC7B,EA04GAwf,EAAQhc,YAt4GR,WACC,OAAO/K,KAAK6nB,YACb,EAq4GAd,EAAQ3c,QAh4GR,SAAiBvE,GAChB,OAAO7F,KAAK8nB,SAASld,QAAQ,KAAM/E,CAAM,CAC1C,EA+3GAkhB,EAAQ9J,SAAWmG,GACnB2D,EAAQhD,WAAaX,GACrB2D,EAAQnR,aA92GR,SAAsB/P,EAAQme,EAAevJ,EAAQsN,GACpD,IAAIjd,EAAS9K,KAAKgoB,cAAcvN,GAChC,OAAO1S,EAAW+C,CAAM,EAAIA,EAAOjF,EAAQme,EAAevJ,EAAQsN,CAAQ,EAAIjd,EAAOF,QAAQ,MAAO/E,CAAM,CAC3G,EA42GAkhB,EAAQkB,WA12GR,SAAoB9H,EAAMrV,GAEzB,OAAO/C,EADHhG,EAAS/B,KAAKgoB,cAAqB,EAAP7H,EAAW,SAAW,OAC9B,EAAIpe,EAAO+I,CAAM,EAAI/I,EAAO6I,QAAQ,MAAOE,CAAM,CAC1E,EAw2GAic,EAAQ1e,IAr/GR,SAAa7C,GACZ,IAAIZ,EAAMvD,EACV,IAAKA,KAAKmE,EAELuC,EADJnD,EAAOY,EAAOnE,EACK,EAClBrB,KAAKqB,GAAKuD,EAEV5E,KAAK,IAAMqB,GAAKuD,EAGlB5E,KAAKoX,QAAU5R,EAIfxF,KAAK6iB,+BAAiC,IAAInW,QAAQ1M,KAAK2iB,wBAAwBuF,QAAUloB,KAAK4iB,cAAcsF,QAAU,IAAM,UAAUA,MAAM,CAC7I,EAy+GAnB,EAAQ7X,OAj7FR,SAAsB7M,EAAGN,GACxB,OAAKM,GAGEhC,EAAQL,KAAKkf,OAAO,EAAIlf,KAAKkf,QAAqBlf,KAAKkf,SAASlf,KAAKkf,QAAQiJ,UAAY7Y,IAAkBnE,KAAKpJ,CAAM,EAAI,SAAW,eAAhGM,EAAEsM,MAAM,GAF5CtO,EAAQL,KAAKkf,OAAO,EAAIlf,KAAKkf,QAAUlf,KAAKkf,QAAoB,UAGzE,EA66FA6H,EAAQ9X,YA16FR,SAA2B5M,EAAGN,GAC7B,OAAKM,GAGEhC,EAAQL,KAAKooB,YAAY,EAAIpoB,KAAKooB,aAA0BpoB,KAAKooB,aAAa9Y,GAAiBnE,KAAKpJ,CAAM,EAAI,SAAW,eAA1EM,EAAEsM,MAAM,GAFtDtO,EAAQL,KAAKooB,YAAY,EAAIpoB,KAAKooB,aAAepoB,KAAKooB,aAAyB,UAGxF,EAs6FArB,EAAQ1X,YAx3FR,SAA2BgZ,EAAWtmB,EAAQE,GAC7C,IAAIZ,EAAQiL,EAEZ,GAAItM,KAAKsoB,kBACR,OAhDF,SAA2BD,EAAWtmB,EAAQE,GAC7C,IAAIZ,EACHknB,EACA1d,EACA2d,EAAMH,EAAUI,kBAAkB,EACnC,GAAI,CAACzoB,KAAK0oB,aAKT,IAHA1oB,KAAK0oB,aAAe,GACpB1oB,KAAK2oB,iBAAmB,GACxB3oB,KAAK4oB,kBAAoB,GACpBvnB,EAAI,EAAGA,EAAI,GAAI,EAAEA,EACrBwJ,EAAM/I,EAAU,CAAC,IAAMT,EAAE,EACzBrB,KAAK4oB,kBAAkBvnB,GAAKrB,KAAKiP,YAAYpE,EAAK,EAAE,EAAE4d,kBAAkB,EACxEzoB,KAAK2oB,iBAAiBtnB,GAAKrB,KAAKkP,OAAOrE,EAAK,EAAE,EAAE4d,kBAAkB,EAIpE,OAAIxmB,EACY,QAAXF,EAEW,CAAC,KADfwmB,EAAKla,EAAQ1N,KAAKX,KAAK4oB,kBAAmBJ,CAAG,GAC1BD,EAAK,KAGV,CAAC,KADfA,EAAKla,EAAQ1N,KAAKX,KAAK2oB,iBAAkBH,CAAG,GACzBD,EAAK,KAGV,QAAXxmB,EAEQ,CAAC,KADZwmB,EAAKla,EAAQ1N,KAAKX,KAAK4oB,kBAAmBJ,CAAG,IAK/B,CAAC,KADfD,EAAKla,EAAQ1N,KAAKX,KAAK2oB,iBAAkBH,CAAG,GACzBD,EAAK,KAGb,CAAC,KADZA,EAAKla,EAAQ1N,KAAKX,KAAK2oB,iBAAkBH,CAAG,IAK9B,CAAC,KADfD,EAAKla,EAAQ1N,KAAKX,KAAK4oB,kBAAmBJ,CAAG,GAC1BD,EAAK,IAG3B,EAM2B5nB,KAAKX,KAAMqoB,EAAWtmB,EAAQE,CAAM,EAY9D,IATKjC,KAAK0oB,eACT1oB,KAAK0oB,aAAe,GACpB1oB,KAAK2oB,iBAAmB,GACxB3oB,KAAK4oB,kBAAoB,IAMrBvnB,EAAI,EAAGA,EAAI,GAAIA,CAAC,GAAI,CAYxB,GAVAwJ,EAAM/I,EAAU,CAAC,IAAMT,EAAE,EACrBY,GAAU,CAACjC,KAAK2oB,iBAAiBtnB,KACpCrB,KAAK2oB,iBAAiBtnB,GAAK,IAAIqL,OAAO,IAAM1M,KAAKkP,OAAOrE,EAAK,EAAE,EAAED,QAAQ,IAAK,EAAE,EAAI,IAAK,GAAG,EAC5F5K,KAAK4oB,kBAAkBvnB,GAAK,IAAIqL,OAAO,IAAM1M,KAAKiP,YAAYpE,EAAK,EAAE,EAAED,QAAQ,IAAK,EAAE,EAAI,IAAK,GAAG,GAE9F3I,GAAWjC,KAAK0oB,aAAarnB,KACjCiL,EAAQ,IAAMtM,KAAKkP,OAAOrE,EAAK,EAAE,EAAI,KAAO7K,KAAKiP,YAAYpE,EAAK,EAAE,EACpE7K,KAAK0oB,aAAarnB,GAAK,IAAIqL,OAAOJ,EAAM1B,QAAQ,IAAK,EAAE,EAAG,GAAG,GAG1D3I,GAAqB,SAAXF,GAAqB/B,KAAK2oB,iBAAiBtnB,GAAG8J,KAAKkd,CAAS,EACzE,OAAOhnB,EACD,GAAIY,GAAqB,QAAXF,GAAoB/B,KAAK4oB,kBAAkBvnB,GAAG8J,KAAKkd,CAAS,EAChF,OAAOhnB,EACD,GAAI,CAACY,GAAUjC,KAAK0oB,aAAarnB,GAAG8J,KAAKkd,CAAS,EACxD,OAAOhnB,CAET,CACD,EAq1FA0lB,EAAQ3X,YAtxFR,SAAqB5C,GACpB,OAAIxM,KAAKsoB,mBACH9mB,EAAWxB,KAAM,cAAc,GACnC+P,GAAmBpP,KAAKX,IAAI,EAEzBwM,EACIxM,KAAKuQ,mBAELvQ,KAAKqQ,eAGR7O,EAAWxB,KAAM,cAAc,IACnCA,KAAKqQ,aAAeP,IAEd9P,KAAKuQ,oBAAsB/D,EAAWxM,KAAKuQ,mBAAqBvQ,KAAKqQ,aAE9E,EAuwFA0W,EAAQ5X,iBA1yFR,SAA0B3C,GACzB,OAAIxM,KAAKsoB,mBACH9mB,EAAWxB,KAAM,cAAc,GACnC+P,GAAmBpP,KAAKX,IAAI,EAEzBwM,EACIxM,KAAKwQ,wBAELxQ,KAAKsQ,oBAGR9O,EAAWxB,KAAM,mBAAmB,IACxCA,KAAKsQ,kBAAoBT,IAEnB7P,KAAKwQ,yBAA2BhE,EAAWxM,KAAKwQ,wBAA0BxQ,KAAKsQ,kBAExF,EA6xFAyW,EAAQrV,KA/mFR,SAAoB7G,GACnB,OAAOkH,GAAWlH,EAAK7K,KAAKmZ,MAAM9H,IAAKrR,KAAKmZ,MAAM7H,GAAG,EAAEI,IACxD,EA8mFAqV,EAAQ8B,eAnmFR,WACC,OAAO7oB,KAAKmZ,MAAM7H,GACnB,EAkmFAyV,EAAQ+B,eAxmFR,WACC,OAAO9oB,KAAKmZ,MAAM9H,GACnB,EAymFA0V,EAAQzU,SA//ER,SAAwBjQ,EAAGN,GAC1B,OAAKM,GAGEhC,EAAQL,KAAK+oB,SAAS,EAAI/oB,KAAK+oB,UAAqB/oB,KAAK+oB,UAAU/oB,KAAK+oB,UAAUZ,SAAShd,KAAKpJ,CAAM,EAAI,SAAW,eAA5EM,EAAEiR,IAAI,GAF9CjT,EAAQL,KAAK+oB,SAAS,EAAI/oB,KAAK+oB,UAAY/oB,KAAK+oB,UAAsB,UAG/E,EA2/EAhC,EAAQ3U,YAn/ER,SAA2B/P,GAC1B,OAAOA,EAAIrC,KAAKgpB,aAAa3mB,EAAEiR,IAAI,GAAKtT,KAAKgpB,YAC9C,EAk/EAjC,EAAQ1U,cAz/ER,SAA6BhQ,GAC5B,OAAOA,EAAIrC,KAAKipB,eAAe5mB,EAAEiR,IAAI,GAAKtT,KAAKipB,cAChD,EAw/EAlC,EAAQrU,cA96ER,SAA6BwW,EAAannB,EAAQE,GACjD,IAAIZ,EAAQiL,EAEZ,GAAItM,KAAKmpB,oBACR,OAvEF,SAA6BD,EAAannB,EAAQE,GACjD,IAAIZ,EACHknB,EACA1d,EACA2d,EAAMU,EAAYT,kBAAkB,EACrC,GAAI,CAACzoB,KAAKopB,eAKT,IAJAppB,KAAKopB,eAAiB,GACtBppB,KAAKqpB,oBAAsB,GAC3BrpB,KAAKspB,kBAAoB,GAEpBjoB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACpBwJ,EAAM/I,EAAU,CAAC,IAAM,EAAE,EAAEwR,IAAIjS,CAAC,EAChCrB,KAAKspB,kBAAkBjoB,GAAKrB,KAAKoS,YAAYvH,EAAK,EAAE,EAAE4d,kBAAkB,EACxEzoB,KAAKqpB,oBAAoBhoB,GAAKrB,KAAKqS,cAAcxH,EAAK,EAAE,EAAE4d,kBAAkB,EAC5EzoB,KAAKopB,eAAe/nB,GAAKrB,KAAKsS,SAASzH,EAAK,EAAE,EAAE4d,kBAAkB,EAIpE,OAAIxmB,EACY,SAAXF,EAEW,CAAC,KADfwmB,EAAKla,EAAQ1N,KAAKX,KAAKopB,eAAgBZ,CAAG,GACvBD,EAAK,KACH,QAAXxmB,EAEI,CAAC,KADfwmB,EAAKla,EAAQ1N,KAAKX,KAAKqpB,oBAAqBb,CAAG,GAC5BD,EAAK,KAGV,CAAC,KADfA,EAAKla,EAAQ1N,KAAKX,KAAKspB,kBAAmBd,CAAG,GAC1BD,EAAK,KAGV,SAAXxmB,EAEQ,CAAC,KADZwmB,EAAKla,EAAQ1N,KAAKX,KAAKopB,eAAgBZ,CAAG,IAK/B,CAAC,KADZD,EAAKla,EAAQ1N,KAAKX,KAAKqpB,oBAAqBb,CAAG,IAKjC,CAAC,KADfD,EAAKla,EAAQ1N,KAAKX,KAAKspB,kBAAmBd,CAAG,GAC1BD,EAAK,KACH,QAAXxmB,EAEC,CAAC,KADZwmB,EAAKla,EAAQ1N,KAAKX,KAAKqpB,oBAAqBb,CAAG,IAKpC,CAAC,KADZD,EAAKla,EAAQ1N,KAAKX,KAAKopB,eAAgBZ,CAAG,IAK5B,CAAC,KADfD,EAAKla,EAAQ1N,KAAKX,KAAKspB,kBAAmBd,CAAG,GAC1BD,EAAK,KAGb,CAAC,KADZA,EAAKla,EAAQ1N,KAAKX,KAAKspB,kBAAmBd,CAAG,IAKlC,CAAC,KADZD,EAAKla,EAAQ1N,KAAKX,KAAKopB,eAAgBZ,CAAG,IAK5B,CAAC,KADfD,EAAKla,EAAQ1N,KAAKX,KAAKqpB,oBAAqBb,CAAG,GAC5BD,EAAK,IAG3B,EAM6B5nB,KAAKX,KAAMkpB,EAAannB,EAAQE,CAAM,EAUlE,IAPKjC,KAAKopB,iBACTppB,KAAKopB,eAAiB,GACtBppB,KAAKspB,kBAAoB,GACzBtpB,KAAKqpB,oBAAsB,GAC3BrpB,KAAKupB,mBAAqB,IAGtBloB,EAAI,EAAGA,EAAI,EAAGA,CAAC,GAAI,CAcvB,GAXAwJ,EAAM/I,EAAU,CAAC,IAAM,EAAE,EAAEwR,IAAIjS,CAAC,EAC5BY,GAAU,CAACjC,KAAKupB,mBAAmBloB,KACtCrB,KAAKupB,mBAAmBloB,GAAK,IAAIqL,OAAO,IAAM1M,KAAKsS,SAASzH,EAAK,EAAE,EAAED,QAAQ,IAAK,IAAI,EAAI,IAAK,GAAG,EAClG5K,KAAKqpB,oBAAoBhoB,GAAK,IAAIqL,OAAO,IAAM1M,KAAKqS,cAAcxH,EAAK,EAAE,EAAED,QAAQ,IAAK,IAAI,EAAI,IAAK,GAAG,EACxG5K,KAAKspB,kBAAkBjoB,GAAK,IAAIqL,OAAO,IAAM1M,KAAKoS,YAAYvH,EAAK,EAAE,EAAED,QAAQ,IAAK,IAAI,EAAI,IAAK,GAAG,GAEhG5K,KAAKopB,eAAe/nB,KACxBiL,EAAQ,IAAMtM,KAAKsS,SAASzH,EAAK,EAAE,EAAI,KAAO7K,KAAKqS,cAAcxH,EAAK,EAAE,EAAI,KAAO7K,KAAKoS,YAAYvH,EAAK,EAAE,EAC3G7K,KAAKopB,eAAe/nB,GAAK,IAAIqL,OAAOJ,EAAM1B,QAAQ,IAAK,EAAE,EAAG,GAAG,GAG5D3I,GAAqB,SAAXF,GAAqB/B,KAAKupB,mBAAmBloB,GAAG8J,KAAK+d,CAAW,EAC7E,OAAO7nB,EACD,GAAIY,GAAqB,QAAXF,GAAoB/B,KAAKqpB,oBAAoBhoB,GAAG8J,KAAK+d,CAAW,EACpF,OAAO7nB,EACD,GAAIY,GAAqB,OAAXF,GAAmB/B,KAAKspB,kBAAkBjoB,GAAG8J,KAAK+d,CAAW,EACjF,OAAO7nB,EACD,GAAI,CAACY,GAAUjC,KAAKopB,eAAe/nB,GAAG8J,KAAK+d,CAAW,EAC5D,OAAO7nB,CAET,CACD,EA04EA0lB,EAAQtU,cA/1ER,SAAuBjG,GACtB,OAAIxM,KAAKmpB,qBACH3nB,EAAWxB,KAAM,gBAAgB,GACrCiT,GAAqBtS,KAAKX,IAAI,EAE3BwM,EACIxM,KAAK0T,qBAEL1T,KAAKuT,iBAGR/R,EAAWxB,KAAM,gBAAgB,IACrCA,KAAKuT,eAAiBT,IAEhB9S,KAAK0T,sBAAwBlH,EAAWxM,KAAK0T,qBAAuB1T,KAAKuT,eAElF,EAg1EAwT,EAAQvU,mBA70ER,SAA4BhG,GAC3B,OAAIxM,KAAKmpB,qBACH3nB,EAAWxB,KAAM,gBAAgB,GACrCiT,GAAqBtS,KAAKX,IAAI,EAE3BwM,EACIxM,KAAK2T,0BAEL3T,KAAKwT,sBAGRhS,EAAWxB,KAAM,qBAAqB,IAC1CA,KAAKwT,oBAAsBT,IAErB/S,KAAK2T,2BAA6BnH,EAAWxM,KAAK2T,0BAA4B3T,KAAKwT,oBAE5F,EA8zEAuT,EAAQxU,iBA3zER,SAA0B/F,GACzB,OAAIxM,KAAKmpB,qBACH3nB,EAAWxB,KAAM,gBAAgB,GACrCiT,GAAqBtS,KAAKX,IAAI,EAE3BwM,EACIxM,KAAK4T,wBAEL5T,KAAKyT,oBAGRjS,EAAWxB,KAAM,mBAAmB,IACxCA,KAAKyT,kBAAoBT,IAEnBhT,KAAK4T,yBAA2BpH,EAAWxM,KAAK4T,wBAA0B5T,KAAKyT,kBAExF,EA8yEAsT,EAAQzS,KAhpER,SAAoBhU,GAGnB,MAAgD,OAAxCA,EAAQ,IAAIsI,YAAY,EAAE4gB,OAAO,CAAC,CAC3C,EA6oEAzC,EAAQ7jB,SA1oER,SAAwB4Q,EAAOE,EAASyV,GACvC,OAAY,GAAR3V,EACI2V,EAAU,KAAO,KAEjBA,EAAU,KAAO,IAE1B,EAguEA5S,GAAmB,KAAM,CACxBlB,uBAAwB,uBACxBvL,QAAS,SAAUvE,GAClB,IAAInE,EAAImE,EAAS,GAEjB,OAAOA,GADkC,IAA/BI,EAAOJ,EAAS,IAAO,EAAE,EAAU,KAAa,GAANnE,EAAU,KAAa,GAANA,EAAU,KAAa,GAANA,EAAU,KAAO,KAExG,CACD,CAAC,EAGDxB,EAAMkiB,KAAOlb,EAAU,wDAAyD2P,EAAkB,EAClG3W,EAAMwpB,SAAWxiB,EAAU,gEAAiE+P,EAAS,EAErG,IAAI0S,EAAU7jB,KAAKc,IAmBnB,SAASgjB,GAAcrL,EAAUje,EAAO8F,EAAOkb,GAC1CrD,EAAQ2C,EAAetgB,EAAO8F,CAAK,EAMvC,OAJAmY,EAASS,eAAiBsC,EAAYrD,EAAMe,cAC5CT,EAASU,OAASqC,EAAYrD,EAAMgB,MACpCV,EAASW,SAAWoC,EAAYrD,EAAMiB,QAE/BX,EAASa,QAAQ,CACzB,CAYA,SAASyK,GAAQhkB,GAChB,OAAIA,EAAS,EACLC,KAAKE,MAAMH,CAAM,EAEjBC,KAAKC,KAAKF,CAAM,CAEzB,CAgDA,SAASikB,GAAalL,GAGrB,OAAe,KAAPA,EAAe,MACxB,CAEA,SAASmL,GAAa7a,GAErB,OAAiB,OAATA,EAAmB,IAC5B,CA+CA,SAAS8a,GAAOC,GACf,OAAO,WACN,OAAOjqB,KAAKkqB,GAAGD,CAAK,CACrB,CACD,CAEIE,GAAiBH,GAAO,IAAI,EAC5BI,GAAYJ,GAAO,GAAG,EACtBK,GAAYL,GAAO,GAAG,EACtBM,GAAUN,GAAO,GAAG,EACpBO,GAASP,GAAO,GAAG,EACnBQ,GAAUR,GAAO,GAAG,EACpBS,EAAWT,GAAO,GAAG,EACrBU,GAAUV,GAAO,GAAG,EAWxB,SAASW,GAAW7iB,GACnB,OAAO,WACN,OAAO9H,KAAKqD,QAAQ,EAAIrD,KAAKmf,MAAMrX,GAAQ1D,GAC5C,CACD,CAEA,IAAIya,GAAe8L,GAAW,cAAc,EACxCxW,GAAUwW,GAAW,SAAS,EAC9B3W,GAAU2W,GAAW,SAAS,EAC9B7W,GAAQ6W,GAAW,OAAO,EAC1B/L,EAAO+L,GAAW,MAAM,EACxBzb,GAASyb,GAAW,QAAQ,EAC5BnM,GAAQmM,GAAW,OAAO,EAM9B,IAAIpL,GAAQzZ,KAAKyZ,MACbqL,EAAa,CAChB7U,GAAI,GACJ9I,EAAG,GACH5K,EAAG,GACHsO,EAAG,GACHD,EAAG,GACHE,EAAG,EACJ,EAOA,SAASia,GAAeC,EAAgB9G,EAAehiB,GACtD,IAAIuc,EAAWqC,EAAekK,CAAc,EAAElkB,IAAI,EAC9CuN,EAAUoL,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAChClW,EAAUuL,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAChCpW,EAAQyL,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAC9BtL,EAAOW,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAC7Bhb,EAASqQ,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAC/B1L,EAAQe,GAAMhB,EAAS2L,GAAG,GAAG,CAAC,EAE9BzoB,GAAK0S,GAAWyW,EAAW7U,GAAM,CAAC,IAAK5B,GAAcA,EAAUyW,EAAW3d,GAAK,CAAC,KAAMkH,MAAcH,GAAW,EAAK,CAAC,KAAUA,EAAU4W,EAAWvoB,GAAK,CAAC,KAAM2R,MAAcF,GAAS,EAAK,CAAC,KAAUA,EAAQ8W,EAAWja,GAAK,CAAC,KAAMmD,MAAY8K,GAAQ,EAAK,CAAC,KAAUA,EAAOgM,EAAWla,GAAK,CAAC,KAAMkO,MAAW1P,GAAU,EAAK,CAAC,KAAUA,EAAS0b,EAAWha,GAAK,CAAC,KAAM1B,MAAasP,GAAS,EAAK,CAAC,KAAS,CAAC,KAAMA,IAK7Z,OAHA/c,EAAE,GAAKuiB,EACPviB,EAAE,GAAuB,EAAlB,CAACqpB,EACRrpB,EAAE,GAAKO,EAjBR,SAA2ByY,EAAQ5U,EAAQme,EAAe+D,EAAU/lB,GACnE,OAAOA,EAAO4T,aAAa/P,GAAU,EAAG,CAAC,CAACme,EAAevJ,EAAQsN,CAAQ,CAC1E,EAgB0B5nB,MAAM,KAAMsB,CAAC,CACvC,CA4CA,IAAIspB,GAAQjlB,KAAKc,IAEjB,SAAS+Y,GAAK7Q,GACb,OAAY,EAAJA,IAAUA,EAAI,IAAM,CAACA,CAC9B,CAEA,SAASkc,KAQR,IAoBIC,EACAra,EACAsa,EACAva,EACAtO,EACA4K,EACAke,EASAC,EACAC,EACAC,EArCJ,OAAKtrB,KAAKqD,QAAQ,GAId8Q,EAAU4W,GAAM/qB,KAAKgf,aAAa,EAAI,IACtCJ,EAAOmM,GAAM/qB,KAAKif,KAAK,EACvB/P,EAAS6b,GAAM/qB,KAAKkf,OAAO,EAI/BlL,EAAUpO,EAASuO,EAAU,EAAE,EAC/BL,EAAQlO,EAASoO,EAAU,EAAE,EAC7BG,GAAW,GACXH,GAAW,GAOPiX,EAJIrlB,EAASsJ,EAAS,EAAE,EAKxB0B,EAJJ1B,GAAU,GAKNgc,EAAItM,EACJjO,EAAImD,EACJzR,EAAI2R,EACJ/G,EAAIkH,EAAUA,EAAQoX,QAAQ,CAAC,EAAE3gB,QAAQ,SAAU,EAAE,EAAI,IACzDugB,EAAQnrB,KAAKoqB,UAAU,IASvBgB,EAASzL,GAAK3f,KAAKkf,OAAO,IAAMS,GAAKwL,CAAK,EAAI,IAAM,GACpDE,EAAW1L,GAAK3f,KAAKif,KAAK,IAAMU,GAAKwL,CAAK,EAAI,IAAM,GACpDG,EAAU3L,GAAK3f,KAAKgf,aAAa,IAAMW,GAAKwL,CAAK,EAAI,IAAM,IAH/CA,EAAQ,EAAI,IAAM,IAKf,KAAOF,EAAIG,EAASH,EAAI,IAAM,KAAOra,EAAIwa,EAASxa,EAAI,IAAM,KAAOsa,EAAIG,EAAWH,EAAI,IAAM,KAAOva,GAAKtO,GAAK4K,EAAI,IAAM,KAAO0D,EAAI2a,EAAU3a,EAAI,IAAM,KAAOtO,EAAIipB,EAAUjpB,EAAI,IAAM,KAAO4K,EAAIqe,EAAUre,EAAI,IAAM,KAR/N,OA9BAjN,KAAKuK,WAAW,EAAEQ,YAAY,CAuCvC,CAEA,IAAIygB,EAAUlN,GAAS7d,UAuGvB,OArGA+qB,EAAQnoB,QA3oDR,WACC,OAAOrD,KAAKsD,QACb,EA0oDAkoB,EAAQ5kB,IAlUR,WACC,IAAIoQ,EAAOhX,KAAKmf,MAahB,OAXAnf,KAAKgf,cAAgB2K,EAAQ3pB,KAAKgf,aAAa,EAC/Chf,KAAKif,MAAQ0K,EAAQ3pB,KAAKif,KAAK,EAC/Bjf,KAAKkf,QAAUyK,EAAQ3pB,KAAKkf,OAAO,EAEnClI,EAAK6H,aAAe8K,EAAQ3S,EAAK6H,YAAY,EAC7C7H,EAAK7C,QAAUwV,EAAQ3S,EAAK7C,OAAO,EACnC6C,EAAKhD,QAAU2V,EAAQ3S,EAAKhD,OAAO,EACnCgD,EAAKlD,MAAQ6V,EAAQ3S,EAAKlD,KAAK,EAC/BkD,EAAK9H,OAASya,EAAQ3S,EAAK9H,MAAM,EACjC8H,EAAKwH,MAAQmL,EAAQ3S,EAAKwH,KAAK,EAExBxe,IACR,EAoTAwrB,EAAQzN,IAvSR,SAAezd,EAAO8F,GACrB,OAAOwjB,GAAc5pB,KAAMM,EAAO8F,EAAO,CAAC,CAC3C,EAsSAolB,EAAQ5J,SAnSR,SAAoBthB,EAAO8F,GAC1B,OAAOwjB,GAAc5pB,KAAMM,EAAO8F,EAAO,CAAC,CAAC,CAC5C,EAkSAolB,EAAQtB,GA/NR,SAAYphB,GACX,GAAI,CAAC9I,KAAKqD,QAAQ,EACjB,OAAOe,IAER,IAAIwa,EACA1P,EACA2P,EAAe7e,KAAKgf,cAIxB,GAAc,WAFdlW,EAAQD,EAAeC,CAAK,IAEO,SAAVA,EAGxB,OAFA8V,EAAO5e,KAAKif,MAAQJ,EAAe,MACnC3P,EAASlP,KAAKkf,QAAU4K,GAAalL,CAAI,EACxB,UAAV9V,EAAoBoG,EAASA,EAAS,GAI7C,OADA0P,EAAO5e,KAAKif,MAAQnZ,KAAKyZ,MAAMwK,GAAa/pB,KAAKkf,OAAO,CAAC,EACjDpW,GACP,IAAK,OACJ,OAAO8V,EAAO,EAAIC,EAAe,OAClC,IAAK,MACJ,OAAOD,EAAOC,EAAe,MAC9B,IAAK,OACJ,OAAc,GAAPD,EAAYC,EAAe,KACnC,IAAK,SACJ,OAAc,KAAPD,EAAcC,EAAe,IACrC,IAAK,SACJ,OAAc,MAAPD,EAAeC,EAAe,IAEtC,IAAK,cACJ,OAAO/Y,KAAKE,MAAa,MAAP4Y,CAAY,EAAIC,EACnC,QACC,MAAM,IAAInX,MAAM,gBAAkBoB,CAAK,CACzC,CAEF,EA6LA0iB,EAAQrB,eAAiBA,GACzBqB,EAAQpB,UAAYA,GACpBoB,EAAQnB,UAAYA,GACpBmB,EAAQlB,QAAUA,GAClBkB,EAAQjB,OAASA,GACjBiB,EAAQhB,QAAUA,GAClBgB,EAAQf,SAAWA,EACnBe,EAAQd,QAAUA,GAClBc,EAAQ3pB,QAlMR,WACC,OAAK7B,KAAKqD,QAAQ,EAGXrD,KAAKgf,cAA6B,MAAbhf,KAAKif,MAAiBjf,KAAKkf,QAAU,GAAM,OAAoC,QAA3BjZ,EAAMjG,KAAKkf,QAAU,EAAE,EAF/F9a,GAGT,EA8LAonB,EAAQpM,QAlSR,WACC,IAAIP,EAAe7e,KAAKgf,cACpBJ,EAAO5e,KAAKif,MACZ/P,EAASlP,KAAKkf,QACdlI,EAAOhX,KAAKmf,MAuChB,OAlCuB,GAAhBN,GAA6B,GAARD,GAAuB,GAAV1P,GAAiB2P,GAAgB,GAAKD,GAAQ,GAAK1P,GAAU,IACrG2P,GAAuD,MAAvCgL,GAAQE,GAAa7a,CAAM,EAAI0P,CAAI,EAEnD1P,EADA0P,EAAO,GAMR5H,EAAK6H,aAAeA,EAAe,IAEnC1K,EAAUvO,EAASiZ,EAAe,GAAI,EACtC7H,EAAK7C,QAAUA,EAAU,GAEzBH,EAAUpO,EAASuO,EAAU,EAAE,EAC/B6C,EAAKhD,QAAUA,EAAU,GAEzBF,EAAQlO,EAASoO,EAAU,EAAE,EAC7BgD,EAAKlD,MAAQA,EAAQ,GAErB8K,GAAQhZ,EAASkO,EAAQ,EAAE,EAI3B5E,GADAuc,EAAiB7lB,EAASkkB,GAAalL,CAAI,CAAC,EAE5CA,GAAQiL,GAAQE,GAAa0B,CAAc,CAAC,EAG5CjN,EAAQ5Y,EAASsJ,EAAS,EAAE,EAC5BA,GAAU,GAEV8H,EAAK4H,KAAOA,EACZ5H,EAAK9H,OAASA,EACd8H,EAAKwH,MAAQA,EAENxe,IACR,EAuPAwrB,EAAQpL,MA9KR,WACC,OAAOQ,EAAe5gB,IAAI,CAC3B,EA6KAwrB,EAAQ9c,IA3KR,SAAe5F,GAEd,OADAA,EAAQD,EAAeC,CAAK,EACrB9I,KAAKqD,QAAQ,EAAIrD,KAAK8I,EAAQ,KAAK,EAAI1E,GAC/C,EAyKAonB,EAAQ3M,aAAeA,GACvB2M,EAAQrX,QAAUA,GAClBqX,EAAQxX,QAAUA,GAClBwX,EAAQ1X,MAAQA,GAChB0X,EAAQ5M,KAAOA,EACf4M,EAAQ7M,MA9JR,WACC,OAAO/Y,EAAS5F,KAAK4e,KAAK,EAAI,CAAC,CAChC,EA6JA4M,EAAQtc,OAASA,GACjBsc,EAAQhN,MAAQA,GAChBgN,EAAQvH,SAlGR,SAAkByH,GACjB,IAII1pB,EACA8I,EALJ,OAAK9K,KAAKqD,QAAQ,GAIdrB,EAAShC,KAAKuK,WAAW,EACzBO,EAAS+f,GAAe7qB,KAAM,CAAC0rB,EAAY1pB,CAAM,EAEjD0pB,IACH5gB,EAAS9I,EAAOimB,WAAW,CAACjoB,KAAM8K,CAAM,GAGlC9I,EAAO+hB,WAAWjZ,CAAM,GAVvB9K,KAAKuK,WAAW,EAAEQ,YAAY,CAWvC,EAsFAygB,EAAQpG,YAAc4F,GACtBQ,EAAQ9qB,SAAWsqB,GACnBQ,EAAQ9F,OAASsF,GACjBQ,EAAQxpB,OAASA,GACjBwpB,EAAQjhB,WAAaA,GAGrBihB,EAAQG,YAAczkB,EAAU,sFAAuF8jB,EAAa,EACpIQ,EAAQpJ,KAAOA,GAMfnY,EAAe,IAAK,EAAG,EAAG,MAAM,EAChCA,EAAe,IAAK,EAAG,EAAG,SAAS,EAInCoC,EAAc,IAAKL,EAAW,EAC9BK,EAAc,IAvrHO,sBAurHY,EACjCc,EAAc,IAAK,SAAU7M,EAAOoK,EAAOlF,GAC1CA,EAAO5B,GAAK,IAAI5C,KAA6B,IAAxB+d,WAAWze,EAAO,EAAE,CAAQ,CAClD,CAAC,EACD6M,EAAc,IAAK,SAAU7M,EAAOoK,EAAOlF,GAC1CA,EAAO5B,GAAK,IAAI5C,KAAKiF,EAAM3F,CAAK,CAAC,CAClC,CAAC,EAIDJ,EAAM0rB,QAAU,SA5yIf3rB,EA8yIeiZ,EAEhBhZ,EAAMiB,GAAKgiB,EACXjjB,EAAMwG,IAhvDN,WAGC,OAAOyX,GAAO,WAFH,GAAG3W,MAAM7G,KAAKP,UAAW,CAAC,CAEP,CAC/B,EA6uDAF,EAAMyJ,IA3uDN,WAGC,OAAOwU,GAAO,UAFH,GAAG3W,MAAM7G,KAAKP,UAAW,CAAC,CAER,CAC9B,EAwuDAF,EAAMuY,IAtuDI,WACT,OAAOzX,KAAKyX,IAAMzX,KAAKyX,IAAI,EAAI,CAAC,IAAIzX,IACrC,EAquDAd,EAAMiC,IAAML,EACZ5B,EAAMylB,KA7hBN,SAAoBrlB,GACnB,OAAO4Y,EAAoB,IAAR5Y,CAAY,CAChC,EA4hBAJ,EAAMgP,OAtaN,SAAoBnN,EAAQklB,GAC3B,OAAOG,GAAerlB,EAAQklB,EAAO,QAAQ,CAC9C,EAqaA/mB,EAAMa,OAASA,EACfb,EAAM8B,OAAS6U,GACf3W,EAAMyhB,QAAUxd,EAChBjE,EAAMqe,SAAWqC,EACjB1gB,EAAMwF,SAAWA,EACjBxF,EAAMoS,SApaN,SAAsBiV,EAAcxlB,EAAQklB,GAC3C,OAAOK,GAAiBC,EAAcxlB,EAAQklB,EAAO,UAAU,CAChE,EAmaA/mB,EAAMmmB,UAjiBN,WACC,OAAOnN,EAAY/Y,MAAM,KAAMC,SAAS,EAAEimB,UAAU,CACrD,EAgiBAnmB,EAAMqK,WAAa0M,GACnB/W,EAAMmf,WAAaA,GACnBnf,EAAM+O,YA5aN,SAAyBlN,EAAQklB,GAChC,OAAOG,GAAerlB,EAAQklB,EAAO,aAAa,CACnD,EA2aA/mB,EAAMkS,YAjaN,SAAyBmV,EAAcxlB,EAAQklB,GAC9C,OAAOK,GAAiBC,EAAcxlB,EAAQklB,EAAO,aAAa,CACnE,EAgaA/mB,EAAMgX,aAAeA,GACrBhX,EAAM2rB,aAx+EN,SAAsB/jB,EAAMtC,GAC3B,IAGE0C,EAED4jB,EAqBD,OA1Bc,MAAVtmB,GAGF0C,EAAe2M,IAOhB7S,EAAS,IAAIoG,EADb5C,EAASyC,EAFRC,EADgB,OADjB4jB,EAAYrV,GAAW3O,CAAI,GAEXgkB,EAAU1U,QAEJlP,EAAc1C,CAAM,CAChB,GACnB6R,aAAef,EAAQxO,GAC9BwO,EAAQxO,GAAQ9F,EAGhB6U,GAAmB/O,CAAI,GAGF,MAAjBwO,EAAQxO,KACuB,MAA9BwO,EAAQxO,GAAMuP,aACjBf,EAAQxO,GAAQwO,EAAQxO,GAAMuP,aACH,MAAjBf,EAAQxO,IAClB,OAAOwO,EAAQxO,IAIXwO,EAAQxO,EAChB,EA68EA5H,EAAMoW,QAn7EN,WACC,OAAOhO,GAAKgO,CAAO,CACpB,EAk7EApW,EAAMmS,cAzaN,SAA2BkV,EAAcxlB,EAAQklB,GAChD,OAAOK,GAAiBC,EAAcxlB,EAAQklB,EAAO,eAAe,CACrE,EAwaA/mB,EAAM2I,eAAiBA,EACvB3I,EAAM6rB,qBAtLN,SAAoCC,GACnC,OAAyBhoB,KAAAA,IAArBgoB,EACIzM,GAEwB,YAA5B,OAAOyM,IACVzM,GAAQyM,EACD,CAAA,EAGT,EA8KA9rB,EAAM+rB,sBA3KN,SAAqCC,EAAWC,GAC/C,OAA8BnoB,KAAAA,IAA1B4mB,EAAWsB,KAGDloB,KAAAA,IAAVmoB,EACIvB,EAAWsB,IAEnBtB,EAAWsB,GAAaC,EACN,MAAdD,IACHtB,EAAW7U,GAAKoW,EAAQ,GAElB,CAAA,GACR,EAgKAjsB,EAAMujB,eAnzCN,SAA2B2I,EAAU3T,GAEpC,OADI0H,EAAOiM,EAASjM,KAAK1H,EAAK,OAAQ,CAAA,CAAI,GAC5B,CAAC,EAAI,WAAa0H,EAAO,CAAC,EAAI,WAAaA,EAAO,EAAI,UAAYA,EAAO,EAAI,UAAYA,EAAO,EAAI,UAAYA,EAAO,EAAI,WAAa,UACvJ,EAizCAjgB,EAAMO,UAAY0iB,EAGlBjjB,EAAMmsB,UAAY,CACjBC,eAAgB,mBAChBC,uBAAwB,sBACxBC,kBAAmB,0BACnBhf,KAAM,aACNif,KAAM,QACNC,aAAc,WACdC,QAAS,eACT9e,KAAM,aACNN,MAAO,SACR,EAEOrN,CACR,CAAC"}
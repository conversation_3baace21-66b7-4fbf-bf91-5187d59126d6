!function(t){"function"==typeof define&&define.amd?define(["jquery","datatables.net"],function(e){return t(e,window,document)}):"object"==typeof exports?module.exports=function(e,a){return e=e||window,(a=a||("undefined"!=typeof window?require("jquery"):require("jquery")(e))).fn.dataTable||require("datatables.net")(e,a),t(a,0,e.document)}:t(jQuery,window,document)}(function(x,e,r,s){"use strict";var o=x.fn.dataTable;return x.extend(!0,o.defaults,{dom:"<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row dt-row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",renderer:"bootstrap"}),x.extend(o.ext.classes,{sWrapper:"dataTables_wrapper dt-bootstrap5",sFilterInput:"form-control form-control-sm",sLengthSelect:"form-select form-select-sm",sProcessing:"dataTables_processing card",sPageButton:"paginate_button page-item"}),o.ext.renderer.pageButton.bootstrap=function(d,e,l,a,c,u){var p,f,t,b=new o.Api(d),g=d.oClasses,m=d.oLanguage.oPaginate,w=d.oLanguage.oAria.paginate||{},e=x(e);try{t=e.find(r.activeElement).data("dt-idx")}catch(e){}var n=e.children("ul.pagination");n.length?n.empty():n=e.html("<ul/>").children("ul").addClass("pagination"),function e(a,t){for(var n,r,s=function(e){e.preventDefault(),x(e.currentTarget).hasClass("disabled")||b.page()==e.data.action||b.page(e.data.action).draw("page")},o=0,i=t.length;o<i;o++)if(r=t[o],Array.isArray(r))e(a,r);else{switch(f=p="",r){case"ellipsis":p="&#x2026;",f="disabled";break;case"first":p=m.sFirst,f=r+(0<c?"":" disabled");break;case"previous":p=m.sPrevious,f=r+(0<c?"":" disabled");break;case"next":p=m.sNext,f=r+(c<u-1?"":" disabled");break;case"last":p=m.sLast,f=r+(c<u-1?"":" disabled");break;default:p=r+1,f=c===r?"active":""}p&&(n=x("<li>",{class:g.sPageButton+" "+f,id:0===l&&"string"==typeof r?d.sTableId+"_"+r:null}).append(x("<a>",{href:"#","aria-controls":d.sTableId,"aria-label":w[r],"data-dt-idx":r,tabindex:d.iTabIndex,class:"page-link"}).html(p)).appendTo(a),d.oApi._fnBindAction(n,{action:r},s))}}(n,a),t!==s&&e.find("[data-dt-idx="+t+"]").trigger("focus")},o});
//# sourceMappingURL=dataTables.bs5.min.js.map

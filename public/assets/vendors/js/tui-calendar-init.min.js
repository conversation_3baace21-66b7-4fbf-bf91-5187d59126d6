"use strict";!function(e,t){var l,c,i,n=!0;function a(e,t){var n=[],a=moment(e.start.toUTCString());return t||n.push("<strong>"+a.format("HH:mm")+"</strong> "),e.isPrivate?(n.push('<span class="calendar-font-icon ic-lock-b"></span>'),n.push(" Private")):(e.isReadOnly?n.push('<span class="calendar-font-icon ic-readonly-b"></span>'):e.recurrenceRule?n.push('<span class="calendar-font-icon ic-repeat-b"></span>'):e.attendees.length?n.push('<span class="calendar-font-icon ic-user-b"></span>'):e.location&&n.push('<span class="calendar-font-icon ic-location-b"></span>'),n.push(" "+e.title)),n.join("")}function o(e){var t=$(e.target).closest('div[role="menuitem"]')[0],e=b(t),n=l.getOptions(),a="";switch(console.log(t),console.log(e),e){case"toggle-daily":a="day";break;case"toggle-weekly":a="week";break;case"toggle-monthly":n.month.visibleWeeksCount=0,a="month";break;case"toggle-weeks2":n.month.visibleWeeksCount=2,a="month";break;case"toggle-weeks3":n.month.visibleWeeksCount=3,a="month";break;case"toggle-narrow-weekend":n.month.narrowWeekend=!n.month.narrowWeekend,n.week.narrowWeekend=!n.week.narrowWeekend,a=l.getViewName(),t.querySelector("input").checked=n.month.narrowWeekend;break;case"toggle-start-day-1":n.month.startDayOfWeek=n.month.startDayOfWeek?0:1,n.week.startDayOfWeek=n.week.startDayOfWeek?0:1,a=l.getViewName(),t.querySelector("input").checked=n.month.startDayOfWeek;break;case"toggle-workweek":n.month.workweek=!n.month.workweek,n.week.workweek=!n.week.workweek,a=l.getViewName(),t.querySelector("input").checked=!n.month.workweek}l.setOptions(n,!0),l.changeView(a,!0),m(),f(),p()}function r(e){switch(b(e.target)){case"move-prev":l.prev();break;case"move-next":l.next();break;case"move-today":l.today();break;default:return}f(),p()}function d(){var e=$("#new-schedule-title").val(),t=$("#new-schedule-location").val(),n=document.getElementById("new-schedule-allday").checked,a=c.getStartDate(),o=c.getEndDate(),r=i||CalendarList[0];e&&(l.createSchedules([{id:String(chance.guid()),calendarId:r.id,title:e,isAllDay:n,location:t,start:a,end:o,category:n?"allday":"time",dueDateClass:"",color:r.color,bgColor:r.bgColor,dragBgColor:r.bgColor,borderColor:r.borderColor,state:"Busy"}]),$("#modal-new-schedule").modal("hide"))}function s(e){var t,n,e=b($(e.target).closest('div[role="menuitem"]')[0]);e=e,t=document.getElementById("calendarName"),e=findCalendar(e),(n=[]).push('<span class="calendar-bar" style="background-color: '+e.bgColor+"; border-color:"+e.borderColor+';"></span>'),n.push('<span class="calendar-name">'+e.name+"</span>"),t.innerHTML=n.join(""),i=e}function u(e){var t=e.start?new Date(e.start.getTime()).format("DD/MM/YYYY"):new Date,e=e.end?new Date(e.end.getTime()).format("DD/MM/YYYY"):moment().add(1,"hours").toDate();n&&l.openCreationPopup({start:t,end:e})}function h(e){var t=e.target.value,n=e.target.checked,e=document.querySelector(".lnb-calendars-item input"),a=Array.prototype.slice.call(document.querySelectorAll("#calendarList input")),o=!0;"all"===t?(o=n,a.forEach(function(e){var t=e.parentNode;e.checked=n,t.style.backgroundColor=n?t.style.borderColor:"transparent"}),CalendarList.forEach(function(e){e.checked=n})):(findCalendar(t).checked=n,o=a.every(function(e){return e.checked}),e.checked=!!o),g()}function g(){var e=Array.prototype.slice.call(document.querySelectorAll("#calendarList input"));CalendarList.forEach(function(e){l.toggleSchedules(e.id,!e.checked,!1)}),l.render(!0),e.forEach(function(e){var t=e.nextElementSibling;t.style.backgroundColor=e.checked?t.style.borderColor:"transparent"})}function m(){var e=document.getElementById("calendarTypeName"),t=document.getElementById("calendarTypeIcon"),n=l.getOptions(),a=l.getViewName(),n="day"===a?(a="Daily","calendar-icon feather feather-list fs-12 me-1"):"week"===a?(a="Weekly","calendar-icon feather feather-umbrella fs-12 me-1"):2===n.month.visibleWeeksCount?(a="2 weeks","calendar-icon feather feather-sliders fs-12 me-1"):3===n.month.visibleWeeksCount?(a="3 weeks","calendar-icon feather feather-framer fs-12 me-1"):(a="Monthly","calendar-icon feather feather-grid fs-12 me-1");e.innerHTML=a,t.className=n}function k(e){return moment([l.getDate().getFullYear(),l.getDate().getMonth(),l.getDate().getDate()]).format(e)}function f(){var e=document.getElementById("renderRange"),t=l.getOptions(),n=l.getViewName(),a=[];"day"===n||"month"===n&&(!t.month.visibleWeeksCount||4<t.month.visibleWeeksCount)?a.push(k("DD.MM.YY")):(a.push(moment(l.getDateRangeStart().getTime()).format("DD/MM/YYYY")),a.push(" ~ "),a.push(moment(l.getDateRangeEnd().getTime()).format("DD/MM/YYYY"))),e.innerHTML=a.join("")}function p(){l.clear(),generateSchedule(l.getViewName(),l.getDateRangeStart(),l.getDateRangeEnd()),l.createSchedules(ScheduleList),g()}function b(e){return e.dataset?e.dataset.action:e.getAttribute("data-action")}(l=new t("#tui-calendar-init",{defaultView:"month",useCreationPopup:n,useDetailPopup:!0,calendars:CalendarList,template:{milestone:function(e){return'<span class="calendar-font-icon ic-milestone-b"></span> <span style="background-color: '+e.bgColor+'">'+e.title+"</span>"},allday:function(e){return a(e,!0)},time:function(e){return a(e,!1)}}})).on({clickMore:function(e){console.log("clickMore",e)},clickSchedule:function(e){console.log("clickSchedule",e)},clickDayname:function(e){console.log("clickDayname",e)},beforeCreateSchedule:function(e){console.log("beforeCreateSchedule",e);var t=(e=e).calendar||findCalendar(e.calendarId),e={id:String(chance.guid()),title:e.title,isAllDay:e.isAllDay,start:e.start,end:e.end,category:e.isAllDay?"allday":"time",dueDateClass:"",color:t.color,bgColor:t.bgColor,dragBgColor:t.bgColor,borderColor:t.borderColor,location:e.location,isPrivate:e.isPrivate,state:e.state};t&&(e.calendarId=t.id,e.color=t.color,e.bgColor=t.bgColor,e.borderColor=t.borderColor),l.createSchedules([e]),g()},beforeUpdateSchedule:function(e){var t=e.schedule,n=e.changes;console.log("beforeUpdateSchedule",e),n&&!n.isAllDay&&"allday"===t.category&&(n.category="time"),l.updateSchedule(t.id,t.calendarId,n),g()},beforeDeleteSchedule:function(e){console.log("beforeDeleteSchedule",e),l.deleteSchedule(e.schedule.id,e.schedule.calendarId)},afterRenderSchedule:function(e){e.schedule},clickTimezonesCollapseBtn:function(e){return console.log("timezonesCollapsed",e),e?l.setTheme({"week.daygridLeft.width":"77px","week.timegridLeft.width":"77px"}):l.setTheme({"week.daygridLeft.width":"60px","week.timegridLeft.width":"60px"}),!0}}),t=tui.util.throttle(function(){l.render()},50),e.cal=l,m(),f(),p(),$(".menu-navi").on("click",r),$('.dropdown-menu div[role="menuitem"]').on("click",o),$("#lnb-calendars").on("change",h),$("#btn-save-schedule").on("click",d),$("#btn-new-schedule").on("click",u),$("#dropdownMenu-calendars-list").on("click",s),e.addEventListener("resize",t)}(window,tui.Calendar),function(){var e=document.getElementById("calendarList"),t=[];CalendarList.forEach(function(e){t.push('<div class="lnb-calendars-item"><label><input type="checkbox" class="tui-full-calendar-checkbox-round" value="'+e.id+'" checked><span style="border-color: '+e.borderColor+"; background-color: "+e.borderColor+';"></span><span>'+e.name+"</span></label></div>")}),e.innerHTML=t.join("\n")}();
//# sourceMappingURL=tui-calendar-init.min.js.map

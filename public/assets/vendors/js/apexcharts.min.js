!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).ApexCharts=e()}(this,function(){"use strict";function C(t){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function E(t,e){for(var i=0;i<e.length;i++){var s=e[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}function t(t,e,i){e&&E(t.prototype,e),i&&E(t,i)}function M(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Q(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{},s=Object.keys(i);(s="function"==typeof Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(i).filter(function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable})):s).forEach(function(t){M(e,t,i[t])})}return e}function e(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&T(t,e)}function i(t){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function T(t,e){return(T=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a(t,e){if(!e||"object"!=typeof e&&"function"!=typeof e){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}return e}function u(t){return function(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}t(R,[{key:"shadeRGBColor",value:function(t,e){var i=e.split(","),s=t<0?0:255,a=t<0?-1*t:t,e=parseInt(i[0].slice(4)),t=parseInt(i[1]),i=parseInt(i[2]);return"rgb("+(Math.round((s-e)*a)+e)+","+(Math.round((s-t)*a)+t)+","+(Math.round((s-i)*a)+i)+")"}},{key:"shadeHexColor",value:function(t,e){var i=t<0?0:255,s=t<0?-1*t:t,e=(a=parseInt(e.slice(1),16))>>16,t=a>>8&255,a=255&a;return"#"+(16777216+65536*(Math.round((i-e)*s)+e)+256*(Math.round((i-t)*s)+t)+(Math.round((i-a)*s)+a)).toString(16).slice(1)}},{key:"shadeColor",value:function(t,e){return 7<e.length?this.shadeRGBColor(t,e):this.shadeHexColor(t,e)}}],[{key:"bind",value:function(t,e){return function(){return t.apply(e,arguments)}}},{key:"isObject",value:function(t){return t&&"object"===C(t)&&!Array.isArray(t)&&null!=t}},{key:"listToArray",value:function(t){for(var e=[],i=0;i<t.length;i++)e[i]=t[i];return e}},{key:"extend",value:function(e,i){var s=this,a=("function"!=typeof Object.assign&&(Object.assign=function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var s=arguments[i];if(null!=s)for(var a in s)s.hasOwnProperty(a)&&(e[a]=s[a])}return e}),Object.assign({},e));return this.isObject(e)&&this.isObject(i)&&Object.keys(i).forEach(function(t){s.isObject(i[t])&&t in e?a[t]=s.extend(e[t],i[t]):Object.assign(a,M({},t,i[t]))}),a}},{key:"extendArray",value:function(t,e){var i=[];return t.map(function(t){i.push(R.extend(e,t))}),i}},{key:"monthMod",value:function(t){return t%12}},{key:"addProps",value:function(t,e,i){t[(e="string"==typeof e?e.split("."):e)[0]]=t[e[0]]||{};var s=t[e[0]];return 1<e.length?(e.shift(),this.addProps(s,e,i)):t[e[0]]=i,t}},{key:"clone",value:function(t){if("[object Array]"===Object.prototype.toString.call(t)){for(var e=[],i=0;i<t.length;i++)e[i]=this.clone(t[i]);return e}if("object"!==C(t))return t;var s,a={};for(s in t)t.hasOwnProperty(s)&&(a[s]=this.clone(t[s]));return a}},{key:"log10",value:function(t){return Math.log(t)/Math.LN10}},{key:"roundToBase10",value:function(t){return Math.pow(10,Math.floor(Math.log10(t)))}},{key:"roundToBase",value:function(t,e){return Math.pow(e,Math.floor(Math.log(t)/Math.log(e)))}},{key:"parseNumber",value:function(t){return null===t?t:parseFloat(t)}},{key:"noExponents",value:function(t){var e=String(t).split(/[eE]/);if(1==e.length)return e[0];var i="",s=t<0?"-":"",t=e[0].replace(".",""),a=Number(e[1])+1;if(a<0){for(i=s+"0.";a++;)i+="0";return i+t.replace(/^\-/,"")}for(a-=t.length;a--;)i+="0";return t+i}},{key:"getDimensions",value:function(t){var e=getComputedStyle(t),i=[],s=t.clientHeight,t=t.clientWidth;return s-=parseFloat(e.paddingTop)+parseFloat(e.paddingBottom),t-=parseFloat(e.paddingLeft)+parseFloat(e.paddingRight),i.push(t),i.push(s),i}},{key:"getBoundingClientRect",value:function(t){return{top:(t=t.getBoundingClientRect()).top,right:t.right,bottom:t.bottom,left:t.left,width:t.width,height:t.height,x:t.x,y:t.y}}},{key:"hexToRgba",value:function(){for(var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"#999999",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:.6,i=(i=(t="#"!==t.substring(0,1)?"#999999":t).replace("#","")).match(new RegExp("(.{"+i.length/3+"})","g")),s=0;s<i.length;s++)i[s]=parseInt(1===i[s].length?i[s]+i[s]:i[s],16);return void 0!==e&&i.push(e),"rgba("+i.join(",")+")"}},{key:"getOpacityFromRGBA",value:function(t){return(t=t.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))[3]}},{key:"rgb2hex",value:function(t){return(t=t.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i))&&4===t.length?"#"+("0"+parseInt(t[1],10).toString(16)).slice(-2)+("0"+parseInt(t[2],10).toString(16)).slice(-2)+("0"+parseInt(t[3],10).toString(16)).slice(-2):""}},{key:"isColorHex",value:function(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}},{key:"polarToCartesian",value:function(t,e,i,s){return s=(s-90)*Math.PI/180,{x:t+i*Math.cos(s),y:e+i*Math.sin(s)}}},{key:"escapeString",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"x",t=t.toString().slice();return t.replace(/[` ~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,e)}},{key:"negToZero",value:function(t){return t<0?0:t}},{key:"moveIndexInArray",value:function(t,e,i){if(i>=t.length)for(var s=i-t.length+1;s--;)t.push(void 0);return t.splice(i,0,t.splice(e,1)[0]),t}},{key:"extractNumber",value:function(t){return parseFloat(t.replace(/[^\d\.]*/g,""))}},{key:"randomString",value:function(t){for(var e="",i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",s=0;s<t;s++)e+=i.charAt(Math.floor(Math.random()*i.length));return e}},{key:"findAncestor",value:function(t,e){for(;(t=t.parentElement)&&!t.classList.contains(e););return t}},{key:"setELstyles",value:function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t.style.key=e[i])}},{key:"isNumber",value:function(t){return!isNaN(t)&&parseFloat(Number(t))===t&&!isNaN(parseInt(t,10))}},{key:"isFloat",value:function(t){return Number(t)===t&&t%1!=0}},{key:"isSafari",value:function(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}},{key:"isFirefox",value:function(){return-1<navigator.userAgent.toLowerCase().indexOf("firefox")}},{key:"isIE11",value:function(){if(-1!==window.navigator.userAgent.indexOf("MSIE")||-1<window.navigator.appVersion.indexOf("Trident/"))return!0}},{key:"isIE",value:function(){var t,e=window.navigator.userAgent,i=e.indexOf("MSIE ");return 0<i?parseInt(e.substring(i+5,e.indexOf(".",i)),10):0<e.indexOf("Trident/")?(t=e.indexOf("rv:"),parseInt(e.substring(t+3,e.indexOf(".",t)),10)):0<(t=e.indexOf("Edge/"))&&parseInt(e.substring(t+5,e.indexOf(".",t)),10)}}]);var K=R,L=(t(F,[{key:"getDefaultFilter",value:function(t,e){var i=this.w;t.unfilter(!0),(new window.SVG.Filter).size("120%","180%","-5%","-40%"),"none"!==i.config.states.normal.filter?this.applyFilter(t,e,i.config.states.normal.filter.type,i.config.states.normal.filter.value):i.config.chart.dropShadow.enabled&&this.dropShadow(t,i.config.chart.dropShadow,e)}},{key:"addNormalFilter",value:function(t,e){var i=this.w;i.config.chart.dropShadow.enabled&&this.dropShadow(t,i.config.chart.dropShadow,e)}},{key:"addLightenFilter",value:function(t,i,e){var s=this,a=this.w,n=e.intensity;K.isFirefox()||(t.unfilter(!0),(new window.SVG.Filter).size("120%","180%","-5%","-40%"),t.filter(function(t){var e=a.config.chart.dropShadow;(e.enabled?s.addShadow(t,i,e):t).componentTransfer({rgb:{type:"linear",slope:1.5,intercept:n}})}),t.filterer.node.setAttribute("filterUnits","userSpaceOnUse"))}},{key:"addDarkenFilter",value:function(t,i,e){var s=this,a=this.w,n=e.intensity;K.isFirefox()||(t.unfilter(!0),(new window.SVG.Filter).size("120%","180%","-5%","-40%"),t.filter(function(t){var e=a.config.chart.dropShadow;(e.enabled?s.addShadow(t,i,e):t).componentTransfer({rgb:{type:"linear",slope:n}})}),t.filterer.node.setAttribute("filterUnits","userSpaceOnUse"))}},{key:"applyFilter",value:function(t,e,i){var s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:.5;switch(i){case"none":this.addNormalFilter(t,e);break;case"lighten":this.addLightenFilter(t,e,{intensity:s});break;case"darken":this.addDarkenFilter(t,e,{intensity:s})}}},{key:"addShadow",value:function(t,e,i){var s=i.blur,a=i.top,n=i.left,o=i.color,i=i.opacity,s=t.flood(Array.isArray(o)?o[e]:o,i).composite(t.sourceAlpha,"in").offset(n,a).gaussianBlur(s).merge(t.source);return t.blend(t.source,s)}},{key:"dropShadow",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,s=e.top,a=e.left,n=e.blur,o=e.color,r=e.opacity,l=e.noUserSpaceOnUse,e=this.w;return t.unfilter(!0),K.isIE()&&"radialBar"===e.config.chart.type||(o=Array.isArray(o)?o[i]:o,(new window.SVG.Filter).size("120%","180%","-5%","-40%"),t.filter(function(t){var e=K.isSafari()||K.isFirefox()||K.isIE()?t.flood(o,r).composite(t.sourceAlpha,"in").offset(a,s).gaussianBlur(n):t.flood(o,r).composite(t.sourceAlpha,"in").offset(a,s).gaussianBlur(n).merge(t.source);t.blend(t.source,e)}),l)||t.filterer.node.setAttribute("filterUnits","userSpaceOnUse"),t}},{key:"setSelectionFilter",value:function(t,e,i){var s=this.w;void 0!==s.globals.selectedDataPoints[e]&&-1<s.globals.selectedDataPoints[e].indexOf(i)&&(t.node.setAttribute("selected",!0),"none"!==(s=s.config.states.active.filter))&&this.applyFilter(t,e,s.type,s.value)}}]),F),z=(t(Y,[{key:"setEasingFunctions",value:function(){var t;switch(this.w.config.chart.animations.easing){case"linear":t="-";break;case"easein":t="<";break;case"easeout":t=">";break;case"easeinout":t="<>";break;case"swing":t=function(t){return--t*t*(2.70158*t********)+1};break;case"bounce":t=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375};break;case"elastic":t=function(t){return t===!!t?t:Math.pow(2,-10*t)*Math.sin((t-.075)*(2*Math.PI)/.3)+1};break;default:t="<>"}this.w.globals.easing=t}},{key:"animateLine",value:function(t,e,i,s){t.attr(e).animate(s).attr(i)}},{key:"animateCircleRadius",value:function(t,e,i,s,a){t.attr({r:e=e||0}).animate(s,a).attr({r:i})}},{key:"animateCircle",value:function(t,e,i,s,a){t.attr({r:e.r,cx:e.cx,cy:e.cy}).animate(s,a).attr({r:i.r,cx:i.cx,cy:i.cy})}},{key:"animateRect",value:function(t,e,i,s,a){t.attr(e).animate(s).attr(i).afterAll(function(){a()})}},{key:"animatePathsGradually",value:function(t){var e=t.el,i=t.j,s=t.pathFrom,a=t.pathTo,n=t.speed,o=t.delay,r=t.strokeWidth,l=this.w,t=0;l.config.chart.animations.animateGradually.enabled&&(t=l.config.chart.animations.animateGradually.delay),l.config.chart.animations.dynamicAnimation.enabled&&l.globals.dataChanged&&(t=0),this.morphSVG(e,i,s,a,n,r,o*t)}},{key:"showDelayedElements",value:function(){this.w.globals.delayedElements.forEach(function(t){t.el.classList.remove("hidden")})}},{key:"morphSVG",value:function(t,e,i,s,a,n,o){var r=this,l=this.w;i=i||t.attr("pathFrom"),s=s||t.attr("pathTo"),(!i||-1<i.indexOf("undefined")||-1<i.indexOf("NaN"))&&(i="M 0 ".concat(l.globals.gridHeight),a=1),(-1<s.indexOf("undefined")||-1<s.indexOf("NaN"))&&(s="M 0 ".concat(l.globals.gridHeight),a=1),l.globals.shouldAnimate||(a=1),t.plot(i).animate(1,l.globals.easing,o).plot(i).animate(a,l.globals.easing,o).plot(s).afterAll(function(){K.isNumber(e)?e===l.globals.series[l.globals.maxValsInArrayIndex].length-2&&l.globals.shouldAnimate&&(l.globals.animationEnded=!0):l.globals.shouldAnimate&&(l.globals.animationEnded=!0,"function"==typeof l.config.chart.events.animationEnd)&&l.config.chart.events.animationEnd(r.ctx,l),r.showDelayedElements()})}}]),Y),tt=(t(I,[{key:"drawLine",value:function(t,e,i,s){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:"#a8a8a8",n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:0,o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:null;return this.w.globals.dom.Paper.line().attr({x1:t,y1:e,x2:i,y2:s,stroke:a,"stroke-dasharray":n,"stroke-width":o})}},{key:"drawRect",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0,e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:0,s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0,a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0,n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:"#fefefe",o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:1,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:null,l=8<arguments.length&&void 0!==arguments[8]?arguments[8]:null,h=9<arguments.length&&void 0!==arguments[9]?arguments[9]:0,c=this.w.globals.dom.Paper.rect();return c.attr({x:t,y:e,width:0<i?i:0,height:0<s?s:0,rx:a,ry:a,fill:n,opacity:o,"stroke-width":null!==r?r:0,stroke:null!==l?l:"none","stroke-dasharray":h}),c}},{key:"drawPolygon",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"#e1e1e1",i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"none";return this.w.globals.dom.Paper.polygon(t).attr({fill:i,stroke:e})}},{key:"drawCircle",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,t=this.w.globals.dom.Paper.circle(2*t);return null!==e&&t.attr(e),t}},{key:"drawPath",value:function(t){var e=void 0===(l=t.d)?"":l,i=void 0===(o=t.stroke)?"#a8a8a8":o,s=void 0===(r=t.strokeWidth)?1:r,a=t.fill,n=void 0===(h=t.fillOpacity)?1:h,o=void 0===(l=t.strokeOpacity)?1:l,r=t.classes,l=void 0===(h=t.strokeLinecap)?null:h,t=void 0===(h=t.strokeDashArray)?0:h,h=this.w;return null===l&&(l=h.config.stroke.lineCap),(-1<e.indexOf("undefined")||-1<e.indexOf("NaN"))&&(e="M 0 ".concat(h.globals.gridHeight)),h.globals.dom.Paper.path(e).attr({fill:a,"fill-opacity":n,stroke:i,"stroke-opacity":o,"stroke-linecap":l,"stroke-width":s,"stroke-dasharray":t,class:r})}},{key:"group",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,e=this.w.globals.dom.Paper.group();return null!==t&&e.attr(t),e}},{key:"move",value:function(t,e){return["M",t,e].join(" ")}},{key:"line",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,s=null;return null===i?s=["L",t,e].join(" "):"H"===i?s=["H",t].join(" "):"V"===i&&(s=["V",e].join(" ")),s}},{key:"curve",value:function(t,e,i,s,a,n){return["C",t,e,i,s,a,n].join(" ")}},{key:"quadraticCurve",value:function(t,e,i,s){return["Q",t,e,i,s].join(" ")}},{key:"arc",value:function(t,e,i,s,a,n,o){return[7<arguments.length&&void 0!==arguments[7]&&arguments[7]?"a":"A",t,e,i,s,a,n,o].join(" ")}},{key:"renderPaths",value:function(t){var e,i=t.i,s=t.j,a=t.realIndex,n=t.pathFrom,o=t.pathTo,r=t.stroke,l=t.strokeWidth,h=t.strokeLinecap,c=t.fill,d=t.animationDelay,u=t.initialSpeed,g=t.dataChangeSpeed,p=t.className,f=t.id,x=void 0===(C=t.shouldClipToGrid)||C,b=t.bindEventsOnPaths,m=void 0===b||b,y=t.drawShadow,v=void 0===y||y,w=this.w,k=new L(this.ctx),A=new z(this.ctx),S=this.w.config.chart.animations.enabled,C=S&&this.w.config.chart.animations.dynamicAnimation.enabled;return(b=!!(S&&!w.globals.resized||C&&w.globals.dataChanged&&w.globals.shouldAnimate))?e=n:(e=o,this.w.globals.animationEnded=!0),t=w.config.stroke.dashArray,y=0,y=Array.isArray(t)?t[a]:w.config.stroke.dashArray,(y=this.drawPath({d:e,stroke:r,strokeWidth:l,fill:c,fillOpacity:1,classes:p,strokeLinecap:h,strokeDashArray:y})).attr("id","".concat(f,"-").concat(i)),y.attr("index",a),x&&y.attr({"clip-path":"url(#gridRectMask".concat(w.globals.cuid,")")}),"none"!==w.config.states.normal.filter.type?k.getDefaultFilter(y,a):w.config.chart.dropShadow.enabled&&v&&(!w.config.chart.dropShadow.enabledSeries||w.config.chart.dropShadow.enabledSeries&&-1!==w.config.chart.dropShadow.enabledSeries.indexOf(a))&&(v=w.config.chart.dropShadow,k.dropShadow(y,v,a)),m&&(y.node.addEventListener("mouseenter",this.pathMouseEnter.bind(this,y)),y.node.addEventListener("mouseleave",this.pathMouseLeave.bind(this,y)),y.node.addEventListener("mousedown",this.pathMouseDown.bind(this,y))),y.attr({pathTo:o,pathFrom:n}),l={el:y,j:s,pathFrom:n,pathTo:o,strokeWidth:l},!S||w.globals.resized||w.globals.dataChanged?!w.globals.resized&&w.globals.dataChanged||A.showDelayedElements():A.animatePathsGradually(Q({},l,{speed:u,delay:d})),w.globals.dataChanged&&C&&b&&A.animatePathsGradually(Q({},l,{speed:g})),y}},{key:"drawPattern",value:function(e,i,s){var a=3<arguments.length&&void 0!==arguments[3]?arguments[3]:"#a8a8a8",n=4<arguments.length&&void 0!==arguments[4]?arguments[4]:0;return this.w.globals.dom.Paper.pattern(i,s,function(t){"horizontalLines"===e?t.line(0,0,s,0).stroke({color:a,width:n+1}):"verticalLines"===e?t.line(0,0,0,i).stroke({color:a,width:n+1}):"slantedLines"===e?t.line(0,0,i,s).stroke({color:a,width:n}):"squares"===e?t.rect(i,s).fill("none").stroke({color:a,width:n}):"circles"===e&&t.circle(i).fill("none").stroke({color:a,width:n})})}},{key:"drawGradient",value:function(t,e,i,s,a){var n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:null,o=6<arguments.length&&void 0!==arguments[6]?arguments[6]:null,r=7<arguments.length&&void 0!==arguments[7]?arguments[7]:null,l=8<arguments.length&&void 0!==arguments[8]?arguments[8]:0,h=this.w,c=(e=K.hexToRgba(e,s),i=K.hexToRgba(i,a),0),d=1,u=1,g=null,p=(null!==o&&(c=void 0!==o[0]?o[0]/100:0,d=void 0!==o[1]?o[1]/100:1,u=void 0!==o[2]?o[2]/100:1,g=void 0!==o[3]?o[3]/100:null),!("donut"!==h.config.chart.type&&"pie"!==h.config.chart.type&&"bubble"!==h.config.chart.type)),f=null===r||0===r.length?h.globals.dom.Paper.gradient(p?"radial":"linear",function(t){t.at(c,e,s),t.at(d,i,a),t.at(u,i,a),null!==g&&t.at(g,e,s)}):h.globals.dom.Paper.gradient(p?"radial":"linear",function(e){(Array.isArray(r[l])?r[l]:r).forEach(function(t){e.at(t.offset/100,t.color,t.opacity)})});return p?(o=h.globals.gridWidth/2,p=h.globals.gridHeight/2,"bubble"!==h.config.chart.type?f.attr({gradientUnits:"userSpaceOnUse",cx:o,cy:p,r:n}):f.attr({cx:.5,cy:.5,r:.8,fx:.2,fy:.2})):"vertical"===t?f.from(0,0).to(0,1):"diagonal"===t?f.from(0,0).to(1,1):"horizontal"===t?f.from(0,1).to(1,1):"diagonal2"===t&&f.from(0,1).to(2,2),f}},{key:"drawText",value:function(t){var e=this.w,i=t.x,s=t.y,a=t.text,n=t.textAnchor,o=t.fontSize,r=t.fontFamily,l=t.foreColor,h=t.opacity;return void 0===a&&(a=""),n=n||"start",l=l||e.config.chart.foreColor,r=r||e.config.chart.fontFamily,(e=Array.isArray(a)?e.globals.dom.Paper.text(function(t){for(var e=0;e<a.length;e++)t.tspan(a[e])}):e.globals.dom.Paper.plain(a)).attr({x:i,y:s,"text-anchor":n,"dominant-baseline":"auto","font-size":o,"font-family":r,fill:l,class:(t.cssClass,t.cssClass)}),e.node.style.fontFamily=r,e.node.style.opacity=h,e}},{key:"addTspan",value:function(t,e,i){e=t.tspan(e),i=i||this.w.config.chart.fontFamily,e.node.style.fontFamily=i}},{key:"drawMarker",value:function(t,e,i){t=t||0;var s,a,n=i.pSize||0,o=null;return"square"===i.shape?(a=void 0===i.pRadius?n/2:i.pRadius,null===e&&(a=n=0),(a=this.drawRect(s=1.2*n+a,s,s,s,a)).attr({x:t-s/2,y:e-s/2,cx:t,cy:e,class:i.class||"",fill:i.pointFillColor,"fill-opacity":i.pointFillOpacity||1,stroke:i.pointStrokeColor,"stroke-width":i.pWidth||0,"stroke-opacity":i.pointStrokeOpacity||1}),o=a):"circle"===i.shape&&(K.isNumber(e)||(e=n=0),o=this.drawCircle(n,{cx:t,cy:e,class:i.class||"",stroke:i.pointStrokeColor,fill:i.pointFillColor,"fill-opacity":i.pointFillOpacity||1,"stroke-width":i.pWidth||0,"stroke-opacity":i.pointStrokeOpacity||1})),o}},{key:"pathMouseEnter",value:function(t,e){var i=this.w,s=new L(this.ctx),a=parseInt(t.node.getAttribute("index")),n=parseInt(t.node.getAttribute("j"));"function"==typeof i.config.chart.events.dataPointMouseEnter&&i.config.chart.events.dataPointMouseEnter(e,this.ctx,{seriesIndex:a,dataPointIndex:n,w:i}),this.ctx.fireEvent("dataPointMouseEnter",[e,this.ctx,{seriesIndex:a,dataPointIndex:n,w:i}]),"none"!==i.config.states.active.filter.type&&"true"===t.node.getAttribute("selected")||"none"===i.config.states.hover.filter.type||"none"===i.config.states.active.filter.type||i.globals.isTouchDevice||(i=i.config.states.hover.filter,s.applyFilter(t,a,i.type,i.value))}},{key:"pathMouseLeave",value:function(t,e){var i=this.w,s=new L(this.ctx),a=parseInt(t.node.getAttribute("index")),n=parseInt(t.node.getAttribute("j"));"function"==typeof i.config.chart.events.dataPointMouseLeave&&i.config.chart.events.dataPointMouseLeave(e,this.ctx,{seriesIndex:a,dataPointIndex:n,w:i}),this.ctx.fireEvent("dataPointMouseLeave",[e,this.ctx,{seriesIndex:a,dataPointIndex:n,w:i}]),"none"!==i.config.states.active.filter.type&&"true"===t.node.getAttribute("selected")||"none"!==i.config.states.hover.filter.type&&s.getDefaultFilter(t,a)}},{key:"pathMouseDown",value:function(t,e){var i,s,a=this.w,n=new L(this.ctx),o=parseInt(t.node.getAttribute("index")),r=parseInt(t.node.getAttribute("j")),l="false";"true"===t.node.getAttribute("selected")?(t.node.setAttribute("selected","false"),-1<a.globals.selectedDataPoints[o].indexOf(r)&&(s=a.globals.selectedDataPoints[o].indexOf(r),a.globals.selectedDataPoints[o].splice(s,1))):(!a.config.states.active.allowMultipleDataPointsSelection&&0<a.globals.selectedDataPoints.length&&(a.globals.selectedDataPoints=[],i=a.globals.dom.Paper.select(".apexcharts-series path").members,s=a.globals.dom.Paper.select(".apexcharts-series circle, .apexcharts-series rect").members,i.forEach(function(t){t.node.setAttribute("selected","false"),n.getDefaultFilter(t,o)}),s.forEach(function(t){t.node.setAttribute("selected","false"),n.getDefaultFilter(t,o)})),t.node.setAttribute("selected","true"),l="true",void 0===a.globals.selectedDataPoints[o]&&(a.globals.selectedDataPoints[o]=[]),a.globals.selectedDataPoints[o].push(r)),"true"===l?"none"!==(l=a.config.states.active.filter)&&n.applyFilter(t,o,l.type,l.value):"none"!==a.config.states.active.filter.type&&n.getDefaultFilter(t,o),"function"==typeof a.config.chart.events.dataPointSelection&&a.config.chart.events.dataPointSelection(e,this.ctx,{selectedDataPoints:a.globals.selectedDataPoints,seriesIndex:o,dataPointIndex:r,w:a}),e&&this.ctx.fireEvent("dataPointSelection",[e,this.ctx,{selectedDataPoints:a.globals.selectedDataPoints,seriesIndex:o,dataPointIndex:r,w:a}])}},{key:"rotateAroundCenter",value:function(t){return{x:(t=t.getBBox()).x+t.width/2,y:t.y+t.height/2}}},{key:"getTextRects",value:function(t,e,i,s){var a=!(4<arguments.length&&void 0!==arguments[4])||arguments[4],n=this.w,i=this.drawText({x:-200,y:-200,text:t,textAnchor:"start",fontSize:e,fontFamily:i,foreColor:"#fff",opacity:0});return s&&i.attr("transform",s),n.globals.dom.Paper.add(i),n=i.bbox(),a||(n=i.node.getBoundingClientRect()),i.remove(),{width:n.width,height:n.height}}},{key:"placeTextWithEllipsis",value:function(t,e,i){if(0<(t.textContent=e).length&&t.getComputedTextLength()>=i){for(var s=e.length-3;0<s;s-=3)if(t.getSubStringLength(0,s)<=i)return void(t.textContent=e.substring(0,s)+"...");t.textContent="..."}}}],[{key:"setAttrs",value:function(t,e){for(var i in e)e.hasOwnProperty(i)&&t.setAttribute(i,e[i])}}]),I);function I(t){s(this,I),this.ctx=t,this.w=t.w}function Y(t){s(this,Y),this.ctx=t,this.w=t.w,this.setEasingFunctions()}function F(t){s(this,F),this.ctx=t,this.w=t.w}function R(){s(this,R)}var D={name:"en",options:{months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],toolbar:{exportToSVG:"Download SVG",exportToPNG:"Download PNG",menu:"Menu",selection:"Selection",selectionZoom:"Selection Zoom",zoomIn:"Zoom In",zoomOut:"Zoom Out",pan:"Panning",reset:"Reset Zoom"}}},d=(t(Bt,[{key:"init",value:function(){return{annotations:{position:"front",yaxis:[this.yAxisAnnotation],xaxis:[this.xAxisAnnotation],points:[this.pointAnnotation]},chart:{animations:{enabled:!0,easing:"easeinout",speed:800,animateGradually:{delay:150,enabled:!0},dynamicAnimation:{enabled:!0,speed:350}},background:"transparent",locales:[D],defaultLocale:"en",dropShadow:{enabled:!1,enabledSeries:void 0,top:2,left:2,blur:4,color:"#000",opacity:.35},events:{animationEnd:void 0,beforeMount:void 0,mounted:void 0,updated:void 0,click:void 0,legendClick:void 0,markerClick:void 0,selection:void 0,dataPointSelection:void 0,dataPointMouseEnter:void 0,dataPointMouseLeave:void 0,beforeZoom:void 0,zoomed:void 0,scrolled:void 0},foreColor:"#373d3f",fontFamily:"Helvetica, Arial, sans-serif",height:"auto",parentHeightOffset:15,id:void 0,group:void 0,offsetX:0,offsetY:0,selection:{enabled:!1,type:"x",fill:{color:"#24292e",opacity:.1},stroke:{width:1,color:"#24292e",opacity:.4,dashArray:3},xaxis:{min:void 0,max:void 0},yaxis:{min:void 0,max:void 0}},sparkline:{enabled:!1},brush:{enabled:!1,autoScaleYaxis:!0,target:void 0},stacked:!1,stackType:"normal",toolbar:{show:!0,tools:{download:!0,selection:!0,zoom:!0,zoomin:!0,zoomout:!0,pan:!0,reset:!0,customIcons:[]},autoSelected:"zoom"},type:"line",width:"100%",zoom:{enabled:!0,type:"x",autoScaleYaxis:!1,zoomedArea:{fill:{color:"#90CAF9",opacity:.4},stroke:{color:"#0D47A1",opacity:.4,width:1}}}},plotOptions:{bar:{horizontal:!1,columnWidth:"70%",barHeight:"70%",distributed:!1,endingShape:"flat",colors:{ranges:[],backgroundBarColors:[],backgroundBarOpacity:1},dataLabels:{maxItems:100,hideOverflowingLabels:!0,position:"top"}},candlestick:{colors:{upward:"#00B746",downward:"#EF403C"},wick:{useFillColor:!0}},heatmap:{radius:2,enableShades:!0,shadeIntensity:.5,reverseNegativeShade:!0,distributed:!1,colorScale:{inverse:!1,ranges:[],min:void 0,max:void 0}},radialBar:{size:void 0,inverseOrder:!1,startAngle:0,endAngle:360,offsetX:0,offsetY:0,hollow:{margin:5,size:"50%",background:"transparent",image:void 0,imageWidth:150,imageHeight:150,imageOffsetX:0,imageOffsetY:0,imageClipped:!0,position:"front",dropShadow:{enabled:!1,top:0,left:0,blur:3,color:"#000",opacity:.5}},track:{show:!0,startAngle:void 0,endAngle:void 0,background:"#f2f2f2",strokeWidth:"97%",opacity:1,margin:5,dropShadow:{enabled:!1,top:0,left:0,blur:3,color:"#000",opacity:.5}},dataLabels:{show:!0,name:{show:!0,fontSize:"16px",fontFamily:void 0,color:void 0,offsetY:0},value:{show:!0,fontSize:"14px",fontFamily:void 0,color:void 0,offsetY:16,formatter:function(t){return t+"%"}},total:{show:!1,label:"Total",color:void 0,formatter:function(t){return t.globals.seriesTotals.reduce(function(t,e){return t+e},0)/t.globals.series.length+"%"}}}},rangeBar:{},pie:{size:void 0,customScale:1,offsetX:0,offsetY:0,expandOnClick:!0,dataLabels:{offset:0,minAngleToShowLabel:10},donut:{size:"65%",background:"transparent",labels:{show:!1,name:{show:!0,fontSize:"16px",fontFamily:void 0,color:void 0,offsetY:-10},value:{show:!0,fontSize:"20px",fontFamily:void 0,color:void 0,offsetY:10,formatter:function(t){return t}},total:{show:!1,label:"Total",color:void 0,formatter:function(t){return t.globals.seriesTotals.reduce(function(t,e){return t+e},0)}}}}},radar:{size:void 0,offsetX:0,offsetY:0,polygons:{strokeColors:"#e8e8e8",connectorColors:"#e8e8e8",fill:{colors:void 0}}}},colors:void 0,dataLabels:{enabled:!0,enabledOnSeries:void 0,formatter:function(t){return null!==t?t:""},textAnchor:"middle",offsetX:0,offsetY:0,style:{fontSize:"12px",fontFamily:void 0,colors:void 0},dropShadow:{enabled:!1,top:1,left:1,blur:1,color:"#000",opacity:.45}},fill:{type:"solid",colors:void 0,opacity:.85,gradient:{shade:"dark",type:"horizontal",shadeIntensity:.5,gradientToColors:void 0,inverseColors:!0,opacityFrom:1,opacityTo:1,stops:[0,50,100],colorStops:[]},image:{src:[],width:void 0,height:void 0},pattern:{style:"sqaures",width:6,height:6,strokeWidth:2}},grid:{show:!0,borderColor:"#e0e0e0",strokeDashArray:0,position:"back",xaxis:{lines:{show:!1,animate:!1}},yaxis:{lines:{show:!0,animate:!1}},row:{colors:void 0,opacity:.5},column:{colors:void 0,opacity:.5},padding:{top:0,right:10,bottom:0,left:12}},labels:[],legend:{show:!0,showForSingleSeries:!1,showForNullSeries:!0,showForZeroSeries:!0,floating:!1,position:"bottom",horizontalAlign:"center",fontSize:"12px",fontFamily:void 0,width:void 0,height:void 0,formatter:void 0,offsetX:-20,offsetY:0,labels:{colors:void 0,useSeriesColors:!1},markers:{width:12,height:12,strokeWidth:0,strokeColor:"#fff",radius:12,customHTML:void 0,offsetX:0,offsetY:0,onClick:void 0},itemMargin:{horizontal:0,vertical:5},onItemClick:{toggleDataSeries:!0},onItemHover:{highlightDataSeries:!0}},markers:{discrete:[],size:0,colors:void 0,strokeColors:"#fff",strokeWidth:2,strokeOpacity:.9,fillOpacity:1,shape:"circle",radius:2,offsetX:0,offsetY:0,onClick:void 0,onDblClick:void 0,hover:{size:void 0,sizeOffset:3}},noData:{text:void 0,align:"center",verticalAlign:"middle",offsetX:0,offsetY:0,style:{color:void 0,fontSize:"14px",fontFamily:void 0}},responsive:[],series:void 0,states:{normal:{filter:{type:"none",value:0}},hover:{filter:{type:"lighten",value:.15}},active:{allowMultipleDataPointsSelection:!1,filter:{type:"darken",value:.65}}},title:{text:void 0,align:"left",margin:10,offsetX:0,offsetY:0,floating:!1,style:{fontSize:"14px",fontFamily:void 0,color:void 0}},subtitle:{text:void 0,align:"left",margin:10,offsetX:0,offsetY:30,floating:!1,style:{fontSize:"12px",fontFamily:void 0,color:void 0}},stroke:{show:!0,curve:"smooth",lineCap:"butt",width:2,colors:void 0,dashArray:0},tooltip:{enabled:!0,enabledOnSeries:void 0,shared:!0,followCursor:!1,intersect:!1,inverseOrder:!1,custom:void 0,fillSeriesColor:!1,theme:"light",style:{fontSize:"12px",fontFamily:void 0},onDatasetHover:{highlightDataSeries:!1},x:{show:!0,format:"dd MMM",formatter:void 0},y:{formatter:void 0,title:{formatter:function(t){return t}}},z:{formatter:void 0,title:"Size: "},marker:{show:!0},items:{display:"flex"},fixed:{enabled:!1,position:"topRight",offsetX:0,offsetY:0}},xaxis:{type:"category",categories:[],offsetX:0,offsetY:0,labels:{show:!0,rotate:-45,rotateAlways:!1,hideOverlappingLabels:!0,trim:!0,minHeight:void 0,maxHeight:120,showDuplicates:!0,style:{colors:[],fontSize:"12px",fontFamily:void 0,cssClass:""},offsetX:0,offsetY:0,format:void 0,formatter:void 0,datetimeFormatter:{year:"yyyy",month:"MMM 'yy",day:"dd MMM",hour:"HH:mm",minute:"HH:mm:ss"}},axisBorder:{show:!0,color:"#78909C",width:"100%",height:1,offsetX:0,offsetY:0},axisTicks:{show:!0,color:"#78909C",height:6,offsetX:0,offsetY:0},tickAmount:void 0,tickPlacement:"on",min:void 0,max:void 0,range:void 0,floating:!1,position:"bottom",title:{text:void 0,offsetX:0,offsetY:0,style:{color:void 0,fontSize:"12px",fontFamily:void 0,cssClass:""}},crosshairs:{show:!0,width:1,position:"back",opacity:.9,stroke:{color:"#b6b6b6",width:1,dashArray:3},fill:{type:"solid",color:"#B1B9C4",gradient:{colorFrom:"#D8E3F0",colorTo:"#BED1E6",stops:[0,100],opacityFrom:.4,opacityTo:.5}},dropShadow:{enabled:!1,left:0,top:0,blur:1,opacity:.4}},tooltip:{enabled:!0,offsetY:0,formatter:void 0,style:{fontSize:"12px",fontFamily:void 0}}},yaxis:this.yAxis,theme:{mode:"light",palette:"palette1",monochrome:{enabled:!1,color:"#008FFB",shadeTo:"light",shadeIntensity:.65}}}}}]),Bt),N=(t(Wt,[{key:"drawAnnotations",value:function(){var t=this.w;if(t.globals.axisCharts){for(var e=this.drawYAxisAnnotations(),i=this.drawXAxisAnnotations(),s=this.drawPointAnnotations(),a=t.config.chart.animations.enabled,n=[e,i,s],o=[i.node,e.node,s.node],r=0;r<3;r++)t.globals.dom.elGraphical.add(n[r]),!a||t.globals.resized||t.globals.dataChanged||o[r].classList.add("hidden"),t.globals.delayedElements.push({el:o[r],index:0});this.annotationsBackground()}}},{key:"getStringX",value:function(t){var e=this.w,i=t,t=e.globals.labels.indexOf(t);return(t=e.globals.dom.baseEl.querySelector(".apexcharts-xaxis-texts-g text:nth-child("+(t+1)+")"))?parseFloat(t.getAttribute("x")):i}},{key:"addXaxisAnnotation",value:function(t,e,i){var s=this.w,a=this.invertAxis?s.globals.minY:s.globals.minX,n=this.invertAxis?s.globals.yRange[0]:s.globals.xRange,o=(t.x-a)/(n/s.globals.gridWidth),r=t.label.text;"category"!==s.config.xaxis.type&&!s.config.xaxis.convertedCatToNumeric||this.invertAxis||s.globals.isXNumeric||(o=this.getStringX(t.x));var l,h=t.strokeDashArray;o<0||o>s.globals.gridWidth||(null===t.x2?(l=this.graphics.drawLine(o+t.offsetX,0+t.offsetY,o+t.offsetX,s.globals.gridHeight+t.offsetY,t.borderColor,h),e.appendChild(l.node)):(a=(t.x2-a)/(n/s.globals.gridWidth),(a="category"!==s.config.xaxis.type&&!s.config.xaxis.convertedCatToNumeric||this.invertAxis||s.globals.isXNumeric?a:this.getStringX(t.x2))<o&&(n=o,o=a,a=n),r&&(h=this.graphics.drawRect(o+t.offsetX,0+t.offsetY,a-o,s.globals.gridHeight+t.offsetY,0,t.fillColor,t.opacity,1,t.borderColor,h),e.appendChild(h.node))),s="top"===t.label.position?-3:s.globals.gridHeight,(r=this.graphics.drawText({x:o+t.label.offsetX,y:s+t.label.offsetY,text:r,textAnchor:t.label.textAnchor,fontSize:t.label.style.fontSize,fontFamily:t.label.style.fontFamily,foreColor:t.label.style.color,cssClass:"apexcharts-xaxis-annotation-label "+t.label.style.cssClass})).attr({rel:i}),e.appendChild(r.node),this.setOrientations(t,i))}},{key:"drawXAxisAnnotations",value:function(){var i=this,t=this.w,s=this.graphics.group({class:"apexcharts-xaxis-annotations"});return t.config.annotations.xaxis.map(function(t,e){i.addXaxisAnnotation(t,s.node,e)}),s}},{key:"addYaxisAnnotation",value:function(t,e,i){var s=this.w,a=t.strokeDashArray;this.invertAxis?(h=s.globals.labels.indexOf(t.y),(n=s.globals.dom.baseEl.querySelector(".apexcharts-yaxis-texts-g text:nth-child("+(h+1)+")"))&&(r=parseFloat(n.getAttribute("y")))):(r=s.globals.gridHeight-(t.y-s.globals.minYArr[t.yAxisIndex])/(s.globals.yRange[t.yAxisIndex]/s.globals.gridHeight),s.config.yaxis[t.yAxisIndex]&&s.config.yaxis[t.yAxisIndex].reversed&&(r=(t.y-s.globals.minYArr[t.yAxisIndex])/(s.globals.yRange[t.yAxisIndex]/s.globals.gridHeight)));var n,o,r,l,h=t.label.text;null===t.y2?(n=this.graphics.drawLine(0+t.offsetX,r+t.offsetY,s.globals.gridWidth+t.offsetX,r+t.offsetY,t.borderColor,a),e.appendChild(n.node)):(this.invertAxis?(o=s.globals.labels.indexOf(t.y2),(o=s.globals.dom.baseEl.querySelector(".apexcharts-yaxis-texts-g text:nth-child("+(o+1)+")"))&&(l=parseFloat(o.getAttribute("y")))):(l=s.globals.gridHeight-(t.y2-s.globals.minYArr[t.yAxisIndex])/(s.globals.yRange[t.yAxisIndex]/s.globals.gridHeight),s.config.yaxis[t.yAxisIndex]&&s.config.yaxis[t.yAxisIndex].reversed&&(l=(t.y2-s.globals.minYArr[t.yAxisIndex])/(s.globals.yRange[t.yAxisIndex]/s.globals.gridHeight))),r<l&&(o=r,r=l,l=o),h&&(a=this.graphics.drawRect(0+t.offsetX,l+t.offsetY,s.globals.gridWidth+t.offsetX,r-l,0,t.fillColor,t.opacity,1,t.borderColor,a),e.appendChild(a.node))),s="right"===t.label.position?s.globals.gridWidth:0,(t=this.graphics.drawText({x:s+t.label.offsetX,y:(l||r)+t.label.offsetY-3,text:h,textAnchor:t.label.textAnchor,fontSize:t.label.style.fontSize,fontFamily:t.label.style.fontFamily,foreColor:t.label.style.color,cssClass:"apexcharts-yaxis-annotation-label "+t.label.style.cssClass})).attr({rel:i}),e.appendChild(t.node)}},{key:"drawYAxisAnnotations",value:function(){var i=this,t=this.w,s=this.graphics.group({class:"apexcharts-yaxis-annotations"});return t.config.annotations.yaxis.map(function(t,e){i.addYaxisAnnotation(t,s.node,e)}),s}},{key:"clearAnnotations",value:function(t){t=t.w.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis-annotations, .apexcharts-xaxis-annotations, .apexcharts-point-annotations"),(t=K.listToArray(t)).forEach(function(t){for(;t.firstChild;)t.removeChild(t.firstChild)})}},{key:"addPointAnnotation",value:function(t,e,i){var s,a,n=this.w,o=0,r=0,l=0;this.invertAxis&&console.warn("Point annotation is not supported in horizontal bar charts."),"string"==typeof t.x?(s=n.globals.labels.indexOf(t.x),a=n.globals.dom.baseEl.querySelector(".apexcharts-xaxis-texts-g text:nth-child("+(s+1)+")"),o=parseFloat(a.getAttribute("x")),a=t.y,null===t.y&&(a=n.globals.series[t.seriesIndex][s]),r=n.globals.gridHeight-(a-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight)-parseInt(t.label.style.fontSize)-t.marker.size,l=n.globals.gridHeight-(a-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight),n.config.yaxis[t.yAxisIndex]&&n.config.yaxis[t.yAxisIndex].reversed&&(r=(a-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight)+parseInt(t.label.style.fontSize)+t.marker.size,l=(a-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight))):(o=(t.x-n.globals.minX)/(n.globals.xRange/n.globals.gridWidth),r=n.globals.gridHeight-(parseFloat(t.y)-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight)-parseInt(t.label.style.fontSize)-t.marker.size,l=n.globals.gridHeight-(t.y-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight),n.config.yaxis[t.yAxisIndex]&&n.config.yaxis[t.yAxisIndex].reversed&&(r=(parseFloat(t.y)-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight)-parseInt(t.label.style.fontSize)-t.marker.size,l=(t.y-n.globals.minYArr[t.yAxisIndex])/(n.globals.yRange[t.yAxisIndex]/n.globals.gridHeight))),o<0||o>n.globals.gridWidth||(n={pSize:t.marker.size,pWidth:t.marker.strokeWidth,pointFillColor:t.marker.fillColor,pointStrokeColor:t.marker.strokeColor,shape:t.marker.shape,radius:t.marker.radius,class:"apexcharts-point-annotation-marker "+t.marker.cssClass},n=this.graphics.drawMarker(o+t.marker.offsetX,l+t.marker.offsetY,n),e.appendChild(n.node),n=t.label.text||"",(n=this.graphics.drawText({x:o+t.label.offsetX,y:r+t.label.offsetY,text:n,textAnchor:t.label.textAnchor,fontSize:t.label.style.fontSize,fontFamily:t.label.style.fontFamily,foreColor:t.label.style.color,cssClass:"apexcharts-point-annotation-label "+t.label.style.cssClass})).attr({rel:i}),e.appendChild(n.node),t.customSVG.SVG&&((n=this.graphics.group({class:"apexcharts-point-annotations-custom-svg "+t.customSVG.cssClass})).attr({transform:"translate(".concat(o+t.customSVG.offsetX,", ").concat(r+t.customSVG.offsetY,")")}),n.node.innerHTML=t.customSVG.SVG,e.appendChild(n.node)))}},{key:"drawPointAnnotations",value:function(){var i=this,t=this.w,s=this.graphics.group({class:"apexcharts-point-annotations"});return t.config.annotations.points.map(function(t,e){i.addPointAnnotation(t,s.node,e)}),s}},{key:"setOrientations",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i=this.w;"vertical"===t.label.orientation&&null!==(i=i.globals.dom.baseEl.querySelector(".apexcharts-xaxis-annotations .apexcharts-xaxis-annotation-label[rel='".concat(null!==e?e:0,"']")))&&(e=i.getBoundingClientRect(),i.setAttribute("x",parseFloat(i.getAttribute("x"))-e.height+4),"top"===t.label.position?i.setAttribute("y",parseFloat(i.getAttribute("y"))+e.width):i.setAttribute("y",parseFloat(i.getAttribute("y"))-e.width),e=(t=this.graphics.rotateAroundCenter(i)).x,t=t.y,i.setAttribute("transform","rotate(-90 ".concat(e," ").concat(t,")")))}},{key:"addBackgroundToAnno",value:function(t,e){var i,s,a,n,o,r=this.w;return!e.label.text||e.label.text&&!e.label.text.trim()?null:(i=r.globals.dom.baseEl.querySelector(".apexcharts-grid").getBoundingClientRect(),s=t.getBoundingClientRect(),a=e.label.style.padding.left,n=e.label.style.padding.right,o=e.label.style.padding.top,r=e.label.style.padding.bottom,"vertical"===e.label.orientation&&(o=e.label.style.padding.left,r=e.label.style.padding.right,a=e.label.style.padding.top,n=e.label.style.padding.bottom),t=s.left-i.left-a,i=s.top-i.top-o,this.graphics.drawRect(t,i,s.width+a+n,s.height+o+r,0,e.label.style.background,1,e.label.borderWidth,e.label.borderColor,0))}},{key:"annotationsBackground",value:function(){function i(t,e,i){(i=a.globals.dom.baseEl.querySelector(".apexcharts-".concat(i,"-annotations .apexcharts-").concat(i,"-annotation-label[rel='").concat(e,"']")))&&(e=i.parentNode,t=s.addBackgroundToAnno(i,t))&&e.insertBefore(t.node,i)}var s=this,a=this.w;a.config.annotations.xaxis.map(function(t,e){i(t,e,"xaxis")}),a.config.annotations.yaxis.map(function(t,e){i(t,e,"yaxis")}),a.config.annotations.points.map(function(t,e){i(t,e,"point")})}},{key:"addText",value:function(t,e,i){var s=t.x,a=t.y,n=t.text,o=t.textAnchor,r=void 0===(v=t.appendTo)?".apexcharts-inner":v,l=t.foreColor,h=t.fontSize,c=t.fontFamily,d=t.cssClass,u=t.backgroundColor,g=t.borderWidth,p=t.strokeDashArray,f=t.radius,x=t.borderColor,b=void 0===(k=t.paddingLeft)?4:k,m=void 0===(w=t.paddingRight)?4:w,y=t.paddingBottom,v=void 0===y?2:y,w=void 0===(k=t.paddingTop)?2:k,k=(t=(y=i).w).globals.dom.baseEl.querySelector(r),c=this.graphics.drawText({x:s,y:a,text:n,textAnchor:o||"start",fontSize:h||"12px",fontFamily:c||t.config.chart.fontFamily,foreColor:l||t.config.chart.foreColor,cssClass:d});return k.appendChild(c.node),k=c.bbox(),n&&(k=this.graphics.drawRect(k.x-b,k.y-w,k.width+b+m,k.height+v+w,f,u,1,g,x,p),c.before(k)),e&&t.globals.memory.methodsToExec.push({context:y,method:y.addText,params:{x:s,y:a,text:n,textAnchor:o,appendTo:r,foreColor:l,fontSize:h,cssClass:d,backgroundColor:u,borderWidth:g,strokeDashArray:p,radius:f,borderColor:x,paddingLeft:b,paddingRight:m,paddingBottom:v,paddingTop:w}}),i}},{key:"addPointAnnotationExternal",value:function(t,e,i){return void 0===this.invertAxis&&(this.invertAxis=i.w.globals.isBarHorizontal),this.addAnnotationExternal({params:t,pushToMemory:e,context:i,type:"point",contextMethod:i.addPointAnnotation}),i}},{key:"addYaxisAnnotationExternal",value:function(t,e,i){return this.addAnnotationExternal({params:t,pushToMemory:e,context:i,type:"yaxis",contextMethod:i.addYaxisAnnotation}),i}},{key:"addXaxisAnnotationExternal",value:function(t,e,i){return this.addAnnotationExternal({params:t,pushToMemory:e,context:i,type:"xaxis",contextMethod:i.addXaxisAnnotation}),i}},{key:"addAnnotationExternal",value:function(t){var e=t.params,i=t.pushToMemory,s=t.context,a=t.type,n=t.contextMethod,o=s,r=o.w,l=r.globals.dom.baseEl.querySelector(".apexcharts-".concat(a,"-annotations")),h=l.childNodes.length+1,t=new d,t=Object.assign({},"xaxis"===a?t.xAxisAnnotation:"yaxis"===a?t.yAxisAnnotation:t.pointAnnotation),c=K.extend(t,e);switch(a){case"xaxis":this.addXaxisAnnotation(c,l,h);break;case"yaxis":this.addYaxisAnnotation(c,l,h);break;case"point":this.addPointAnnotation(c,l,h)}return t=r.globals.dom.baseEl.querySelector(".apexcharts-".concat(a,"-annotations .apexcharts-").concat(a,"-annotation-label[rel='").concat(h,"']")),(a=this.addBackgroundToAnno(t,c))&&l.insertBefore(a.node,t),i&&r.globals.memory.methodsToExec.push({context:o,method:n,params:e}),s}}]),Wt),y=(t(Ht,[{key:"isValidDate",value:function(t){return!isNaN(this.parseDate(t))}},{key:"getUTCTimeStamp",value:function(t){return Date.parse(t)?new Date(new Date(t).toISOString().substr(0,25)).getTime():t}},{key:"parseDate",value:function(t){var e=Date.parse(t);return isNaN(e)&&(t=Date.parse(t.replace(/-/g,"/").replace(/[a-z]+/gi," "))),this.getUTCTimeStamp(t)}},{key:"treatAsUtc",value:function(t){return(t=new Date(t)).setMinutes(t.getMinutes()-t.getTimezoneOffset()),t}},{key:"formatDate",value:function(t,e){var i=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],s=!(3<arguments.length&&void 0!==arguments[3])||arguments[3],a=this.w.globals.locale,n=["\0"].concat(u(a.months)),o=[""].concat(u(a.shortMonths)),r=[""].concat(u(a.days)),l=[""].concat(u(a.shortDays));function h(t,e){var i=t+"";for(e=e||2;i.length<e;)i="0"+i;return i}s&&(t=this.treatAsUtc(t));var c=i?t.getUTCFullYear():t.getFullYear(),d=(e=(e=(e=e.replace(/(^|[^\\])yyyy+/g,"$1"+c)).replace(/(^|[^\\])yy/g,"$1"+c.toString().substr(2,2))).replace(/(^|[^\\])y/g,"$1"+c),(i?t.getUTCMonth():t.getMonth())+1);return e=(e=(e=(e=e.replace(/(^|[^\\])MMMM+/g,"$1"+n[0])).replace(/(^|[^\\])MMM/g,"$1"+o[0])).replace(/(^|[^\\])MM/g,"$1"+h(d))).replace(/(^|[^\\])M/g,"$1"+d),a=i?t.getUTCDate():t.getDate(),e=(e=(e=(e=e.replace(/(^|[^\\])dddd+/g,"$1"+r[0])).replace(/(^|[^\\])ddd/g,"$1"+l[0])).replace(/(^|[^\\])dd/g,"$1"+h(a))).replace(/(^|[^\\])d/g,"$1"+a),c=12<(s=i?t.getUTCHours():t.getHours())?s-12:0===s?12:s,e=(e=(e=(e=e.replace(/(^|[^\\])HH+/g,"$1"+h(s))).replace(/(^|[^\\])H/g,"$1"+s)).replace(/(^|[^\\])hh+/g,"$1"+h(c))).replace(/(^|[^\\])h/g,"$1"+c),a=i?t.getUTCMinutes():t.getMinutes(),e=(e=e.replace(/(^|[^\\])mm+/g,"$1"+h(a))).replace(/(^|[^\\])m/g,"$1"+a),c=i?t.getUTCSeconds():t.getSeconds(),e=(e=e.replace(/(^|[^\\])ss+/g,"$1"+h(c))).replace(/(^|[^\\])s/g,"$1"+c),a=i?t.getUTCMilliseconds():t.getMilliseconds(),e=e.replace(/(^|[^\\])fff+/g,"$1"+h(a,3)),a=Math.round(a/10),e=e.replace(/(^|[^\\])ff/g,"$1"+h(a)),a=Math.round(a/10),c=s<12?"AM":"PM",e=(e=(e=e.replace(/(^|[^\\])f/g,"$1"+a)).replace(/(^|[^\\])TT+/g,"$1"+c)).replace(/(^|[^\\])T/g,"$1"+c.charAt(0)),s=c.toLowerCase(),e=(e=e.replace(/(^|[^\\])tt+/g,"$1"+s)).replace(/(^|[^\\])t/g,"$1"+s.charAt(0)),a=-t.getTimezoneOffset(),c=i||!a?"Z":0<a?"+":"-",i||(s=(a=Math.abs(a))%60,c+=h(Math.floor(a/60))+":"+h(s)),e=e.replace(/(^|[^\\])K/g,"$1"+c),t=(i?t.getUTCDay():t.getDay())+1,(e=(e=(e=(e=e.replace(new RegExp(r[0],"g"),r[t])).replace(new RegExp(l[0],"g"),l[t])).replace(new RegExp(n[0],"g"),n[d])).replace(new RegExp(o[0],"g"),o[d])).replace(/\\(.)/g,"$1")}},{key:"getTimeUnitsfromTimestamp",value:function(t,e){void 0!==(h=this.w).config.xaxis.min&&(t=h.config.xaxis.min),void 0!==h.config.xaxis.max&&(e=h.config.xaxis.max);var i=new Date(t).getFullYear(),s=new Date(e).getFullYear(),a=new Date(t).getMonth(),n=new Date(e).getMonth(),o=new Date(t).getDate(),r=new Date(e).getDate(),l=new Date(t).getHours(),h=new Date(e).getHours();return{minMinute:new Date(t).getMinutes(),maxMinute:new Date(e).getMinutes(),minHour:l,maxHour:h,minDate:o,maxDate:r,minMonth:a,maxMonth:n,minYear:i,maxYear:s}}},{key:"isLeapYear",value:function(t){return t%4==0&&t%100!=0||t%400==0}},{key:"calculcateLastDaysOfMonth",value:function(t,e,i){return this.determineDaysOfMonths(t,e)-i}},{key:"determineDaysOfYear",value:function(t){return this.isLeapYear(t)?366:365}},{key:"determineRemainingDaysOfYear",value:function(t,e,i){return i=this.daysCntOfYear[e]+i,1<e&&this.isLeapYear()&&i++,i}},{key:"determineDaysOfMonths",value:function(t,e){var i=30;return t=K.monthMod(t),!0==-1<this.months30.indexOf(t)?2===t&&(i=this.isLeapYear(e)?29:28):(this.months31.indexOf(t),i=31),i}}]),Ht),O=(t(Ot,[{key:"line",value:function(){return{chart:{animations:{easing:"swing"}},dataLabels:{enabled:!1},stroke:{width:5,curve:"straight"},markers:{size:0,hover:{sizeOffset:6}},xaxis:{crosshairs:{width:1}}}}},{key:"sparkline",value:function(t){return this.opts.yaxis[0].labels.show=!1,this.opts.yaxis[0].floating=!0,K.extend(t,{grid:{show:!1,padding:{left:0,right:0,top:0,bottom:0}},legend:{show:!1},xaxis:{labels:{show:!1},tooltip:{enabled:!1},axisBorder:{show:!1}},chart:{toolbar:{show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1}})}},{key:"bar",value:function(){return{chart:{stacked:!1,animations:{easing:"swing"}},plotOptions:{bar:{dataLabels:{position:"center"}}},dataLabels:{style:{colors:["#fff"]}},stroke:{width:0},fill:{opacity:.85},legend:{markers:{shape:"square",radius:2,size:8}},tooltip:{shared:!1},xaxis:{tooltip:{enabled:!1},crosshairs:{width:"barWidth",position:"back",fill:{type:"gradient"},dropShadow:{enabled:!1},stroke:{width:0}}}}}},{key:"candlestick",value:function(){return{stroke:{width:1,colors:["#333"]},dataLabels:{enabled:!1},tooltip:{shared:!0,custom:function(t){var e=t.seriesIndex,i=t.dataPointIndex;return'<div class="apexcharts-tooltip-candlestick"><div>Open: <span class="value">'+(t=t.w).globals.seriesCandleO[e][i]+'</span></div><div>High: <span class="value">'+t.globals.seriesCandleH[e][i]+'</span></div><div>Low: <span class="value">'+t.globals.seriesCandleL[e][i]+'</span></div><div>Close: <span class="value">'+t.globals.seriesCandleC[e][i]+"</span></div></div>"}},states:{active:{filter:{type:"none"}}},xaxis:{crosshairs:{width:1}}}}},{key:"rangeBar",value:function(){return{stroke:{width:0},plotOptions:{bar:{dataLabels:{position:"center"}}},dataLabels:{enabled:!1,formatter:function(t,e){e.ctx;var i=e.seriesIndex,s=e.dataPointIndex,a=e.w,e=a.globals.seriesRangeStart[i][s];return a.globals.seriesRangeEnd[i][s]-e},style:{colors:["#fff"]}},tooltip:{shared:!1,followCursor:!0,custom:function(t){var e=t.ctx,i=t.seriesIndex,s=t.dataPointIndex,a=t.w,n=a.globals.seriesRangeStart[i][s],o=a.globals.seriesRangeEnd[i][s],r="",t=a.globals.colors[i],o=void 0===a.config.tooltip.x.formatter?"datetime"===a.config.xaxis.type?(r=(e=new y(e)).formatDate(new Date(n),a.config.tooltip.x.format,!0,!0),e.formatDate(new Date(o),a.config.tooltip.x.format,!0,!0)):(r=n,o):(r=a.config.tooltip.x.formatter(n),a.config.tooltip.x.formatter(o)),s=a.globals.labels[s];return'<div class="apexcharts-tooltip-rangebar"><div> <span class="series-name" style="color: '+t+'">'+(a.config.series[i].name||"")+'</span></div><div> <span class="category">'+s+': </span> <span class="value start-value">'+r+'</span> <span class="separator">-</span> <span class="value end-value">'+o+"</span></div></div>"}},xaxis:{tooltip:{enabled:!1},crosshairs:{stroke:{width:0}}}}}},{key:"area",value:function(){return{stroke:{width:4},fill:{type:"gradient",gradient:{inverseColors:!1,shade:"light",type:"vertical",opacityFrom:.65,opacityTo:.5,stops:[0,100,100]}},markers:{size:0,hover:{sizeOffset:6}},tooltip:{followCursor:!1}}}},{key:"brush",value:function(t){return K.extend(t,{chart:{toolbar:{autoSelected:"selection",show:!1},zoom:{enabled:!1}},dataLabels:{enabled:!1},stroke:{width:1},tooltip:{enabled:!1},xaxis:{tooltip:{enabled:!1}}})}},{key:"stacked100",value:function(){var i=this,t=(this.opts.dataLabels=this.opts.dataLabels||{},this.opts.dataLabels.formatter=this.opts.dataLabels.formatter||void 0,this.opts.dataLabels.formatter);this.opts.yaxis.forEach(function(t,e){i.opts.yaxis[e].min=0,i.opts.yaxis[e].max=100}),"bar"===this.opts.chart.type&&(this.opts.dataLabels.formatter=t||function(t){return"number"==typeof t&&t?t.toFixed(0)+"%":t})}},{key:"bubble",value:function(){return{dataLabels:{style:{colors:["#fff"]}},tooltip:{shared:!1,intersect:!0},xaxis:{crosshairs:{width:0}},fill:{type:"solid",gradient:{shade:"light",inverse:!0,shadeIntensity:.55,opacityFrom:.4,opacityTo:.8}}}}},{key:"scatter",value:function(){return{dataLabels:{enabled:!1},tooltip:{shared:!1,intersect:!0},markers:{size:6,strokeWidth:2,hover:{sizeOffset:2}}}}},{key:"heatmap",value:function(){return{chart:{stacked:!1,zoom:{enabled:!1}},fill:{opacity:1},dataLabels:{style:{colors:["#fff"]}},stroke:{colors:["#fff"]},tooltip:{followCursor:!0,marker:{show:!1},x:{show:!1}},legend:{position:"top",markers:{shape:"square",size:10,offsetY:2}},grid:{padding:{right:20}}}}},{key:"pie",value:function(){return{chart:{toolbar:{show:!1}},plotOptions:{pie:{donut:{labels:{show:!1}}}},dataLabels:{formatter:function(t){return t.toFixed(1)+"%"},style:{colors:["#fff"]},dropShadow:{enabled:!0}},stroke:{colors:["#fff"]},fill:{opacity:1,gradient:{shade:"dark",shadeIntensity:.35,inverseColors:!1,stops:[0,100,100]}},padding:{right:0,left:0},tooltip:{theme:"dark",fillSeriesColor:!0},legend:{position:"right"}}}},{key:"donut",value:function(){return{chart:{toolbar:{show:!1}},dataLabels:{formatter:function(t){return t.toFixed(1)+"%"},style:{colors:["#fff"]},dropShadow:{enabled:!0}},stroke:{colors:["#fff"]},fill:{opacity:1,gradient:{shade:"dark",shadeIntensity:.4,inverseColors:!1,type:"vertical",opacityFrom:1,opacityTo:1,stops:[70,98,100]}},padding:{right:0,left:0},tooltip:{theme:"dark",fillSeriesColor:!0},legend:{position:"right"}}}},{key:"radar",value:function(){return this.opts.yaxis[0].labels.style.fontSize="13px",{dataLabels:{enabled:!0,style:{colors:["#a8a8a8"],fontSize:"11px"}},stroke:{width:2},markers:{size:3,strokeWidth:1,strokeOpacity:1},fill:{opacity:.2},tooltip:{shared:!(this.opts.yaxis[0].labels.offsetY=6),intersect:!0,followCursor:!0},grid:{show:!1},xaxis:{tooltip:{enabled:!1},crosshairs:{show:!1}}}}},{key:"radialBar",value:function(){return{chart:{animations:{dynamicAnimation:{enabled:!0,speed:800}},toolbar:{show:!1}},fill:{gradient:{shade:"dark",shadeIntensity:.4,inverseColors:!1,type:"diagonal2",opacityFrom:1,opacityTo:1,stops:[70,98,100]}},padding:{right:0,left:0},legend:{show:!1,position:"right"},tooltip:{enabled:!1,fillSeriesColor:!0}}}}],[{key:"convertCatToNumeric",value:function(t){t.xaxis.type="numeric",t.xaxis.convertedCatToNumeric=!0,t.xaxis.labels=t.xaxis.labels||{},t.xaxis.labels.formatter=t.xaxis.labels.formatter||function(t){return t},t.chart=t.chart||{},t.chart.zoom=t.chart.zoom||window.Apex.chart&&window.Apex.chart.zoom||{};var e=t.xaxis.labels.formatter,i=t.xaxis.categories&&t.xaxis.categories.length?t.xaxis.categories:t.labels;return i&&i.length&&(t.xaxis.labels.formatter=function(t){return e(i[t-1])}),t.xaxis.categories=[],t.labels=[],t.chart.zoom.enabled=t.chart.zoom.enabled||!1,t}}]),Ot),et=(t(Nt,[{key:"getStackedSeriesTotals",value:function(){var t=this.w,e=[];if(0===t.globals.series.length)return e;for(var i=0;i<t.globals.series[t.globals.maxValsInArrayIndex].length;i++){for(var s=0,a=0;a<t.globals.series.length;a++)s+=t.globals.series[a][i];e.push(s)}return t.globals.stackedSeriesTotals=e}},{key:"getSeriesTotalByIndex",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;return null===t?this.w.config.series.reduce(function(t,e){return t+e},0):this.w.globals.series[t].reduce(function(t,e){return t+e},0)}},{key:"isSeriesNull",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;return 0===(null===t?this.w.config.series.filter(function(t){return null!==t}):this.w.globals.series[t].filter(function(t){return null!==t})).length}},{key:"seriesHaveSameValues",value:function(t){return this.w.globals.series[t].every(function(t,e,i){return t===i[0]})}},{key:"getLargestSeries",value:function(){var t=this.w;t.globals.maxValsInArrayIndex=t.globals.series.map(function(t){return t.length}).indexOf(Math.max.apply(Math,t.globals.series.map(function(t){return t.length})))}},{key:"getLargestMarkerSize",value:function(){var t=this.w,e=0;return t.globals.markers.size.forEach(function(t){e=Math.max(e,t)}),t.globals.markers.largestSize=e}},{key:"getSeriesTotals",value:function(){var t=this.w;t.globals.seriesTotals=t.globals.series.map(function(t,e){var i=0;if(Array.isArray(t))for(var s=0;s<t.length;s++)i+=t[s];else i+=t;return i})}},{key:"getSeriesTotalsXRange",value:function(a,n){var o=this.w;return o.globals.series.map(function(t,e){for(var i=0,s=0;s<t.length;s++)o.globals.seriesX[e][s]>a&&o.globals.seriesX[e][s]<n&&(i+=t[s]);return i})}},{key:"getPercentSeries",value:function(){var o=this.w;o.globals.seriesPercent=o.globals.series.map(function(t,e){var i=[];if(Array.isArray(t))for(var s=0;s<t.length;s++){var a=o.globals.stackedSeriesTotals[s],a=100*t[s]/a;i.push(a)}else{var n=100*t/o.globals.seriesTotals.reduce(function(t,e){return t+e},0);i.push(n)}return i})}},{key:"getCalculatedRatios",value:function(){var t,e,i,s,a,n=this.w.globals,o=[],r=[],l=.1,h=0;if(n.yRange=[],n.isMultipleYAxis)for(var c=0;c<n.minYArr.length;c++)n.yRange.push(Math.abs(n.minYArr[c]-n.maxYArr[c])),r.push(0);else n.yRange.push(Math.abs(n.minY-n.maxY));n.xRange=Math.abs(n.maxX-n.minX),n.zRange=Math.abs(n.maxZ-n.minZ);for(var d=0;d<n.yRange.length;d++)o.push(n.yRange[d]/n.gridHeight);if(e=n.xRange/n.gridWidth,i=Math.abs(n.initialmaxX-n.initialminX)/n.gridWidth,t=n.yRange/n.gridWidth,s=n.xRange/n.gridHeight,a=n.zRange/n.gridHeight*16,n.minY!==Number.MIN_VALUE&&0!==Math.abs(n.minY)&&(n.hasNegs=!0),n.isMultipleYAxis)for(var r=[],u=0;u<o.length;u++)r.push(-n.minYArr[u]/o[u]);else r.push(-n.minY/o[0]),n.minY!==Number.MIN_VALUE&&0!==Math.abs(n.minY)&&(l=-n.minY/t,h=n.minX/e);return{yRatio:o,invertedYRatio:t,zRatio:a,xRatio:e,initialXRatio:i,invertedXRatio:s,baseLineInvertedY:l,baseLineY:r,baseLineX:h}}},{key:"getLogSeries",value:function(t){var i=this.w;return i.globals.seriesLog=t.map(function(t,e){return i.config.yaxis[e]&&i.config.yaxis[e].logarithmic?t.map(function(t){return null===t?null:(Math.log(t)-Math.log(i.globals.minYArr[e]))/(Math.log(i.globals.maxYArr[e])-Math.log(i.globals.minYArr[e]))}):t}),i.globals.seriesLog}},{key:"getLogYRatios",value:function(t){var n=this,o=this.w,r=this.w.globals;return r.yLogRatio=t.slice(),r.logYRange=r.yRange.map(function(t,e){var i,s,a;if(o.config.yaxis[e]&&n.w.config.yaxis[e].logarithmic)return s=-Number.MAX_VALUE,a=Number.MIN_VALUE,r.seriesLog.forEach(function(t,e){t.forEach(function(t){o.config.yaxis[e]&&o.config.yaxis[e].logarithmic&&(s=Math.max(t,s),a=Math.min(t,a))})}),i=Math.pow(r.yRange[e],Math.abs(a-s)/r.yRange[e]),r.yLogRatio[e]=i/r.gridHeight,i}),r.yLogRatio}}],[{key:"checkComboSeries",value:function(t){var e=!1,i=!1;return t.length&&void 0!==t[0].type&&(e=!0,t.forEach(function(t){"bar"!==t.type&&"column"!==t.type||(i=!0)})),{comboCharts:e,comboChartsHasBars:i}}},{key:"extendArrayProps",value:function(t,e){return(e=e.yaxis?t.extendYAxis(e):e).annotations&&(e=(e=e.annotations.yaxis?t.extendYAxisAnnotations(e):e).annotations.xaxis?t.extendXAxisAnnotations(e):e).annotations.points?t.extendPointAnnotations(e):e}}]),Nt),l=(t(Dt,[{key:"init",value:function(){var t=this.opts,e=new d,i=new O(t),t=(this.chartType=t.chart.type,"histogram"===this.chartType&&(t.chart.type="bar",t=K.extend({plotOptions:{bar:{columnWidth:"99.99%"}}},t)),t=this.extendYAxis(t),this.extendAnnotations(t)),s=e.init(),a={};if(t&&"object"===C(t)){var n={};switch(this.chartType){case"line":n=i.line();break;case"area":n=i.area();break;case"bar":n=i.bar();break;case"candlestick":n=i.candlestick();break;case"rangeBar":n=i.rangeBar();break;case"histogram":n=i.bar();break;case"bubble":n=i.bubble();break;case"scatter":n=i.scatter();break;case"heatmap":n=i.heatmap();break;case"pie":n=i.pie();break;case"donut":n=i.donut();break;case"radar":n=i.radar();break;case"radialBar":n=i.radialBar();break;default:n=i.line()}t.chart.brush&&t.chart.brush.enabled&&(n=i.brush(n)),t.chart.stacked&&"100%"===t.chart.stackType&&i.stacked100(),this.checkForDarkTheme(window.Apex),this.checkForDarkTheme(t),t.xaxis=t.xaxis||window.Apex.xaxis||{},e=et.checkComboSeries(t.series),((t="line"!==t.chart.type&&"area"!==t.chart.type&&"scatter"!==t.chart.type||e.comboChartsHasBars||"datetime"===t.xaxis.type||"numeric"===t.xaxis.type||"between"===t.xaxis.tickPlacement?t:O.convertCatToNumeric(t)).chart.sparkline&&t.chart.sparkline.enabled||window.Apex.chart&&window.Apex.chart.sparkline&&window.Apex.chart.sparkline.enabled)&&(n=i.sparkline(n)),a=K.extend(s,n)}return a=K.extend(a,window.Apex),s=K.extend(a,t),this.handleUserInputErrors(s)}},{key:"extendYAxis",value:function(t){var e=new d;return void 0===t.yaxis&&(t.yaxis={}),t.yaxis.constructor!==Array&&window.Apex.yaxis&&window.Apex.yaxis.constructor!==Array&&(t.yaxis=K.extend(t.yaxis,window.Apex.yaxis)),t.yaxis.constructor!==Array?t.yaxis=[K.extend(e.yAxis,t.yaxis)]:t.yaxis=K.extendArray(t.yaxis,e.yAxis),t}},{key:"extendAnnotations",value:function(t){return void 0===t.annotations&&(t.annotations={},t.annotations.yaxis=[],t.annotations.xaxis=[],t.annotations.points=[]),t=this.extendYAxisAnnotations(t),t=this.extendXAxisAnnotations(t),this.extendPointAnnotations(t)}},{key:"extendYAxisAnnotations",value:function(t){var e=new d;return t.annotations.yaxis=K.extendArray(void 0!==t.annotations.yaxis?t.annotations.yaxis:[],e.yAxisAnnotation),t}},{key:"extendXAxisAnnotations",value:function(t){var e=new d;return t.annotations.xaxis=K.extendArray(void 0!==t.annotations.xaxis?t.annotations.xaxis:[],e.xAxisAnnotation),t}},{key:"extendPointAnnotations",value:function(t){var e=new d;return t.annotations.points=K.extendArray(void 0!==t.annotations.points?t.annotations.points:[],e.pointAnnotation),t}},{key:"checkForDarkTheme",value:function(t){t.theme&&"dark"===t.theme.mode&&(t.tooltip||(t.tooltip={}),"light"!==t.tooltip.theme&&(t.tooltip.theme="dark"),t.chart.foreColor||(t.chart.foreColor="#f6f7f8"),t.theme.palette||(t.theme.palette="palette4"))}},{key:"handleUserInputErrors",value:function(t){if(t.tooltip.shared&&t.tooltip.intersect)throw new Error("tooltip.shared cannot be enabled when tooltip.intersect is true. Turn off any other option by setting it to false.");if(t.chart.scroller&&console.warn("Scroller has been deprecated since v2.0.0. Please remove the configuration for chart.scroller"),("bar"===t.chart.type||"rangeBar"===t.chart.type)&&t.plotOptions.bar.horizontal){if(1<t.yaxis.length)throw new Error("Multiple Y Axis for bars are not supported. Switch to column chart by setting plotOptions.bar.horizontal=false");t.yaxis[0].reversed&&(t.yaxis[0].opposite=!0),t.xaxis.tooltip.enabled=!1,t.yaxis[0].tooltip.enabled=!1,t.chart.zoom.enabled=!1}return"bar"!==t.chart.type&&"rangeBar"!==t.chart.type||t.tooltip.shared&&("barWidth"===t.xaxis.crosshairs.width&&1<t.series.length&&(console.warn('crosshairs.width = "barWidth" is only supported in single series, not in a multi-series barChart.'),t.xaxis.crosshairs.width="tickWidth"),t.plotOptions.bar.horizontal&&(t.states.hover.type="none",t.tooltip.shared=!1),t.tooltip.followCursor||(console.warn("followCursor option in shared columns cannot be turned off. Please set %ctooltip.followCursor: true","color: blue;"),t.tooltip.followCursor=!0)),"candlestick"===t.chart.type&&t.yaxis[0].reversed&&(console.warn("Reversed y-axis in candlestick chart is not supported."),t.yaxis[0].reversed=!1),t.chart.group&&0===t.yaxis[0].labels.minWidth&&console.warn("It looks like you have multiple charts in synchronization. You must provide yaxis.labels.minWidth which must be EQUAL for all grouped charts to prevent incorrect behaviour."),Array.isArray(t.stroke.width)&&"line"!==t.chart.type&&"area"!==t.chart.type&&(console.warn("stroke.width option accepts array only for line and area charts. Reverted back to Number"),t.stroke.width=t.stroke.width[0]),t}}]),Dt),H=(t(Rt,[{key:"globalVars",value:function(t){return{chartID:null,cuid:null,events:{beforeMount:[],mounted:[],updated:[],clicked:[],selection:[],dataPointSelection:[],zoomed:[],scrolled:[]},colors:[],clientX:null,clientY:null,fill:{colors:[]},stroke:{colors:[]},dataLabels:{style:{colors:[]}},radarPolygons:{fill:{colors:[]}},markers:{colors:[],size:t.markers.size,largestSize:0},animationEnded:!1,isTouchDevice:"ontouchstart"in window||navigator.msMaxTouchPoints,isDirty:!1,isExecCalled:!1,initialConfig:null,lastXAxis:[],lastYAxis:[],series:[],seriesRangeStart:[],seriesRangeEnd:[],seriesPercent:[],seriesTotals:[],stackedSeriesTotals:[],seriesX:[],seriesZ:[],labels:[],timelineLabels:[],invertedTimelineLabels:[],seriesNames:[],noLabelsProvided:!1,allSeriesCollapsed:!1,collapsedSeries:[],collapsedSeriesIndices:[],ancillaryCollapsedSeries:[],ancillaryCollapsedSeriesIndices:[],risingSeries:[],dataFormatXNumeric:!1,selectedDataPoints:[],ignoreYAxisIndexes:[],yAxisSameScaleIndices:[],padHorizontal:0,maxValsInArrayIndex:0,radialSize:0,zoomEnabled:"zoom"===t.chart.toolbar.autoSelected&&t.chart.toolbar.tools.zoom&&t.chart.zoom.enabled,panEnabled:"pan"===t.chart.toolbar.autoSelected&&t.chart.toolbar.tools.pan,selectionEnabled:"selection"===t.chart.toolbar.autoSelected&&t.chart.toolbar.tools.selection,yaxis:null,minY:Number.MIN_VALUE,maxY:-Number.MAX_VALUE,minYArr:[],maxYArr:[],maxX:-Number.MAX_VALUE,initialmaxX:-Number.MAX_VALUE,minX:Number.MIN_VALUE,initialminX:Number.MIN_VALUE,minZ:Number.MIN_VALUE,maxZ:-Number.MAX_VALUE,minXDiff:Number.MAX_VALUE,mousedown:!1,lastClientPosition:{},visibleXRange:void 0,yRange:[],zRange:0,xRange:0,yValueDecimal:0,total:0,SVGNS:"http://www.w3.org/2000/svg",svgWidth:0,svgHeight:0,noData:!1,locale:{},dom:{},memory:{methodsToExec:[]},shouldAnimate:!0,skipLastTimelinelabel:!1,delayedElements:[],axisCharts:!0,isXNumeric:!1,isDataXYZ:!1,resized:!1,resizeTimer:null,comboCharts:!1,comboChartsHasBars:!1,dataChanged:!1,previousPaths:[],seriesXvalues:[],seriesYvalues:[],seriesCandleO:[],seriesCandleH:[],seriesCandleL:[],seriesCandleC:[],allSeriesHasEqualX:!0,dataPoints:0,pointsArray:[],dataLabelsRects:[],lastDrawnDataLabelsIndexes:[],hasNullValues:!1,easing:null,zoomed:!1,gridWidth:0,gridHeight:0,yAxisScale:[],xAxisScale:null,xAxisTicksPositions:[],timescaleTicks:[],rotateXLabels:!1,defaultLabels:!1,xLabelFormatter:void 0,yLabelFormatters:[],xaxisTooltipFormatter:void 0,ttKeyFormatter:void 0,ttVal:void 0,ttZFormatter:void 0,LINE_HEIGHT_RATIO:1.618,xAxisLabelsHeight:0,yAxisLabelsWidth:0,scaleX:1,scaleY:1,translateX:0,translateY:0,translateYAxisX:[],yLabelsCoords:[],yTitleCoords:[],yAxisWidths:[],translateXAxisY:0,translateXAxisX:0,tooltip:null,tooltipOpts:null}}},{key:"init",value:function(t){var e=this.globalVars(t);return e.initialConfig=K.extend({},t),e.initialSeries=JSON.parse(JSON.stringify(e.initialConfig.series)),e.lastXAxis=JSON.parse(JSON.stringify(e.initialConfig.xaxis)),e.lastYAxis=JSON.parse(JSON.stringify(e.initialConfig.yaxis)),e}}]),Rt),W=(t(Ft,[{key:"init",value:function(){var t=new l(this.opts).init();return{config:t,globals:(new H).init(t)}}}]),Ft),it=(t(Yt,[{key:"clippedImgArea",value:function(t){var e=this.w,i=e.config,s=parseInt(e.globals.gridWidth),a=(o=parseInt(e.globals.gridHeight))<s?s:o,n=t.image,s=0,o=void 0===t.width&&void 0===t.height?void 0!==i.fill.image.width&&void 0!==i.fill.image.height?(s=i.fill.image.width+1,i.fill.image.height):(s=a+1,a):(s=t.width,t.height),i=document.createElementNS(e.globals.SVGNS,"pattern");tt.setAttrs(i,{id:t.patternID,patternUnits:t.patternUnits||"userSpaceOnUse",width:s+"px",height:o+"px"}),a=document.createElementNS(e.globals.SVGNS,"image"),i.appendChild(a),a.setAttributeNS("http://www.w3.org/1999/xlink","href",n),tt.setAttrs(a,{x:0,y:0,preserveAspectRatio:"none",width:s+"px",height:o+"px"}),a.style.opacity=t.opacity,e.globals.dom.elDefs.node.appendChild(i)}},{key:"getSeriesIndex",value:function(t){var e=this.w;return"bar"===e.config.chart.type&&e.config.plotOptions.bar.distributed||"heatmap"===e.config.chart.type?this.seriesIndex=t.seriesNumber:this.seriesIndex=t.seriesNumber%e.globals.series.length,this.seriesIndex}},{key:"fillPath",value:function(t){var e=this.w;this.opts=t;var i,s,a=this.w.config,n=(this.seriesIndex=this.getSeriesIndex(t),this.getFillColors()[this.seriesIndex]),o=("function"==typeof n&&(n=n({seriesIndex:this.seriesIndex,value:t.value,w:e})),this.getFillType(this.seriesIndex)),r=Array.isArray(a.fill.opacity)?a.fill.opacity[this.seriesIndex]:a.fill.opacity,l=n;return-1===(n=t.color||n).indexOf("rgb")?l=K.hexToRgba(n,r):-1<n.indexOf("rgba")&&(r="0."+K.getOpacityFromRGBA(n)),"pattern"===o&&(i=this.handlePatternFill(i,n,r,l)),"gradient"===o&&(s=this.handleGradientFill(s,n,r,this.seriesIndex)),i=0<a.fill.image.src.length&&"image"===o?t.seriesNumber<a.fill.image.src.length?(this.clippedImgArea({opacity:r,image:a.fill.image.src[t.seriesNumber],patternUnits:t.patternUnits,patternID:"pattern".concat(e.globals.cuid).concat(t.seriesNumber+1)}),"url(#pattern".concat(e.globals.cuid).concat(t.seriesNumber+1,")")):l:"gradient"===o?s:"pattern"===o?i:l,t.solid?l:i}},{key:"getFillType",value:function(t){var e=this.w;return Array.isArray(e.config.fill.type)?e.config.fill.type[t]:e.config.fill.type}},{key:"getFillColors",value:function(){var t=this.w,e=t.config,i=this.opts,s=[];return t.globals.comboCharts?"line"===t.config.series[this.seriesIndex].type?t.globals.stroke.colors instanceof Array?s=t.globals.stroke.colors:s.push(t.globals.stroke.colors):t.globals.fill.colors instanceof Array?s=t.globals.fill.colors:s.push(t.globals.fill.colors):"line"===e.chart.type?t.globals.stroke.colors instanceof Array?s=t.globals.stroke.colors:s.push(t.globals.stroke.colors):t.globals.fill.colors instanceof Array?s=t.globals.fill.colors:s.push(t.globals.fill.colors),void 0!==i.fillColors&&(s=[],i.fillColors instanceof Array?s=i.fillColors.slice():s.push(i.fillColors)),s}},{key:"handlePatternFill",value:function(t,e,i,s){var a=this.w.config,n=this.opts,o=new tt(this.ctx),r=void 0===a.fill.pattern.strokeWidth?Array.isArray(a.stroke.width)?a.stroke.width[this.seriesIndex]:a.stroke.width:Array.isArray(a.fill.pattern.strokeWidth)?a.fill.pattern.strokeWidth[this.seriesIndex]:a.fill.pattern.strokeWidth;return a.fill.pattern.style instanceof Array?void 0!==a.fill.pattern.style[n.seriesNumber]?o.drawPattern(a.fill.pattern.style[n.seriesNumber],a.fill.pattern.width,a.fill.pattern.height,e,r,i):s:o.drawPattern(a.fill.pattern.style,a.fill.pattern.width,a.fill.pattern.height,e,r,i)}},{key:"handleGradientFill",value:function(t,e,i,s){var a=this.w.config,n=this.opts,o=new tt(this.ctx),r=new K,l=a.fill.gradient.type,h=void 0===a.fill.gradient.opacityFrom?i:Array.isArray(a.fill.gradient.opacityFrom)?a.fill.gradient.opacityFrom[s]:a.fill.gradient.opacityFrom,c=void 0===a.fill.gradient.opacityTo?i:Array.isArray(a.fill.gradient.opacityTo)?a.fill.gradient.opacityTo[s]:a.fill.gradient.opacityTo;return i=e,r=void 0===a.fill.gradient.gradientToColors||0===a.fill.gradient.gradientToColors.length?"dark"===a.fill.gradient.shade?r.shadeColor(-1*parseFloat(a.fill.gradient.shadeIntensity),e):r.shadeColor(parseFloat(a.fill.gradient.shadeIntensity),e):a.fill.gradient.gradientToColors[n.seriesNumber],a.fill.gradient.inverseColors&&(e=i,i=r,r=e),o.drawGradient(l,i,r,h,c,n.size,a.fill.gradient.stops,a.fill.gradient.colorStops,s)}}]),Yt),st=(t(It,[{key:"setGlobalMarkerSize",value:function(){var e=this.w;if(e.globals.markers.size=Array.isArray(e.config.markers.size)?e.config.markers.size:[e.config.markers.size],0<e.globals.markers.size.length){if(e.globals.markers.size.length<e.globals.series.length+1)for(var t=0;t<=e.globals.series.length;t++)void 0===e.globals.markers.size[t]&&e.globals.markers.size.push(e.globals.markers.size[0])}else e.globals.markers.size=e.config.series.map(function(t){return e.config.markers.size})}},{key:"plotChartMarkers",value:function(t,e,i){var s=this.w,a=e,n=t,o=null,r=new tt(this.ctx);if(0<s.globals.markers.size[e]&&(o=r.group({class:"apexcharts-series-markers"})).attr("clip-path","url(#gridRectMarkerMask".concat(s.globals.cuid,")")),n.x instanceof Array)for(var l=0;l<n.x.length;l++){var h,c=1===i&&1===l?1:1===i&&0===l?0:i,d="apexcharts-marker";"line"!==s.config.chart.type&&"area"!==s.config.chart.type||s.globals.comboCharts||s.config.tooltip.intersect||(d+=" no-pointer-events"),(Array.isArray(s.config.markers.size)?0<s.globals.markers.size[e]:0<s.config.markers.size)?(K.isNumber(n.y[l])?d+=" w".concat((Math.random()+1).toString(36).substring(4)):d="apexcharts-nullpoint",h=this.getMarkerConfig(d,e,c),s.config.series[a].data[i]&&(s.config.series[a].data[i].fillColor&&(h.pointFillColor=s.config.series[a].data[i].fillColor),s.config.series[a].data[i].strokeColor)&&(h.pointStrokeColor=s.config.series[a].data[i].strokeColor),(d=r.drawMarker(n.x[l],n.y[l],h)).attr("rel",c),d.attr("j",c),d.attr("index",e),d.node.setAttribute("default-marker-size",h.pSize),new L(this.ctx).setSelectionFilter(d,e,c),this.addEvents(d),o&&o.add(d)):(void 0===s.globals.pointsArray[e]&&(s.globals.pointsArray[e]=[]),s.globals.pointsArray[e].push([n.x[l],n.y[l]]))}return o}},{key:"getMarkerConfig",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,s=this.w,a=this.getMarkerStyle(e),n=s.globals.markers.size[e];return null!==i&&s.config.markers.discrete.length&&s.config.markers.discrete.map(function(t){t.seriesIndex===e&&t.dataPointIndex===i&&(a.pointStrokeColor=t.strokeColor,a.pointFillColor=t.fillColor,n=t.size)}),{pSize:n,pRadius:s.config.markers.radius,pWidth:s.config.markers.strokeWidth,pointStrokeColor:a.pointStrokeColor,pointFillColor:a.pointFillColor,shape:s.config.markers.shape instanceof Array?s.config.markers.shape[e]:s.config.markers.shape,class:t,pointStrokeOpacity:s.config.markers.strokeOpacity,pointFillOpacity:s.config.markers.fillOpacity,seriesIndex:e}}},{key:"addEvents",value:function(t){var e=this.w,i=new tt(this.ctx);t.node.addEventListener("mouseenter",i.pathMouseEnter.bind(this.ctx,t)),t.node.addEventListener("mouseleave",i.pathMouseLeave.bind(this.ctx,t)),t.node.addEventListener("mousedown",i.pathMouseDown.bind(this.ctx,t)),t.node.addEventListener("click",e.config.markers.onClick),t.node.addEventListener("dblclick",e.config.markers.onDblClick),t.node.addEventListener("touchstart",i.pathMouseDown.bind(this.ctx,t),{passive:!0})}},{key:"getMarkerStyle",value:function(t){var e=this.w,i=e.globals.markers.colors;return{pointStrokeColor:(e=e.config.markers.strokeColor||e.config.markers.strokeColors)instanceof Array?e[t]:e,pointFillColor:i instanceof Array?i[t]:i}}}]),It),B=(t(Xt,[{key:"draw",value:function(t,e,i){var s=this.w,a=new tt(this.ctx),n=i.realIndex,o=i.pointsPos,r=i.zRatio,l=i.elParent,h=a.group({class:"apexcharts-series-markers apexcharts-series-".concat(s.config.chart.type)});if(h.attr("clip-path","url(#gridRectMarkerMask".concat(s.globals.cuid,")")),o.x instanceof Array)for(var c=0;c<o.x.length;c++){var d=e+1,u=(0===e&&0===c&&(d=0),0===e&&1===c&&(d=1),0),g=s.globals.markers.size[n],p=(r!==1/0&&(g=s.globals.seriesZ[n][d]/r,void 0===this.radiusSizes[n]&&this.radiusSizes.push([]),this.radiusSizes[n].push(g)),s.config.chart.animations.enabled||(u=g),o.x[c]),f=o.y[c],u=u||0;(0!==p||0!==f)&&null!==f&&void 0!==s.globals.series[n][d]&&!0&&(d=this.drawPoint(p,f,u,g,n,d,e),h.add(d)),l.add(h)}}},{key:"drawPoint",value:function(t,e,i,s,a,n,o){var r,l=this.w,h=a,c=new z(this.ctx),d=new L(this.ctx),u=new it(this.ctx),g=new st(this.ctx),p=new tt(this.ctx),f=g.getMarkerConfig("apexcharts-marker",h),u=u.fillPath({seriesNumber:a,patternUnits:"objectBoundingBox",value:l.globals.series[a][o]}),i=p.drawCircle(i);if(l.config.series[h].data[n]&&l.config.series[h].data[n].fillColor&&(u=l.config.series[h].data[n].fillColor),i.attr({cx:t,cy:e,fill:u,stroke:f.pointStrokeColor,strokeWidth:f.pWidth}),l.config.chart.dropShadow.enabled&&(r=l.config.chart.dropShadow,d.dropShadow(i,r,a)),this.initialAnim&&!l.globals.dataChanged&&(r=1,l.globals.resized||(r=l.config.chart.animations.speed),c.animateCircleRadius(i,0,s,r,l.globals.easing)),l.globals.dataChanged)if(this.dynamicAnim){var x,b,m,y=l.config.chart.animations.dynamicAnimation.speed;null!=(o=l.globals.previousPaths[a]&&l.globals.previousPaths[a][o])&&(x=o.x,b=o.y,m=void 0!==o.r?o.r:s);for(var v=0;v<l.globals.collapsedSeries.length;v++)l.globals.collapsedSeries[v].index===a&&(y=1,s=0);c.animateCircle(i,{cx:x,cy:b,r:m},{cx:t,cy:e,r:s=0===t&&0===e?0:s},y,l.globals.easing)}else i.attr({r:s});return i.attr({rel:n,j:n,index:a,"default-marker-size":s}),d.setSelectionFilter(i,a,n),g.addEvents(i),i.node.classList.add("apexcharts-marker"),i}},{key:"centerTextInBubble",value:function(t){var e=this.w;return{y:t+=parseInt(e.config.dataLabels.style.fontSize)/4}}}]),Xt),at=(t(Tt,[{key:"dataLabelsCorrection",value:function(t,e,i,s,a,n,o){var r=this.w,l=!1,h=new tt(this.ctx).getTextRects(i,o),i=h.width,o=h.height;return void 0===r.globals.dataLabelsRects[s]&&(r.globals.dataLabelsRects[s]=[]),r.globals.dataLabelsRects[s].push({x:t,y:e,width:i,height:o}),h=r.globals.dataLabelsRects[s].length-2,o=void 0!==r.globals.lastDrawnDataLabelsIndexes[s]?r.globals.lastDrawnDataLabelsIndexes[s][r.globals.lastDrawnDataLabelsIndexes[s].length-1]:0,void 0!==r.globals.dataLabelsRects[s][h]&&(t>(o=r.globals.dataLabelsRects[s][o]).x+o.width+2||e>o.y+o.height+2||t+i<o.x)&&(l=!0),{x:t,y:e,drawnextLabel:l=!(0!==a&&!n)||l}}},{key:"drawDataLabel",value:function(t,e,i){var s=4<arguments.length&&void 0!==arguments[4]?arguments[4]:"top",a=this.w,n=new tt(this.ctx),o=a.config.dataLabels,r=0,l=i,h=null;if(!o.enabled||t.x instanceof Array!=1)return null;(h=n.group({class:"apexcharts-data-labels"})).attr("clip-path","url(#gridRectMarkerMask".concat(a.globals.cuid,")"));for(var c=0;c<t.x.length;c++){var d,u,g=t.x[c]+o.offsetX,r=t.y[c]+o.offsetY-a.globals.markers.size[e]-5;"bottom"===s&&(r=r+2*a.globals.markers.size[e]+1.4*parseInt(o.style.fontSize)),isNaN(g)||(1===i&&0===c&&(l=0),d=a.globals.series[e][l=1===i&&1===c?1:l],u="","bubble"===a.config.chart.type?(u=a.globals.seriesZ[e][l],r=t.y[c]+a.config.dataLabels.offsetY,r=new B(this.ctx).centerTextInBubble(r,e,l).y):void 0!==d&&(u=a.config.dataLabels.formatter(d,{ctx:this.ctx,seriesIndex:e,dataPointIndex:l,w:a})),this.plotDataLabelsText({x:g,y:r,text:u,i:e,j:l,parent:h,offsetCorrection:!0,dataLabelsConfig:a.config.dataLabels}))}return h}},{key:"plotDataLabelsText",value:function(t){var e=this.w,i=new tt(this.ctx),s=t.x,a=t.y,n=t.i,o=t.j,r=t.text,l=t.textAnchor,h=t.parent,c=t.dataLabelsConfig,d=t.alwaysDrawDataLabel,u=t.offsetCorrection;Array.isArray(e.config.dataLabels.enabledOnSeries)&&-1<e.config.dataLabels.enabledOnSeries.indexOf(n)||(t={x:s,y:a,drawnextLabel:!0},u&&(t=this.dataLabelsCorrection(s,a,r,n,o,d,parseInt(c.style.fontSize))),e.globals.zoomed||(s=t.x,a=t.y),t.drawnextLabel&&((r=i.drawText({width:100,height:parseInt(c.style.fontSize),x:s,y:a,foreColor:e.globals.dataLabels.style.colors[n],textAnchor:l||c.textAnchor,text:r,fontSize:c.style.fontSize,fontFamily:c.style.fontFamily})).attr({class:"apexcharts-datalabel",cx:s,cy:a}),c.dropShadow.enabled&&(c=c.dropShadow,new L(this.ctx).dropShadow(r,c)),h.add(r),void 0===e.globals.lastDrawnDataLabelsIndexes[n]&&(e.globals.lastDrawnDataLabelsIndexes[n]=[]),e.globals.lastDrawnDataLabelsIndexes[n].push(o)))}}]),Tt),P=(t(Mt,[{key:"draw",value:function(t,e){var i=this.w,s=new tt(this.ctx),a=new et(this.ctx,i),n=(t=a.getLogSeries(t),this.series=t,this.yRatio=a.getLogYRatios(this.yRatio),this.initVariables(t),s.group({class:"apexcharts-bar-series apexcharts-plot-series"}));i.config.dataLabels.enabled&&this.totalItems>i.config.plotOptions.bar.dataLabels.maxItems&&console.warn("WARNING: DataLabels are enabled but there are too many to display. This may cause performance issue when rendering.");for(var o=0,r=0;o<t.length;o++,r++){var l=void 0,h=void 0,c=void 0,d=void 0,u=[],g=[],p=i.globals.comboCharts?e[o]:o,f=s.group({class:"apexcharts-series",rel:o+1,seriesName:K.escapeString(i.globals.seriesNames[p]),"data:realIndex":p}),x=(this.ctx.series.addCollapsedClassToSeries(f,p),0<t[o].length&&(this.visibleI=this.visibleI+1),0),b=0,m=0,y=(1<this.yRatio.length&&(this.yaxisIndex=p),this.isReversed=i.config.yaxis[this.yaxisIndex]&&i.config.yaxis[this.yaxisIndex].reversed,this.initialPositions()),d=y.y,b=y.barHeight,v=y.yDivision,w=y.zeroW,c=y.x,m=y.barWidth,k=y.xDivision,A=y.zeroH,S=(this.horizontal||g.push(c+m/2),s.group({class:"apexcharts-datalabels"})),C=0;for(i.globals.dataPoints;C<i.globals.dataPoints;C++){void 0===this.series[o][C]||null===t[o][C]?this.isNullValue=!0:this.isNullValue=!1,i.config.stroke.show&&(x=this.isNullValue?0:Array.isArray(this.strokeWidth)?this.strokeWidth[p]:this.strokeWidth);var L=null;this.isHorizontal?(L=this.drawBarPaths({indexes:{i:o,j:C,realIndex:p,bc:r},barHeight:b,strokeWidth:x,pathTo:l,pathFrom:h,zeroW:w,x:c,y:d,yDivision:v,elSeries:f}),m=this.series[o][C]/this.invertedYRatio):(L=this.drawColumnPaths({indexes:{i:o,j:C,realIndex:p,bc:r},x:c,y:d,xDivision:k,pathTo:l,pathFrom:h,barWidth:m,zeroH:A,strokeWidth:x,elSeries:f}),b=this.series[o][C]/this.yRatio[this.yaxisIndex]),l=L.pathTo,h=L.pathFrom,d=L.y,c=L.x,0<C&&g.push(c+m/2),u.push(d),L=this.getPathFillColor(t,o,C,p),f=this.renderSeries({realIndex:p,pathFill:L,j:C,i:o,pathFrom:h,pathTo:l,strokeWidth:x,elSeries:f,x:c,y:d,series:t,barHeight:b,barWidth:m,elDataLabelsWrap:S,visibleSeries:this.visibleI,type:"bar"})}i.globals.seriesXvalues[p]=g,i.globals.seriesYvalues[p]=u,n.add(f)}return n}},{key:"getPathFillColor",value:function(e,i,s,t){var a=this.w,n=new it(this.ctx),o=null,r=this.barOptions.distributed?s:i;return 0<this.barOptions.colors.ranges.length&&this.barOptions.colors.ranges.map(function(t){e[i][s]>=t.from&&e[i][s]<=t.to&&(o=t.color)}),a.config.series[i].data[s]&&a.config.series[i].data[s].fillColor&&(o=a.config.series[i].data[s].fillColor),n.fillPath({seriesNumber:this.barOptions.distributed?r:t,color:o,value:e[i][s]})}},{key:"renderSeries",value:function(t){var e=t.realIndex,i=t.pathFill,s=t.lineFill,a=t.j,n=t.i,o=t.pathFrom,r=t.pathTo,l=t.strokeWidth,h=t.elSeries,c=t.x,d=t.y,u=t.series,g=t.barHeight,p=t.barWidth,f=t.elDataLabelsWrap,x=t.visibleSeries,b=t.type,m=this.w,y=new tt(this.ctx),s=s||(this.barOptions.distributed?m.globals.stroke.colors[a]:m.globals.stroke.colors[e]);return m.config.series[n].data[a]&&m.config.series[n].data[a].strokeColor&&(s=m.config.series[n].data[a].strokeColor),this.isNullValue&&(i="none"),t=a/m.config.chart.animations.animateGradually.delay*(m.config.chart.animations.speed/m.globals.dataPoints)/2.4,(b=y.renderPaths({i:n,j:a,realIndex:e,pathFrom:o,pathTo:r,stroke:s,strokeWidth:l,strokeLineCap:m.config.stroke.lineCap,fill:i,animationDelay:t,initialSpeed:m.config.chart.animations.speed,dataChangeSpeed:m.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-".concat(b,"-area")})).attr("clip-path","url(#gridRectMask".concat(m.globals.cuid,")")),new L(this.ctx).setSelectionFilter(b,e,a),h.add(b),null!==(x=this.calculateDataLabelsPos({x:c,y:d,i:n,j:a,series:u,realIndex:e,barHeight:g,barWidth:p,renderedPath:b,visibleSeries:x}))&&f.add(x),h.add(f),h}},{key:"initVariables",value:function(t){var e=this.w;this.series=t,this.totalItems=0,this.seriesLen=0,this.visibleI=-1,this.visibleItems=1;for(var i=0;i<t.length;i++)if(0<t[i].length&&(this.seriesLen=this.seriesLen+1,this.totalItems+=t[i].length),e.globals.isXNumeric)for(var s=0;s<t[i].length;s++)e.globals.seriesX[i][s]>e.globals.minX&&e.globals.seriesX[i][s]<e.globals.maxX&&this.visibleItems++;else this.visibleItems=e.globals.dataPoints;0===this.seriesLen&&(this.seriesLen=1)}},{key:"initialPositions",value:function(){var t,e,i,s,a,n,o,r,l=this.w;return this.isHorizontal?(a=(i=l.globals.gridHeight/l.globals.dataPoints)/this.seriesLen,a=(a=l.globals.isXNumeric?(i=l.globals.gridHeight/this.totalItems)/this.seriesLen:a)*parseInt(this.barOptions.barHeight)/100,r=this.baseLineInvertedY+l.globals.padHorizontal+(this.isReversed?l.globals.gridWidth:0)-(this.isReversed?2*this.baseLineInvertedY:0),e=(i-a*this.seriesLen)/2):(n=(s=l.globals.gridWidth/this.visibleItems)/this.seriesLen*parseInt(this.barOptions.columnWidth)/100,l.globals.isXNumeric&&(n=(s=l.globals.minXDiff/this.xRatio)/this.seriesLen*parseInt(this.barOptions.columnWidth)/100),o=l.globals.gridHeight-this.baseLineY[this.yaxisIndex]-(this.isReversed?l.globals.gridHeight:0)+(this.isReversed?2*this.baseLineY[this.yaxisIndex]:0),t=l.globals.padHorizontal+(s-n*this.seriesLen)/2),{x:t,y:e,yDivision:i,xDivision:s,barHeight:a,barWidth:n,zeroH:o,zeroW:r}}},{key:"drawBarPaths",value:function(t){var e,i=t.indexes,s=t.barHeight,a=t.strokeWidth,n=(t.pathTo,t.pathFrom),o=t.zeroW,r=(t.x,t.y),l=t.yDivision,h=t.elSeries,c=this.w,d=new tt(this.ctx),u=i.i,g=i.j,p=i.realIndex,t=i.bc,i=(r=c.globals.isXNumeric?(c.globals.seriesX[u][g]-c.globals.minX)/this.invertedXRatio-s:r)+s*this.visibleI,f=d.move(o,i),n=d.move(o,i);return 0<c.globals.previousPaths.length&&(n=this.getPathFrom(p,g)),p={barHeight:s,strokeWidth:a,barYPosition:i,x:e=void 0===this.series[u][g]||null===this.series[u][g]?o:o+this.series[u][g]/this.invertedYRatio-2*(this.isReversed?this.series[u][g]/this.invertedYRatio:0),zeroW:o},g=this.barEndingShape(c,p,this.series,u,g),f=f+d.line(g.newX,i)+g.path+d.line(o,i+s-a)+d.line(o,i),n=n+d.line(o,i)+g.ending_p_from+d.line(o,i+s-a)+d.line(o,i+s-a)+d.line(o,i),c.globals.isXNumeric||(r+=l),0<this.barOptions.colors.backgroundBarColors.length&&0===u&&(t>=this.barOptions.colors.backgroundBarColors.length&&(t=0),t=this.barOptions.colors.backgroundBarColors[t],t=d.drawRect(0,i-s*this.visibleI,c.globals.gridWidth,s*this.seriesLen,0,t,this.barOptions.colors.backgroundBarOpacity),h.add(t),t.node.classList.add("apexcharts-backgroundBar")),{pathTo:f,pathFrom:n,x:e,y:r,barYPosition:i}}},{key:"drawColumnPaths",value:function(t){var e,i=t.indexes,s=t.x,a=(t.y,t.xDivision),n=(t.pathTo,t.pathFrom),o=t.barWidth,r=t.zeroH,l=t.strokeWidth,h=t.elSeries,c=this.w,d=new tt(this.ctx),u=i.i,g=i.j,p=i.realIndex,t=i.bc,i=(s=c.globals.isXNumeric?(c.globals.seriesX[u][g]-c.globals.minX)/this.xRatio-o/2:s)+o*this.visibleI,f=d.move(i,r),n=d.move(i,r);return 0<c.globals.previousPaths.length&&(n=this.getPathFrom(p,g)),p={barWidth:o,strokeWidth:l,barXPosition:i,y:e=void 0===this.series[u][g]||null===this.series[u][g]?r:r-this.series[u][g]/this.yRatio[this.yaxisIndex]+2*(this.isReversed?this.series[u][g]/this.yRatio[this.yaxisIndex]:0),zeroH:r},g=this.barEndingShape(c,p,this.series,u,g),f=f+d.line(i,g.newY)+g.path+d.line(i+o-l,r)+d.line(i-l/2,r),n=n+d.line(i,r)+g.ending_p_from+d.line(i+o-l,r)+d.line(i+o-l,r)+d.line(i-l/2,r),c.globals.isXNumeric||(s+=a),0<this.barOptions.colors.backgroundBarColors.length&&0===u&&(t>=this.barOptions.colors.backgroundBarColors.length&&(t=0),t=this.barOptions.colors.backgroundBarColors[t],t=d.drawRect(i-o*this.visibleI,0,o*this.seriesLen,c.globals.gridHeight,0,t,this.barOptions.colors.backgroundBarOpacity),h.add(t),t.node.classList.add("apexcharts-backgroundBar")),{pathTo:f,pathFrom:n,x:s,y:e,barXPosition:i}}},{key:"getPathFrom",value:function(t,e){for(var i,s=this.w,a=0;a<s.globals.previousPaths.length;a++){var n=s.globals.previousPaths[a];0<n.paths.length&&parseInt(n.realIndex)===parseInt(t)&&void 0!==s.globals.previousPaths[a].paths[e]&&(i=s.globals.previousPaths[a].paths[e].d)}return i}},{key:"calculateDataLabelsPos",value:function(t){var e=t.x,i=t.y,s=t.i,a=t.j,n=t.realIndex,o=t.series,r=t.barHeight,l=t.barWidth,h=t.visibleSeries,c=t.renderedPath,d=this.w,u=new tt(this.ctx),g=Array.isArray(this.strokeWidth)?this.strokeWidth[n]:this.strokeWidth,p=e+parseFloat(l*h),f=i+parseFloat(r*h),x=(d.globals.isXNumeric&&!d.globals.isBarHorizontal&&(p=e+parseFloat(l*(h+1))-g,f=i+parseFloat(r*(h+1))-g),e),b=i,m=d.config.dataLabels,y=this.barOptions.dataLabels,v=m.offsetX,t=m.offsetY,h={width:0,height:0};return d.config.dataLabels.enabled&&(h=u.getTextRects(d.globals.yLabelFormatters[0](d.globals.maxY),parseInt(m.style.fontSize))),t=this.isHorizontal?this.calculateBarsDataLabelsPosition({x:e,y:i,i:s,j:a,renderedPath:c,bcy:f,barHeight:r,barWidth:l,textRects:h,strokeWidth:g,dataLabelsX:x,dataLabelsY:b,barDataLabelsConfig:y,offX:v,offY:t}):this.calculateColumnsDataLabelsPosition({x:e,y:i,i:s,j:a,renderedPath:c,realIndex:n,bcx:p,bcy:f,barHeight:r,barWidth:l,textRects:h,strokeWidth:g,dataLabelsY:b,barDataLabelsConfig:y,offX:v,offY:t}),c.attr({cy:t.bcy,cx:t.bcx,j:a,val:o[s][a],barHeight:r,barWidth:l}),this.drawCalculatedDataLabels({x:t.dataLabelsX,y:t.dataLabelsY,val:o[s][a],i:n,j:a,barWidth:l,barHeight:r,textRects:h,dataLabelsConfig:m})}},{key:"calculateColumnsDataLabelsPosition",value:function(t){var e=this.w,i=t.i,s=t.j,a=t.y,n=t.bcx,o=t.barWidth,r=t.barHeight,l=t.textRects,h=t.dataLabelsY,c=t.barDataLabelsConfig,d=t.strokeWidth,u=t.offX,g=t.offY,t=e.globals.gridWidth/e.globals.dataPoints,u=(n-=d/2,e.globals.isXNumeric?n-o/2+u:n-t+o/2+u),p=this.series[i][s]<=0;switch(this.isReversed&&(a-=r),c.position){case"center":h=p?a+r/2+l.height/2+g:a+r/2+l.height/2-g;break;case"bottom":h=p?a+r+l.height+d+g:a+r-l.height/2+d-g;break;case"top":h=p?a-l.height/2-g:a+l.height+g}return e.config.chart.stacked||(h<0?h=0+d:h+l.height/3>e.globals.gridHeight&&(h=e.globals.gridHeight-d)),{bcx:n,bcy:a,dataLabelsX:u,dataLabelsY:h}}},{key:"calculateBarsDataLabelsPosition",value:function(t){var e=this.w,i=t.x,s=t.i,a=t.j,n=t.bcy,o=t.barHeight,r=t.barWidth,l=t.textRects,h=t.dataLabelsX,c=t.strokeWidth,d=t.barDataLabelsConfig,u=t.offX,t=t.offY,t=n-e.globals.gridHeight/e.globals.dataPoints+o/2+l.height/2+t-3,g=this.series[s][a]<=0;switch(this.isReversed&&(i+=r),d.position){case"center":h=g?i-r/2-u:i-r/2+u;break;case"bottom":h=g?i-r-c-Math.round(l.width/2)-u:i-r+c+Math.round(l.width/2)+u;break;case"top":h=g?i-c+Math.round(l.width/2)-u:i-c-Math.round(l.width/2)+u}return e.config.chart.stacked||(h<0?h=h+l.width+c:h+l.width/2>e.globals.gridWidth&&(h=e.globals.gridWidth-l.width-c)),{bcx:i,bcy:n,dataLabelsX:h,dataLabelsY:t}}},{key:"drawCalculatedDataLabels",value:function(t){var e=t.x,i=t.y,s=t.val,a=t.i,n=t.j,o=t.textRects,r=t.barHeight,l=(t.barWidth,t.dataLabelsConfig),h=this.w,c=new at(this.ctx),d=new tt(this.ctx),u=l.formatter,g=null,t=-1<h.globals.collapsedSeriesIndices.indexOf(a);return l.enabled&&!t&&(g=d.group({class:"apexcharts-data-labels"}),d="",void 0!==s&&(d=u(s,{seriesIndex:a,dataPointIndex:n,w:h})),0===s&&h.config.chart.stacked&&(d=""),h.config.chart.stacked&&this.barOptions.dataLabels.hideOverflowingLabels&&(this.isHorizontal?(0<(t=this.series[a][n]/this.yRatio[this.yaxisIndex])&&o.width/1.6>t||t<0&&o.width/1.6<t)&&(d=""):(r=this.series[a][n]/this.yRatio[this.yaxisIndex],o.height/1.6>r&&(d=""))),r=Q({},l),this.isHorizontal&&s<0&&("start"===l.textAnchor?r.textAnchor="end":"end"===l.textAnchor&&(r.textAnchor="start")),c.plotDataLabelsText({x:e,y:i,text:d,i:a,j:n,parent:g,dataLabelsConfig:r,alwaysDrawDataLabel:!0,offsetCorrection:!0})),g}},{key:"barEndingShape",value:function(t,e,i,s,a){var n=new tt(this.ctx);if(this.isHorizontal){var o=null,r=e.x;if(void 0!==i[s][a]||null!==i[s][a]){var l=i[s][a]<0,h=e.barHeight/2-e.strokeWidth;switch(l&&(h=-e.barHeight/2-e.strokeWidth),t.config.chart.stacked||"rounded"===this.barOptions.endingShape&&(r=e.x-h/2),this.barOptions.endingShape){case"flat":o=n.line(r,e.barYPosition+e.barHeight-e.strokeWidth);break;case"rounded":o=n.quadraticCurve(r+h,e.barYPosition+(e.barHeight-e.strokeWidth)/2,r,e.barYPosition+e.barHeight-e.strokeWidth)}}return{path:o,ending_p_from:"",newX:r}}var c=null,d=e.y;if(void 0!==i[s][a]||null!==i[s][a]){var a=i[s][a]<0,u=e.barWidth/2-e.strokeWidth;switch(a&&(u=-e.barWidth/2-e.strokeWidth),t.config.chart.stacked||"rounded"===this.barOptions.endingShape&&(d+=u/2),this.barOptions.endingShape){case"flat":c=n.line(e.barXPosition+e.barWidth-e.strokeWidth,d);break;case"rounded":c=n.quadraticCurve(e.barXPosition+(e.barWidth-e.strokeWidth)/2,d-u,e.barXPosition+e.barWidth-e.strokeWidth,d)}}return{path:c,ending_p_from:"",newY:d}}}]),Mt),V=(e(h,P),t(h,[{key:"draw",value:function(t,e){var i=this.w,s=(this.graphics=new tt(this.ctx),this.fill=new it(this.ctx),this.bar=new P(this.ctx,this.xyRatios),new et(this.ctx,i));t=s.getLogSeries(t),this.yRatio=s.getLogYRatios(this.yRatio),this.initVariables(t),"100%"===i.config.chart.stackType&&(t=i.globals.seriesPercent.slice()),this.series=t,this.totalItems=0,this.prevY=[],this.prevX=[],this.prevYF=[],this.prevXF=[],this.prevYVal=[],this.prevXVal=[],this.xArrj=[],this.xArrjF=[],this.xArrjVal=[],this.yArrj=[],this.yArrjF=[],this.yArrjVal=[];for(var a=0;a<t.length;a++)0<t[a].length&&(this.totalItems+=t[a].length);for(var n=this.graphics.group({class:"apexcharts-bar-series apexcharts-plot-series"}),o=0,r=0,l=0,h=0;l<t.length;l++,h++){var c=void 0,d=void 0,u=[],g=[],p=i.globals.comboCharts?e[l]:l,f=(1<this.yRatio.length&&(this.yaxisIndex=p),this.isReversed=i.config.yaxis[this.yaxisIndex]&&i.config.yaxis[this.yaxisIndex].reversed,this.graphics.group({class:"apexcharts-series",seriesName:K.escapeString(i.globals.seriesNames[p]),rel:l+1,"data:realIndex":p})),x=this.graphics.group({class:"apexcharts-datalabels"}),b=0,m=0,y=0,v=this.initialPositions(o,r,void 0,void 0,void 0,void 0),r=v.y,m=v.barHeight,w=v.yDivision,k=v.zeroW,o=v.x,y=v.barWidth,A=v.xDivision,S=v.zeroH;this.yArrj=[],this.yArrjF=[],this.yArrjVal=[],this.xArrj=[],this.xArrjF=[],this.xArrjVal=[];for(var C=0;C<i.globals.dataPoints;C++){i.config.stroke.show&&(b=this.isNullValue?0:Array.isArray(this.strokeWidth)?this.strokeWidth[p]:this.strokeWidth);var L=null;this.isHorizontal?(L=this.drawBarPaths({indexes:{i:l,j:C,realIndex:p,bc:h},barHeight:m,strokeWidth:b,pathTo:c,pathFrom:d,zeroW:k,x:o,y:r,yDivision:w,elSeries:f}),y=this.series[l][C]/this.invertedYRatio):(L=this.drawColumnPaths({indexes:{i:l,j:C,realIndex:p,bc:h},x:o,y:r,xDivision:A,pathTo:c,pathFrom:d,barWidth:y,zeroH:S,strokeWidth:b,elSeries:f}),m=this.series[l][C]/this.yRatio[this.yaxisIndex]),c=L.pathTo,d=L.pathFrom,r=L.y,o=L.x,u.push(o),g.push(r),L=this.bar.getPathFillColor(t,l,C,p),f=this.renderSeries({realIndex:p,pathFill:L,j:C,i:l,pathFrom:d,pathTo:c,strokeWidth:b,elSeries:f,x:o,y:r,series:t,barHeight:m,barWidth:y,elDataLabelsWrap:x,type:"bar",visibleSeries:0})}i.globals.seriesXvalues[p]=u,i.globals.seriesYvalues[p]=g,this.prevY.push(this.yArrj),this.prevYF.push(this.yArrjF),this.prevYVal.push(this.yArrjVal),this.prevX.push(this.xArrj),this.prevXF.push(this.xArrjF),this.prevXVal.push(this.xArrjVal),n.add(f)}return n}},{key:"initialPositions",value:function(t,e,i,s,a,n){var o,r,l=this.w;return this.isHorizontal?(o=(o=s=l.globals.gridHeight/l.globals.dataPoints)*parseInt(l.config.plotOptions.bar.barHeight)/100,n=this.baseLineInvertedY+l.globals.padHorizontal+(this.isReversed?l.globals.gridWidth:0)-(this.isReversed?2*this.baseLineInvertedY:0),e=(s-o)/2):(r=i=l.globals.gridWidth/l.globals.dataPoints,r=l.globals.isXNumeric?(i=l.globals.minXDiff/this.xRatio)*parseInt(this.barOptions.columnWidth)/100:r*parseInt(l.config.plotOptions.bar.columnWidth)/100,a=this.baseLineY[this.yaxisIndex]+(this.isReversed?l.globals.gridHeight:0)-(this.isReversed?2*this.baseLineY[this.yaxisIndex]:0),t=l.globals.padHorizontal+(i-r)/2),{x:t,y:e,yDivision:s,xDivision:i,barHeight:o,barWidth:r,zeroH:a,zeroW:n}}},{key:"drawBarPaths",value:function(t){for(var e,i=t.indexes,s=t.barHeight,a=t.strokeWidth,n=(t.pathTo,t.pathFrom),o=t.zeroW,r=(t.x,t.y),l=t.yDivision,h=t.elSeries,c=this.w,d=r,u=i.i,g=i.j,t=i.realIndex,i=i.bc,p=0,f=0;f<this.prevXF.length;f++)p+=this.prevXF[f][g];var x=0<u?(x=o,this.prevXVal[u-1][g]<0?x=0<=this.series[u][g]?this.prevX[u-1][g]+p-2*(this.isReversed?p:0):this.prevX[u-1][g]:0<=this.prevXVal[u-1][g]&&(x=0<=this.series[u][g]?this.prevX[u-1][g]:this.prevX[u-1][g]-p+2*(this.isReversed?p:0)),x):o,b=null===this.series[u][g]?x:x+this.series[u][g]/this.invertedYRatio-2*(this.isReversed?this.series[u][g]/this.invertedYRatio:0),o={barHeight:s,strokeWidth:a,invertedYRatio:this.invertedYRatio,barYPosition:d,x:b},o=this.bar.barEndingShape(c,o,this.series,u,g);return 1<this.series.length&&u!==this.endingShapeOnSeriesNumber&&(o.path=this.graphics.line(o.newX,d+s-a)),this.xArrj.push(o.newX),this.xArrjF.push(Math.abs(x-o.newX)),this.xArrjVal.push(this.series[u][g]),e=this.graphics.move(x,d),n=this.graphics.move(x,d),0<c.globals.previousPaths.length&&(n=this.bar.getPathFrom(t,g,!1)),e=e+this.graphics.line(o.newX,d)+o.path+this.graphics.line(x,d+s-a)+this.graphics.line(x,d),n=n+this.graphics.line(x,d)+this.graphics.line(x,d+s-a)+this.graphics.line(x,d+s-a)+this.graphics.line(x,d+s-a)+this.graphics.line(x,d),0<c.config.plotOptions.bar.colors.backgroundBarColors.length&&0===u&&(i>=c.config.plotOptions.bar.colors.backgroundBarColors.length&&(i=0),i=c.config.plotOptions.bar.colors.backgroundBarColors[i],c=this.graphics.drawRect(0,d,c.globals.gridWidth,s,0,i,c.config.plotOptions.bar.colors.backgroundBarOpacity),h.add(c),c.node.classList.add("apexcharts-backgroundBar")),{pathTo:e,pathFrom:n,x:b,y:r+=l}}},{key:"drawColumnPaths",value:function(t){for(var e,i,s,a=t.indexes,n=t.x,o=(t.y,t.xDivision),r=(t.pathTo,t.pathFrom),l=t.barWidth,h=t.zeroH,c=t.strokeWidth,d=t.elSeries,u=this.w,g=a.i,p=a.j,f=a.realIndex,t=a.bc,a=n=u.globals.isXNumeric?((u.globals.seriesX[g][p]||0)-u.globals.minX)/this.xRatio-l/2:n,x=0,b=0;b<this.prevYF.length;b++)x+=this.prevYF[b][p];return i=(e=0<g&&!u.globals.isXNumeric||0<g&&u.globals.isXNumeric&&u.globals.seriesX[g-1][p]===u.globals.seriesX[g][p]?(e=this.prevY[g-1][p],this.prevYVal[g-1][p]<0?0<=this.series[g][p]?e-x+2*(this.isReversed?x:0):e:0<=this.series[g][p]?e:e+x-2*(this.isReversed?x:0)):u.globals.gridHeight-h)-this.series[g][p]/this.yRatio[this.yaxisIndex]+2*(this.isReversed?this.series[g][p]/this.yRatio[this.yaxisIndex]:0),h={barWidth:l,strokeWidth:c,yRatio:this.yRatio[this.yaxisIndex],barXPosition:a,y:i},h=this.bar.barEndingShape(u,h,this.series,g,p),this.yArrj.push(h.newY),this.yArrjF.push(Math.abs(e-h.newY)),this.yArrjVal.push(this.series[g][p]),s=this.graphics.move(a,e),r=this.graphics.move(a,e),0<u.globals.previousPaths.length&&(r=this.bar.getPathFrom(f,p,!1)),s=s+this.graphics.line(a,h.newY)+h.path+this.graphics.line(a+l-c,e)+this.graphics.line(a-c/2,e),r=r+this.graphics.line(a,e)+this.graphics.line(a+l-c,e)+this.graphics.line(a+l-c,e)+this.graphics.line(a+l-c,e)+this.graphics.line(a-c/2,e),0<u.config.plotOptions.bar.colors.backgroundBarColors.length&&0===g&&(t>=u.config.plotOptions.bar.colors.backgroundBarColors.length&&(t=0),t=u.config.plotOptions.bar.colors.backgroundBarColors[t],t=this.graphics.drawRect(a,0,l,u.globals.gridHeight,0,t,u.config.plotOptions.bar.colors.backgroundBarOpacity),d.add(t),t.node.classList.add("apexcharts-backgroundBar")),n+=o,{pathTo:s,pathFrom:r,x:u.globals.isXNumeric?n-o:n,y:i}}},{key:"checkZeroSeries",value:function(t){for(var e=t.series,i=this.w,s=0;s<e.length;s++){for(var a=0,n=0;n<e[i.globals.maxValsInArrayIndex].length;n++)a+=e[s][n];0===a&&this.zeroSerieses.push(s)}for(var o=e.length-1;0<=o;o--)-1<this.zeroSerieses.indexOf(o)&&o===this.endingShapeOnSeriesNumber&&--this.endingShapeOnSeriesNumber}}]),h),G=(e(r,P),t(r,[{key:"draw",value:function(t,e){var i=this.w,s=new tt(this.ctx),a=new it(this.ctx),n=(this.candlestickOptions=this.w.config.plotOptions.candlestick,new et(this.ctx,i));t=n.getLogSeries(t),this.series=t,this.yRatio=n.getLogYRatios(this.yRatio),this.initVariables(t);for(var o=s.group({class:"apexcharts-candlestick-series apexcharts-plot-series"}),r=0,l=0;r<t.length;r++,l++){var h,c,d=void 0,u=void 0,g=void 0,p=void 0,f=[],x=[],b=i.globals.comboCharts?e[r]:r,m=s.group({class:"apexcharts-series",seriesName:K.escapeString(i.globals.seriesNames[b]),rel:r+1,"data:realIndex":b});0<t[r].length&&(this.visibleI=this.visibleI+1);var y,v,w=0,k=(1<this.yRatio.length&&(this.yaxisIndex=b),this.initialPositions()),A=(p=k.y,y=k.barHeight,g=k.x,v=k.barWidth,h=k.xDivision,c=k.zeroH,x.push(g+v/2),s.group({class:"apexcharts-datalabels"})),S=0;for(i.globals.dataPoints;S<i.globals.dataPoints;S++){void 0===this.series[r][S]||null===t[r][S]?this.isNullValue=!0:this.isNullValue=!1,i.config.stroke.show&&(w=this.isNullValue?0:Array.isArray(this.strokeWidth)?this.strokeWidth[b]:this.strokeWidth);var C=this.drawCandleStickPaths({indexes:{i:r,j:S,realIndex:b,bc:l},x:g,y:p,xDivision:h,pathTo:d,pathFrom:u,barWidth:v,zeroH:c,strokeWidth:w,elSeries:m}),d=C.pathTo,u=C.pathFrom,p=C.y,g=C.x,L=C.color;0<S&&x.push(g+v/2),f.push(p),C=a.fillPath({seriesNumber:b,color:L,value:t[r][S]}),L=this.candlestickOptions.wick.useFillColor?L:void 0,m=this.renderSeries({realIndex:b,pathFill:C,lineFill:L,j:S,i:r,pathFrom:u,pathTo:d,strokeWidth:w,elSeries:m,x:g,y:p,series:t,barHeight:y,barWidth:v,elDataLabelsWrap:A,visibleSeries:this.visibleI,type:"candlestick"})}i.globals.seriesXvalues[b]=x,i.globals.seriesYvalues[b]=f,o.add(m)}return o}},{key:"drawCandleStickPaths",value:function(t){var e=t.indexes,i=t.x,s=(t.y,t.xDivision),a=(t.pathTo,t.pathFrom),n=t.barWidth,o=t.zeroH,r=t.strokeWidth,l=this.w,h=new tt(this.ctx),c=e.i,d=e.j,u=!0,g=l.config.plotOptions.candlestick.colors.upward,p=l.config.plotOptions.candlestick.colors.downward,f=this.yRatio[this.yaxisIndex],x=e.realIndex,b=this.getOHLCValue(x,d),m=o,y=o,v=(b.o>b.c&&(u=!1),Math.min(b.o,b.c)),t=Math.max(b.o,b.c),e=(i=l.globals.isXNumeric?(l.globals.seriesX[c][d]-l.globals.minX)/this.xRatio-n/2:i)+n*this.visibleI;return void 0===this.series[c][d]||null===this.series[c][d]?v=o:(v=o-v/f,t=o-t/f,m=o-b.h/f,y=o-b.l/f),h.move(e,o),a=h.move(e,v),0<l.globals.previousPaths.length&&(a=this.getPathFrom(x,d,!0)),c=h.move(e,t)+h.line(e+n/2,t)+h.line(e+n/2,m)+h.line(e+n/2,t)+h.line(e+n,t)+h.line(e+n,v)+h.line(e+n/2,v)+h.line(e+n/2,y)+h.line(e+n/2,v)+h.line(e,v)+h.line(e,t-r/2),a+=h.move(e,v),l.globals.isXNumeric||(i+=s),{pathTo:c,pathFrom:a,x:i,y:t,barXPosition:e,color:u?g:p}}},{key:"getOHLCValue",value:function(t,e){var i=this.w;return{o:i.globals.seriesCandleO[t][e],h:i.globals.seriesCandleH[t][e],l:i.globals.seriesCandleL[t][e],c:i.globals.seriesCandleC[t][e]}}}]),r),_=(t(Et,[{key:"drawXCrosshairs",value:function(){var t=this.w,e=new tt(this.ctx),i=new L(this.ctx),s=t.config.xaxis.crosshairs.fill.gradient,a=t.config.xaxis.crosshairs.dropShadow,n=t.config.xaxis.crosshairs.fill.type,o=s.colorFrom,r=s.colorTo,l=s.opacityFrom,h=s.opacityTo,c=s.stops,d=a.enabled,u=a.left,g=a.top,p=a.blur,f=a.color,s=a.opacity,a=t.config.xaxis.crosshairs.fill.color;t.config.xaxis.crosshairs.show&&("gradient"===n&&(a=e.drawGradient("vertical",o,r,l,h,null,c,null)),c=e.drawRect(),(c=1===t.config.xaxis.crosshairs.width?e.drawLine():c).attr({class:"apexcharts-xcrosshairs",x:0,y:0,y2:t.globals.gridHeight,width:K.isNumber(t.config.xaxis.crosshairs.width)?t.config.xaxis.crosshairs.width:0,height:t.globals.gridHeight,fill:a,filter:"none","fill-opacity":t.config.xaxis.crosshairs.opacity,stroke:t.config.xaxis.crosshairs.stroke.color,"stroke-width":t.config.xaxis.crosshairs.stroke.width,"stroke-dasharray":t.config.xaxis.crosshairs.stroke.dashArray}),d&&(c=i.dropShadow(c,{left:u,top:g,blur:p,color:f,opacity:s})),t.globals.dom.elGraphical.add(c))}},{key:"drawYCrosshairs",value:function(){var t,e=this.w,i=new tt(this.ctx),s=e.config.yaxis[0].crosshairs;e.config.yaxis[0].crosshairs.show&&((t=i.drawLine(0,0,e.globals.gridWidth,0,s.stroke.color,s.stroke.dashArray,s.stroke.width)).attr({class:"apexcharts-ycrosshairs"}),e.globals.dom.elGraphical.add(t)),(s=i.drawLine(0,0,e.globals.gridWidth,0,s.stroke.color,0,0)).attr({class:"apexcharts-ycrosshairs-hidden"}),e.globals.dom.elGraphical.add(s)}}]),Et),j=(t(Pt,[{key:"draw",value:function(t){var e=this.w,i=new tt(this.ctx),s=i.group({class:"apexcharts-heatmap"}),a=(s.attr("clip-path","url(#gridRectMask".concat(e.globals.cuid,")")),e.globals.gridWidth/e.globals.dataPoints),n=e.globals.gridHeight/e.globals.series.length,o=0,r=!1,l=(this.checkColorRange(),t.slice());e.config.yaxis[0].reversed&&(r=!0,l.reverse());for(var h=r?0:l.length-1;r?h<l.length:0<=h;r?h++:h--){var c,d=i.group({class:"apexcharts-series apexcharts-heatmap-series",seriesName:K.escapeString(e.globals.seriesNames[h]),rel:h+1,"data:realIndex":h});e.config.chart.dropShadow.enabled&&(c=e.config.chart.dropShadow,new L(this.ctx).dropShadow(d,c,h));for(var u=0,g=0;g<l[h].length;g++){var p,f=this.determineHeatColor(h,g),x=e.globals.hasNegs||this.negRange?(p=e.config.plotOptions.heatmap.shadeIntensity,e.config.plotOptions.heatmap.reverseNegativeShade?f.percent<0?f.percent/100*(1.25*p):(1-f.percent/100)*(1.25*p):f.percent<0?1-(1+f.percent/100)*p:(1-f.percent/100)*p):1-f.percent/100,b=f.color;e.config.plotOptions.heatmap.enableShades&&(p=new K,b=K.hexToRgba(p.shadeColor(x,f.color),e.config.fill.opacity));var m,f=this.rectRadius,y=((f=i.drawRect(u,o,a,n,f)).attr({cx:u,cy:o}),f.node.classList.add("apexcharts-heatmap-rect"),d.add(f),f.attr({fill:b,i:h,index:h,j:g,val:l[h][g],"stroke-width":this.strokeWidth,stroke:e.globals.stroke.colors[0],color:b}),f.node.addEventListener("mouseenter",i.pathMouseEnter.bind(this,f)),f.node.addEventListener("mouseleave",i.pathMouseLeave.bind(this,f)),f.node.addEventListener("mousedown",i.pathMouseDown.bind(this,f)),e.config.chart.animations.enabled&&!e.globals.dataChanged&&(m=1,e.globals.resized||(m=e.config.chart.animations.speed),this.animateHeatMap(f,u,o,a,n,m)),e.globals.dataChanged&&(y=1,this.dynamicAnim.enabled)&&e.globals.shouldAnimate&&(y=this.dynamicAnim.speed,m=e.globals.previousPaths[h]&&e.globals.previousPaths[h][g]&&e.globals.previousPaths[h][g].color||"rgba(255, 255, 255, 0)",this.animateHeatColor(f,K.isColorHex(m)?m:K.rgb2hex(m),K.isColorHex(b)?b:K.rgb2hex(b),y)),this.calculateHeatmapDataLabels({x:u,y:o,i:h,j:g,series:l,rectHeight:n,rectWidth:a}));null!==y&&d.add(y),u+=a}o+=n,s.add(d)}return t=e.globals.yAxisScale[0].result.slice(),e.config.yaxis[0].reversed?t.unshift(""):t.push(""),e.globals.yAxisScale[0].result=t,t=e.globals.gridHeight/e.globals.series.length,e.config.yaxis[0].labels.offsetY=-t/2,s}},{key:"checkColorRange",value:function(){var i=this,t=this.w.config.plotOptions.heatmap;0<t.colorScale.ranges.length&&t.colorScale.ranges.map(function(t,e){t.from<0&&(i.negRange=!0)})}},{key:"determineHeatColor",value:function(t,e){var i=(r=this.w).globals.series[t][e],s=r.config.plotOptions.heatmap,e=s.colorScale.inverse?e:t,a=r.globals.colors[e],n=Math.min.apply(Math,u(r.globals.series[t])),o=Math.max.apply(Math,u(r.globals.series[t])),r=(s.distributed||(n=r.globals.minY,o=r.globals.maxY),void 0!==s.colorScale.min&&(n=s.colorScale.min<r.globals.minY?s.colorScale.min:r.globals.minY,o=s.colorScale.max>r.globals.maxY?s.colorScale.max:r.globals.maxY),Math.abs(o)+Math.abs(n)),l=100*i/(0===r?r-1e-6:r);return 0<s.colorScale.ranges.length&&s.colorScale.ranges.map(function(t,e){i>=t.from&&i<=t.to&&(a=t.color,n=t.from,o=t.to,t=Math.abs(o)+Math.abs(n),l=100*i/(0===t?t-1e-6:t))}),{color:a,percent:l}}},{key:"calculateHeatmapDataLabels",value:function(t){var e=t.x,i=t.y,s=t.i,a=t.j,n=(t.series,t.rectHeight),o=t.rectWidth,r=this.w,l=r.config.dataLabels,h=new tt(this.ctx),c=new at(this.ctx),d=l.formatter,u=null;return l.enabled&&(u=h.group({class:"apexcharts-data-labels"}),t=l.offsetX,h=l.offsetY,t=e+o/2+t,h=i+n/2+parseInt(l.style.fontSize)/3+h,r=d(r.globals.series[s][a],{seriesIndex:s,dataPointIndex:a,w:r}),c.plotDataLabelsText({x:t,y:h,text:r,i:s,j:a,parent:u,dataLabelsConfig:l})),u}},{key:"animateHeatMap",value:function(t,e,i,s,a,n){var o=this;new z(this.ctx).animateRect(t,{x:e+s/2,y:i+a/2,width:0,height:0},{x:e,y:i,width:s,height:a},n,function(){o.w.globals.animationEnded=!0})}},{key:"animateHeatColor",value:function(t,e,i,s){t.attr({fill:e}).animate(s).attr({fill:i})}}]),Pt),U=(t(zt,[{key:"draw",value:function(t){var e=this.w,i=new tt(this.ctx),s=i.group({class:"apexcharts-pie"});if(!e.globals.noData){for(var a=0,n=0;n<t.length;n++)a+=K.negToZero(t[n]);var o=[],r=i.group();0===a&&(a=1e-5);for(var l=0;l<t.length;l++){var h=this.fullAngle*K.negToZero(t[l])/a;o.push(h)}if(e.globals.dataChanged){for(var c,d=0,u=0;u<e.globals.previousPaths.length;u++)d+=K.negToZero(e.globals.previousPaths[u]);for(var g=0;g<e.globals.previousPaths.length;g++)c=this.fullAngle*K.negToZero(e.globals.previousPaths[g])/d,this.prevSectorAngleArr.push(c)}e.globals.radialSize=this.defaultSize/2.05-e.config.stroke.width-e.config.chart.dropShadow.blur,void 0!==e.config.plotOptions.pie.size&&(e.globals.radialSize=e.config.plotOptions.pie.size),this.donutSize=e.globals.radialSize*parseInt(e.config.plotOptions.pie.donut.size)/100,this.donutSize<0&&(this.donutSize=0);var p=e.config.plotOptions.pie.customScale,f=e.globals.gridWidth/2,x=e.globals.gridHeight/2,b=f-e.globals.gridWidth/2*p,f=x-e.globals.gridHeight/2*p,m=(this.donutDataLabels.show&&(x=this.renderInnerDataLabels(this.donutDataLabels,{hollowSize:this.donutSize,centerX:this.centerX,centerY:this.centerY,opacity:this.donutDataLabels.show,translateX:b,translateY:f}),s.add(x)),"donut"===e.config.chart.type&&((i=i.drawCircle(this.donutSize)).attr({cx:this.centerX,cy:this.centerY,fill:e.config.plotOptions.pie.donut.background}),r.add(i)),this.drawArcs(o,t));this.sliceLabels.forEach(function(t){m.add(t)}),r.attr({transform:"translate(".concat(b,", ").concat(f-5,") scale(").concat(p,")")}),s.attr({"data:innerTranslateX":b,"data:innerTranslateY":f-25}),r.add(m),s.add(r)}return s}},{key:"drawArcs",value:function(t,e){var i,s=this.w,a=new L(this.ctx),n=new tt(this.ctx),o=new it(this.ctx),r=n.group({class:"apexcharts-slices"}),l=0,h=0;this.strokeWidth=s.config.stroke.show?s.config.stroke.width:0;for(var c=0;c<t.length;c++){var d=n.group({class:"apexcharts-series apexcharts-pie-series",seriesName:K.escapeString(s.globals.seriesNames[c]),rel:c+1,"data:realIndex":c}),u=(r.add(d),h),l=(i=l)+t[c],h=u+this.prevSectorAngleArr[c],g=l-i,p=o.fillPath({seriesNumber:c,size:s.globals.radialSize,value:e[c]}),f=this.getChangedPath(u,h),x=((p=n.drawPath({d:f,stroke:this.lineColorArr instanceof Array?this.lineColorArr[c]:this.lineColorArr,strokeWidth:this.strokeWidth,fill:p,fillOpacity:s.config.fill.opacity,classes:"apexcharts-pie-area apexcharts-".concat(s.config.chart.type,"-slice-").concat(c)})).attr({index:0,j:c}),s.config.chart.dropShadow.enabled&&(x=s.config.chart.dropShadow,a.dropShadow(p,x,c)),this.addListeners(p,this.donutDataLabels),tt.setAttrs(p.node,{"data:angle":g,"data:startAngle":i,"data:strokeWidth":this.strokeWidth,"data:value":e[c]}),{x:0,y:0});"pie"===s.config.chart.type?x=K.polarToCartesian(this.centerX,this.centerY,s.globals.radialSize/1.25+s.config.plotOptions.pie.dataLabels.offset,i+(l-i)/2):"donut"===s.config.chart.type&&(x=K.polarToCartesian(this.centerX,this.centerY,(s.globals.radialSize+this.donutSize)/2+s.config.plotOptions.pie.dataLabels.offset,i+(l-i)/2)),d.add(p),d=0,!this.initialAnim||s.globals.resized||s.globals.dataChanged?this.animBeginArr.push(0):(d=(l-i)/this.fullAngle*s.config.chart.animations.speed,this.animDur=d+this.animDur,this.animBeginArr.push(this.animDur)),this.dynamicAnim&&s.globals.dataChanged?this.animatePaths(p,{size:s.globals.radialSize,endAngle:l,startAngle:i,prevStartAngle:u,prevEndAngle:h,animateStartingPos:!0,i:c,animBeginArr:this.animBeginArr,dur:s.config.chart.animations.dynamicAnimation.speed}):this.animatePaths(p,{size:s.globals.radialSize,endAngle:l,startAngle:i,i:c,totalItems:t.length-1,animBeginArr:this.animBeginArr,dur:d}),s.config.plotOptions.pie.expandOnClick&&p.click(this.pieClicked.bind(this,c)),s.config.dataLabels.enabled&&(d=x.x,p=x.y,x=100*(l-i)/360+"%",0!=g)&&s.config.plotOptions.pie.dataLabels.minAngleToShowLabel<t[c]&&(void 0!==(g=s.config.dataLabels.formatter)&&(x=g(s.globals.seriesPercent[c][0],{seriesIndex:c,w:s})),g=s.globals.dataLabels.style.colors[c],x=n.drawText({x:d,y:p,text:x,textAnchor:"middle",fontSize:s.config.dataLabels.style.fontSize,fontFamily:s.config.dataLabels.style.fontFamily,foreColor:g}),s.config.dataLabels.dropShadow.enabled&&(g=s.config.dataLabels.dropShadow,new L(this.ctx).dropShadow(x,g)),x.node.classList.add("apexcharts-pie-label"),s.config.chart.animations.animate&&!1===s.globals.resized&&(x.node.classList.add("apexcharts-pie-label-delay"),x.node.style.animationDelay=s.config.chart.animations.speed/940+"s"),this.sliceLabels.push(x))}return r}},{key:"addListeners",value:function(t,e){var i=new tt(this.ctx);t.node.addEventListener("mouseenter",i.pathMouseEnter.bind(this,t)),t.node.addEventListener("mouseenter",this.printDataLabelsInner.bind(this,t.node,e)),t.node.addEventListener("mouseleave",i.pathMouseLeave.bind(this,t)),t.node.addEventListener("mouseleave",this.revertDataLabelsInner.bind(this,t.node,e)),t.node.addEventListener("mousedown",i.pathMouseDown.bind(this,t)),t.node.addEventListener("mousedown",this.printDataLabelsInner.bind(this,t.node,e))}},{key:"animatePaths",value:function(t,e){var i=this.w,s=e.endAngle-e.startAngle,a=s,n=e.startAngle,o=e.startAngle;void 0!==e.prevStartAngle&&void 0!==e.prevEndAngle&&(n=e.prevEndAngle,a=e.prevEndAngle-e.prevStartAngle),e.i===i.config.series.length-1&&(s+o>this.fullAngle?e.endAngle=e.endAngle-(s+o):s+o<this.fullAngle&&(e.endAngle=e.endAngle+(this.fullAngle-(s+o)))),s===this.fullAngle&&(s=this.fullAngle-.01),this.animateArc(t,n,o,s,a,e)}},{key:"animateArc",value:function(e,i,s,a,n,o){var r,l=this,t=this.w,h=o.size,c=((isNaN(i)||isNaN(n))&&(i=s,n=a,o.dur=0),a),d=s,u=i-s;t.globals.dataChanged&&o.shouldSetPrevPaths&&(r=l.getPiePath({me:l,startAngle:d,angle:n,size:h}),e.attr({d:r})),0!==o.dur?e.animate(o.dur,t.globals.easing,o.animBeginArr[o.i]).afterAll(function(){"pie"!==t.config.chart.type&&"donut"!==t.config.chart.type||this.animate(300).attr({"stroke-width":t.config.stroke.width}),t.globals.animationEnded=!0}).during(function(t){c=u+(a-u)*t,o.animateStartingPos&&(c=n+(a-n)*t,d=i-n+(s-(i-n))*t),r=l.getPiePath({me:l,startAngle:d,angle:c,size:h}),e.node.setAttribute("data:pathOrig",r),e.attr({d:r})}):(r=l.getPiePath({me:l,startAngle:d,angle:a,size:h}),o.isTrack||(t.globals.animationEnded=!0),e.node.setAttribute("data:pathOrig",r),e.attr({d:r}))}},{key:"pieClicked",value:function(t){var e,i=this.w,s=this.w.globals.radialSize+4,a=i.globals.dom.Paper.select(".apexcharts-".concat(i.config.chart.type.toLowerCase(),"-slice-").concat(t)).members[0],t=a.attr("d");"true"!==a.attr("data:pieClicked")?(i=i.globals.dom.baseEl.querySelectorAll(".apexcharts-pie-area"),Array.prototype.forEach.call(i,function(t){t.setAttribute("data:pieClicked","false");var e=t.getAttribute("data:pathOrig");t.setAttribute("d",e)}),a.attr("data:pieClicked","true"),e=parseInt(a.attr("data:startAngle")),i=parseInt(a.attr("data:angle")),e=this.getPiePath({me:this,startAngle:e,angle:i,size:s}),360!==i&&a.plot(e).animate(1).plot(t).animate(100).plot(e)):(a.attr({"data:pieClicked":"false"}),this.revertDataLabelsInner(a.node,this.donutDataLabels),e=a.attr("data:pathOrig"),a.attr({d:e}))}},{key:"getChangedPath",value:function(t,e){return this.dynamicAnim&&this.w.globals.dataChanged?this.getPiePath({me:this,startAngle:t,angle:e-t,size:this.size}):""}},{key:"getPiePath",value:function(t){var e=t.me,i=t.startAngle,s=t.angle,a=t.size,n=this.w,o=i,r=Math.PI*(o-90)/180,l=s+i,h=(360<=Math.ceil(l)&&(l=359.99),Math.PI*(l-90)/180),t=e.centerX+a*Math.cos(r),i=e.centerY+a*Math.sin(r),r=e.centerX+a*Math.cos(h),h=e.centerY+a*Math.sin(h),l=K.polarToCartesian(e.centerX,e.centerY,e.donutSize,l),o=K.polarToCartesian(e.centerX,e.centerY,e.donutSize,o),s=180<s?1:0;return("donut"===n.config.chart.type?["M",t,i,"A",a,a,0,s,1,r,h,"L",l.x,l.y,"A",e.donutSize,e.donutSize,0,s,0,o.x,o.y,"L",t,i,"z"]:"pie"===n.config.chart.type?["M",t,i,"A",a,a,0,s,1,r,h,"L",e.centerX,e.centerY,"L",t,i]:["M",t,i,"A",a,a,0,s,1,r,h]).join(" ")}},{key:"renderInnerDataLabels",value:function(t,e){var i=this.w,s=new tt(this.ctx),a=s.group({class:"apexcharts-datalabels-group",transform:"translate(".concat(e.translateX||0,", ").concat(e.translateY||0,")")}),n=t.total.show;a.node.style.opacity=e.opacity;var o,r=e.centerX,l=e.centerY,h=void 0===t.name.color?i.globals.colors[0]:t.name.color,c=void 0===t.value.color?i.config.chart.foreColor:t.value.color,d=t.value.formatter,u="",e="";return n?(h=t.total.color,e=t.total.label,u=t.total.formatter(i)):1===i.globals.series.length&&(u=d(i.globals.series[0],i),e=i.globals.seriesNames[0]),t.name.show&&((o=s.drawText({x:r,y:l+parseInt(t.name.offsetY),text:e,textAnchor:"middle",foreColor:h,fontSize:t.name.fontSize,fontFamily:t.name.fontFamily})).node.classList.add("apexcharts-datalabel-label"),a.add(o)),t.value.show&&(o=t.name.show?parseInt(t.value.offsetY)+16:t.value.offsetY,(t=s.drawText({x:r,y:l+o,text:u,textAnchor:"middle",foreColor:c,fontSize:t.value.fontSize,fontFamily:t.value.fontFamily})).node.classList.add("apexcharts-datalabel-value"),a.add(t)),a}},{key:"printInnerLabels",value:function(t,e,i,s){var a,n=this.w,o=(s?a=void 0===t.name.color?n.globals.colors[parseInt(s.parentNode.getAttribute("rel"))-1]:t.name.color:1<n.globals.series.length&&t.total.show&&(a=t.total.color),n.globals.dom.baseEl.querySelector(".apexcharts-datalabel-label")),r=n.globals.dom.baseEl.querySelector(".apexcharts-datalabel-value");i=(0,t.value.formatter)(i,n),s||"function"!=typeof t.total.formatter||(i=t.total.formatter(n)),null!==o&&(o.textContent=e),null!==r&&(r.textContent=i),null!==o&&(o.style.fill=a)}},{key:"printDataLabelsInner",value:function(t,e){var i=this.w,s=t.getAttribute("data:value"),a=i.globals.seriesNames[parseInt(t.parentNode.getAttribute("rel"))-1];1<i.globals.series.length&&this.printInnerLabels(e,a,s,t),null!==(i=i.globals.dom.baseEl.querySelector(".apexcharts-datalabels-group"))&&(i.style.opacity=1)}},{key:"revertDataLabelsInner",value:function(t,e,i){var s,a,n=this,o=this.w,r=o.globals.dom.baseEl.querySelector(".apexcharts-datalabels-group");e.total.show&&1<o.globals.series.length?new zt(this.ctx).printInnerLabels(e,e.total.label,e.total.formatter(o)):(a=document.querySelectorAll(".apexcharts-pie-area"),s=!1,Array.prototype.forEach.call(a,function(t){"true"===t.getAttribute("data:pieClicked")&&(s=!0,n.printDataLabelsInner(t,e))}),s||(o.globals.selectedDataPoints.length&&1<o.globals.series.length?0<o.globals.selectedDataPoints[0].length?(a=o.globals.selectedDataPoints[0],a=o.globals.dom.baseEl.querySelector(".apexcharts-".concat(o.config.chart.type.toLowerCase(),"-slice-").concat(a)),this.printDataLabelsInner(a,e)):r&&o.globals.selectedDataPoints.length&&0===o.globals.selectedDataPoints[0].length&&(r.style.opacity=0):r&&1<o.globals.series.length&&(r.style.opacity=0)))}}]),zt),q=(t(Lt,[{key:"draw",value:function(t){var c=this,d=this.w,u=new it(this.ctx),g=[];t.length&&(this.dataPointsLen=t[d.globals.maxValsInArrayIndex].length),this.disAngle=2*Math.PI/this.dataPointsLen;var p,e=d.globals.gridWidth/2,i=d.globals.gridHeight/2,s=this.graphics.group({class:"apexcharts-radar-series","data:innerTranslateX":e,"data:innerTranslateY":i-25,transform:"translate(".concat(e||0,", ").concat(i||0,")")}),f=null;return this.yaxisLabels=this.graphics.group({class:"apexcharts-yaxis"}),t.forEach(function(t,a){var n=c.graphics.group().attr({class:"apexcharts-series",seriesName:K.escapeString(d.globals.seriesNames[a]),rel:a+1,"data:realIndex":a}),e=(c.dataRadiusOfPercent[a]=[],c.dataRadius[a]=[],c.angleArr[a]=[],t.forEach(function(t,e){c.dataRadiusOfPercent[a][e]=t/c.maxValue,c.dataRadius[a][e]=c.dataRadiusOfPercent[a][e]*c.size,c.angleArr[a][e]=e*c.disAngle}),p=c.getDataPointsPos(c.dataRadius[a],c.angleArr[a]),c.createPaths(p,{x:0,y:0})),i=(f=c.graphics.group({class:"apexcharts-series-markers-wrap hidden"}),d.globals.delayedElements.push({el:f.node,index:a}),{i:a,realIndex:a,animationDelay:a,initialSpeed:d.config.chart.animations.speed,dataChangeSpeed:d.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-radar",shouldClipToGrid:!1,bindEventsOnPaths:!1,stroke:d.globals.stroke.colors[a],strokeLineCap:d.config.stroke.lineCap}),s=null;0<d.globals.previousPaths.length&&(s=c.getPathFrom(a));for(var o=0;o<e.linePathsTo.length;o++){var r=c.graphics.renderPaths(Q({},i,{pathFrom:null===s?e.linePathsFrom[o]:s,pathTo:e.linePathsTo[o],strokeWidth:Array.isArray(d.config.stroke.width)?d.config.stroke.width[a]:d.config.stroke.width,fill:"none",drawShadow:!1})),l=(n.add(r),u.fillPath({seriesNumber:a})),h=c.graphics.renderPaths(Q({},i,{pathFrom:null===s?e.areaPathsFrom[o]:s,pathTo:e.areaPathsTo[o],strokeWidth:0,fill:l,drawShadow:!1}));d.config.chart.dropShadow.enabled&&(r=new L(c.ctx),l=d.config.chart.dropShadow,r.dropShadow(h,Object.assign({},l,{noUserSpaceOnUse:!0}),a)),n.add(h)}t.forEach(function(t,e){var i=new st(c.ctx).getMarkerConfig("apexcharts-marker",a,e),s=c.graphics.drawMarker(p[e].x,p[e].y,i);s.attr("rel",e),s.attr("j",e),s.attr("index",a),s.node.setAttribute("default-marker-size",i.pSize),(i=c.graphics.group({class:"apexcharts-series-markers"}))&&i.add(s),f.add(i),n.add(f)}),g.push(n)}),this.drawPolygons({parent:s}),d.config.dataLabels.enabled&&(t=this.drawLabels(),s.add(t)),s.add(this.yaxisLabels),g.forEach(function(t){s.add(t)}),s}},{key:"drawPolygons",value:function(t){for(var n=this,s=this.w,a=t.parent,i=s.globals.yAxisScale[0].result.reverse(),e=i.length,o=[],r=this.size/(e-1),l=0;l<e;l++)o[l]=r*l;o.reverse();var h=[],c=[];o.forEach(function(t,s){var t=n.getPolygonPos(t),a="";t.forEach(function(t,e){var i;0===s&&(i=n.graphics.drawLine(t.x,t.y,0,0,Array.isArray(n.polygons.connectorColors)?n.polygons.connectorColors[e]:n.polygons.connectorColors),c.push(i)),0===e&&n.yaxisLabelsTextsPos.push({x:t.x,y:t.y}),a+=t.x+","+t.y+" "}),h.push(a)}),h.forEach(function(t,e){var i=n.polygons.strokeColors,e=n.graphics.drawPolygon(t,Array.isArray(i)?i[e]:i,s.globals.radarPolygons.fill.colors[e]);a.add(e)}),c.forEach(function(t){a.add(t)}),s.config.yaxis[0].show&&this.yaxisLabelsTextsPos.forEach(function(t,e){e=n.drawYAxisText(t.x,t.y,e,i[e]),n.yaxisLabels.add(e)})}},{key:"drawYAxisText",value:function(t,e,i,s){var a=(n=this.w).config.yaxis[0],n=n.globals.yLabelFormatters[0];return this.graphics.drawText({x:t+a.labels.offsetX,y:e+a.labels.offsetY,text:n(s,i),textAnchor:"middle",fontSize:a.labels.style.fontSize,fontFamily:a.labels.style.fontFamily,foreColor:a.labels.style.color})}},{key:"drawLabels",value:function(){var a=this,n=this.w,o="middle",r=n.config.dataLabels,l=this.graphics.group({class:"apexcharts-datalabels"}),h=this.getPolygonPos(this.size),c=0,d=0;return n.globals.labels.forEach(function(t,e){var i=r.formatter,s=new at(a.ctx);h[e]&&(c=h[e].x,d=h[e].y,10<=Math.abs(h[e].x)?0<h[e].x?(o="start",c+=10):h[e].x<0&&(o="end",c-=10):o="middle",Math.abs(h[e].y)>=a.size-10&&(h[e].y<0?d-=10:0<h[e].y&&(d+=10)),t=i(t,{seriesIndex:-1,dataPointIndex:e,w:n}),s.plotDataLabelsText({x:c,y:d,text:t,textAnchor:o,i:e,j:e,parent:l,dataLabelsConfig:r,offsetCorrection:!1}))}),l}},{key:"createPaths",value:function(i,t){var s,a,n=this,e=[],o=[],r=[],l=[];return i.length&&(o=[this.graphics.move(t.x,t.y)],l=[this.graphics.move(t.x,t.y)],s=this.graphics.move(i[0].x,i[0].y),a=this.graphics.move(i[0].x,i[0].y),i.forEach(function(t,e){s+=n.graphics.line(t.x,t.y),a+=n.graphics.line(t.x,t.y),e===i.length-1&&(s+="Z",a+="Z")}),e.push(s),r.push(a)),{linePathsFrom:o,linePathsTo:e,areaPathsFrom:l,areaPathsTo:r}}},{key:"getPathFrom",value:function(t){for(var e=this.w,i=null,s=0;s<e.globals.previousPaths.length;s++){var a=e.globals.previousPaths[s];0<a.paths.length&&parseInt(a.realIndex)===parseInt(t)&&void 0!==e.globals.previousPaths[s].paths[0]&&(i=e.globals.previousPaths[s].paths[0].d)}return i}},{key:"getDataPointsPos",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:this.dataPointsLen;t=t||[],e=e||[];for(var s=[],a=0;a<i;a++){var n={};n.x=t[a]*Math.sin(e[a]),n.y=-t[a]*Math.cos(e[a]),s.push(n)}return s}},{key:"getPolygonPos",value:function(t){for(var e=[],i=2*Math.PI/this.dataPointsLen,s=0;s<this.dataPointsLen;s++){var a={};a.x=t*Math.sin(s*i),a.y=-t*Math.cos(s*i),e.push(a)}return e}}]),Lt),Z=(e(o,U),t(o,[{key:"draw",value:function(t){var e,i,s,a,n,o=this.w,r=new tt(this.ctx),l=r.group({class:"apexcharts-radialbar"});return o.globals.noData||(e=r.group(),i=this.defaultSize/2,s=o.globals.gridWidth/2,a=this.defaultSize/2.05-o.config.stroke.width-o.config.chart.dropShadow.blur,void 0!==o.config.plotOptions.radialBar.size&&(a=o.config.plotOptions.radialBar.size),n=o.globals.fill.colors,o.config.plotOptions.radialBar.track.show&&(r=this.drawTracks({size:a,centerX:s,centerY:i,colorArr:n,series:t}),e.add(r)),n=this.drawArcs({size:a,centerX:s,centerY:i,colorArr:n,series:t}),t=360,o.config.plotOptions.radialBar.startAngle<0&&(t=Math.abs(o.config.plotOptions.radialBar.endAngle-o.config.plotOptions.radialBar.startAngle)),o.globals.radialSize=a-a/(360/(360-t))+10,e.add(n.g),"front"===o.config.plotOptions.radialBar.hollow.position&&(n.g.add(n.elHollow),n.dataLabels)&&n.g.add(n.dataLabels),l.add(e)),l}},{key:"drawTracks",value:function(t){var e=this.w,i=new tt(this.ctx),s=i.group({class:"apexcharts-tracks"}),a=new L(this.ctx),n=new it(this.ctx),o=this.getStrokeWidth(t);t.size=t.size-o/2;for(var r=0;r<t.series.length;r++){var l=i.group({class:"apexcharts-radialbar-track apexcharts-track"}),h=(s.add(l),l.attr({rel:r+1}),t.size=t.size-o-this.margin,e.config.plotOptions.radialBar.track),c=n.fillPath({seriesNumber:0,size:t.size,fillColors:Array.isArray(h.background)?h.background[r]:h.background,solid:!0}),d=this.trackStartAngle,u=this.trackEndAngle;360<=Math.abs(u)+Math.abs(d)&&(u=360-Math.abs(this.startAngle)-.1),c=i.drawPath({d:"",stroke:c,strokeWidth:o*parseInt(h.strokeWidth)/100,fill:"none",strokeOpacity:h.opacity,classes:"apexcharts-radialbar-area"}),h.dropShadow.enabled&&(h=h.dropShadow,a.dropShadow(c,h)),l.add(c),c.attr("id","apexcharts-radialbarTrack-"+r),this.animatePaths(c,{centerX:t.centerX,centerY:t.centerY,endAngle:u,startAngle:d,size:t.size,i:r,totalItems:2,animBeginArr:0,dur:0,isTrack:!0,easing:e.globals.easing})}return s}},{key:"drawArcs",value:function(t){var e=this.w,i=new tt(this.ctx),s=new it(this.ctx),a=new L(this.ctx),n=i.group(),o=this.getStrokeWidth(t),r=(t.size=t.size-o/2,e.config.plotOptions.radialBar.hollow.background),l=t.size-o*t.series.length-this.margin*t.series.length-o*parseInt(e.config.plotOptions.radialBar.track.strokeWidth)/100/2,h=l-e.config.plotOptions.radialBar.hollow.margin,c=(void 0!==e.config.plotOptions.radialBar.hollow.image&&(r=this.drawHollowImage(t,n,l,r)),h=this.drawHollow({size:h,centerX:t.centerX,centerY:t.centerY,fill:r}),e.config.plotOptions.radialBar.hollow.dropShadow.enabled&&(c=e.config.plotOptions.radialBar.hollow.dropShadow,a.dropShadow(h,c)),r=1,!this.radialDataLabels.total.show&&1<e.globals.series.length&&(r=0),null);this.radialDataLabels.show&&(c=this.renderInnerDataLabels(this.radialDataLabels,{hollowSize:l,centerX:t.centerX,centerY:t.centerY,opacity:r})),"back"===e.config.plotOptions.radialBar.hollow.position&&(n.add(h),c)&&n.add(c);for(var d,u=(d=!!e.config.plotOptions.radialBar.inverseOrder||!1)?t.series.length-1:0;d?0<=u:u<t.series.length;d?u--:u++){var g=i.group({class:"apexcharts-series apexcharts-radial-series",seriesName:K.escapeString(e.globals.seriesNames[u])}),p=(n.add(g),g.attr({rel:u+1,"data:realIndex":u}),this.ctx.series.addCollapsedClassToSeries(g,u),t.size=t.size-o-this.margin,s.fillPath({seriesNumber:u,size:t.size,value:t.series[u]})),f=this.startAngle,x=void 0,b=Math.abs(e.config.plotOptions.radialBar.endAngle-e.config.plotOptions.radialBar.startAngle),m=K.negToZero(100<t.series[u]?100:t.series[u])/100,y=Math.round(b*m)+this.startAngle,v=void 0;e.globals.dataChanged&&(x=this.startAngle,v=Math.round(b*K.negToZero(e.globals.previousPaths[u])/100)+x),360<=Math.abs(y)+Math.abs(f)&&(y-=.01),360<=Math.abs(v)+Math.abs(x)&&(v-=.01),m=y-f,b=Array.isArray(e.config.stroke.dashArray)?e.config.stroke.dashArray[u]:e.config.stroke.dashArray,b=i.drawPath({d:"",stroke:p,strokeWidth:o,fill:"none",fillOpacity:e.config.fill.opacity,classes:"apexcharts-radialbar-area apexcharts-radialbar-slice-"+u,strokeDashArray:b}),tt.setAttrs(b.node,{"data:angle":m,"data:value":t.series[u]}),e.config.chart.dropShadow.enabled&&(m=e.config.chart.dropShadow,a.dropShadow(b,m,u)),this.addListeners(b,this.radialDataLabels),g.add(b),b.attr({index:0,j:u}),g=0,!this.initialAnim||e.globals.resized||e.globals.dataChanged||(g=(y-f)/360*e.config.chart.animations.speed,this.animDur=g/(1.2*t.series.length)+this.animDur,this.animBeginArr.push(this.animDur)),e.globals.dataChanged&&(g=(y-f)/360*e.config.chart.animations.dynamicAnimation.speed,this.animDur=g/(1.2*t.series.length)+this.animDur,this.animBeginArr.push(this.animDur)),this.animatePaths(b,{centerX:t.centerX,centerY:t.centerY,endAngle:y,startAngle:f,prevEndAngle:v,prevStartAngle:x,size:t.size,i:u,totalItems:2,animBeginArr:this.animBeginArr,dur:g,shouldSetPrevPaths:!0,easing:e.globals.easing})}return{g:n,elHollow:h,dataLabels:c}}},{key:"drawHollow",value:function(t){var e=new tt(this.ctx).drawCircle(2*t.size);return e.attr({class:"apexcharts-radialbar-hollow",cx:t.centerX,cy:t.centerY,r:t.size,fill:t.fill}),e}},{key:"drawHollowImage",value:function(e,t,i,s){var a,n,o=this.w,r=new it(this.ctx),l=(Math.random()+1).toString(36).substring(4),h=o.config.plotOptions.radialBar.hollow.image;return o.config.plotOptions.radialBar.hollow.imageClipped?(r.clippedImgArea({width:i,height:i,image:h,patternID:"pattern".concat(o.globals.cuid).concat(l)}),s="url(#pattern".concat(o.globals.cuid).concat(l,")")):(a=o.config.plotOptions.radialBar.hollow.imageWidth,n=o.config.plotOptions.radialBar.hollow.imageHeight,void 0===a&&void 0===n?(l=o.globals.dom.Paper.image(h).loaded(function(t){this.move(e.centerX-t.width/2+o.config.plotOptions.radialBar.hollow.imageOffsetX,e.centerY-t.height/2+o.config.plotOptions.radialBar.hollow.imageOffsetY)}),t.add(l)):(h=o.globals.dom.Paper.image(h).loaded(function(t){this.move(e.centerX-a/2+o.config.plotOptions.radialBar.hollow.imageOffsetX,e.centerY-n/2+o.config.plotOptions.radialBar.hollow.imageOffsetY),this.size(a,n)}),t.add(h))),s}},{key:"getStrokeWidth",value:function(t){var e=this.w;return t.size*(100-parseInt(e.config.plotOptions.radialBar.hollow.size))/100/(t.series.length+1)-this.margin}}]),o),$=(e(n,P),t(n,[{key:"draw",value:function(t,e){var i=this.w,s=new tt(this.ctx),a=new it(this.ctx);this.rangeBarOptions=this.w.config.plotOptions.rangeBar,this.series=t,this.seriesRangeStart=i.globals.seriesRangeStart,this.seriesRangeEnd=i.globals.seriesRangeEnd,this.initVariables(t);for(var n=s.group({class:"apexcharts-rangebar-series apexcharts-plot-series"}),o=0,r=0;o<t.length;o++,r++){var l=void 0,h=void 0,c=void 0,d=void 0,u=[],g=[],p=i.globals.comboCharts?e[o]:o,f=s.group({class:"apexcharts-series",seriesName:K.escapeString(i.globals.seriesNames[p]),rel:o+1,"data:realIndex":p}),x=(0<t[o].length&&(this.visibleI=this.visibleI+1),0),b=0,m=0,y=(1<this.yRatio.length&&(this.yaxisIndex=p),this.initialPositions()),d=y.y,v=y.yDivision,b=y.barHeight,w=y.zeroW,c=y.x,m=y.barWidth,k=y.xDivision,A=y.zeroH,S=(g.push(c+m/2),s.group({class:"apexcharts-datalabels"})),C=0;for(i.globals.dataPoints;C<i.globals.dataPoints;C++){void 0===this.series[o][C]||null===t[o][C]?this.isNullValue=!0:this.isNullValue=!1,i.config.stroke.show&&(x=this.isNullValue?0:Array.isArray(this.strokeWidth)?this.strokeWidth[p]:this.strokeWidth);var L=null,z=(this.isHorizontal?m=(L=this.drawRangeBarPaths({indexes:{i:o,j:C,realIndex:p,bc:r},barHeight:b,strokeWidth:x,pathTo:l,pathFrom:h,zeroW:w,x:c,y:d,yDivision:v,elSeries:f})).barWidth:b=(L=this.drawRangeColumnPaths({indexes:{i:o,j:C,realIndex:p,bc:r},x:c,y:d,xDivision:k,pathTo:l,pathFrom:h,barWidth:m,zeroH:A,strokeWidth:x,elSeries:f})).barHeight,l=L.pathTo,h=L.pathFrom,d=L.y,c=L.x,0<C&&g.push(c+m/2),u.push(d),a.fillPath({seriesNumber:p})),L=i.globals.stroke.colors[p],f=this.renderSeries({realIndex:p,pathFill:z,lineFill:L,j:C,i:o,pathFrom:h,pathTo:l,strokeWidth:x,elSeries:f,x:c,y:d,series:t,barHeight:b,barWidth:m,elDataLabelsWrap:S,visibleSeries:this.visibleI,type:"rangebar"})}i.globals.seriesXvalues[p]=g,i.globals.seriesYvalues[p]=u,n.add(f)}return n}},{key:"drawRangeColumnPaths",value:function(t){var e=t.indexes,i=t.x,s=(t.y,t.strokeWidth),a=t.xDivision,n=(t.pathTo,t.pathFrom),o=t.barWidth,r=t.zeroH,l=this.w,h=new tt(this.ctx),c=e.i,d=e.j,u=this.yRatio[this.yaxisIndex],g=e.realIndex,p=this.getRangeValue(g,d),t=Math.min(p.start,p.end),e=Math.max(p.start,p.end),p=(i=l.globals.isXNumeric?(l.globals.seriesX[c][d]-l.globals.minX)/this.xRatio-o/2:i)+o*this.visibleI;return void 0===this.series[c][d]||null===this.series[c][d]?t=r:(t=r-t/u,e=r-e/u),u=Math.abs(e-t),h.move(p,r),n=h.move(p,t),0<l.globals.previousPaths.length&&(n=this.getPathFrom(g,d,!0)),c=h.move(p,e)+h.line(p+o,e)+h.line(p+o,t)+h.line(p,t)+h.line(p,e-s/2),n=n+h.move(p,t)+h.line(p+o,t)+h.line(p+o,t)+h.line(p,t),l.globals.isXNumeric||(i+=a),{pathTo:c,pathFrom:n,barHeight:u,x:i,y:e,barXPosition:p}}},{key:"drawRangeBarPaths",value:function(t){var e=t.indexes,i=(t.x,t.y),s=t.yDivision,a=(t.pathTo,t.pathFrom),n=t.barHeight,o=t.zeroW,r=this.w,l=new tt(this.ctx),h=e.i,c=e.j,d=e.realIndex,u=o,t=o,e=(i=r.globals.isXNumeric?(r.globals.seriesX[h][c]-r.globals.minX)/this.invertedXRatio-n:i)+n*this.visibleI;return void 0!==this.series[h][c]&&null!==this.series[h][c]&&(u=o+this.seriesRangeStart[h][c]/this.invertedYRatio,t=o+this.seriesRangeEnd[h][c]/this.invertedYRatio),l.move(o,e),a=l.move(o,e),0<r.globals.previousPaths.length&&(a=this.getPathFrom(d,c)),c=Math.abs(t-u),h=l.move(u,e)+l.line(t,e)+l.line(t,e+n)+l.line(u,e+n)+l.line(u,e),a=a+l.line(u,e)+l.line(u,e+n)+l.line(u,e+n)+l.line(u,e),r.globals.isXNumeric||(i+=s),{pathTo:h,pathFrom:a,barWidth:c,x:t,y:i,barYPosition:e}}},{key:"getRangeValue",value:function(t,e){var i=this.w;return{start:i.globals.seriesRangeStart[t][e],end:i.globals.seriesRangeEnd[t][e]}}}]),n),J=(t(Ct,[{key:"xLabelFormat",value:function(t,e,i){var s=this.w;return"datetime"===s.config.xaxis.type&&void 0===s.config.xaxis.labels.formatter&&void 0===s.config.tooltip.x.formatter?new y(this.ctx).formatDate(new Date(e),s.config.tooltip.x.format,!0,!0):t(e,i)}},{key:"setLabelFormatters",value:function(){var s=this.w;return s.globals.xLabelFormatter=function(t){return t},s.globals.xaxisTooltipFormatter=function(t){return t},s.globals.ttKeyFormatter=function(t){return t},s.globals.ttZFormatter=function(t){return t},s.globals.legendFormatter=function(t){return t},void 0!==s.config.xaxis.labels.formatter?s.globals.xLabelFormatter=s.config.xaxis.labels.formatter:s.globals.xLabelFormatter=function(t){return K.isNumber(t)?"numeric"===s.config.xaxis.type&&s.globals.dataPoints<50?t.toFixed(1):t.toFixed(0):t},"function"==typeof s.config.tooltip.x.formatter?s.globals.ttKeyFormatter=s.config.tooltip.x.formatter:s.globals.ttKeyFormatter=s.globals.xLabelFormatter,"function"==typeof s.config.xaxis.tooltip.formatter&&(s.globals.xaxisTooltipFormatter=s.config.xaxis.tooltip.formatter),!Array.isArray(s.config.tooltip.y)&&void 0===s.config.tooltip.y.formatter||(s.globals.ttVal=s.config.tooltip.y),void 0!==s.config.tooltip.z.formatter&&(s.globals.ttZFormatter=s.config.tooltip.z.formatter),void 0!==s.config.legend.formatter&&(s.globals.legendFormatter=s.config.legend.formatter),s.config.yaxis.forEach(function(e,i){void 0!==e.labels.formatter?s.globals.yLabelFormatters[i]=e.labels.formatter:s.globals.yLabelFormatters[i]=function(t){return K.isNumber(t)?0!==s.globals.yValueDecimal?t.toFixed(void 0!==e.decimalsInFloat?e.decimalsInFloat:s.globals.yValueDecimal):s.globals.maxYArr[i]-s.globals.minYArr[i]<10?t.toFixed(1):t.toFixed(0):t}}),s.globals}},{key:"heatmapLabelFormatters",value:function(){var t,e=this.w;"heatmap"===e.config.chart.type&&(e.globals.yAxisScale[0].result=e.globals.seriesNames.slice(),t=e.globals.seriesNames.reduce(function(t,e){return t.length>e.length?t:e},0),e.globals.yAxisScale[0].niceMax=t,e.globals.yAxisScale[0].niceMin=t)}}]),Ct),nt=(t(St,[{key:"getLabel",value:function(t,e,i,s){var a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:[],n=this.w,o=void 0===t[s]?"":t[s],r=n.globals.xLabelFormatter,l=n.config.xaxis.labels.formatter,h=o,h=new J(this.ctx).xLabelFormat(r,o,h);return void 0!==l&&(h=l(o,t[s],s)),0<e.length?(i=e[s].position,h=e[s].value):"datetime"===n.config.xaxis.type&&void 0===l&&(h=""),{x:i,text:h=0===(h=(h=void 0===h?"":h).toString()).indexOf("NaN")||0===h.toLowerCase().indexOf("invalid")||0<=h.toLowerCase().indexOf("infinity")||0<=a.indexOf(h)&&!n.config.xaxis.labels.showDuplicates?"":h}}},{key:"drawYAxisTicks",value:function(t,e,i,s,a,n,o){var r=this.w,l=new tt(this.ctx),h=r.globals.translateY;if(s.show){!0===r.config.yaxis[a].opposite&&(t+=s.width);for(var c=e;0<=c;c--){var d=h+e/10+r.config.yaxis[a].labels.offsetY-1;r.globals.isBarHorizontal&&(d=n*c),d=l.drawLine(t+i.offsetX-s.width+s.offsetX,d+s.offsetY,t+i.offsetX+s.offsetX,d+s.offsetY,i.color),o.add(d),h+=n}}}}]),St),ot=(t(At,[{key:"drawYaxis",value:function(t){var e=this.w,i=new tt(this.ctx),s=e.config.yaxis[t].labels.style.fontSize,a=e.config.yaxis[t].labels.style.fontFamily,n=i.group({class:"apexcharts-yaxis",rel:t,transform:"translate("+e.globals.translateYAxisX[t]+", 0)"});if(e.config.yaxis[t].show){var o=i.group({class:"apexcharts-yaxis-texts-g"}),r=(n.add(o),e.globals.yAxisScale[t].result.length-1),l=e.globals.gridHeight/r+.1,h=e.globals.translateY,c=e.globals.yLabelFormatters[t],d=e.globals.yAxisScale[t].result.slice();if(e.config.yaxis[t]&&e.config.yaxis[t].reversed&&d.reverse(),e.config.yaxis[t].labels.show)for(var u=r;0<=u;u--){var g=c(d[u],u),p=e.config.yaxis[t].labels.padding;e.config.yaxis[t].opposite&&0!==e.config.yaxis.length&&(p*=-1),p=i.drawText({x:p,y:h+r/10+e.config.yaxis[t].labels.offsetY+1,text:g,textAnchor:e.config.yaxis[t].opposite?"start":"end",fontSize:s,fontFamily:a,foreColor:e.config.yaxis[t].labels.style.color,cssClass:"apexcharts-yaxis-label "+e.config.yaxis[t].labels.style.cssClass}),o.add(p),g=i.rotateAroundCenter(p.node),0!==e.config.yaxis[t].labels.rotate&&p.node.setAttribute("transform","rotate(".concat(e.config.yaxis[t].labels.rotate," ").concat(g.x," ").concat(g.y,")")),h+=l}void 0!==e.config.yaxis[t].title.text&&(x=i.group({class:"apexcharts-yaxis-title"}),b=0,e.config.yaxis[t].opposite&&(b=e.globals.translateYAxisX[t]),f=i.drawText({x:b,y:e.globals.gridHeight/2+e.globals.translateY,text:e.config.yaxis[t].title.text,textAnchor:"end",foreColor:e.config.yaxis[t].title.style.color,fontSize:e.config.yaxis[t].title.style.fontSize,fontFamily:e.config.yaxis[t].title.style.fontFamily,cssClass:"apexcharts-yaxis-title-text "+e.config.yaxis[t].title.style.cssClass}),x.add(f),n.add(x));var f,x,b=e.config.yaxis[t].axisBorder;b.show&&(f=31+b.offsetX,e.config.yaxis[t].opposite&&(f=-31-b.offsetX),x=i.drawLine(f,e.globals.translateY+b.offsetY-2,f,e.globals.gridHeight+e.globals.translateY+b.offsetY+2,b.color),n.add(x),this.axesUtils.drawYAxisTicks(f,r,b,e.config.yaxis[t].axisTicks,t,l,n))}return n}},{key:"drawYaxisInversed",value:function(t){var e=this.w,i=new tt(this.ctx),s=i.group({class:"apexcharts-xaxis apexcharts-yaxis-inversed"}),a=i.group({class:"apexcharts-xaxis-texts-g",transform:"translate(".concat(e.globals.translateXAxisX,", ").concat(e.globals.translateXAxisY,")")}),n=(s.add(a),e.globals.yAxisScale[t].result.length-1),o=e.globals.gridWidth/n+.1,r=o+e.config.xaxis.labels.offsetX,l=e.globals.xLabelFormatter,h=e.globals.yAxisScale[t].result.slice(),c=e.globals.invertedTimelineLabels,d=(0<c.length&&(this.xaxisLabels=c.slice(),n=(h=c.slice()).length),e.config.yaxis[t]&&e.config.yaxis[t].reversed&&h.reverse(),c.length);if(e.config.xaxis.labels.show)for(var u=d?0:n;d?u<d-1:0<=u;d?u++:u--){var g=l(g=h[u],u),p=e.globals.gridWidth+e.globals.padHorizontal-(r-o+e.config.xaxis.labels.offsetX),f=(c.length&&(p=(f=this.axesUtils.getLabel(h,c,p,u,this.drawnLabels)).x,g=f.text,this.drawnLabels.push(f.text)),i.drawText({x:p,y:this.xAxisoffX+e.config.xaxis.labels.offsetY+30,text:"",textAnchor:"middle",foreColor:Array.isArray(this.xaxisForeColors)?this.xaxisForeColors[t]:this.xaxisForeColors,fontSize:this.xaxisFontSize,fontFamily:this.xaxisFontFamily,cssClass:"apexcharts-xaxis-label "+e.config.xaxis.labels.style.cssClass}));a.add(f),f.tspan(g),(p=document.createElementNS(e.globals.SVGNS,"title")).textContent=g,f.node.appendChild(p),r+=o}void 0!==e.config.xaxis.title.text&&(x=i.group({class:"apexcharts-xaxis-title apexcharts-yaxis-title-inversed"}),n=i.drawText({x:e.globals.gridWidth/2,y:this.xAxisoffX+parseInt(this.xaxisFontSize)+parseInt(e.config.xaxis.title.style.fontSize)+20,text:e.config.xaxis.title.text,textAnchor:"middle",fontSize:e.config.xaxis.title.style.fontSize,fontFamily:e.config.xaxis.title.style.fontFamily,cssClass:"apexcharts-xaxis-title-text "+e.config.xaxis.title.style.cssClass}),x.add(n),s.add(x));var x=e.config.yaxis[t].axisBorder;return x.show&&(x=i.drawLine(e.globals.padHorizontal+x.offsetX,1+x.offsetY,e.globals.padHorizontal+x.offsetX,e.globals.gridHeight+x.offsetY,x.color),s.add(x)),s}},{key:"yAxisTitleRotate",value:function(t,e){var i=this.w,s=new tt(this.ctx),a={width:0,height:0},n={width:0,height:0},o=i.globals.dom.baseEl.querySelector(" .apexcharts-yaxis[rel='".concat(t,"'] .apexcharts-yaxis-texts-g"));null!==o&&(a=o.getBoundingClientRect()),null!==(o=i.globals.dom.baseEl.querySelector(".apexcharts-yaxis[rel='".concat(t,"'] .apexcharts-yaxis-title text")))&&(n=o.getBoundingClientRect()),null!==o&&(n=this.xPaddingForYAxisTitle(t,a,n,e),o.setAttribute("x",n.xPos-(e?10:0))),null!==o&&(s=s.rotateAroundCenter(o),e?o.setAttribute("transform","rotate(".concat(i.config.yaxis[t].title.rotate," ").concat(s.x," ").concat(s.y,")")):o.setAttribute("transform","rotate(-".concat(i.config.yaxis[t].title.rotate," ").concat(s.x," ").concat(s.y,")")))}},{key:"xPaddingForYAxisTitle",value:function(t,e,i,s){var a=this.w,n=0,o=10;return void 0===a.config.yaxis[t].title.text||t<0?{xPos:n,padd:0}:(s?n=e.width+a.config.yaxis[t].title.offsetX+i.width/2+o/2:(n=-1*e.width+a.config.yaxis[t].title.offsetX+o/2+i.width/2,a.globals.isBarHorizontal&&(n=-1*e.width-a.config.yaxis[t].title.offsetX-(o=25))),{xPos:n,padd:o})}},{key:"setYAxisXPosition",value:function(a,n){var o,r=this.w,l=0,h=21,c=1;1<r.config.yaxis.length&&(this.multipleYs=!0),r.config.yaxis.map(function(t,e){var i=-1<r.globals.ignoreYAxisIndexes.indexOf(e)||!t.show||t.floating||0===a[e].width,s=a[e].width+n[e].width;t.opposite?r.globals.isBarHorizontal?(l=r.globals.gridWidth+r.globals.translateX-1,r.globals.translateYAxisX[e]=l-t.labels.offsetX):(l=r.globals.gridWidth+r.globals.translateX+c,i||(c=c+s+20),r.globals.translateYAxisX[e]=l-t.labels.offsetX+20):(o=r.globals.translateX-h,i||(h=h+s+20),r.globals.translateYAxisX[e]=o+t.labels.offsetX)})}},{key:"setYAxisTextAlignments",value:function(){var n=this.w,t=n.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis");(t=K.listToArray(t)).forEach(function(t,e){var i,s,a=n.config.yaxis[e];void 0!==a.labels.align&&(i=n.globals.dom.baseEl.querySelector(".apexcharts-yaxis[rel='".concat(e,"'] .apexcharts-yaxis-texts-g")),s=n.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis[rel='".concat(e,"'] .apexcharts-yaxis-label")),s=K.listToArray(s),e=i.getBoundingClientRect(),"left"===a.labels.align?(s.forEach(function(t,e){t.setAttribute("text-anchor","start")}),a.opposite||i.setAttribute("transform","translate(-".concat(e.width,", 0)"))):"center"===a.labels.align?(s.forEach(function(t,e){t.setAttribute("text-anchor","middle")}),i.setAttribute("transform","translate(".concat(e.width/2*(a.opposite?1:-1),", 0)"))):"right"===a.labels.align&&(s.forEach(function(t,e){t.setAttribute("text-anchor","end")}),a.opposite)&&i.setAttribute("transform","translate(".concat(e.width,", 0)")))})}}]),At),rt=(t(kt,[{key:"plotCoords",value:function(){var t=this.w,e=t.globals,i=this.getLegendsRect();e.axisCharts?this.setGridCoordsForAxisCharts(i):this.setGridCoordsForNonAxisCharts(i),this.titleSubtitleOffset(),e.gridHeight=e.gridHeight-t.config.grid.padding.top-t.config.grid.padding.bottom,e.gridWidth=e.gridWidth-t.config.grid.padding.left-t.config.grid.padding.right-this.xPadRight-this.xPadLeft,e.translateX=e.translateX+t.config.grid.padding.left+this.xPadLeft,e.translateY=e.translateY+t.config.grid.padding.top}},{key:"conditionalChecksForAxisCoords",value:function(t,e){var i=this.w,s=(this.xAxisHeight=(t.height+e.height)*i.globals.LINE_HEIGHT_RATIO+15,this.xAxisWidth=t.width,this.xAxisHeight-e.height>i.config.xaxis.labels.maxHeight&&(this.xAxisHeight=i.config.xaxis.labels.maxHeight),i.config.xaxis.labels.minHeight&&this.xAxisHeight<i.config.xaxis.labels.minHeight&&(this.xAxisHeight=i.config.xaxis.labels.minHeight),i.config.xaxis.floating&&(this.xAxisHeight=0),i.globals.isBarHorizontal?this.yAxisWidth=i.globals.yLabelsCoords[0].width+i.globals.yTitleCoords[0].width+15:this.yAxisWidth=this.getTotalYAxisWidth(),0),a=0;i.config.yaxis.forEach(function(t){s+=t.labels.minWidth,a+=t.labels.maxWidth}),this.yAxisWidth<s&&(this.yAxisWidth=s),this.yAxisWidth>a&&(this.yAxisWidth=a)}},{key:"setGridCoordsForAxisCharts",value:function(t){var i=this.w,e=i.globals,s=this.getyAxisLabelsCoords(),a=this.getxAxisLabelsCoords(),n=this.getyAxisTitleCoords(),o=this.getxAxisTitleCoords(),r=(i.globals.yLabelsCoords=[],i.globals.yTitleCoords=[],i.config.yaxis.map(function(t,e){i.globals.yLabelsCoords.push({width:s[e].width,index:e}),i.globals.yTitleCoords.push({width:n[e].width,index:e})}),this.conditionalChecksForAxisCoords(a,o),e.translateXAxisY=i.globals.rotateXLabels?this.xAxisHeight/8:-4,e.translateXAxisX=i.globals.rotateXLabels&&i.globals.isXNumeric&&i.config.xaxis.labels.rotate<=-45?-this.xAxisWidth/4:0,i.globals.isBarHorizontal&&(e.rotateXLabels=!1,e.translateXAxisY=parseInt(i.config.xaxis.labels.style.fontSize)/1.5*-1),e.translateXAxisY=e.translateXAxisY+i.config.xaxis.labels.offsetY,e.translateXAxisX=e.translateXAxisX+i.config.xaxis.labels.offsetX,this.yAxisWidth),l=this.xAxisHeight,h=(e.xAxisLabelsHeight=this.xAxisHeight,e.xAxisHeight=this.xAxisHeight,10);switch(i.config.grid.show&&"radar"!==i.config.chart.type||(r=0,l=35),this.isSparkline&&(t={height:0,width:0},h=r=l=0),this.additionalPaddingXLabels(a),i.config.legend.position){case"bottom":e.translateY=h,e.translateX=r,e.gridHeight=e.svgHeight-t.height-l-(this.isSparkline?0:i.globals.rotateXLabels?10:15),e.gridWidth=e.svgWidth-r;break;case"top":e.translateY=t.height+h,e.translateX=r,e.gridHeight=e.svgHeight-t.height-l-(this.isSparkline?0:i.globals.rotateXLabels?10:15),e.gridWidth=e.svgWidth-r;break;case"left":e.translateY=h,e.translateX=t.width+r,e.gridHeight=e.svgHeight-l-12,e.gridWidth=e.svgWidth-t.width-r;break;case"right":e.translateY=h,e.translateX=r,e.gridHeight=e.svgHeight-l-12,e.gridWidth=e.svgWidth-t.width-r-5;break;default:throw new Error("Legend position not supported")}this.setGridXPosForDualYAxis(n,s),new ot(this.ctx).setYAxisXPosition(s,n)}},{key:"setGridCoordsForNonAxisCharts",value:function(t){var e=this.w,i=e.globals,s=0,a=(e.config.legend.show&&!e.config.legend.floating&&(s=20),10),n=0;if("pie"===e.config.chart.type||"donut"===e.config.chart.type?(a+=e.config.plotOptions.pie.offsetY,n+=e.config.plotOptions.pie.offsetX):"radialBar"===e.config.chart.type&&(a+=e.config.plotOptions.radialBar.offsetY,n+=e.config.plotOptions.radialBar.offsetX),e.config.legend.show)switch(e.config.legend.position){case"bottom":i.gridHeight=i.svgHeight-t.height-35,i.gridWidth=i.gridHeight,i.translateY=a-20,i.translateX=n+(i.svgWidth-i.gridWidth)/2;break;case"top":i.gridHeight=i.svgHeight-t.height-35,i.gridWidth=i.gridHeight,i.translateY=t.height+a+10,i.translateX=n+(i.svgWidth-i.gridWidth)/2;break;case"left":i.gridWidth=i.svgWidth-t.width-s,i.gridHeight=i.gridWidth,i.translateY=a,i.translateX=n+t.width+s;break;case"right":i.gridWidth=i.svgWidth-t.width-s-5,i.gridHeight=i.gridWidth,i.translateY=a,i.translateX=n+10;break;default:throw new Error("Legend position not supported")}else i.gridHeight=i.svgHeight-35,i.gridWidth=i.gridHeight,i.translateY=a-10,i.translateX=n+(i.svgWidth-i.gridWidth)/2}},{key:"setGridXPosForDualYAxis",value:function(i,s){var a=this.w;a.config.yaxis.map(function(t,e){-1===a.globals.ignoreYAxisIndexes.indexOf(e)&&!a.config.yaxis[e].floating&&a.config.yaxis[e].show&&t.opposite&&(a.globals.translateX=a.globals.translateX-(s[e].width+i[e].width)-parseInt(a.config.yaxis[e].labels.style.fontSize)/1.2-12)})}},{key:"additionalPaddingXLabels",value:function(i){var s,a=this,n=this.w;("category"===n.config.xaxis.type&&n.globals.isBarHorizontal||"numeric"===n.config.xaxis.type||"datetime"===n.config.xaxis.type)&&(s=n.globals.isXNumeric,n.config.yaxis.forEach(function(t,e){(!t.show||t.floating||-1!==n.globals.collapsedSeriesIndices.indexOf(e)||s||t.opposite&&n.globals.isBarHorizontal)&&((s&&n.globals.isMultipleYAxis&&-1!==n.globals.collapsedSeriesIndices.indexOf(e)||n.globals.isBarHorizontal&&t.opposite)&&n.config.grid.padding.left<i.width&&(a.xPadLeft=i.width/2+1),!n.globals.isBarHorizontal&&t.opposite&&-1!==n.globals.collapsedSeriesIndices.indexOf(e)||s&&!n.globals.isMultipleYAxis)&&(e=i,a.timescaleLabels?a.timescaleLabels[a.timescaleLabels.length-1].position+e.width>n.globals.gridWidth?n.globals.skipLastTimelinelabel=!0:n.globals.skipLastTimelinelabel=!1:"datetime"===n.config.xaxis.type?n.config.grid.padding.right<e.width&&(n.globals.skipLastTimelinelabel=!0):"datetime"!==n.config.xaxis.type&&n.config.grid.padding.right<e.width&&(a.xPadRight=e.width/2+1))}))}},{key:"titleSubtitleOffset",value:function(){var t=(s=this.w).globals,e=this.isSparkline||!s.globals.axisCharts?0:10,i=(void 0!==s.config.title.text?e+=s.config.title.margin:e+=this.isSparkline||!s.globals.axisCharts?0:5,void 0!==s.config.subtitle.text?e+=s.config.subtitle.margin:e+=this.isSparkline||!s.globals.axisCharts?0:5,s.config.legend.show&&"bottom"===s.config.legend.position&&!s.config.legend.floating&&(1<s.config.series.length||!s.globals.axisCharts||s.config.legend.showForSingleSeries)&&(e+=10),this.getTitleSubtitleCoords("title")),s=this.getTitleSubtitleCoords("subtitle");t.gridHeight=t.gridHeight-i.height-s.height-e,t.translateY=t.translateY+i.height+s.height+e}},{key:"getTotalYAxisWidth",value:function(){function s(t){return-1<a.globals.ignoreYAxisIndexes.indexOf(t)}var a=this.w,n=0,o=10;return a.globals.yLabelsCoords.map(function(t,e){var i=a.config.yaxis[e].floating;0<t.width&&!i?(n=n+t.width+o,s(e)&&(n=n-t.width-o)):n+=i||!a.config.yaxis[e].show?0:5}),a.globals.yTitleCoords.map(function(t,e){var i=a.config.yaxis[e].floating;o=parseInt(a.config.yaxis[e].title.style.fontSize),0<t.width&&!i?(n=n+t.width+o,s(e)&&(n=n-t.width-o)):n+=i||!a.config.yaxis[e].show?0:5}),n}},{key:"getxAxisTimeScaleLabelsCoords",value:function(){var t=this.w,e=(this.timescaleLabels=t.globals.timelineLabels.slice(),t.globals.isBarHorizontal&&"datetime"===t.config.xaxis.type&&(this.timescaleLabels=t.globals.invertedTimelineLabels.slice()),this.timescaleLabels.map(function(t){return t.value})),i=e.reduce(function(t,e){return void 0===t?(console.error("You have possibly supplied invalid Date format. Please supply a valid JavaScript Date"),0):t.length>e.length?t:e},0);return 1.05*(i=new tt(this.ctx).getTextRects(i,t.config.xaxis.labels.style.fontSize)).width*e.length>t.globals.gridWidth&&0!==t.config.xaxis.labels.rotate&&(t.globals.overlappingXLabels=!0),i}},{key:"getxAxisLabelsCoords",value:function(){var t,e,i,s,a,n=this.w,o=n.globals.labels.slice();return 0<n.globals.timelineLabels.length?t={width:(a=this.getxAxisTimeScaleLabelsCoords()).width,height:a.height}:(e="left"!==n.config.legend.position||"right"!==n.config.legend.position||n.config.legend.floating?0:this.lgRect.width,s=n.globals.xLabelFormatter,i=o.reduce(function(t,e){return t.length>e.length?t:e},0),a=i=n.globals.isBarHorizontal?n.globals.yAxisScale[0].result.reduce(function(t,e){return t.length>e.length?t:e},0):i,i=new J(this.ctx).xLabelFormat(s,i,a),(t={width:(a=(s=new tt(this.ctx)).getTextRects(i,n.config.xaxis.labels.style.fontSize)).width,height:a.height}).width*o.length>n.globals.svgWidth-e-this.yAxisWidth&&0!==n.config.xaxis.labels.rotate?n.globals.isBarHorizontal||(n.globals.rotateXLabels=!0,a=s.getTextRects(i,n.config.xaxis.labels.style.fontSize,n.config.xaxis.labels.style.fontFamily,"rotate(".concat(n.config.xaxis.labels.rotate," 0 0)"),!1),t.height=a.height/1.66):n.globals.rotateXLabels=!1),{width:(t=n.config.xaxis.labels.show?t:{width:0,height:0}).width,height:t.height}}},{key:"getyAxisLabelsCoords",value:function(){var a=this,n=this.w,o=[],r=10;return n.config.yaxis.map(function(t,e){var i,s;t.show&&t.labels.show&&n.globals.yAxisScale[e].result.length?(void 0!==(s=(i=n.globals.yLabelFormatters[e])(n.globals.yAxisScale[e].niceMax,-1))&&0!==s.length||(s=n.globals.yAxisScale[e].niceMax),n.globals.isBarHorizontal&&(r=0,s=i(s=n.globals.labels.slice().reduce(function(t,e){return t.length>e.length?t:e},0),-1)),t=new tt(a.ctx).getTextRects(s,t.labels.style.fontSize),o.push({width:t.width+r,height:t.height})):o.push({width:0,height:0})}),o}},{key:"getxAxisTitleCoords",value:function(){var t=this.w,e=0,i=0;return void 0!==t.config.xaxis.title.text&&(e=(t=new tt(this.ctx).getTextRects(t.config.xaxis.title.text,t.config.xaxis.title.style.fontSize)).width,i=t.height),{width:e,height:i}}},{key:"getyAxisTitleCoords",value:function(){var i=this,t=this.w,s=[];return t.config.yaxis.map(function(t,e){t.show&&void 0!==t.title.text?(t=new tt(i.ctx).getTextRects(t.title.text,t.title.style.fontSize,t.title.style.fontFamily,"rotate(-90 0 0)",!1),s.push({width:t.width,height:t.height})):s.push({width:0,height:0})}),s}},{key:"getTitleSubtitleCoords",value:function(t){var e=this.w,i=0,s=0,a=("title"===t?e.config.title:e.config.subtitle).floating;return null===(t=e.globals.dom.baseEl.querySelector(".apexcharts-".concat(t,"-text")))||a||(i=(t=t.getBoundingClientRect()).width,s=e.globals.axisCharts?t.height+5:t.height),{width:i,height:s}}},{key:"getLegendsRect",value:function(){var t=this.w,e=t.globals.dom.baseEl.querySelector(".apexcharts-legend"),i=Object.assign({},K.getBoundingClientRect(e));return null!==e&&!t.config.legend.floating&&t.config.legend.show?this.lgRect={x:i.x,y:i.y,height:i.height,width:0===i.height?0:i.width}:this.lgRect={x:0,y:0,height:0,width:0},this.lgRect}}]),kt),g=(t(wt,[{key:"getAllSeriesEls",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series")}},{key:"getSeriesByName",value:function(t){return this.w.globals.dom.baseEl.querySelector("[seriesName='".concat(K.escapeString(t),"']"))}},{key:"addCollapsedClassToSeries",value:function(i,s){var t=this.w;function e(t){for(var e=0;e<t.length;e++)t[e].index===s&&i.node.classList.add("apexcharts-series-collapsed")}e(t.globals.collapsedSeries),e(t.globals.ancillaryCollapsedSeries)}},{key:"resetSeries",value:function(){var t=!(0<arguments.length&&void 0!==arguments[0])||arguments[0],e=this.w,i=e.globals.initialSeries.slice();e.config.series=i,e.globals.collapsedSeries=[],e.globals.ancillaryCollapsedSeries=[],e.globals.collapsedSeriesIndices=[],e.globals.ancillaryCollapsedSeriesIndices=[],e.globals.previousPaths=[],t&&this.ctx._updateSeries(i,e.config.chart.animations.dynamicAnimation.enabled)}},{key:"toggleSeriesOnHover",value:function(t,e){var i=this.w,s=i.globals.dom.baseEl.querySelectorAll(".apexcharts-series");if("mousemove"===t.type){for(var a=parseInt(e.getAttribute("rel"))-1,e=null,e=i.globals.axisCharts||"radialBar"===i.config.chart.type?i.globals.axisCharts?i.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(a,"']")):i.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(1+a,"']")):i.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(1+a,"'] path")),n=0;n<s.length;n++)s[n].classList.add("legend-mouseover-inactive");null!==e&&(i.globals.axisCharts||e.parentNode.classList.remove("legend-mouseover-inactive"),e.classList.remove("legend-mouseover-inactive"))}else if("mouseout"===t.type)for(var o=0;o<s.length;o++)s[o].classList.remove("legend-mouseover-inactive")}},{key:"highlightRangeInSeries",value:function(t,e){function i(){for(var t=0;t<a.length;t++)a[t].classList.remove("legend-mouseover-inactive")}var s=this.w,a=s.globals.dom.baseEl.querySelectorAll(".apexcharts-heatmap-rect");if("mousemove"===t.type){e=parseInt(e.getAttribute("rel"))-1,i();for(var n=0;n<a.length;n++)a[n].classList.add("legend-mouseover-inactive");for(var o=s.config.plotOptions.heatmap.colorScale.ranges[e],r=0;r<a.length;r++){var l=parseInt(a[r].getAttribute("val"));l>=o.from&&l<=o.to&&a[r].classList.remove("legend-mouseover-inactive")}}else"mouseout"===t.type&&i()}},{key:"getActiveSeriesIndex",value:function(){var i=this.w,t=0;if(1<i.globals.series.length)for(var e=i.globals.series.map(function(t,e){return 0<t.length&&"bar"!==i.config.series[e].type&&"column"!==i.config.series[e].type?e:-1}),s=0;s<e.length;s++)if(-1!==e[s]){t=e[s];break}return t}},{key:"getActiveConfigSeriesIndex",value:function(){var t=this.w,e=0;if(1<t.config.series.length)for(var i=t.config.series.map(function(t,e){return t.data&&0<t.data.length?e:-1}),s=0;s<i.length;s++)if(-1!==i[s]){e=i[s];break}return e}},{key:"getPreviousPaths",value:function(){var r=this.w;function t(t,e,i){for(var s,a=t[e].childNodes,n={type:i,paths:[],realIndex:t[e].getAttribute("data:realIndex")},o=0;o<a.length;o++)a[o].hasAttribute("pathTo")&&(s=a[o].getAttribute("pathTo"),n.paths.push({d:s}));r.globals.previousPaths.push(n)}r.globals.previousPaths=[];var e=r.globals.dom.baseEl.querySelectorAll(".apexcharts-line-series .apexcharts-series");if(0<e.length)for(var i=e.length-1;0<=i;i--)t(e,i,"line");var s=r.globals.dom.baseEl.querySelectorAll(".apexcharts-area-series .apexcharts-series");if(0<s.length)for(var a=s.length-1;0<=a;a--)t(s,a,"area");var n=r.globals.dom.baseEl.querySelectorAll(".apexcharts-bar-series .apexcharts-series");if(0<n.length)for(var o=0;o<n.length;o++)t(n,o,"bar");var l=r.globals.dom.baseEl.querySelectorAll(".apexcharts-candlestick-series .apexcharts-series");if(0<l.length)for(var h=0;h<l.length;h++)t(l,h,"candlestick");var c=r.globals.dom.baseEl.querySelectorAll(".apexcharts-radar-series .apexcharts-series");if(0<c.length)for(var d=0;d<c.length;d++)t(c,d,"radar");var u=r.globals.dom.baseEl.querySelectorAll(".apexcharts-bubble-series .apexcharts-series");if(0<u.length)for(var g=0;g<u.length;g++){for(var p=r.globals.dom.baseEl.querySelectorAll(".apexcharts-bubble-series .apexcharts-series[data\\:realIndex='".concat(g,"'] circle")),f=[],x=0;x<p.length;x++)f.push({x:p[x].getAttribute("cx"),y:p[x].getAttribute("cy"),r:p[x].getAttribute("r")});r.globals.previousPaths.push(f)}var b=r.globals.dom.baseEl.querySelectorAll(".apexcharts-scatter-series .apexcharts-series");if(0<b.length)for(var m=0;m<b.length;m++){for(var y=r.globals.dom.baseEl.querySelectorAll(".apexcharts-scatter-series .apexcharts-series[data\\:realIndex='".concat(m,"'] circle")),v=[],w=0;w<y.length;w++)v.push({x:y[w].getAttribute("cx"),y:y[w].getAttribute("cy"),r:y[w].getAttribute("r")});r.globals.previousPaths.push(v)}var k=r.globals.dom.baseEl.querySelectorAll(".apexcharts-heatmap .apexcharts-series");if(0<k.length)for(var A=0;A<k.length;A++){for(var S=r.globals.dom.baseEl.querySelectorAll(".apexcharts-heatmap .apexcharts-series[data\\:realIndex='".concat(A,"'] rect")),C=[],L=0;L<S.length;L++)C.push({color:S[L].getAttribute("color")});r.globals.previousPaths.push(C)}r.globals.axisCharts||(r.globals.previousPaths=r.globals.series)}},{key:"handleNoData",value:function(){var t=this.w,e=t.config.noData,i=new tt(this.ctx),s=t.globals.svgWidth/2,a=t.globals.svgHeight/2,n="middle";t.globals.noData=!0,t.globals.animationEnded=!0,"left"===e.align?(s=10,n="start"):"right"===e.align&&(s=t.globals.svgWidth-10,n="end"),"top"===e.verticalAlign?a=50:"bottom"===e.verticalAlign&&(a=t.globals.svgHeight-50),s+=e.offsetX,a=a+parseInt(e.style.fontSize)+2,void 0!==e.text&&""!==e.text&&((e=i.drawText({x:s,y:a,text:e.text,textAnchor:n,fontSize:e.style.fontSize,fontFamily:e.style.fontFamily,foreColor:e.style.color,opacity:1,class:"apexcharts-text-nodata"})).node.setAttribute("class","apexcharts-title-text"),t.globals.dom.Paper.add(e))}},{key:"setNullSeriesToZeroValues",value:function(t){for(var e=this.w,i=0;i<t.length;i++)if(0===t[i].length)for(var s=0;s<t[e.globals.maxValsInArrayIndex].length;s++)t[i].push(0);return t}},{key:"hasAllSeriesEqualX",value:function(){for(var t=!0,e=this.w,i=this.filteredSeriesX(),s=0;s<i.length-1;s++)if(i[s][0]!==i[s+1][0]){t=!1;break}return e.globals.allSeriesHasEqualX=t}},{key:"filteredSeriesX",value:function(){return this.w.globals.seriesX.map(function(t,e){return 0<t.length?t:[]})}}]),wt),lt=(t(vt,[{key:"init",value:function(){var t=this.w,e=t.globals;if(((t=t.config).legend.showForSingleSeries&&1===e.series.length||1<e.series.length||!e.axisCharts)&&t.legend.show){for(;e.dom.elLegendWrap.firstChild;)e.dom.elLegendWrap.removeChild(e.dom.elLegendWrap.firstChild);this.drawLegends(),K.isIE11()?document.getElementsByTagName("head")[0].appendChild(this.getLegendStyles()):this.appendToForeignObject(),"bottom"===t.legend.position||"top"===t.legend.position?this.legendAlignHorizontal():"right"!==t.legend.position&&"left"!==t.legend.position||this.legendAlignVertical()}}},{key:"appendToForeignObject",value:function(){var t=this.w.globals,e=(t.dom.elLegendForeign=document.createElementNS(t.SVGNS,"foreignObject"),t.dom.elLegendForeign);e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("width",t.svgWidth),e.setAttribute("height",t.svgHeight),t.dom.elLegendWrap.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),e.appendChild(t.dom.elLegendWrap),e.appendChild(this.getLegendStyles()),t.dom.Paper.node.insertBefore(e,t.dom.elGraphical.node)}},{key:"drawLegends",value:function(){var t,e=this.w,i=e.config.legend.fontFamily,s=e.globals.seriesNames,a=e.globals.colors.slice();"heatmap"===e.config.chart.type&&(s=(t=e.config.plotOptions.heatmap.colorScale.ranges).map(function(t){return t.name||t.from+" - "+t.to}),a=t.map(function(t){return t.color}));for(var n=e.globals.legendFormatter,o=0;o<=s.length-1;o++){var r=n(s[o],{seriesIndex:o,w:e}),l=!1,h=!1;if(0<e.globals.collapsedSeries.length)for(var c=0;c<e.globals.collapsedSeries.length;c++)e.globals.collapsedSeries[c].index===o&&(l=!0);if(0<e.globals.ancillaryCollapsedSeriesIndices.length)for(var d=0;d<e.globals.ancillaryCollapsedSeriesIndices.length;d++)e.globals.ancillaryCollapsedSeriesIndices[d]===o&&(h=!0);var u=document.createElement("span"),g=(u.classList.add("apexcharts-legend-marker"),e.config.legend.markers.offsetX),p=e.config.legend.markers.offsetY,f=e.config.legend.markers.height,x=e.config.legend.markers.width,b=e.config.legend.markers.strokeWidth,m=e.config.legend.markers.strokeColor,y=e.config.legend.markers.radius,v=u.style;v.background=a[o],v.color=a[o],v.height=Array.isArray(f)?parseFloat(f[o])+"px":parseFloat(f)+"px",v.width=Array.isArray(x)?parseFloat(x[o])+"px":parseFloat(x)+"px",v.left=Array.isArray(g)?g[o]:g,v.top=Array.isArray(p)?p[o]:p,v.borderWidth=Array.isArray(b)?b[o]:b,v.borderColor=Array.isArray(m)?m[o]:m,v.borderRadius=Array.isArray(y)?parseFloat(y[o])+"px":parseFloat(y)+"px",e.config.legend.markers.customHTML&&(Array.isArray(e.config.legend.markers.customHTML)?u.innerHTML=e.config.legend.markers.customHTML[o]():u.innerHTML=e.config.legend.markers.customHTML()),tt.setAttrs(u,{rel:o+1,"data:collapsed":l||h}),(l||h)&&u.classList.add("inactive-legend"),v=document.createElement("div"),(y=document.createElement("span")).classList.add("apexcharts-legend-text"),y.innerHTML=r,r=(e.config.legend.labels.useSeriesColors?e.globals.colors[o]:e.config.legend.labels.colors)||e.config.chart.foreColor,y.style.color=r,y.style.fontSize=parseFloat(e.config.legend.fontSize)+"px",y.style.fontFamily=i||e.config.chart.fontFamily,tt.setAttrs(y,{rel:o+1,"data:collapsed":l||h}),v.appendChild(u),v.appendChild(y),y=new et(this.ctx),e.config.legend.showForZeroSeries||0===y.getSeriesTotalByIndex(o)&&y.seriesHaveSameValues(o)&&!y.isSeriesNull(o)&&-1===e.globals.collapsedSeriesIndices.indexOf(o)&&-1===e.globals.ancillaryCollapsedSeriesIndices.indexOf(o)&&v.classList.add("apexcharts-hidden-zero-series"),e.config.legend.showForNullSeries||y.isSeriesNull(o)&&-1===e.globals.collapsedSeriesIndices.indexOf(o)&&-1===e.globals.ancillaryCollapsedSeriesIndices.indexOf(o)&&v.classList.add("apexcharts-hidden-null-series"),e.globals.dom.elLegendWrap.appendChild(v),e.globals.dom.elLegendWrap.classList.add(e.config.legend.horizontalAlign),e.globals.dom.elLegendWrap.classList.add("position-"+e.config.legend.position),v.classList.add("apexcharts-legend-series"),v.style.margin="".concat(e.config.legend.itemMargin.horizontal,"px ").concat(e.config.legend.itemMargin.vertical,"px"),e.globals.dom.elLegendWrap.style.width=e.config.legend.width?e.config.legend.width+"px":"",e.globals.dom.elLegendWrap.style.height=e.config.legend.height?e.config.legend.height+"px":"",tt.setAttrs(v,{rel:o+1,"data:collapsed":l||h}),(l||h)&&v.classList.add("inactive-legend"),e.config.legend.onItemClick.toggleDataSeries||v.classList.add("no-click")}"heatmap"!==e.config.chart.type&&e.config.legend.onItemClick.toggleDataSeries&&e.globals.dom.elWrap.addEventListener("click",this.onLegendClick,!0),e.config.legend.onItemHover.highlightDataSeries&&(e.globals.dom.elWrap.addEventListener("mousemove",this.onLegendHovered,!0),e.globals.dom.elWrap.addEventListener("mouseout",this.onLegendHovered,!0))}},{key:"getLegendBBox",value:function(){var t=this.w.globals.dom.baseEl.querySelector(".apexcharts-legend").getBoundingClientRect(),e=t.width;return{clwh:t.height,clww:e}}},{key:"setLegendWrapXY",value:function(t,e){var i,s=this.w,a=s.globals.dom.baseEl.querySelector(".apexcharts-legend"),n=a.getBoundingClientRect(),o=0;"bottom"===s.config.legend.position?o+=s.globals.svgHeight-n.height/2:"top"===s.config.legend.position&&(o=o+(0<(n=(i=new rt(this.ctx)).getTitleSubtitleCoords("title").height)?n-10:0)+(0<(i=i.getTitleSubtitleCoords("subtitle").height)?i-10:0)),a.style.position="absolute",t=0+t+s.config.legend.offsetX,o=o+e+s.config.legend.offsetY,a.style.left=t+"px",a.style.top=o+"px","bottom"===s.config.legend.position?(a.style.top="auto",a.style.bottom=10+s.config.legend.offsetY+"px"):"right"===s.config.legend.position&&(a.style.left="auto",a.style.right=25+s.config.legend.offsetX+"px"),a.style.width&&(a.style.width=parseInt(s.config.legend.width)+"px"),a.style.height&&(a.style.height=parseInt(s.config.legend.height)+"px")}},{key:"legendAlignHorizontal",value:function(){var t=this.w,e=(t.globals.dom.baseEl.querySelector(".apexcharts-legend").style.right=0,this.getLegendBBox()),i=(a=new rt(this.ctx)).getTitleSubtitleCoords("title"),s=a.getTitleSubtitleCoords("subtitle"),a=0;"bottom"===t.config.legend.position?a=-e.clwh/1.8:"top"===t.config.legend.position&&(a=i.height+s.height+t.config.title.margin+t.config.subtitle.margin-15),this.setLegendWrapXY(20,a)}},{key:"legendAlignVertical",value:function(){var t=this.w,e=this.getLegendBBox(),i=0;"left"===t.config.legend.position&&(i=20),"right"===t.config.legend.position&&(i=t.globals.svgWidth-e.clww-10),this.setLegendWrapXY(i,20)}},{key:"onLegendHovered",value:function(t){var e=this.w,i=t.target.classList.contains("apexcharts-legend-text")||t.target.classList.contains("apexcharts-legend-marker");"heatmap"!==e.config.chart.type?!t.target.classList.contains("inactive-legend")&&i&&new g(this.ctx).toggleSeriesOnHover(t,t.target):i&&(i=parseInt(t.target.getAttribute("rel"))-1,this.ctx.fireEvent("legendHover",[this.ctx,i,this.w]),new g(this.ctx).highlightRangeInSeries(t,t.target))}},{key:"onLegendClick",value:function(t){var e,i,s;(t.target.classList.contains("apexcharts-legend-text")||t.target.classList.contains("apexcharts-legend-marker"))&&(e=parseInt(t.target.getAttribute("rel"))-1,i="true"===t.target.getAttribute("data:collapsed"),"function"==typeof(s=this.w.config.chart.events.legendClick)&&s(this.ctx,e,this.w),this.ctx.fireEvent("legendClick",[this.ctx,e,this.w]),"function"==typeof(s=this.w.config.legend.markers.onClick)&&t.target.classList.contains("apexcharts-legend-marker")&&(s(this.ctx,e,this.w),this.ctx.fireEvent("legendMarkerClick",[this.ctx,e,this.w])),this.toggleDataSeries(e,i))}},{key:"getLegendStyles",value:function(){var t=document.createElement("style"),e=(t.setAttribute("type","text/css"),document.createTextNode("\n    \n      .apexcharts-legend {\n        display: flex;\n        overflow: auto;\n        padding: 0 10px;\n      }\n\n      .apexcharts-legend.position-bottom, .apexcharts-legend.position-top {\n        flex-wrap: wrap\n      }\n      .apexcharts-legend.position-right, .apexcharts-legend.position-left {\n        flex-direction: column;\n        bottom: 0;\n      }\n\n      .apexcharts-legend.position-bottom.left, .apexcharts-legend.position-top.left, .apexcharts-legend.position-right, .apexcharts-legend.position-left {\n        justify-content: flex-start;\n      }\n\n      .apexcharts-legend.position-bottom.center, .apexcharts-legend.position-top.center {\n        justify-content: center;  \n      }\n\n      .apexcharts-legend.position-bottom.right, .apexcharts-legend.position-top.right {\n        justify-content: flex-end;\n      }\n\n      .apexcharts-legend-series {\n        cursor: pointer;\n        line-height: normal;\n      }\n\n      .apexcharts-legend.position-bottom .apexcharts-legend-series, .apexcharts-legend.position-top .apexcharts-legend-series{\n        display: flex;\n        align-items: center;\n      }\n\n      .apexcharts-legend-text {\n        position: relative;\n        font-size: 14px;\n      }\n\n      .apexcharts-legend-text *, .apexcharts-legend-marker * {\n        pointer-events: none;\n      }\n\n      .apexcharts-legend-marker {\n        position: relative;\n        display: inline-block;\n        cursor: pointer;\n        margin-right: 3px;\n      }\n      \n      .apexcharts-legend.right .apexcharts-legend-series, .apexcharts-legend.left .apexcharts-legend-series{\n        display: inline-block;\n      }\n\n      .apexcharts-legend-series.no-click {\n        cursor: auto;\n      }\n\n      .apexcharts-legend .apexcharts-hidden-zero-series, .apexcharts-legend .apexcharts-hidden-null-series {\n        display: none !important;\n      }\n\n      .inactive-legend {\n        opacity: 0.45;\n      }"));return t.appendChild(e),t}},{key:"toggleDataSeries",value:function(t,e){var i=this.w;if(i.globals.axisCharts||"radialBar"===i.config.chart.type){i.globals.resized=!0;var s=null,a=null;if(i.globals.risingSeries=[],a=i.globals.axisCharts?(s=i.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(t,"']")),parseInt(s.getAttribute("data:realIndex"))):(s=i.globals.dom.baseEl.querySelector(".apexcharts-series[rel='".concat(t+1,"']")),parseInt(s.getAttribute("rel"))-1),e)this.riseCollapsedSeries(i.globals.collapsedSeries,i.globals.collapsedSeriesIndices,a),this.riseCollapsedSeries(i.globals.ancillaryCollapsedSeries,i.globals.ancillaryCollapsedSeriesIndices,a);else{i.globals.axisCharts?(e=!1,i.config.yaxis[a]&&i.config.yaxis[a].show&&i.config.yaxis[a].showAlways&&(e=!0,i.globals.ancillaryCollapsedSeriesIndices.indexOf(a)<0)&&(i.globals.ancillaryCollapsedSeries.push({index:a,data:i.config.series[a].data.slice(),type:s.parentNode.className.baseVal.split("-")[1]}),i.globals.ancillaryCollapsedSeriesIndices.push(a)),e||(i.globals.collapsedSeries.push({index:a,data:i.config.series[a].data.slice(),type:s.parentNode.className.baseVal.split("-")[1]}),i.globals.collapsedSeriesIndices.push(a),e=i.globals.risingSeries.indexOf(a),i.globals.risingSeries.splice(e,1)),i.config.series[a].data=[]):(i.globals.collapsedSeries.push({index:a,data:i.config.series[a]}),i.globals.collapsedSeriesIndices.push(a),i.config.series[a]=0);for(var n=s.childNodes,o=0;o<n.length;o++)n[o].classList.contains("apexcharts-series-markers-wrap")&&(n[o].classList.contains("apexcharts-hide")?n[o].classList.remove("apexcharts-hide"):n[o].classList.add("apexcharts-hide"));i.globals.allSeriesCollapsed=i.globals.collapsedSeries.length===i.globals.series.length,this.ctx._updateSeries(i.config.series,i.config.chart.animations.dynamicAnimation.enabled)}}else a=i.globals.dom.Paper.select(" .apexcharts-series[rel='".concat(t+1,"'] path")),"pie"!==(s=i.config.chart.type)&&"donut"!==s||(t=i.config.plotOptions.pie.donut.labels,s=new tt(this.ctx),i=new U(this.ctx),s.pathMouseDown(a.members[0],null),i.printDataLabelsInner(a.members[0].node,t)),a.fire("click")}},{key:"riseCollapsedSeries",value:function(t,e,i){var s=this.w;if(0<t.length)for(var a=0;a<t.length;a++)t[a].index===i&&(s.globals.axisCharts?s.config.series[i].data=t[a].data.slice():s.config.series[i]=t[a].data,t.splice(a,1),e.splice(a,1),s.globals.risingSeries.push(i),this.ctx._updateSeries(s.config.series,s.config.chart.animations.dynamicAnimation.enabled))}}]),vt),ht=(t(yt,[{key:"draw",value:function(t,R,D){var e=this.w,i=new tt(this.ctx),N=new it(this.ctx),s=e.globals.comboCharts?R:e.config.chart.type,O=i.group({class:"apexcharts-".concat(s,"-series apexcharts-plot-series")}),a=new et(this.ctx,e);t=a.getLogSeries(t);for(var n=this.xyRatios.yRatio,n=a.getLogYRatios(n),H=this.xyRatios.zRatio,o=this.xyRatios.xRatio,W=this.xyRatios.baseLineY,r=[],l=[],h=0;h<t.length;h++){"line"!==s||"gradient"!==e.config.fill.type&&"gradient"!==e.config.fill.type[h]||!a.seriesHaveSameValues(h)||((C=t[h].slice())[C.length-1]=C[C.length-1]+1e-6,t[h]=C);var c=e.globals.gridWidth/e.globals.dataPoints,d=e.globals.comboCharts?D[h]:h,u=(1<n.length&&(this.yaxisIndex=d),this.isReversed=e.config.yaxis[this.yaxisIndex]&&e.config.yaxis[this.yaxisIndex].reversed,[]),g=[],p=e.globals.gridHeight-W[this.yaxisIndex]-(this.isReversed?e.globals.gridHeight:0)+(this.isReversed?2*W[this.yaxisIndex]:0),f=p,B=(p>e.globals.gridHeight&&(f=e.globals.gridHeight),c/2),x=e.globals.padHorizontal+B,b=(e.globals.isXNumeric&&0<e.globals.seriesX.length&&(x=(e.globals.seriesX[d][0]-e.globals.minX)/o),g.push(x),void 0),m=void 0,y=void 0,v=void 0,w=[],k=[],A=i.group({class:"apexcharts-series",seriesName:K.escapeString(e.globals.seriesNames[d])}),S=i.group({class:"apexcharts-series-markers-wrap"}),V=i.group({class:"apexcharts-datalabels"}),C=(this.ctx.series.addCollapsedClassToSeries(A,d),t[h].length===e.globals.dataPoints),G=(A.attr({"data:longestSeries":C,rel:h+1,"data:realIndex":d}),this.appendPathFrom=!0,void 0),L=j=x,z=p,z=this.determineFirstPrevY({i:h,series:t,yRatio:n[this.yaxisIndex],zeroY:p,prevY:z,prevSeriesY:l,lineYPosition:0}).prevY;if(u.push(z),G=z,null===t[h][0]){for(var P=0;P<t[h].length;P++)if(null!==t[h][P]){z=p-t[h][P]/n[this.yaxisIndex],b=i.move(L=c*P,z),m=i.move(L,f);break}}else b=i.move(L,z),m=i.move(L,f)+i.line(L,z);y=i.move(-1,p)+i.line(-1,p),v=i.move(-1,p)+i.line(-1,p),0<e.globals.previousPaths.length&&(y=(C=this.checkPreviousPaths({pathFromLine:y,pathFromArea:v,realIndex:d})).pathFromLine,v=C.pathFromArea);for(var _=1<e.globals.dataPoints?e.globals.dataPoints-1:e.globals.dataPoints,E=0;E<_;E++){e.globals.isXNumeric?(M=e.globals.seriesX[d][E+1],x=((M=void 0===e.globals.seriesX[d][E+1]?e.globals.seriesX[d][_-1]:M)-e.globals.minX)/o):x+=c;var M=K.isNumber(e.globals.minYArr[d])?e.globals.minYArr[d]:e.globals.minY,T=e.config.chart.stacked?(T=0<h&&e.globals.collapsedSeries.length<e.config.series.length-1?l[h-1][E+1]:p,void 0===t[h][E+1]||null===t[h][E+1]?T-M/n[this.yaxisIndex]+2*(this.isReversed?M/n[this.yaxisIndex]:0):T-t[h][E+1]/n[this.yaxisIndex]+2*(this.isReversed?t[h][E+1]/n[this.yaxisIndex]:0)):void 0===t[h][E+1]||null===t[h][E+1]?p-M/n[this.yaxisIndex]+2*(this.isReversed?M/n[this.yaxisIndex]:0):p-t[h][E+1]/n[this.yaxisIndex]+2*(this.isReversed?t[h][E+1]/n[this.yaxisIndex]:0),k=(g.push(x),u.push(T),(M=this.createPaths({series:t,i:h,j:E,x:x,y:T,xDivision:c,pX:j,pY:G,areaBottomY:f,linePath:b,areaPath:m,linePaths:w,areaPaths:k,seriesIndex:D})).areaPaths),w=M.linePaths,j=M.pX,G=M.pY,m=M.areaPath,b=M.linePath,X=(this.appendPathFrom&&(y+=i.line(x,p),v+=i.line(x,p)),M=this.calculatePoints({series:t,x:x,y:T,realIndex:d,i:h,j:E,prevY:z,categoryAxisCorrection:B,xRatio:o}),this.pointsChart?this.scatter.draw(A,E,{realIndex:d,pointsPos:M,zRatio:H,elParent:S}):(X=new st(this.ctx),1<e.globals.dataPoints&&S.node.classList.add("hidden"),null!==(X=X.plotChartMarkers(M,d,E+1))&&S.add(X)),!t[h][E+1]||t[h][E+1]>t[h][E]?"top":"bottom");null!==(X=new at(this.ctx).drawDataLabel(M,d,E+1,null,X))&&V.add(X)}l.push(u),e.globals.seriesXvalues[d]=g,e.globals.seriesYvalues[d]=u,this.pointsChart||e.globals.delayedElements.push({el:S.node,index:d});var U={i:h,realIndex:d,animationDelay:h,initialSpeed:e.config.chart.animations.speed,dataChangeSpeed:e.config.chart.animations.dynamicAnimation.speed,className:"apexcharts-".concat(s)};if("area"===s)for(var q=N.fillPath({seriesNumber:d}),I=0;I<k.length;I++){var Z=i.renderPaths(Q({},U,{pathFrom:v,pathTo:k[I],stroke:"none",strokeWidth:0,strokeLineCap:null,fill:q}));A.add(Z)}if(e.config.stroke.show&&!this.pointsChart)for(var $="line"===s?N.fillPath({seriesNumber:d,i:h}):e.globals.stroke.colors[d],Y=0;Y<w.length;Y++){var J=i.renderPaths(Q({},U,{pathFrom:y,pathTo:w[Y],stroke:$,strokeWidth:Array.isArray(e.config.stroke.width)?e.config.stroke.width[d]:e.config.stroke.width,strokeLineCap:e.config.stroke.lineCap,fill:"none"}));A.add(J)}A.add(S),A.add(V),r.push(A)}for(var F=r.length;0<F;F--)O.add(r[F-1]);return O}},{key:"createPaths",value:function(t){var e=t.series,i=t.i,s=t.j,a=t.x,n=t.y,o=t.pX,r=t.pY,l=t.xDivision,h=t.areaBottomY,c=t.linePath,d=t.areaPath,u=t.linePaths,g=t.areaPaths,p=t.seriesIndex,f=this.w,x=new tt(this.ctx),t=f.config.stroke.curve;return"smooth"===(t=Array.isArray(f.config.stroke.curve)?Array.isArray(p)?f.config.stroke.curve[p[i]]:f.config.stroke.curve[i]:t)?(p=.35*(a-o),f.globals.hasNullValues?(null!==e[i][s]&&(d=null!==e[i][s+1]?(c=x.move(o,r)+x.curve(o+p,r,a-p,n,a+1,n),x.move(o+1,r)+x.curve(o+p,r,a-p,n,a+1,n)+x.line(a,h)+x.line(o,h)+"z"):(c=x.move(o,r),x.move(o,r)+"z")),u.push(c),g.push(d)):(c+=x.curve(o+p,r,a-p,n,a,n),d+=x.curve(o+p,r,a-p,n,a,n)),o=a,r=n,s===e[i].length-2&&(d=d+x.curve(o,r,a,n,a,h)+x.move(a,n)+"z",f.globals.hasNullValues||(u.push(c),g.push(d)))):(null===e[i][s+1]&&(c+=x.move(a,n),d=d+x.line(a-l,h)+x.move(a,n)),null===e[i][s]&&(c+=x.move(a,n),d+=x.move(a,h)),"stepline"===t?(c=c+x.line(a,null,"H")+x.line(null,n,"V"),d=d+x.line(a,null,"H")+x.line(null,n,"V")):"straight"===t&&(c+=x.line(a,n),d+=x.line(a,n)),s===e[i].length-2&&(d=d+x.line(a,h)+x.move(a,n)+"z",u.push(c),g.push(d))),{linePaths:u,areaPaths:g,pX:o,pY:r,linePath:c,areaPath:d}}},{key:"calculatePoints",value:function(t){var e=t.series,i=t.realIndex,s=t.x,a=t.y,n=t.i,o=t.j,r=t.prevY,l=t.categoryAxisCorrection,h=t.xRatio,c=this.w,d=[],t=[];return 0===o&&(l+=c.config.markers.offsetX,c.globals.isXNumeric&&(l=(c.globals.seriesX[i][0]-c.globals.minX)/h+c.config.markers.offsetX),d.push(l),t.push(K.isNumber(e[n][0])?r+c.config.markers.offsetY:null)),d.push(s+c.config.markers.offsetX),t.push(K.isNumber(e[n][o+1])?a+c.config.markers.offsetY:null),{x:d,y:t}}},{key:"checkPreviousPaths",value:function(t){for(var e=t.pathFromLine,i=t.pathFromArea,s=t.realIndex,a=this.w,n=0;n<a.globals.previousPaths.length;n++){var o=a.globals.previousPaths[n];("line"===o.type||"area"===o.type)&&0<o.paths.length&&parseInt(o.realIndex)===parseInt(s)&&("line"===o.type?(this.appendPathFrom=!1,e=a.globals.previousPaths[n].paths[0].d):"area"===o.type&&(this.appendPathFrom=!1,i=a.globals.previousPaths[n].paths[0].d,a.config.stroke.show)&&(e=a.globals.previousPaths[n].paths[1].d))}return{pathFromLine:e,pathFromArea:i}}},{key:"determineFirstPrevY",value:function(t){var e=t.i,i=t.series,s=t.yRatio,a=t.zeroY,n=t.prevY,o=t.prevSeriesY,r=t.lineYPosition,t=this.w;if(void 0!==i[e][0])n=t.config.chart.stacked?(r=0<e?o[e-1][0]:a)-i[e][0]/s+2*(this.isReversed?i[e][0]/s:0):a-i[e][0]/s+2*(this.isReversed?i[e][0]/s:0);else if(t.config.chart.stacked&&0<e&&void 0===i[e][0])for(var l=e-1;0<=l;l--)if(null!==i[l][0]&&void 0!==i[l][0]){n=r=o[l][0];break}return{prevY:n,lineYPosition:r}}}]),yt),X=(t(mt,[{key:"drawXaxis",value:function(){var t,e=this.w,i=new tt(this.ctx),s=i.group({class:"apexcharts-xaxis",transform:"translate(".concat(e.config.xaxis.offsetX,", ").concat(e.config.xaxis.offsetY,")")}),a=i.group({class:"apexcharts-xaxis-texts-g",transform:"translate(".concat(e.globals.translateXAxisX,", ").concat(e.globals.translateXAxisY,")")});s.add(a);for(var n=e.globals.padHorizontal,o=[],r=0;r<this.xaxisLabels.length;r++)o.push(this.xaxisLabels[r]);var l,h,n=e.globals.isXNumeric?n+(t=e.globals.gridWidth/(o.length-1))/2+e.config.xaxis.labels.offsetX:n+(t=e.globals.gridWidth/o.length)+e.config.xaxis.labels.offsetX,c=o.length;if(e.config.xaxis.labels.show)for(var d=0;d<=c-1;d++){var u=n-t/2+e.config.xaxis.labels.offsetX,g=this.axesUtils.getLabel(o,e.globals.timelineLabels,u,d,this.drawnLabels),p=(this.drawnLabels.push(g.text),28);e.globals.rotateXLabels&&(p=22),u=i.drawText({x:g.x,y:this.offY+e.config.xaxis.labels.offsetY+p,text:"",textAnchor:"middle",fontSize:this.xaxisFontSize,fontFamily:this.xaxisFontFamily,foreColor:Array.isArray(this.xaxisForeColors)?this.xaxisForeColors[d]:this.xaxisForeColors,cssClass:"apexcharts-xaxis-label "+e.config.xaxis.labels.style.cssClass}),d===c-1&&e.globals.skipLastTimelinelabel&&(g.text=""),a.add(u),i.addTspan(u,g.text,this.xaxisFontFamily),(p=document.createElementNS(e.globals.SVGNS,"title")).textContent=g.text,u.node.appendChild(p),n+=t}return void 0!==e.config.xaxis.title.text&&(h=i.group({class:"apexcharts-xaxis-title"}),l=i.drawText({x:e.globals.gridWidth/2+e.config.xaxis.title.offsetX,y:this.offY-parseInt(this.xaxisFontSize)+e.globals.xAxisLabelsHeight+e.config.xaxis.title.offsetY,text:e.config.xaxis.title.text,textAnchor:"middle",fontSize:e.config.xaxis.title.style.fontSize,fontFamily:e.config.xaxis.title.style.fontFamily,foreColor:e.config.xaxis.title.style.color,cssClass:"apexcharts-xaxis-title-text "+e.config.xaxis.title.style.cssClass}),h.add(l),s.add(h)),e.config.xaxis.axisBorder.show&&(h=0,"bar"===e.config.chart.type&&e.globals.isXNumeric&&(h-=15),h=i.drawLine(e.globals.padHorizontal+h+e.config.xaxis.axisBorder.offsetX,this.offY,this.xaxisBorderWidth,this.offY,e.config.xaxis.axisBorder.color,0,this.xaxisBorderHeight),s.add(h)),s}},{key:"drawXaxisInversed",value:function(t){var e,i=this.w,s=new tt(this.ctx),a=i.config.yaxis[0].opposite?i.globals.translateYAxisX[t]:0,n=s.group({class:"apexcharts-yaxis apexcharts-xaxis-inversed",rel:t}),o=s.group({class:"apexcharts-yaxis-texts-g apexcharts-xaxis-inversed-texts-g",transform:"translate("+a+", 0)"});n.add(o);for(var r=[],l=0;l<this.xaxisLabels.length;l++)r.push(this.xaxisLabels[l]);var h,c=-(e=i.globals.gridHeight/r.length)/2.2,d=i.globals.yLabelFormatters[0],u=i.config.yaxis[0].labels;if(u.show)for(var g=0;g<=r.length-1;g++){var p=d(void 0===r[g]?"":r[g]),f=s.drawText({x:u.offsetX-15,y:c+e+u.offsetY,text:p,textAnchor:this.yaxis.opposite?"start":"end",foreColor:u.style.color||u.style.colors[g],fontSize:u.style.fontSize,fontFamily:u.style.fontFamily,cssClass:"apexcharts-yaxis-label "+u.style.cssClass});o.add(f),0!==i.config.yaxis[t].labels.rotate&&(p=s.rotateAroundCenter(f.node),f.node.setAttribute("transform","rotate(".concat(i.config.yaxis[t].labels.rotate," ").concat(p.x," ").concat(p.y,")"))),c+=e}return void 0!==i.config.yaxis[0].title.text&&(h=s.group({class:"apexcharts-yaxis-title apexcharts-xaxis-title-inversed",transform:"translate("+a+", 0)"}),a=s.drawText({x:0,y:i.globals.gridHeight/2,text:i.config.yaxis[0].title.text,textAnchor:"middle",foreColor:i.config.yaxis[0].title.style.color,fontSize:i.config.yaxis[0].title.style.fontSize,fontFamily:i.config.yaxis[0].title.style.fontFamily,cssClass:"apexcharts-yaxis-title-text "+i.config.yaxis[0].title.style.cssClass}),h.add(a),n.add(h)),i.config.xaxis.axisBorder.show&&(h=s.drawLine(i.globals.padHorizontal+i.config.xaxis.axisBorder.offsetX,this.offY,this.xaxisBorderWidth,this.offY,this.yaxis.axisBorder.color,0,this.xaxisBorderHeight),n.add(h),this.axesUtils.drawYAxisTicks(0,r.length,i.config.yaxis[0].axisBorder,i.config.yaxis[0].axisTicks,0,e,n)),n}},{key:"drawXaxisTicks",value:function(t,e){var i,s,a=this.w,n=t;t<0||t>a.globals.gridWidth||(s=(i=this.offY+a.config.xaxis.axisTicks.offsetY)+a.config.xaxis.axisTicks.height,a.config.xaxis.axisTicks.show&&(a=new tt(this.ctx).drawLine(t+a.config.xaxis.axisTicks.offsetX,i+a.config.xaxis.offsetY,n+a.config.xaxis.axisTicks.offsetX,s+a.config.xaxis.offsetY,a.config.xaxis.axisTicks.color),e.add(a),a.node.classList.add("apexcharts-xaxis-tick")))}},{key:"getXAxisTicksPositions",value:function(){var t=this.w,e=[],i=this.xaxisLabels.length,s=t.globals.padHorizontal;if(0<t.globals.timelineLabels.length)for(var a=0;a<i;a++)s=this.xaxisLabels[a].position,e.push(s);else for(var n=i,o=0;o<n;o++){var r=n;t.globals.isXNumeric&&"bar"!==t.config.chart.type&&--r,s+=t.globals.gridWidth/r,e.push(s)}return e}},{key:"xAxisLabelCorrections",value:function(){var t=this.w,e=new tt(this.ctx),i=t.globals.dom.baseEl.querySelector(".apexcharts-xaxis-texts-g"),s=t.globals.dom.baseEl.querySelectorAll(".apexcharts-xaxis-texts-g text"),a=t.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxis-inversed text"),n=t.globals.dom.baseEl.querySelectorAll(".apexcharts-xaxis-inversed-texts-g text");if(t.globals.rotateXLabels||t.config.xaxis.labels.rotateAlways)for(var o=0;o<s.length;o++){var r=e.rotateAroundCenter(s[o]);r.y=r.y-1,r.x=r.x+1,s[o].setAttribute("transform","rotate(".concat(t.config.xaxis.labels.rotate," ").concat(r.x," ").concat(r.y,")")),s[o].setAttribute("text-anchor","end"),i.setAttribute("transform","translate(0, ".concat(-10,")")),r=s[o].childNodes,t.config.xaxis.labels.trim&&e.placeTextWithEllipsis(r[0],r[0].textContent,t.config.xaxis.labels.maxHeight-40)}else for(var l=t.globals.gridWidth/t.globals.labels.length,h=0;h<s.length;h++){var c=s[h].childNodes;t.config.xaxis.labels.trim&&"datetime"!==t.config.xaxis.type&&e.placeTextWithEllipsis(c[0],c[0].textContent,l)}if(0<a.length){var d=a[a.length-1].getBBox(),u=a[0].getBBox();d.x<-20&&a[a.length-1].parentNode.removeChild(a[a.length-1]),u.x+u.width>t.globals.gridWidth&&a[0].parentNode.removeChild(a[0]);for(var g=0;g<n.length;g++)e.placeTextWithEllipsis(n[g],n[g].textContent,t.config.yaxis[0].labels.maxWidth-2*parseInt(t.config.yaxis[0].title.style.fontSize)-20)}}}]),mt),ct=(t(bt,[{key:"niceScale",value:function(t,e,i){var s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:0,a=4<arguments.length&&void 0!==arguments[4]?arguments[4]:10,n=this.w,o=void 0===this.w.config.yaxis[s].max&&void 0===this.w.config.yaxis[s].min||this.w.config.yaxis[s].forceNiceScale;if(t===Number.MIN_VALUE&&0===e||!K.isNumber(t)&&!K.isNumber(e)||t===Number.MIN_VALUE&&e===-Number.MAX_VALUE)return this.linearScale(t=0,e=a,a);e<t?(console.warn("yaxis.min cannot be greater than yaxis.max"),e=t+.1):t===e&&(t=0===t?0:t-.5,e=0===e?2:e+.5);var r=[];(l=Math.abs(e-t))<1&&o&&("candlestick"===n.config.chart.type||"candlestick"===n.config.series[s].type||n.globals.isRangeData)&&(e*=1.01),(n=a+1)<2?n=2:2<n&&(n-=2);for(var l=l/n,n=Math.floor(K.log10(l)),n=Math.pow(10,n),h=parseInt(l/n)*n,n=h*Math.floor(t/h),c=h*Math.ceil(e/h),d=n;r.push(d),!((d+=h)>c););if(!(o&&10<i)){var u=t;(r=[]).push(u);for(var g=Math.abs(e-t)/a,p=0;p<=a;p++)r.push(u+=g);r[r.length-2]>=e&&r.pop()}return{result:r,niceMin:r[0],niceMax:r[r.length-1]}}},{key:"linearScale",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:10,s=Math.abs(e-t)/i;i===Number.MAX_VALUE&&(i=10,s=1);for(var a=[],n=t;0<=i;)a.push(n),n+=s,--i;return{result:a,niceMin:a[0],niceMax:a[a.length-1]}}},{key:"logarithmicScale",value:function(t,s,a,e){(s<0||s===Number.MIN_VALUE)&&(s=.01);for(var n=Math.log(s)/Math.log(10),o=Math.log(a)/Math.log(10),i=Math.abs(a-s)/e,r=[],l=s;0<=e;)r.push(l),l+=i,--e;var h=r.map(function(t,e){t<=0&&(t=.01);var i=(o-n)/(a-s),t=Math.pow(10,n+i*(t-n));return Math.round(t/K.roundToBase(t,10))*K.roundToBase(t,10)});return 0===h[0]&&(h[0]=1),{result:h,niceMin:h[0],niceMax:h[h.length-1]}}},{key:"setYScaleForIndex",value:function(t,e,i){var s=this.w.globals,a=this.w.config,n=s.isBarHorizontal?a.xaxis:a.yaxis[t];void 0===s.yAxisScale[t]&&(s.yAxisScale[t]=[]),n.logarithmic?(s.allSeriesCollapsed=!1,s.yAxisScale[t]=this.logarithmicScale(t,e,i,n.tickAmount||Math.floor(Math.log10(i)))):i!==-Number.MAX_VALUE&&K.isNumber(i)?(s.allSeriesCollapsed=!1,void 0===n.min&&void 0===n.max||n.forceNiceScale?(a=Math.abs(i-e),s.yAxisScale[t]=this.niceScale(e,i,a,t,n.tickAmount||(a<5&&1<a?a+1:5))):s.yAxisScale[t]=this.linearScale(e,i,n.tickAmount)):s.yAxisScale[t]=this.linearScale(0,5,5)}},{key:"setMultipleYScales",value:function(){var n=this,o=this.w.globals,r=this.w.config,l=o.minYArr.concat([]),h=o.maxYArr.concat([]),c=[];r.yaxis.forEach(function(i,s){var a=s,t=(r.series.forEach(function(t,e){t.name===i.seriesName&&-1===o.collapsedSeriesIndices.indexOf(e)&&(s!==(a=e)?c.push({index:e,similarIndex:s,alreadyExists:!0}):c.push({index:e}))}),l[a]),e=h[a];n.setYScaleForIndex(s,t,e)}),this.sameScaleInMultipleAxes(l,h,c)}},{key:"sameScaleInMultipleAxes",value:function(t,a,e){var r=this,l=this.w.config,h=this.w.globals,n=[],e=(e.forEach(function(t){t.alreadyExists&&(void 0===n[t.index]&&(n[t.index]=[]),n[t.index].push(t.index),n[t.index].push(t.similarIndex))}),(h.yAxisSameScaleIndices=n).forEach(function(s,a){n.forEach(function(t,e){var i;a!==e&&(i=t,0<s.filter(function(t){return-1!==i.indexOf(t)}).length)&&(n[a]=n[a].concat(n[e]))})}),n.map(function(i){return i.filter(function(t,e){return i.indexOf(t)===e})}).map(function(t){return t.sort()})),n=n.filter(function(t){return!!t}),i=(o=e.slice()).map(function(t){return JSON.stringify(t)}),o=o.filter(function(t,e){return i.indexOf(JSON.stringify(t))===e}),c=[],d=[],u=(t.forEach(function(i,s){o.forEach(function(t,e){-1<t.indexOf(s)&&(void 0===c[e]&&(c[e]=[],d[e]=[]),c[e].push({key:s,value:i}),d[e].push({key:s,value:a[s]}))})}),Array.apply(null,Array(o.length)).map(Number.prototype.valueOf,Number.MIN_VALUE)),g=Array.apply(null,Array(o.length)).map(Number.prototype.valueOf,-Number.MAX_VALUE);c.forEach(function(t,i){t.forEach(function(t,e){u[i]=Math.min(t.value,u[i])})}),d.forEach(function(t,i){t.forEach(function(t,e){g[i]=Math.max(t.value,g[i])})}),t.forEach(function(t,o){d.forEach(function(i,s){var a=u[s],n=g[s];l.chart.stacked&&(n=0,i.forEach(function(t,e){n+=t.value,a!==Number.MIN_VALUE&&(a+=c[s][e].value)})),i.forEach(function(t,e){i[e].key===o&&(void 0!==l.yaxis[o].min&&(a="function"==typeof l.yaxis[o].min?l.yaxis[o].min(h.minY):l.yaxis[o].min),void 0!==l.yaxis[o].max&&(n="function"==typeof l.yaxis[o].max?l.yaxis[o].max(h.maxY):l.yaxis[o].max),r.setYScaleForIndex(o,a,n))})})})}},{key:"autoScaleY",value:function(t,c,d){var u,g,p=(t=t||this).w;return p.globals.isMultipleYAxis||(u=p.globals.seriesX[0],g=p.config.chart.stacked,c.forEach(function(s,t){for(var a=0,e=0;e<u.length;e++)if(u[e]>=d.xaxis.min){a=e;break}var o,r,n=p.globals.minYArr[t],l=p.globals.maxYArr[t],h=p.globals.stackedSeriesTotals;p.globals.series.forEach(function(i,t){var e=i[a];g?(e=h[a],o=r=e,h.forEach(function(t,e){u[e]<=d.xaxis.max&&u[e]>=d.xaxis.min&&(r<t&&null!==t&&(r=t),i[e]<o)&&null!==i[e]&&(o=i[e])})):(o=r=e,i.forEach(function(i,s){var a,n;u[s]<=d.xaxis.max&&u[s]>=d.xaxis.min&&(n=a=i,p.globals.series.forEach(function(t,e){null!==i&&(a=Math.min(t[s],a),n=Math.max(t[s],n))}),r<n&&null!==n&&(r=n),a<o)&&null!==a&&(o=a)})),void 0===o&&void 0===r&&(o=n,r=l),(r*=r<0?.9:1.1)<0&&r<l&&(r=l),(o*=o<0?1.1:.9)<0&&n<o&&(o=n),1<c.length?(c[t].min=void 0===s.min?o:s.min,c[t].max=void 0===s.max?r:s.max):(c[0].min=void 0===s.min?o:s.min,c[0].max=void 0===s.max?r:s.max)})})),c}}]),bt),dt=(t(xt,[{key:"init",value:function(){this.setYRange(),this.setXRange(),this.setZRange()}},{key:"getMinYMaxY",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:-Number.MAX_VALUE,s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,a=this.w.globals,n=-Number.MAX_VALUE,o=Number.MIN_VALUE,r=(null===s&&(s=t+1),a.series),l=r,h=r;"candlestick"===this.w.config.chart.type?(l=a.seriesCandleL,h=a.seriesCandleH):a.isRangeData&&(l=a.seriesRangeStart,h=a.seriesRangeEnd);for(var c=t;c<s;c++){a.dataPoints=Math.max(a.dataPoints,r[c].length);for(var d=0;d<a.series[c].length;d++){var u=r[c][d];null!==u&&K.isNumber(u)?(n=Math.max(n,h[c][d]),e=Math.min(e,l[c][d]),i=Math.max(i,l[c][d]),"candlestick"===this.w.config.chart.type&&(n=Math.max(n,a.seriesCandleO[c][d]),n=Math.max(n,a.seriesCandleH[c][d]),n=Math.max(n,a.seriesCandleL[c][d]),i=n=Math.max(n,a.seriesCandleC[c][d])),K.isFloat(u)&&(u=K.noExponents(u),a.yValueDecimal=Math.max(a.yValueDecimal,u.toString().split(".")[1].length)),o>l[c][d]&&l[c][d]<0&&(o=l[c][d])):a.hasNullValues=!0}}return{minY:o,maxY:n,lowestY:e,highestY:i}}},{key:"setYRange",value:function(){var i=this.w.globals,t=this.w.config,e=(i.maxY=-Number.MAX_VALUE,i.minY=Number.MIN_VALUE,Number.MAX_VALUE);if(i.isMultipleYAxis)for(var s=0;s<i.series.length;s++){var a=this.getMinYMaxY(s,e,null,s+1);i.minYArr.push(a.minY),i.maxYArr.push(a.maxY),e=a.lowestY}var n=this.getMinYMaxY(0,e,null,i.series.length);if(i.minY=n.minY,i.maxY=n.maxY,e=n.lowestY,t.chart.stacked){var o=[],r=[];if(i.series.length)for(var l=0;l<i.series[i.maxValsInArrayIndex].length;l++)for(var h=0,c=0,d=0;d<i.series.length;d++)null!==i.series[d][l]&&K.isNumber(i.series[d][l])&&(0<i.series[d][l]?h=h+parseFloat(i.series[d][l])+1e-4:c+=parseFloat(i.series[d][l])),d===i.series.length-1&&(o.push(h),r.push(c));for(var u=0;u<o.length;u++)i.maxY=Math.max(i.maxY,o[u]),i.minY=Math.min(i.minY,r[u])}return"line"!==t.chart.type&&"area"!==t.chart.type&&"candlestick"!==t.chart.type||i.minY!==Number.MIN_VALUE||e===-Number.MAX_VALUE||e===i.maxY||(n=i.maxY-e,i.minY=e-5*(n=0<=e&&e<=10?0:n)/100,i.maxY=i.maxY+5*n/100),t.yaxis.map(function(t,e){void 0!==t.max&&("number"==typeof t.max?i.maxYArr[e]=t.max:"function"==typeof t.max&&(i.maxYArr[e]=t.max(i.maxY)),i.maxY=i.maxYArr[e]),void 0!==t.min&&("number"==typeof t.min?i.minYArr[e]=t.min:"function"==typeof t.min&&(i.minYArr[e]=t.min(i.minY)),i.minY=i.minYArr[e])}),i.isBarHorizontal&&(void 0!==t.xaxis.min&&"number"==typeof t.xaxis.min&&(i.minY=t.xaxis.min),void 0!==t.xaxis.max)&&"number"==typeof t.xaxis.max&&(i.maxY=t.xaxis.max),i.isMultipleYAxis?(this.scales.setMultipleYScales(),i.minY=e,i.yAxisScale.forEach(function(t,e){i.minYArr[e]=t.niceMin,i.maxYArr[e]=t.niceMax})):(this.scales.setYScaleForIndex(0,i.minY,i.maxY),i.minY=i.yAxisScale[0].niceMin,i.maxY=i.yAxisScale[0].niceMax,i.minYArr[0]=i.yAxisScale[0].niceMin,i.maxYArr[0]=i.yAxisScale[0].niceMax),{minY:i.minY,maxY:i.maxY,minYArr:i.minYArr,maxYArr:i.maxYArr}}},{key:"setXRange",value:function(){var t,e,i,s=this.w.globals,a=this.w.config,n="numeric"===a.xaxis.type||"datetime"===a.xaxis.type||"category"===a.xaxis.type&&!s.noLabelsProvided||s.noLabelsProvided||s.isXNumeric;if(s.isXNumeric)for(var o=0;o<s.series.length;o++)if(s.labels[o])for(var r=0;r<s.labels[o].length;r++)null!==s.labels[o][r]&&K.isNumber(s.labels[o][r])&&(s.maxX=Math.max(s.maxX,s.labels[o][r]),s.initialmaxX=Math.max(s.maxX,s.labels[o][r]),s.minX=Math.min(s.minX,s.labels[o][r]),s.initialminX=Math.min(s.minX,s.labels[o][r]));return s.noLabelsProvided&&0===a.xaxis.categories.length&&(s.maxX=s.labels[s.labels.length-1],s.initialmaxX=s.labels[s.labels.length-1],s.minX=1,s.initialminX=1),(s.comboChartsHasBars||"candlestick"===a.chart.type||"bar"===a.chart.type&&s.isXNumeric)&&("category"!==a.xaxis.type||s.isXNumeric)&&(i=s.svgWidth/s.dataPoints*(Math.abs(s.maxX-s.minX)/s.svgWidth),e=s.minX-i/2,s.minX=e,s.initialminX=e,i=s.maxX+i/((s.series.length+1)/s.series.length),s.maxX=i,s.initialmaxX=i),!s.isXNumeric&&!s.noLabelsProvided||a.xaxis.convertedCatToNumeric&&!s.dataFormatXNumeric||(void 0===a.xaxis.tickAmount?(t=Math.round(s.svgWidth/150),(t="numeric"===a.xaxis.type&&s.dataPoints<20?s.dataPoints-1:t)>s.dataPoints&&0!==s.dataPoints&&(t=s.dataPoints-1)):t="dataPoints"===a.xaxis.tickAmount?s.series[s.maxValsInArrayIndex].length-1:a.xaxis.tickAmount,void 0!==a.xaxis.max&&"number"==typeof a.xaxis.max&&(s.maxX=a.xaxis.max),void 0!==a.xaxis.min&&"number"==typeof a.xaxis.min&&(s.minX=a.xaxis.min),void 0!==a.xaxis.range&&(s.minX=s.maxX-a.xaxis.range),s.minX!==Number.MAX_VALUE&&s.maxX!==-Number.MAX_VALUE?s.xAxisScale=this.scales.linearScale(s.minX,s.maxX,t):(s.xAxisScale=this.scales.linearScale(1,t,t),s.noLabelsProvided&&0<s.labels.length&&(s.xAxisScale=this.scales.linearScale(1,s.labels.length,t-1),s.seriesX=s.labels.slice())),n&&(s.labels=s.xAxisScale.result.slice())),s.minX===s.maxX&&("datetime"===a.xaxis.type?((n=new Date(s.minX)).setDate(n.getDate()-2),s.minX=new Date(n).getTime(),(n=new Date(s.maxX)).setDate(n.getDate()+2),s.maxX=new Date(n).getTime()):"numeric"!==a.xaxis.type&&("category"!==a.xaxis.type||s.noLabelsProvided)||(s.minX=s.minX-2,s.maxX=s.maxX+2)),s.isXNumeric&&(s.seriesX.forEach(function(t,i){t.forEach(function(t,e){0<e&&(e=t-s.seriesX[i][e-1],s.minXDiff=Math.min(e,s.minXDiff))})}),this.calcMinXDiffForTinySeries()),{minX:s.minX,maxX:s.maxX}}},{key:"calcMinXDiffForTinySeries",value:function(){var t=this.w,e=t.globals.labels.length;return 1===t.globals.labels.length?t.globals.minXDiff=(t.globals.maxX-t.globals.minX)/e/3:t.globals.minXDiff===Number.MAX_VALUE&&((e=0<t.globals.timelineLabels.length?t.globals.timelineLabels.length:e)<3&&(e=3),t.globals.minXDiff=(t.globals.maxX-t.globals.minX)/e),t.globals.minXDiff}},{key:"setZRange",value:function(){var t=this.w.globals;if(t.isDataXYZ)for(var e=0;e<t.series.length;e++)if(void 0!==t.seriesZ[e])for(var i=0;i<t.seriesZ[e].length;i++)null!==t.seriesZ[e][i]&&K.isNumber(t.seriesZ[e][i])&&(t.maxZ=Math.max(t.maxZ,t.seriesZ[e][i]),t.minZ=Math.min(t.minZ,t.seriesZ[e][i]))}}]),xt),ut=(t(ft,[{key:"calculateTimeScaleTicks",value:function(t,e){var o=this,r=this.w;if(r.globals.allSeriesCollapsed)return r.globals.labels=[],r.globals.timelineLabels=[],[];var i=new y(this.ctx),s=(this.determineInterval(c=(e-t)/864e5),r.globals.disableZoomIn=!1,r.globals.disableZoomOut=!1,c<.005?r.globals.disableZoomIn=!0:5e4<c&&(r.globals.disableZoomOut=!0),i.getTimeUnitsfromTimestamp(t,e)),a=r.globals.gridWidth/c,n=a/24,l=n/60,h=Math.floor(24*c),i=Math.floor(24*c*60),t=Math.floor(c),e=Math.floor(c/30),c=Math.floor(c/365),d={firstVal:s={minMinute:s.minMinute,minHour:s.minHour,minDate:s.minDate,minMonth:s.minMonth,minYear:s.minYear},currentMinute:s.minMinute,currentHour:s.minHour,currentMonthDate:s.minDate,currentDate:s.minDate,currentMonth:s.minMonth,currentYear:s.minYear,daysWidthOnXAxis:a,hoursWidthOnXAxis:n,minutesWidthOnXAxis:l,numberOfMinutes:i,numberOfHours:h,numberOfDays:t,numberOfMonths:e,numberOfYears:c};switch(this.tickInterval){case"years":this.generateYearScale(d);break;case"months":case"half_year":this.generateMonthScale(d);break;case"months_days":case"months_fortnight":case"days":case"week_days":this.generateDayScale(d);break;case"hours":this.generateHourScale(d);break;case"minutes":this.generateMinuteScale(d)}var u=this.timeScaleArray.map(function(t){var e={position:t.position,unit:t.unit,year:t.year,day:t.day||1,hour:t.hour||0,month:t.month+1};return"month"===t.unit?Q({},e,{value:t.value+1}):"day"===t.unit||"hour"===t.unit?Q({},e,{value:t.value}):"minute"===t.unit?Q({},e,{value:t.value,minute:t.value}):t});return u.filter(function(t){var e=1,i=Math.ceil(r.globals.gridWidth/120),s=t.value,a=(void 0!==r.config.xaxis.tickAmount&&(i=r.config.xaxis.tickAmount),u.length>i&&(e=Math.floor(u.length/i)),!1),n=!1;switch(o.tickInterval){case"half_year":e=7,"year"===t.unit&&(a=!0);break;case"months":e=1,"year"===t.unit&&(a=!0);break;case"months_fortnight":e=15,"year"!==t.unit&&"month"!==t.unit||(a=!0),30===s&&(n=!0);break;case"months_days":e=10,"month"===t.unit&&(a=!0),30===s&&(n=!0);break;case"week_days":e=8,"month"===t.unit&&(a=!0);break;case"days":e=1,"month"===t.unit&&(a=!0);break;case"hours":"day"===t.unit&&(a=!0);break;case"minutes":s%5!=0&&(n=!0)}if("minutes"===o.tickInterval||"hours"===o.tickInterval){if(!n)return!0}else if((s%e==0||a)&&!n)return!0})}},{key:"recalcDimensionsBasedOnFormat",value:function(t,e){var i=this.w,t=this.formatDates(t),t=this.removeOverlappingTS(t);e?i.globals.invertedTimelineLabels=t.slice():i.globals.timelineLabels=t.slice(),new rt(this.ctx).plotCoords()}},{key:"determineInterval",value:function(t){switch(!0){case 1825<t:this.tickInterval="years";break;case 800<t&&t<=1825:this.tickInterval="half_year";break;case 180<t&&t<=800:this.tickInterval="months";break;case 90<t&&t<=180:this.tickInterval="months_fortnight";break;case 60<t&&t<=90:this.tickInterval="months_days";break;case 30<t&&t<=60:this.tickInterval="week_days";break;case 2<t&&t<=30:this.tickInterval="days";break;case.1<t&&t<=2:this.tickInterval="hours";break;case t<.1:this.tickInterval="minutes";break;default:this.tickInterval="days"}}},{key:"generateYearScale",value:function(t){var e=t.firstVal,i=t.currentMonth,s=t.currentYear,a=t.daysWidthOnXAxis,n=t.numberOfYears,o=e.minYear,r=0,l=new y(this.ctx);1<e.minDate&&0<e.minMonth?(t=l.determineRemainingDaysOfYear(e.minYear,e.minMonth,e.minDate),r=(l.determineDaysOfYear(e.minYear)-t+1)*a,o=e.minYear+1,this.timeScaleArray.push({position:r,value:o,unit:"year",year:o,month:K.monthMod(i+1)})):1===e.minDate&&0===e.minMonth&&this.timeScaleArray.push({position:r,value:o,unit:"year",year:s,month:K.monthMod(i+1)});for(var h=o,c=r,d=0;d<n;d++)c=l.determineDaysOfYear(++h-1)*a+c,this.timeScaleArray.push({position:c,value:h,unit:"year",year:h,month:1})}},{key:"generateMonthScale",value:function(t){var e=t.firstVal,i=t.currentMonthDate,s=t.currentMonth,a=t.currentYear,n=t.daysWidthOnXAxis,o=t.numberOfMonths,r=s,l=0,h=new y(this.ctx),c="month",d=0;1<e.minDate?(l=(h.determineDaysOfMonths(s+1,e.minYear)-i+1)*n,r=K.monthMod(s+1),t=a+d,e=K.monthMod(r),0===(i=r)&&(c="year",i=t,t+=d+=e=1),this.timeScaleArray.push({position:l,value:i,unit:c,year:t,month:e})):this.timeScaleArray.push({position:l,value:r,unit:c,year:a,month:K.monthMod(s)});for(var u=r+1,g=l,p=0;p<o;p++){0===(u=K.monthMod(u))?(c="year",d+=1):c="month";var f=a+Math.floor(u/12)+d,g=h.determineDaysOfMonths(u,f)*n+g;this.timeScaleArray.push({position:g,value:0===u?f:u,unit:c,year:f,month:0===u?1:u}),u++}}},{key:"generateDayScale",value:function(t){function e(t,e,i){return t>r.determineDaysOfMonths(e+1,i)&&(l="month",h=e+=c=1),e}var i=t.firstVal,s=t.currentMonth,a=t.currentYear,n=t.hoursWidthOnXAxis,o=t.numberOfDays,r=new y(this.ctx),l="day",t=(24-i.minHour)*n,h=i=i.minDate+1,c=i,d=e(c,s,a);this.timeScaleArray.push({position:t,value:h,unit:l,year:a,month:K.monthMod(d),day:c});for(var u=t,g=0;g<o;g++){var l="day",d=e(c+=1,d,a+Math.floor(d/12)+0),p=a+Math.floor(d/12)+0,u=24*n+u,f=1===c?K.monthMod(d):c;this.timeScaleArray.push({position:u,value:f,unit:l,year:p,month:K.monthMod(d),day:f})}}},{key:"generateHourScale",value:function(t){function e(t,e){return t>l.determineDaysOfMonths(e+1,n)?e+1:e}var i=t.firstVal,s=t.currentDate,a=t.currentMonth,n=t.currentYear,o=t.minutesWidthOnXAxis,r=t.numberOfHours,l=new y(this.ctx),h="hour",c=60-i.minMinute,d=c*o,u=(t=i.minHour+1)+1,g=(60==c&&(d=0,u=(t=i.minHour)+1),s),p=e(g,a);this.timeScaleArray.push({position:d,value:t,unit:h,day:g,hour:u,year:n,month:K.monthMod(p)});for(var f=d,x=0;x<r;x++){h="hour",24<=u&&(u=0,h="day",m=p,(g+=1)>l.determineDaysOfMonths(m+1,n)&&(m+=g=1),p=e(g,p=m));var b=n+Math.floor(p/12)+0,f=0===u&&0===x?c*o:60*o+f,m=0===u?g:u;this.timeScaleArray.push({position:f,value:m,unit:h,hour:u,day:g,year:b,month:K.monthMod(p)}),u++}}},{key:"generateMinuteScale",value:function(t){var e=t.firstVal,i=t.currentMinute,s=t.currentHour,a=t.currentDate,n=t.currentMonth,o=t.currentYear,r=t.minutesWidthOnXAxis,l=t.numberOfMinutes,i=r-(i-e.minMinute),h=(e=e.minMinute+1)+1,c=a,d=n,u=s;this.timeScaleArray.push({position:i,value:e,unit:"minute",day:c,hour:u,minute:h,year:o,month:K.monthMod(d)});for(var g=i,p=0;p<l;p++){60<=h&&(h=0,24===(u+=1))&&(u=0);var f=o+Math.floor(d/12)+0,g=r+g;this.timeScaleArray.push({position:g,value:h,unit:"minute",hour:u,minute:h,day:c,year:f,month:K.monthMod(d)}),h++}}},{key:"createRawDateString",value:function(t,e){var i=t.year;return i+="-"+("0"+t.month.toString()).slice(-2),"day"===t.unit?i+="day"===t.unit?"-"+("0"+e).slice(-2):"-01":i+="-"+("0"+(t.day||"1")).slice(-2),"hour"===t.unit?i+="hour"===t.unit?"T"+("0"+e).slice(-2):"T00":i+="T"+("0"+(t.hour||"0")).slice(-2),i+("minute"===t.unit?":"+("0"+e).slice(-2)+":00.000Z":":00:00.000Z")}},{key:"formatDates",value:function(t){var r=this,l=this.w;return t.map(function(t){var e,i,s=t.value.toString(),a=new y(r.ctx),n=r.createRawDateString(t,s),o=new Date(Date.parse(n)),s=void 0===l.config.xaxis.labels.format?(e="dd MMM",i=l.config.xaxis.labels.datetimeFormatter,"year"===t.unit&&(e=i.year),"month"===t.unit&&(e=i.month),"day"===t.unit&&(e=i.day),"hour"===t.unit&&(e=i.hour),"minute"===t.unit&&(e=i.minute),a.formatDate(o,e,!0,!1)):a.formatDate(o,l.config.xaxis.labels.format);return{dateString:n,position:t.position,value:s,unit:t.unit,year:t.year,month:t.month}})}},{key:"removeOverlappingTS",value:function(a){var n=this,o=new tt(this.ctx),r=0;return a.map(function(t,e){var i,s;return 0<e&&n.w.config.xaxis.labels.hideOverlappingLabels?(i=o.getTextRects(a[r].value).width,s=a[r].position,t.position>s+i+10?(r=e,t):null):t}).filter(function(t){return null!==t})}}]),ft),gt=(t(pt,[{key:"setupElements",value:function(){var t=this.w.globals,e=this.w.config,i=e.chart.type;t.axisCharts=-1<["line","area","bar","rangeBar","candlestick","radar","scatter","bubble","heatmap"].indexOf(i),t.xyCharts=-1<["line","area","bar","rangeBar","candlestick","scatter","bubble"].indexOf(i),t.isBarHorizontal=("bar"===e.chart.type||"rangeBar"===e.chart.type)&&e.plotOptions.bar.horizontal,t.chartClass=".apexcharts"+t.cuid,t.dom.baseEl=this.el,t.dom.elWrap=document.createElement("div"),tt.setAttrs(t.dom.elWrap,{id:t.chartClass.substring(1),class:"apexcharts-canvas "+t.chartClass.substring(1)}),this.el.appendChild(t.dom.elWrap),t.dom.Paper=new window.SVG.Doc(t.dom.elWrap),t.dom.Paper.attr({class:"apexcharts-svg","xmlns:data":"ApexChartsNS",transform:"translate(".concat(e.chart.offsetX,", ").concat(e.chart.offsetY,")")}),t.dom.Paper.node.style.background=e.chart.background,this.setSVGDimensions(),t.dom.elGraphical=t.dom.Paper.group().attr({class:"apexcharts-inner apexcharts-graphical"}),t.dom.elDefs=t.dom.Paper.defs(),t.dom.elLegendWrap=document.createElement("div"),t.dom.elLegendWrap.classList.add("apexcharts-legend"),t.dom.elWrap.appendChild(t.dom.elLegendWrap),t.dom.Paper.add(t.dom.elGraphical),t.dom.elGraphical.add(t.dom.elDefs)}},{key:"plotChartType",value:function(i,t){var s=this.w,e=s.config,a=s.globals,n={series:[],i:[]},o={series:[],i:[]},r={series:[],i:[]},l={series:[],i:[]},h={series:[],i:[]};a.series.map(function(t,e){void 0!==i[e].type?("column"===i[e].type||"bar"===i[e].type?(s.config.plotOptions.bar.horizontal=!1,l.series.push(t),l.i.push(e)):"area"===i[e].type?(o.series.push(t),o.i.push(e)):"line"===i[e].type?(n.series.push(t),n.i.push(e)):"scatter"===i[e].type?(r.series.push(t),r.i.push(e)):"bubble"!==i[e].type&&("candlestick"===i[e].type?(h.series.push(t),h.i.push(e)):console.warn("You have specified an unrecognized chart type. Available types for this propery are line/area/column/bar/scatter/bubble")),a.comboCharts=!0):(n.series.push(t),n.i.push(e))});var c,d=new ht(this.ctx,t),u=new G(this.ctx,t),g=new U(this.ctx),p=new Z(this.ctx),f=new $(this.ctx,t),x=new q(this.ctx),b=[];if(a.comboCharts)0<o.series.length&&b.push(d.draw(o.series,"area",o.i)),0<l.series.length&&(c=new(s.config.chart.stacked?V:P)(this.ctx,t),b.push(c.draw(l.series,l.i))),0<n.series.length&&b.push(d.draw(n.series,"line",n.i)),0<h.series.length&&b.push(u.draw(h.series,h.i)),0<r.series.length&&(u=new ht(this.ctx,t,!0),b.push(u.draw(r.series,"scatter",r.i)));else switch(e.chart.type){case"line":b=d.draw(a.series,"line");break;case"area":b=d.draw(a.series,"area");break;case"bar":b=new(e.chart.stacked?V:P)(this.ctx,t).draw(a.series);break;case"candlestick":b=new G(this.ctx,t).draw(a.series);break;case"rangeBar":b=f.draw(a.series);break;case"heatmap":b=new j(this.ctx,t).draw(a.series);break;case"pie":case"donut":b=g.draw(a.series);break;case"radialBar":b=p.draw(a.series);break;case"radar":b=x.draw(a.series);break;default:b=d.draw(a.series)}return b}},{key:"setSVGDimensions",value:function(){var t=this.w.globals,e=this.w.config,i=(t.svgWidth=e.chart.width,t.svgHeight=e.chart.height,K.getDimensions(this.el)),s=e.chart.width.toString().split(/[0-9]+/g).pop();"%"===s?K.isNumber(i[0])&&(0===i[0].width&&(i=K.getDimensions(this.el.parentNode)),t.svgWidth=i[0]*parseInt(e.chart.width)/100):"px"!==s&&""!==s||(t.svgWidth=parseInt(e.chart.width)),"auto"!==t.svgHeight&&""!==t.svgHeight?"%"===e.chart.height.toString().split(/[0-9]+/g).pop()?(s=K.getDimensions(this.el.parentNode),t.svgHeight=s[1]*parseInt(e.chart.height)/100):t.svgHeight=parseInt(e.chart.height):t.axisCharts?t.svgHeight=t.svgWidth/1.61:t.svgHeight=t.svgWidth,t.svgWidth<0&&(t.svgWidth=0),t.svgHeight<0&&(t.svgHeight=0),tt.setAttrs(t.dom.Paper.node,{width:t.svgWidth,height:t.svgHeight}),e=!e.chart.sparkline.enabled&&t.axisCharts?e.chart.parentHeightOffset:0,t.dom.Paper.node.parentNode.parentNode.style.minHeight=t.svgHeight+e+"px",t.dom.elWrap.style.width=t.svgWidth+"px",t.dom.elWrap.style.height=t.svgHeight+"px"}},{key:"shiftGraphPosition",value:function(){var t=this.w.globals,e=t.translateY,e={transform:"translate("+t.translateX+", "+e+")"};tt.setAttrs(t.dom.elGraphical.node,e)}},{key:"resizeNonAxisCharts",value:function(){var t=this.w,e=t.globals,i=0,s=("top"!==t.config.legend.position&&"bottom"!==t.config.legend.position||(i=new lt(this.ctx).getLegendBBox().clwh+10),t.globals.dom.baseEl.querySelector(".apexcharts-radialbar")),a=2*t.globals.radialSize;s&&-90!==t.config.plotOptions.radialBar.startAngle&&(a=K.getBoundingClientRect(s).height),i=Math.max(a,2*t.globals.radialSize)+e.translateY+i+20,e.dom.elLegendForeign&&e.dom.elLegendForeign.setAttribute("height",i),e.dom.elWrap.style.height=i+"px",tt.setAttrs(e.dom.Paper.node,{height:i}),e.dom.Paper.node.parentNode.parentNode.style.minHeight=i+"px"}},{key:"coreCalculations",value:function(){new dt(this.ctx).init()}},{key:"resetGlobals",value:function(){var t=this.w.globals;t.series=[],t.seriesCandleO=[],t.seriesCandleH=[],t.seriesCandleL=[],t.seriesCandleC=[],t.seriesRangeStart=[],t.seriesRangeEnd=[],t.seriesPercent=[],t.seriesX=[],t.seriesZ=[],t.seriesNames=[],t.seriesTotals=[],t.stackedSeriesTotals=[],t.labels=[],t.timelineLabels=[],t.noLabelsProvided=!1,t.timescaleTicks=[],t.resizeTimer=null,t.selectionResizeTimer=null,t.seriesXvalues=this.w.config.series.map(function(t){return[]}),t.seriesYvalues=this.w.config.series.map(function(t){return[]}),t.delayedElements=[],t.pointsArray=[],t.dataLabelsRects=[],t.isXNumeric=!1,t.isDataXYZ=!1,t.maxY=-Number.MAX_VALUE,t.minY=Number.MIN_VALUE,t.minYArr=[],t.maxYArr=[],t.maxX=-Number.MAX_VALUE,t.minX=Number.MAX_VALUE,t.initialmaxX=-Number.MAX_VALUE,t.initialminX=Number.MAX_VALUE,t.maxDate=0,t.minDate=Number.MAX_VALUE,t.minZ=Number.MAX_VALUE,t.maxZ=-Number.MAX_VALUE,t.minXDiff=Number.MAX_VALUE,t.yAxisScale=[],t.xAxisScale=null,t.xAxisTicksPositions=[],t.yLabelsCoords=[],t.yTitleCoords=[],t.xRange=0,t.yRange=[],t.zRange=0,t.dataPoints=0}},{key:"isMultipleY",value:function(){if(this.w.config.yaxis.constructor===Array&&1<this.w.config.yaxis.length)return this.w.globals.isMultipleYAxis=!0}},{key:"excludeCollapsedSeriesInYAxis",value:function(){var i=this,t=this.w;t.globals.ignoreYAxisIndexes=t.globals.collapsedSeries.map(function(t,e){if(i.w.globals.isMultipleYAxis)return t.index})}},{key:"isMultiFormat",value:function(){return this.isFormatXY()||this.isFormat2DArray()}},{key:"isFormatXY",value:function(){var t=this.w.config.series.slice(),e=new g(this.ctx);if(this.activeSeriesIndex=e.getActiveConfigSeriesIndex(),void 0!==t[this.activeSeriesIndex].data&&0<t[this.activeSeriesIndex].data.length&&null!==t[this.activeSeriesIndex].data[0]&&void 0!==t[this.activeSeriesIndex].data[0].x&&null!==t[this.activeSeriesIndex].data[0])return!0}},{key:"isFormat2DArray",value:function(){var t=this.w.config.series.slice(),e=new g(this.ctx);if(this.activeSeriesIndex=e.getActiveConfigSeriesIndex(),void 0!==t[this.activeSeriesIndex].data&&0<t[this.activeSeriesIndex].data.length&&void 0!==t[this.activeSeriesIndex].data[0]&&null!==t[this.activeSeriesIndex].data[0]&&t[this.activeSeriesIndex].data[0].constructor===Array)return!0}},{key:"handleFormat2DArray",value:function(t,e){for(var i,s=this.w.config,a=this.w.globals,n=0;n<t[e].data.length;n++)void 0!==t[e].data[n][1]&&(Array.isArray(t[e].data[n][1])&&4===t[e].data[n][1].length?this.twoDSeries.push(K.parseNumber(t[e].data[n][1][3])):this.twoDSeries.push(K.parseNumber(t[e].data[n][1])),a.dataFormatXNumeric=!0),"datetime"===s.xaxis.type?(i=new Date(t[e].data[n][0]),i=new Date(i).getTime(),this.twoDSeriesX.push(i)):this.twoDSeriesX.push(t[e].data[n][0]);for(var o=0;o<t[e].data.length;o++)void 0!==t[e].data[o][2]&&(this.threeDSeries.push(t[e].data[o][2]),a.isDataXYZ=!0)}},{key:"handleFormatXY",value:function(t,e){var i=this.w.config,s=this.w.globals,a=new y(this.ctx),n=e;-1<s.collapsedSeriesIndices.indexOf(e)&&(n=this.activeSeriesIndex);for(var o=0;o<t[e].data.length;o++)void 0!==t[e].data[o].y&&(Array.isArray(t[e].data[o].y)?this.twoDSeries.push(K.parseNumber(t[e].data[o].y[t[e].data[o].y.length-1])):this.twoDSeries.push(K.parseNumber(t[e].data[o].y)));for(var r=0;r<t[n].data.length;r++){var l="string"==typeof t[n].data[r].x,h=!!a.isValidDate(t[n].data[r].x.toString());l||h?l?"datetime"!==i.xaxis.type||s.isRangeData?(this.fallbackToCategory=!0,this.twoDSeriesX.push(t[n].data[r].x)):this.twoDSeriesX.push(a.parseDate(t[n].data[r].x)):"datetime"===i.xaxis.type?this.twoDSeriesX.push(a.parseDate(t[n].data[r].x.toString())):(s.dataFormatXNumeric=!0,s.isXNumeric=!0,this.twoDSeriesX.push(parseFloat(t[n].data[r].x))):(s.isXNumeric=!0,s.dataFormatXNumeric=!0,this.twoDSeriesX.push(t[n].data[r].x))}if(t[e].data[0]&&void 0!==t[e].data[0].z){for(var c=0;c<t[e].data.length;c++)this.threeDSeries.push(t[e].data[c].z);s.isDataXYZ=!0}}},{key:"handleRangeData",value:function(t,e){var i=this.w.globals,s={};return this.isFormat2DArray()?s=this.handleRangeDataFormat("array",t,e):this.isFormatXY()&&(s=this.handleRangeDataFormat("xy",t,e)),i.seriesRangeStart.push(s.start),i.seriesRangeEnd.push(s.end),s}},{key:"handleCandleStickData",value:function(t,e){var i=this.w.globals,s={};return this.isFormat2DArray()?s=this.handleCandleStickDataFormat("array",t,e):this.isFormatXY()&&(s=this.handleCandleStickDataFormat("xy",t,e)),i.seriesCandleO.push(s.o),i.seriesCandleH.push(s.h),i.seriesCandleL.push(s.l),i.seriesCandleC.push(s.c),s}},{key:"handleRangeDataFormat",value:function(t,e,i){var s=[],a=[],n="Please provide [Start, End] values in valid format. Read more https://apexcharts.com/docs/series/#rangecharts",o=new g(this.ctx).getActiveConfigSeriesIndex();if("array"===t){if(2!==e[o].data[0][1].length)throw new Error(n);for(var r=0;r<e[i].data.length;r++)s.push(e[i].data[r][1][0]),a.push(e[i].data[r][1][1])}else if("xy"===t){if(2!==e[o].data[0].y.length)throw new Error(n);for(var l=0;l<e[i].data.length;l++)s.push(e[i].data[l].y[0]),a.push(e[i].data[l].y[1])}return{start:s,end:a}}},{key:"handleCandleStickDataFormat",value:function(t,e,i){var s=[],a=[],n=[],o=[],r="Please provide [Open, High, Low and Close] values in valid format. Read more https://apexcharts.com/docs/series/#candlestick";if("array"===t){if(4!==e[i].data[0][1].length)throw new Error(r);for(var l=0;l<e[i].data.length;l++)s.push(e[i].data[l][1][0]),a.push(e[i].data[l][1][1]),n.push(e[i].data[l][1][2]),o.push(e[i].data[l][1][3])}else if("xy"===t){if(4!==e[i].data[0].y.length)throw new Error(r);for(var h=0;h<e[i].data.length;h++)s.push(e[i].data[h].y[0]),a.push(e[i].data[h].y[1]),n.push(e[i].data[h].y[2]),o.push(e[i].data[h].y[3])}return{o:s,h:a,l:n,c:o}}},{key:"parseDataAxisCharts",value:function(t){for(var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:this.ctx,i=this.w.config,s=this.w.globals,a=new y(e),n=0;n<t.length;n++){if(this.twoDSeries=[],this.twoDSeriesX=[],this.threeDSeries=[],void 0===t[n].data)return void console.error("It is a possibility that you may have not included 'data' property in series.");if("rangeBar"!==i.chart.type&&"rangeArea"!==i.chart.type&&"rangeBar"!==t[n].type&&"rangeArea"!==t[n].type||(s.isRangeData=!0,this.handleRangeData(t,n)),this.isMultiFormat())this.isFormat2DArray()?this.handleFormat2DArray(t,n):this.isFormatXY()&&this.handleFormatXY(t,n),"candlestick"!==i.chart.type&&"candlestick"!==t[n].type||this.handleCandleStickData(t,n),s.series.push(this.twoDSeries),s.labels.push(this.twoDSeriesX),s.seriesX.push(this.twoDSeriesX),this.fallbackToCategory||(s.isXNumeric=!0);else{if("datetime"===i.xaxis.type){s.isXNumeric=!0;for(var o=(0<i.labels.length?i.labels:i.xaxis.categories).slice(),r=0;r<o.length;r++)if("string"==typeof o[r]){if(!a.isValidDate(o[r]))throw new Error("You have provided invalid Date format. Please provide a valid JavaScript Date");this.twoDSeriesX.push(a.parseDate(o[r]))}else{if(13!==String(o[r]).length)throw new Error("Please provide a valid JavaScript timestamp");this.twoDSeriesX.push(o[r])}s.seriesX.push(this.twoDSeriesX)}else"numeric"===i.xaxis.type&&(s.isXNumeric=!0,0<(l=(0<i.labels.length?i.labels:i.xaxis.categories).slice()).length)&&(this.twoDSeriesX=l,s.seriesX.push(this.twoDSeriesX));s.labels.push(this.twoDSeriesX);var l=t[n].data.map(function(t){return K.parseNumber(t)});s.series.push(l)}s.seriesZ.push(this.threeDSeries),void 0!==t[n].name?s.seriesNames.push(t[n].name):s.seriesNames.push("series-"+parseInt(n+1))}return this.w}},{key:"parseDataNonAxisCharts",value:function(t){var e=this.w.globals,i=this.w.config;e.series=t.slice(),e.seriesNames=i.labels.slice();for(var s=0;s<e.series.length;s++)void 0===e.seriesNames[s]&&e.seriesNames.push("series-"+(s+1));return this.w}},{key:"handleExternalLabelsData",value:function(t){var e=this.w.config,i=this.w.globals;if(0<e.xaxis.categories.length)i.labels=e.xaxis.categories;else if(0<e.labels.length)i.labels=e.labels.slice();else if(this.fallbackToCategory)i.labels=i.labels[0];else{var s=[];if(i.axisCharts){if(0<i.series.length)for(var a=0;a<i.series[i.maxValsInArrayIndex].length;a++)s.push(a+1);for(var n=0;n<t.length;n++)i.seriesX.push(s);i.isXNumeric=!0}if(0===s.length)for(var s=[0,10],o=0;o<t.length;o++)i.seriesX.push(s);i.labels=s,i.noLabelsProvided=!0}}},{key:"parseData",value:function(t){var e=this.w,i=e.config,s=e.globals;this.excludeCollapsedSeriesInYAxis(),this.fallbackToCategory=!1,this.resetGlobals(),this.isMultipleY(),s.axisCharts?this.parseDataAxisCharts(t):this.parseDataNonAxisCharts(t),this.coreUtils.getLargestSeries(),"bar"===i.chart.type&&i.chart.stacked&&(e=new g(this.ctx),s.series=e.setNullSeriesToZeroValues(s.series)),this.coreUtils.getSeriesTotals(),s.axisCharts&&this.coreUtils.getStackedSeriesTotals(),this.coreUtils.getPercentSeries(),s.dataFormatXNumeric||s.isXNumeric&&("numeric"!==i.xaxis.type||0!==i.labels.length||0!==i.xaxis.categories.length)||this.handleExternalLabelsData(t)}},{key:"xySettings",value:function(){var t,e,i=null,s=this.w;return s.globals.axisCharts&&("back"===s.config.xaxis.crosshairs.position&&new _(this.ctx).drawXCrosshairs(),"back"===s.config.yaxis[0].crosshairs.position&&new _(this.ctx).drawYCrosshairs(),i=this.coreUtils.getCalculatedRatios(),"datetime"===s.config.xaxis.type)&&void 0===s.config.xaxis.labels.formatter&&(e=new ut(this.ctx),isFinite(s.globals.minX)&&isFinite(s.globals.maxX)&&!s.globals.isBarHorizontal?(t=e.calculateTimeScaleTicks(s.globals.minX,s.globals.maxX),e.recalcDimensionsBasedOnFormat(t,!1)):s.globals.isBarHorizontal&&(t=e.calculateTimeScaleTicks(s.globals.minY,s.globals.maxY),e.recalcDimensionsBasedOnFormat(t,!0))),i}},{key:"drawAxis",value:function(t,e){var i,s,a=this.w.globals,n=this.w.config,o=new X(this.ctx),r=new ot(this.ctx);a.axisCharts&&"radar"!==t&&(a.isBarHorizontal?(s=r.drawYaxisInversed(0),i=o.drawXaxisInversed(0),a.dom.elGraphical.add(i),a.dom.elGraphical.add(s)):(i=o.drawXaxis(),a.dom.elGraphical.add(i),n.yaxis.map(function(t,e){-1===a.ignoreYAxisIndexes.indexOf(e)&&(s=r.drawYaxis(e),a.dom.Paper.add(s))}))),n.yaxis.map(function(t,e){-1===a.ignoreYAxisIndexes.indexOf(e)&&r.yAxisTitleRotate(e,t.opposite)})}},{key:"setupBrushHandler",value:function(){var e,s=this,a=this.w;a.config.chart.brush.enabled&&"function"!=typeof a.config.chart.events.selection&&((e=a.config.chart.brush.targets||[a.config.chart.brush.target]).forEach(function(t){var e=ApexCharts.getChartByID(t);function i(){s.ctx._updateOptions({chart:{selection:{xaxis:{min:e.w.globals.minX,max:e.w.globals.maxX}}}},!1,!1)}e.w.globals.brushSource=s.ctx,"function"!=typeof e.w.config.chart.events.zoomed&&(e.w.config.chart.events.zoomed=function(){i()}),"function"!=typeof e.w.config.chart.events.scrolled&&(e.w.config.chart.events.scrolled=function(){i()})}),a.config.chart.events.selection=function(t,i){e.forEach(function(t){var e=ApexCharts.getChartByID(t),t=K.clone(a.config.yaxis);a.config.chart.brush.autoScaleYaxis&&(t=new ct(e).autoScaleY(e,t,i)),e._updateOptions({xaxis:{min:i.xaxis.min,max:i.xaxis.max},yaxis:t},!1,!1,!1,!1)})})}}]),pt);function pt(t,e){s(this,pt),this.ctx=e,this.w=e.w,this.el=t,this.coreUtils=new et(this.ctx),this.twoDSeries=[],this.threeDSeries=[],this.twoDSeriesX=[]}function ft(t){s(this,ft),this.ctx=t,this.w=t.w,this.timeScaleArray=[]}function xt(t){s(this,xt),this.ctx=t,this.w=t.w,this.scales=new ct(t)}function bt(t){s(this,bt),this.ctx=t,this.w=t.w}function mt(t){s(this,mt),this.ctx=t,this.w=t.w;var e=this.w;this.xaxisLabels=e.globals.labels.slice(),0<e.globals.timelineLabels.length&&(this.xaxisLabels=e.globals.timelineLabels.slice()),this.drawnLabels=[],"top"===e.config.xaxis.position?this.offY=0:this.offY=e.globals.gridHeight+1,this.offY=this.offY+e.config.xaxis.axisBorder.offsetY,this.xaxisFontSize=e.config.xaxis.labels.style.fontSize,this.xaxisFontFamily=e.config.xaxis.labels.style.fontFamily,this.xaxisForeColors=e.config.xaxis.labels.style.colors,this.xaxisBorderWidth=e.config.xaxis.axisBorder.width,-1<this.xaxisBorderWidth.indexOf("%")?this.xaxisBorderWidth=e.globals.gridWidth*parseInt(this.xaxisBorderWidth)/100:this.xaxisBorderWidth=parseInt(this.xaxisBorderWidth),this.xaxisBorderHeight=e.config.xaxis.axisBorder.height,this.yaxis=e.config.yaxis[0],this.axesUtils=new nt(t)}function yt(t,e,i){s(this,yt),this.ctx=t,this.w=t.w,this.xyRatios=e,this.pointsChart=!("bubble"!==this.w.config.chart.type&&"scatter"!==this.w.config.chart.type)||i,this.scatter=new B(this.ctx),this.noNegatives=this.w.globals.minX===Number.MAX_VALUE,this.yaxisIndex=0}function vt(t,e){s(this,vt),this.ctx=t,this.w=t.w,this.onLegendClick=this.onLegendClick.bind(this),this.onLegendHovered=this.onLegendHovered.bind(this)}function wt(t){s(this,wt),this.ctx=t,this.w=t.w}function kt(t){s(this,kt),this.ctx=t,this.w=t.w,this.lgRect={},this.yAxisWidth=0,this.xAxisHeight=0,this.isSparkline=this.w.config.chart.sparkline.enabled,this.xPadRight=0,this.xPadLeft=0}function At(t){s(this,At),this.ctx=t,this.w=t.w;var e=this.w;this.xaxisFontSize=e.config.xaxis.labels.style.fontSize,this.axisFontFamily=e.config.xaxis.labels.style.fontFamily,this.xaxisForeColors=e.config.xaxis.labels.style.colors,this.xAxisoffX=0,"bottom"===e.config.xaxis.position&&(this.xAxisoffX=e.globals.gridHeight),this.drawnLabels=[],this.axesUtils=new nt(t)}function St(t){s(this,St),this.ctx=t,this.w=t.w}function Ct(t){s(this,Ct),this.ctx=t,this.w=t.w,this.tooltipKeyFormat="dd MMM"}function n(){return s(this,n),a(this,i(n).apply(this,arguments))}function o(t){var e;return s(this,o),(e=a(this,i(o).call(this,t))).ctx=t,e.w=t.w,e.animBeginArr=[0],e.animDur=0,t=e.w,e.startAngle=t.config.plotOptions.radialBar.startAngle,e.endAngle=t.config.plotOptions.radialBar.endAngle,e.trackStartAngle=t.config.plotOptions.radialBar.track.startAngle,e.trackEndAngle=t.config.plotOptions.radialBar.track.endAngle,e.radialDataLabels=t.config.plotOptions.radialBar.dataLabels,e.trackStartAngle||(e.trackStartAngle=e.startAngle),e.trackEndAngle||(e.trackEndAngle=e.endAngle),360===e.endAngle&&(e.endAngle=359.99),e.fullAngle=360-t.config.plotOptions.radialBar.endAngle-t.config.plotOptions.radialBar.startAngle,e.margin=parseInt(t.config.plotOptions.radialBar.track.margin),e}function Lt(t){s(this,Lt),this.ctx=t,this.w=t.w,this.chartType=this.w.config.chart.type,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled,this.animDur=0;var e=this.w;this.graphics=new tt(this.ctx),this.lineColorArr=(void 0!==e.globals.stroke.colors?e.globals.stroke:e.globals).colors,this.defaultSize=e.globals.svgHeight<e.globals.svgWidth?e.globals.svgHeight-35:e.globals.gridWidth,this.maxValue=this.w.globals.maxY,this.polygons=e.config.plotOptions.radar.polygons,this.maxLabelWidth=20,t=e.globals.labels.slice().sort(function(t,e){return e.length-t.length})[0],t=this.graphics.getTextRects(t,e.config.dataLabels.style.fontSize),this.size=this.defaultSize/2.1-e.config.stroke.width-e.config.chart.dropShadow.blur-t.width/1.75,void 0!==e.config.plotOptions.radar.size&&(this.size=e.config.plotOptions.radar.size),this.dataRadiusOfPercent=[],this.dataRadius=[],this.angleArr=[],this.yaxisLabelsTextsPos=[]}function zt(t){s(this,zt),this.ctx=t,this.w=t.w,this.chartType=this.w.config.chart.type,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled,this.animBeginArr=[0],this.animDur=0,this.donutDataLabels=this.w.config.plotOptions.pie.donut.labels,t=this.w,this.lineColorArr=(void 0!==t.globals.stroke.colors?t.globals.stroke:t.globals).colors,this.defaultSize=t.globals.svgHeight<t.globals.svgWidth?t.globals.svgHeight-35:t.globals.gridWidth,this.centerY=this.defaultSize/2,this.centerX=t.globals.gridWidth/2,this.fullAngle=360,this.donutSize=0,this.sliceLabels=[],this.prevSectorAngleArr=[]}function Pt(t,e){s(this,Pt),this.ctx=t,this.w=t.w,this.xRatio=e.xRatio,this.yRatio=e.yRatio,this.negRange=!1,this.dynamicAnim=this.w.config.chart.animations.dynamicAnimation,this.rectRadius=this.w.config.plotOptions.heatmap.radius,this.strokeWidth=this.w.config.stroke.width}function Et(t){s(this,Et),this.ctx=t,this.w=t.w}function r(){return s(this,r),a(this,i(r).apply(this,arguments))}function h(){return s(this,h),a(this,i(h).apply(this,arguments))}function Mt(t,e){s(this,Mt),this.ctx=t,this.w=t.w,t=this.w,this.barOptions=t.config.plotOptions.bar,this.isHorizontal=this.barOptions.horizontal,this.strokeWidth=t.config.stroke.width,this.isNullValue=!1,this.xyRatios=e,null!==this.xyRatios&&(this.xRatio=e.xRatio,this.yRatio=e.yRatio,this.invertedXRatio=e.invertedXRatio,this.invertedYRatio=e.invertedYRatio,this.baseLineY=e.baseLineY,this.baseLineInvertedY=e.baseLineInvertedY),this.yaxisIndex=0,this.seriesLen=0}function Tt(t){s(this,Tt),this.ctx=t,this.w=t.w}function Xt(t){s(this,Xt),this.ctx=t,this.w=t.w,this.initialAnim=this.w.config.chart.animations.enabled,this.dynamicAnim=this.initialAnim&&this.w.config.chart.animations.dynamicAnimation.enabled,this.radiusSizes=[]}function It(t,e){s(this,It),this.ctx=t,this.w=t.w}function Yt(t){s(this,Yt),this.ctx=t,this.w=t.w,this.opts=null,this.seriesIndex=0}function Ft(t){s(this,Ft),this.opts=t}function Rt(){s(this,Rt)}function Dt(t){s(this,Dt),this.opts=t}function Nt(t){s(this,Nt),this.ctx=t,this.w=t.w}function Ot(t){s(this,Ot),this.opts=t}function Ht(t){s(this,Ht),this.ctx=t,this.w=t.w,this.months31=[1,3,5,7,8,10,12],this.months30=[2,4,6,9,11],this.daysCntOfYear=[0,31,59,90,120,151,181,212,243,273,304,334]}function Wt(t){s(this,Wt),this.ctx=t,this.w=t.w,this.graphics=new tt(this.ctx),this.w.globals.isBarHorizontal&&(this.invertAxis=!0),this.xDivision=this.w.globals.gridWidth/this.w.globals.dataPoints}function Bt(){s(this,Bt),this.yAxis={show:!0,showAlways:!1,seriesName:void 0,opposite:!1,reversed:!1,logarithmic:!1,tickAmount:void 0,forceNiceScale:!1,max:void 0,min:void 0,floating:!1,decimalsInFloat:void 0,labels:{show:!0,minWidth:0,maxWidth:160,offsetX:0,offsetY:0,align:void 0,rotate:0,padding:20,style:{colors:[],fontSize:"11px",fontFamily:void 0,cssClass:""},formatter:void 0},axisBorder:{show:!1,color:"#78909C",offsetX:0,offsetY:0},axisTicks:{show:!1,color:"#78909C",width:6,offsetX:0,offsetY:0},title:{text:void 0,rotate:90,offsetY:0,offsetX:0,style:{color:void 0,fontSize:"11px",fontFamily:void 0,cssClass:""}},tooltip:{enabled:!1,offsetX:0},crosshairs:{show:!0,position:"front",stroke:{color:"#b6b6b6",width:1,dashArray:0}}},this.xAxisAnnotation={x:0,x2:null,strokeDashArray:1,fillColor:"#c2c2c2",borderColor:"#c2c2c2",opacity:.3,offsetX:0,offsetY:0,label:{borderColor:"#c2c2c2",borderWidth:1,text:void 0,textAnchor:"middle",orientation:"vertical",position:"top",offsetX:0,offsetY:0,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,cssClass:"",padding:{left:5,right:5,top:2,bottom:2}}}},this.yAxisAnnotation={y:0,y2:null,strokeDashArray:1,fillColor:"#c2c2c2",borderColor:"#c2c2c2",opacity:.3,offsetX:0,offsetY:0,yAxisIndex:0,label:{borderColor:"#c2c2c2",borderWidth:1,text:void 0,textAnchor:"end",position:"right",offsetX:0,offsetY:-3,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,cssClass:"",padding:{left:5,right:5,top:0,bottom:2}}}},this.pointAnnotation={x:0,y:null,yAxisIndex:0,seriesIndex:0,marker:{size:4,fillColor:"#fff",strokeWidth:2,strokeColor:"#333",shape:"circle",offsetX:0,offsetY:0,radius:2,cssClass:""},label:{borderColor:"#c2c2c2",borderWidth:1,text:void 0,textAnchor:"middle",offsetX:0,offsetY:-15,style:{background:"#fff",color:void 0,fontSize:"11px",fontFamily:void 0,cssClass:"",padding:{left:5,right:5,top:0,bottom:2}}},customSVG:{SVG:void 0,cssClass:void 0,offsetX:0,offsetY:0}}}var Vt=setTimeout;function Gt(){}function c(t){if(!(this instanceof c))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],Zt(t,this)}function _t(i,s){for(;3===i._state;)i=i._value;0!==i._state?(i._handled=!0,c._immediateFn(function(){var t,e=1===i._state?s.onFulfilled:s.onRejected;if(null!==e){try{t=e(i._value)}catch(t){return void p(s.promise,t)}jt(s.promise,t)}else(1===i._state?jt:p)(s.promise,i._value)})):i._deferreds.push(s)}function jt(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var i=e.then;if(e instanceof c)return t._state=3,t._value=e,Ut(t);if("function"==typeof i)return Zt((s=i,a=e,function(){s.apply(a,arguments)}),t)}t._state=1,t._value=e,Ut(t)}catch(e){p(t,e)}var s,a}function p(t,e){t._state=2,t._value=e,Ut(t)}function Ut(t){2===t._state&&0===t._deferreds.length&&c._immediateFn(function(){t._handled||c._unhandledRejectionFn(t._value)});for(var e=0,i=t._deferreds.length;e<i;e++)_t(t,t._deferreds[e]);t._deferreds=null}function qt(t,e,i){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=i}function Zt(t,e){var i=!1;try{t(function(t){i||(i=!0,jt(e,t))},function(t){i||(i=!0,p(e,t))})}catch(t){i||(i=!0,p(e,t))}}c.prototype.catch=function(t){return this.then(null,t)},c.prototype.then=function(t,e){var i=new this.constructor(Gt);return _t(this,new qt(t,e,i)),i},c.prototype.finally=function(e){var i=this.constructor;return this.then(function(t){return i.resolve(e()).then(function(){return t})},function(t){return i.resolve(e()).then(function(){return i.reject(t)})})},c.all=function(e){return new c(function(a,n){if(!e||void 0===e.length)throw new TypeError("Promise.all accepts an array");var o=Array.prototype.slice.call(e);if(0===o.length)return a([]);for(var r=o.length,t=0;t<o.length;t++)!function e(i,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var s=t.then;if("function"==typeof s)return s.call(t,function(t){e(i,t)},n),0}o[i]=t,0==--r&&a(o)}catch(i){n(i)}}(t,o[t])})},c.resolve=function(e){return e&&"object"==typeof e&&e.constructor===c?e:new c(function(t){t(e)})},c.reject=function(i){return new c(function(t,e){e(i)})},c.race=function(a){return new c(function(t,e){for(var i=0,s=a.length;i<s;i++)a[i].then(t,e)})},c._immediateFn="function"==typeof setImmediate?function(t){setImmediate(t)}:function(t){Vt(t,0)},c._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)};t(Ae,[{key:"getSvgString",value:function(){return this.w.globals.dom.Paper.svg()}},{key:"cleanup",value:function(){var t=(e=this.w).globals.dom.baseEl.querySelector(".apexcharts-xcrosshairs"),e=e.globals.dom.baseEl.querySelector(".apexcharts-ycrosshairs");t&&t.setAttribute("x",-500),e&&(e.setAttribute("y1",-100),e.setAttribute("y2",-100))}},{key:"svgUrl",value:function(){this.cleanup();var t=this.getSvgString(),t=new Blob([t],{type:"image/svg+xml;charset=utf-8"});return URL.createObjectURL(t)}},{key:"dataURI",value:function(){var r=this;return new c(function(e){var t=r.w,i=(r.cleanup(),document.createElement("canvas")),t=(i.width=t.globals.svgWidth,i.height=t.globals.svgHeight,"transparent"===t.config.chart.background?"#fff":t.config.chart.background),s=i.getContext("2d"),a=(s.fillStyle=t,s.fillRect(0,0,i.width,i.height),window.URL||window.webkitURL||window),n=new Image,t=(n.crossOrigin="anonymous",r.getSvgString()),o="data:image/svg+xml,"+encodeURIComponent(t);n.onload=function(){s.drawImage(n,0,0),a.revokeObjectURL(o);var t=i.toDataURL("image/png");e(t)},n.src=o})}},{key:"exportToSVG",value:function(){this.triggerDownload(this.svgUrl(),".svg")}},{key:"exportToPng",value:function(){var e=this;this.dataURI().then(function(t){e.triggerDownload(t,".png")})}},{key:"triggerDownload",value:function(t,e){var i=document.createElement("a");i.href=t,i.download=this.w.globals.chartID+e,document.body.appendChild(i),i.click(),document.body.removeChild(i)}}]);var f,x,$t,b,m,Jt=Ae,Qt=(t(ke,[{key:"drawGridArea",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,e=this.w,i=new tt(this.ctx),s=(null===t&&(t=i.group({class:"apexcharts-grid"})),i.drawLine(e.globals.padHorizontal,1,e.globals.padHorizontal,e.globals.gridHeight,"transparent")),e=i.drawLine(e.globals.padHorizontal,e.globals.gridHeight,e.globals.gridWidth,e.globals.gridHeight,"transparent");return t.add(e),t.add(s),t}},{key:"drawGrid",value:function(){var t=this.w,e=new X(this.ctx),i=new ot(this.ctx),s=this.w.globals,a=null;s.axisCharts&&(t.config.grid.show?(a=this.renderGrid(),s.dom.elGraphical.add(a.el),this.drawGridArea(a.el)):(t=this.drawGridArea(),s.dom.elGraphical.add(t)),null!==a&&e.xAxisLabelCorrections(a.xAxisTickWidth),i.setYAxisTextAlignments())}},{key:"createGridMask",value:function(){var e,t=this.w,i=t.globals,s=new tt(this.ctx),a=Array.isArray(t.config.stroke.width)?0:t.config.stroke.width;Array.isArray(t.config.stroke.width)&&(e=0,t.config.stroke.width.forEach(function(t){e=Math.max(e,t)}),a=e),i.dom.elGridRectMask=document.createElementNS(i.SVGNS,"clipPath"),i.dom.elGridRectMask.setAttribute("id","gridRectMask".concat(i.cuid)),i.dom.elGridRectMarkerMask=document.createElementNS(i.SVGNS,"clipPath"),i.dom.elGridRectMarkerMask.setAttribute("id","gridRectMarkerMask".concat(i.cuid)),i.dom.elGridRect=s.drawRect(-a/2,-a/2,i.gridWidth+a,i.gridHeight+a,0,"#fff"),new et(this).getLargestMarkerSize(),t=t.globals.markers.largestSize+1,i.dom.elGridRectMarker=s.drawRect(-t,-t,i.gridWidth+2*t,i.gridHeight+2*t,0,"#fff"),i.dom.elGridRectMask.appendChild(i.dom.elGridRect.node),i.dom.elGridRectMarkerMask.appendChild(i.dom.elGridRectMarker.node),(t=i.dom.baseEl.querySelector("defs")).appendChild(i.dom.elGridRectMask),t.appendChild(i.dom.elGridRectMarkerMask)}},{key:"renderGrid",value:function(){var t=this.w,e=new tt(this.ctx),i=t.config.grid.strokeDashArray,s=e.group({class:"apexcharts-grid"}),a=e.group({class:"apexcharts-gridlines-horizontal"}),n=e.group({class:"apexcharts-gridlines-vertical"});s.add(a),s.add(n);for(var o,r,l=8,h=0;h<t.globals.series.length&&!(2<(l=void 0!==t.globals.yAxisScale[h]?t.globals.yAxisScale[h].result.length-1:l));h++);if(t.globals.isBarHorizontal){if(o=l,t.config.grid.xaxis.lines.show||t.config.xaxis.axisTicks.show)for(var c,d=t.globals.padHorizontal,u=t.globals.gridHeight,g=0;g<o+1&&(c=d=d+t.globals.gridWidth/o+.3,g!==o-1);g++)t.config.grid.xaxis.lines.show&&((r=e.drawLine(d,0,c,u,t.config.grid.borderColor,i)).node.classList.add("apexcharts-gridline"),n.add(r),this.animX)&&this.animateLine(r,{x1:0,x2:0},{x1:d,x2:c}),new X(this.ctx).drawXaxisTicks(d,s);if(t.config.grid.yaxis.lines.show)for(var p=0,f=0,x=t.globals.gridWidth,b=0;b<t.globals.dataPoints+1;b++){var m=e.drawLine(0,p,x,f,t.config.grid.borderColor,i);a.add(m),m.node.classList.add("apexcharts-gridline"),this.animY&&this.animateLine(m,{y1:p+20,y2:f+20},{y1:p,y2:f}),f=p+=t.globals.gridHeight/t.globals.dataPoints}}else{if(o=this.xaxisLabels.length,t.config.grid.xaxis.lines.show||t.config.xaxis.axisTicks.show){var y=t.globals.padHorizontal,v=t.globals.gridHeight;if(0<t.globals.timelineLabels.length)for(var w=0;w<o;w++){var y=this.xaxisLabels[w].position,k=this.xaxisLabels[w].position,A=(t.config.grid.xaxis.lines.show&&0<y&&y<t.globals.gridWidth&&((A=e.drawLine(y,0,k,v,t.config.grid.borderColor,i)).node.classList.add("apexcharts-gridline"),n.add(A),this.animX)&&this.animateLine(A,{x1:0,x2:0},{x1:y,x2:k}),new X(this.ctx));w===o-1&&t.globals.skipLastTimelinelabel||A.drawXaxisTicks(y,s)}else for(var S=o,C=0;C<S;C++){var L=S;if(t.globals.isXNumeric&&"bar"!==t.config.chart.type&&--L,k=y+=t.globals.gridWidth/L,C===L-1)break;t.config.grid.xaxis.lines.show&&((L=e.drawLine(y,0,k,v,t.config.grid.borderColor,i)).node.classList.add("apexcharts-gridline"),n.add(L),this.animX)&&this.animateLine(L,{x1:0,x2:0},{x1:y,x2:k}),new X(this.ctx).drawXaxisTicks(y,s)}}if(t.config.grid.yaxis.lines.show)for(var z=0,P=0,E=t.globals.gridWidth,M=0;M<l+1;M++){var T=e.drawLine(0,z,E,P,t.config.grid.borderColor,i);a.add(T),T.node.classList.add("apexcharts-gridline"),this.animY&&this.animateLine(T,{y1:z+20,y2:P+20},{y1:z,y2:P}),P=z+=t.globals.gridHeight/l}}return this.drawGridBands(s,o,l),{el:s,xAxisTickWidth:t.globals.gridWidth/o}}},{key:"drawGridBands",value:function(t,e,i){var s=this.w,a=new tt(this.ctx);if(void 0!==s.config.grid.row.colors&&0<s.config.grid.row.colors.length)for(var n=0,o=s.globals.gridHeight/i,r=s.globals.gridWidth,l=0,h=0;l<i;l++,h++){h>=s.config.grid.row.colors.length&&(h=0);var c=s.config.grid.row.colors[h],c=a.drawRect(0,n,r,o,0,c,s.config.grid.row.opacity);t.add(c),c.node.classList.add("apexcharts-gridRow"),n+=s.globals.gridHeight/i}if(void 0!==s.config.grid.column.colors&&0<s.config.grid.column.colors.length)for(var d=s.globals.padHorizontal,u=s.globals.padHorizontal+s.globals.gridWidth/e,g=s.globals.gridHeight,p=0,f=0;p<e;p++,f++){f>=s.config.grid.column.colors.length&&(f=0);var x=s.config.grid.column.colors[f];(x=a.drawRect(d,0,u,g,0,x,s.config.grid.column.opacity)).node.classList.add("apexcharts-gridColumn"),t.add(x),d+=s.globals.gridWidth/e}}},{key:"animateLine",value:function(t,e,i){var s=this.w,a=s.config.chart.animations;!a||s.globals.resized||s.globals.dataChanged||(a=a.speed,this.anim.animateLine(t,e,i,a))}}]),ke),Kt=(t(we,[{key:"checkResponsiveConfig",value:function(t){var a,n,e,o=this,r=this.w,i=r.config;0!==i.responsive.length&&((a=i.responsive.slice()).sort(function(t,e){return t.breakpoint>e.breakpoint?1:e.breakpoint>t.breakpoint?-1:0}).reverse(),n=new l({}),e=function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=a[0].breakpoint,i=0<window.innerWidth?window.innerWidth:screen.width;if(e<i)e=et.extendArrayProps(n,r.globals.initialConfig),t=K.extend(e,t),t=K.extend(r.config,t),o.overrideResponsiveOptions(t);else for(var s=0;s<a.length;s++)i<a[s].breakpoint&&(t=et.extendArrayProps(n,a[s].options),t=K.extend(r.config,t),o.overrideResponsiveOptions(t))},t?(i=et.extendArrayProps(n,t),i=K.extend(r.config,i),e(i=K.extend(i,t))):e({}))}},{key:"overrideResponsiveOptions",value:function(t){t=new l(t).init(),this.w.config=t}}]),we),te=(t(ve,[{key:"init",value:function(){this.setDefaultColors()}},{key:"setDefaultColors",value:function(){var t=this.w,e=new K;if(t.globals.dom.elWrap.classList.add(t.config.theme.mode),void 0===t.config.colors?t.globals.colors=this.predefined():t.globals.colors=t.config.colors,t.config.theme.monochrome.enabled){var i=[],s=t.globals.series.length;t.config.plotOptions.bar.distributed&&"bar"===t.config.chart.type&&(s=t.globals.series[0].length*t.globals.series.length);for(var a=t.config.theme.monochrome.color,n=1/(s/t.config.theme.monochrome.shadeIntensity),o=t.config.theme.monochrome.shadeTo,r=0,l=0;l<s;l++){var h="dark"===o?e.shadeColor(-1*r,a):e.shadeColor(r,a);r+=n,i.push(h)}t.globals.colors=i.slice()}var c=t.globals.colors.slice();this.pushExtraColors(t.globals.colors),void 0===t.config.stroke.colors?t.globals.stroke.colors=c:t.globals.stroke.colors=t.config.stroke.colors,this.pushExtraColors(t.globals.stroke.colors),void 0===t.config.fill.colors?t.globals.fill.colors=c:t.globals.fill.colors=t.config.fill.colors,this.pushExtraColors(t.globals.fill.colors),void 0===t.config.dataLabels.style.colors?t.globals.dataLabels.style.colors=c:t.globals.dataLabels.style.colors=t.config.dataLabels.style.colors,this.pushExtraColors(t.globals.dataLabels.style.colors,50),void 0===t.config.plotOptions.radar.polygons.fill.colors?t.globals.radarPolygons.fill.colors=["dark"===t.config.theme.mode?"#202D48":"#fff"]:t.globals.radarPolygons.fill.colors=t.config.plotOptions.radar.polygons.fill.colors,this.pushExtraColors(t.globals.radarPolygons.fill.colors,20),void 0===t.config.markers.colors?t.globals.markers.colors=c:t.globals.markers.colors=t.config.markers.colors,this.pushExtraColors(t.globals.markers.colors)}},{key:"pushExtraColors",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,s=this.w,e=e||s.globals.series.length;if((null===i?"bar"===s.config.chart.type&&s.config.plotOptions.bar.distributed||"heatmap"===s.config.chart.type&&s.config.plotOptions.heatmap.colorScale.inverse:i)&&(e=s.globals.series[0].length*s.globals.series.length),t.length<e)for(var a=e-t.length,n=0;n<a;n++)t.push(t[n])}},{key:"updateThemeOptions",value:function(t){t.chart=t.chart||{},t.tooltip=t.tooltip||{};var e=t.theme.mode||"light",i=t.theme.palette||("dark"===e?"palette4":"palette1"),s=t.chart.foreColor||("dark"===e?"#f6f7f8":"#373d3f");return t.tooltip.theme=e,t.chart.foreColor=s,t.theme.palette=i,t}},{key:"predefined",value:function(){switch(this.w.config.theme.palette){case"palette1":this.colors=["#008FFB","#00E396","#FEB019","#FF4560","#775DD0"];break;case"palette2":this.colors=["#3f51b5","#03a9f4","#4caf50","#f9ce1d","#FF9800"];break;case"palette3":this.colors=["#33b2df","#546E7A","#d4526e","#13d8aa","#A5978B"];break;case"palette4":this.colors=["#4ecdc4","#c7f464","#81D4FA","#fd6a6a","#546E7A"];break;case"palette5":this.colors=["#2b908f","#f9a3a4","#90ee7e","#fa4443","#69d2e7"];break;case"palette6":this.colors=["#449DD1","#F86624","#EA3546","#662E9B","#C5D86D"];break;case"palette7":this.colors=["#D7263D","#1B998B","#2E294E","#F46036","#E2C044"];break;case"palette8":this.colors=["#662E9B","#F86624","#F9C80E","#EA3546","#43BCCD"];break;case"palette9":this.colors=["#5C4742","#A5978B","#8D5B4C","#5A2A27","#C4BBAF"];break;case"palette10":this.colors=["#A300D6","#7D02EB","#5653FE","#2983FF","#00B1F2"];break;default:this.colors=["#008FFB","#00E396","#FEB019","#FF4560","#775DD0"]}return this.colors}}]),ve),ee=(t(ye,[{key:"getNearestValues",value:function(t){var e=t.hoverArea,i=t.elGrid,s=t.clientX,a=t.clientY,n=t.hasBars,o=this.w,r=o.globals.gridWidth,t=r/(o.globals.dataPoints-1),i=i.getBoundingClientRect();(n&&o.globals.comboCharts||n)&&(t=r/o.globals.dataPoints),s-=i.left,a-=i.top,s<0||a<0||s>o.globals.gridWidth||a>o.globals.gridHeight?(e.classList.remove("hovering-zoom"),e.classList.remove("hovering-pan")):o.globals.zoomEnabled?(e.classList.remove("hovering-pan"),e.classList.add("hovering-zoom")):o.globals.panEnabled&&(e.classList.remove("hovering-zoom"),e.classList.add("hovering-pan")),i=Math.round(s/t),n&&(i=Math.ceil(s/t),--i);for(var e=null,l=[],h=0;h<o.globals.seriesXvalues.length;h++)l.push([o.globals.seriesXvalues[h][0]-1e-6].concat(o.globals.seriesXvalues[h]));return l=l.map(function(t){return t.filter(function(t){return t})}),t=o.globals.seriesYvalues.map(function(t){return t.filter(function(t){return t})}),o.globals.isXNumeric&&(e=(n=this.closestInMultiArray(s,a,l,t)).index,i=n.j,null!==e)&&(l=o.globals.seriesXvalues[e],i=this.closestInArray(s,l).index),{capturedSeries:e,j:i=!i||i<1?0:i,hoverX:s,hoverY:a}}},{key:"closestInMultiArray",value:function(a,n,o,r){var t=this.w,e=0,l=null,h=-1,t=(1<t.globals.series.length?e=this.getFirstActiveXArray(o):l=0,r[e][0]),e=o[e][0],e=Math.abs(a-e),c=Math.abs(n-t)+e;return r.map(function(t,s){t.map(function(t,e){var i=Math.abs(n-r[s][e]);(i=Math.abs(a-o[s][e])+i)<c&&(c=i,l=s,h=e)})}),{index:l,j:h}}},{key:"getFirstActiveXArray",value:function(t){for(var e=0,i=new et(this.ctx),s=t.map(function(t,e){return 0<t.length?e:-1}),a=0;a<s.length;a++){var n=i.getSeriesTotalByIndex(a);if(-1!==s[a]&&0!==n&&!i.seriesHaveSameValues(a)){e=s[a];break}}return e}},{key:"closestInArray",value:function(t,e){for(var i=e[0],s=null,a=Math.abs(t-i),n=0;n<e.length;n++){var o=Math.abs(t-e[n]);o<a&&(a=o,e[n],s=n)}return{index:s}}},{key:"isXoverlap",value:function(t){var e=[],i=this.w.globals.seriesX.filter(function(t){return void 0!==t[0]});if(0<i.length)for(var s=0;s<i.length-1;s++)void 0!==i[s][t]&&void 0!==i[s+1][t]&&i[s][t]!==i[s+1][t]&&e.push("unEqual");return 0===e.length}},{key:"isinitialSeriesSameLen",value:function(){for(var t=!0,e=this.w.globals.initialSeries,i=0;i<e.length-1;i++)if(e[i].data.length!==e[i+1].data.length){t=!1;break}return t}},{key:"getBarsHeight",value:function(t){return u(t).reduce(function(t,e){return t+e.getBBox().height},0)}},{key:"toggleAllTooltipSeriesGroups",value:function(t){var e=this.w,i=this.ttCtx;0===i.allTooltipSeriesGroups.length&&(i.allTooltipSeriesGroups=e.globals.dom.baseEl.querySelectorAll(".apexcharts-tooltip-series-group"));for(var s=i.allTooltipSeriesGroups,a=0;a<s.length;a++)"enable"===t?(s[a].classList.add("active"),s[a].style.display=e.config.tooltip.items.display):(s[a].classList.remove("active"),s[a].style.display="none")}}]),ye),ie=(t(me,[{key:"drawSeriesTexts",value:function(t){var e=void 0===(a=t.shared)||a,i=t.ttItems,s=t.i,a=void 0===s?0:s,t=void 0===(s=t.j)?null:s;void 0!==this.w.config.tooltip.custom?this.handleCustomTooltip({i:a,j:t}):this.toggleActiveInactiveSeries(e),s=this.getValuesToPrint({i:a,j:t}),this.printLabels({i:a,j:t,values:s,ttItems:i,shared:e}),e=this.ttCtx.getElTooltip(),this.ttCtx.tooltipRect.ttWidth=e.getBoundingClientRect().width,this.ttCtx.tooltipRect.ttHeight=e.getBoundingClientRect().height}},{key:"printLabels",value:function(t){var e=t.i,i=t.j,s=t.values,a=t.ttItems,n=t.shared,o=this.w,r=s.xVal,l=s.zVal,h=s.xAxisTTVal,c="",d=o.globals.colors[e];null!==i&&o.config.plotOptions.bar.distributed&&(d=o.globals.colors[i]);for(var u=0,g=o.globals.series.length-1;u<o.globals.series.length;u++,g--){var p,f,x=this.getFormatters(e),c=this.getSeriesName({fn:x.yLbTitleFormatter,index:e,seriesIndex:e,j:i});n?(p=o.config.tooltip.inverseOrder?g:u,x=this.getFormatters(p),c=this.getSeriesName({fn:x.yLbTitleFormatter,index:p,seriesIndex:e,j:i}),d=o.globals.colors[p],f=x.yLbFormatter(o.globals.series[p][i],{series:o.globals.series,seriesIndex:p,dataPointIndex:i,w:o}),(this.ttCtx.hasBars()&&o.config.chart.stacked&&0===o.globals.series[p][i]||void 0===o.globals.series[p][i])&&(f=void 0)):f=x.yLbFormatter(o.globals.series[e][i],{series:o.globals.series,seriesIndex:e,dataPointIndex:i,w:o}),null===i&&(f=x.yLbFormatter(o.globals.series[e],o)),this.DOMHandling({t:u,ttItems:a,values:{val:f,xVal:r,xAxisTTVal:h,zVal:l},seriesName:c,shared:n,pColor:d})}}},{key:"getFormatters",value:function(t){var e,i=this.w,s=i.globals.yLabelFormatters[t];return void 0!==i.globals.ttVal?Array.isArray(i.globals.ttVal)?(s=i.globals.ttVal[t]&&i.globals.ttVal[t].formatter,e=i.globals.ttVal[t]&&i.globals.ttVal[t].title&&i.globals.ttVal[t].title.formatter):(s=i.globals.ttVal.formatter,"function"==typeof i.globals.ttVal.title.formatter&&(e=i.globals.ttVal.title.formatter)):e=i.config.tooltip.y.title.formatter,{yLbFormatter:s="function"!=typeof s?i.globals.yLabelFormatters[0]||function(t){return t}:s,yLbTitleFormatter:e="function"!=typeof e?function(t){return t}:e}}},{key:"getSeriesName",value:function(t){var e=t.fn,i=t.index,s=t.seriesIndex,a=t.j,t=this.w;return e(String(t.globals.seriesNames[i]),{series:t.globals.series,seriesIndex:s,dataPointIndex:a,w:t})}},{key:"DOMHandling",value:function(t){var e=t.t,i=t.ttItems,s=t.values,a=t.seriesName,n=t.shared,o=t.pColor,r=this.w,l=this.ttCtx,h=s.val,c=s.xVal,d=s.xAxisTTVal,t=s.zVal,s=null,s=i[e].children;r.config.tooltip.fillSeriesColor&&(i[e].style.backgroundColor=o,s[0].style.display="none"),l.showTooltipTitle&&(null===l.tooltipTitle&&(l.tooltipTitle=r.globals.dom.baseEl.querySelector(".apexcharts-tooltip-title")),l.tooltipTitle.innerHTML=c),l.blxaxisTooltip&&(l.xaxisTooltipText.innerHTML=""!==d?d:c),(c=i[e].querySelector(".apexcharts-tooltip-text-label"))&&(c.innerHTML=a?a+": ":""),(a=i[e].querySelector(".apexcharts-tooltip-text-value"))&&(a.innerHTML=h),s[0]&&s[0].classList.contains("apexcharts-tooltip-marker")&&(s[0].style.backgroundColor=o),r.config.tooltip.marker.show||(s[0].style.display="none"),null!==t&&(i[e].querySelector(".apexcharts-tooltip-text-z-label").innerHTML=r.config.tooltip.z.title,i[e].querySelector(".apexcharts-tooltip-text-z-value").innerHTML=t),n&&s[0]&&(null==h||-1<r.globals.collapsedSeriesIndices.indexOf(e)?s[0].parentNode.style.display="none":s[0].parentNode.style.display=r.config.tooltip.items.display)}},{key:"toggleActiveInactiveSeries",value:function(t){var e=this.w;t?this.tooltipUtil.toggleAllTooltipSeriesGroups("enable"):(this.tooltipUtil.toggleAllTooltipSeriesGroups("disable"),(t=e.globals.dom.baseEl.querySelector(".apexcharts-tooltip-series-group"))&&(t.classList.add("active"),t.style.display=e.config.tooltip.items.display))}},{key:"getValuesToPrint",value:function(t){var e=t.i,i=t.j,s=this.w,a=this.ctx.series.filteredSeriesX(),n="",o=null,r=null,l={series:s.globals.series,seriesIndex:e,dataPointIndex:i,w:s},t=s.globals.ttZFormatter;return null===i?r=s.globals.series[e]:s.globals.isXNumeric?(n=a[e][i],0===a[e].length&&(n=a[this.tooltipUtil.getFirstActiveXArray(a)][i])):n=void 0!==s.globals.labels[i]?s.globals.labels[i]:"",a=n,s.globals.isXNumeric&&"datetime"===s.config.xaxis.type?n=new J(this.ctx).xLabelFormat(s.globals.ttKeyFormatter,a,a):s.globals.isBarHorizontal||(n=s.globals.xLabelFormatter(a,l)),void 0!==s.config.tooltip.x.formatter&&(n=s.globals.ttKeyFormatter(a,l)),0<s.globals.seriesZ.length&&0<s.globals.seriesZ[0].length&&(o=t(s.globals.seriesZ[e][i],s)),{val:r,xVal:n,xAxisTTVal:"function"==typeof s.config.xaxis.tooltip.formatter?s.globals.xaxisTooltipFormatter(a,l):n,zVal:o}}},{key:"handleCustomTooltip",value:function(t){var e=t.i,i=t.j,t=this.w;this.ttCtx.getElTooltip().innerHTML=t.config.tooltip.custom({ctx:this.ctx,series:t.globals.series,seriesIndex:e,dataPointIndex:i,w:t})}}]),me),se=(t(be,[{key:"moveXCrosshairs",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null,i=this.ttCtx,s=this.w,a=i.getElXCrosshairs(),n=t-i.xcrosshairsWidth/2,t=s.globals.labels.slice().length;null!==e&&(n=s.globals.gridWidth/t*e),"tickWidth"===s.config.xaxis.crosshairs.width||"barWidth"===s.config.xaxis.crosshairs.width?n+i.xcrosshairsWidth>s.globals.gridWidth&&(n=s.globals.gridWidth-i.xcrosshairsWidth):null!==e&&(n+=s.globals.gridWidth/t/2),(n=n<0?0:n)>s.globals.gridWidth&&(n=s.globals.gridWidth),null!==a&&(a.setAttribute("x",n),a.setAttribute("x1",n),a.setAttribute("x2",n),a.setAttribute("y2",s.globals.gridHeight),a.classList.add("active")),i.blxaxisTooltip&&(a=n,"tickWidth"!==s.config.xaxis.crosshairs.width&&"barWidth"!==s.config.xaxis.crosshairs.width||(a=n+i.xcrosshairsWidth/2),this.moveXAxisTooltip(a))}},{key:"moveYCrosshairs",value:function(t){var e=this.ttCtx;null!==e.ycrosshairs&&(tt.setAttrs(e.ycrosshairs,{y1:t,y2:t}),tt.setAttrs(e.ycrosshairsHidden,{y1:t,y2:t}))}},{key:"moveXAxisTooltip",value:function(t){var e,i=this.w,s=this.ttCtx;null!==s.xaxisTooltip&&(s.xaxisTooltip.classList.add("active"),e=s.xaxisOffY+i.config.xaxis.tooltip.offsetY+i.globals.translateY+1+i.config.xaxis.offsetY,t-=s.xaxisTooltip.getBoundingClientRect().width/2,isNaN(t)||(t+=i.globals.translateX,i=new tt(this.ctx).getTextRects(s.xaxisTooltipText.innerHTML),s.xaxisTooltipText.style.minWidth=i.width+"px",s.xaxisTooltip.style.left=t+"px",s.xaxisTooltip.style.top=e+"px"))}},{key:"moveYAxisTooltip",value:function(t){var e=this.w,i=this.ttCtx,s=(null===i.yaxisTTEls&&(i.yaxisTTEls=e.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxistooltip")),parseInt(i.ycrosshairsHidden.getAttribute("y1"))),a=e.globals.translateY+s,n=i.yaxisTTEls[t].getBoundingClientRect().height,s=e.globals.translateYAxisX[t]-2;e.config.yaxis[t].opposite&&(s-=26),a-=n/2,-1===e.globals.ignoreYAxisIndexes.indexOf(t)?(i.yaxisTTEls[t].classList.add("active"),i.yaxisTTEls[t].style.top=a+"px",i.yaxisTTEls[t].style.left=s+e.config.yaxis[t].tooltip.offsetX+"px"):i.yaxisTTEls[t].classList.remove("active")}},{key:"moveTooltip",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,s=this.w,a=this.ttCtx,n=a.getElTooltip(),o=a.tooltipRect,i=null!==i?parseInt(i):1,t=parseInt(t)+i+5,e=parseInt(e)+i/2;(t=(t=t>s.globals.gridWidth/2?t-o.ttWidth-i-15:t)>s.globals.gridWidth-o.ttWidth-10?s.globals.gridWidth-o.ttWidth:t)<-20&&(t=-20),s.config.tooltip.followCursor&&(i=a.getElGrid().getBoundingClientRect(),e=a.e.clientY+s.globals.translateY-i.top-o.ttHeight/2),t=(o=this.positionChecks(o,t,e)).x,e=o.y,isNaN(t)||(t+=s.globals.translateX,n.style.left=t+"px",n.style.top=e+"px")}},{key:"positionChecks",value:function(t,e,i){var s=this.w;return{x:e,y:i=(i=t.ttHeight+i>s.globals.gridHeight?s.globals.gridHeight-t.ttHeight+s.globals.translateY:i)<0?0:i}}},{key:"moveMarkers",value:function(t,e){var i=this.w,s=this.ttCtx;if(0<i.globals.markers.size[t])for(var a=i.globals.dom.baseEl.querySelectorAll(" .apexcharts-series[data\\:realIndex='".concat(t,"'] .apexcharts-marker")),n=0;n<a.length;n++)parseInt(a[n].getAttribute("rel"))===e&&(s.marker.resetPointsSize(),s.marker.enlargeCurrentPoint(e,a[n]));else s.marker.resetPointsSize(),this.moveDynamicPointOnHover(e,t)}},{key:"moveDynamicPointOnHover",value:function(t,e){var i=this.w,s=this.ttCtx,a=i.globals.pointsArray,n=i.config.markers.hover.size,o=(void 0===n&&(n=i.globals.markers.size[e]+i.config.markers.hover.sizeOffset),a[e][t][0]),t=a[e][t][1]||0;(e=i.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(e,"'] .apexcharts-series-markers circle")))&&(e.setAttribute("r",n),e.setAttribute("cx",o),e.setAttribute("cy",t)),this.moveXCrosshairs(o),s.fixedTooltip||this.moveTooltip(o,t,n)}},{key:"moveDynamicPointsOnHover",value:function(t){var e=this.ttCtx,i=e.w,s=0,a=0,n=i.globals.pointsArray,o=new g(this.ctx).getActiveSeriesIndex(),r=i.config.markers.hover.size,l=(void 0===r&&(r=i.globals.markers.size[o]+i.config.markers.hover.sizeOffset),n[o]&&(s=n[o][t][0],a=n[o][t][1]),null);if(null!==(l=null!==(o=e.getAllMarkers())?o:i.globals.dom.baseEl.querySelectorAll(".apexcharts-series-markers circle")))for(var h=0;h<l.length;h++){var c,d=n[h];d&&d.length&&(c=n[h][t][1],l[h].setAttribute("cx",s),d=parseInt(l[h].parentNode.parentNode.parentNode.getAttribute("data:realIndex")),null!==c?(l[d]&&l[d].setAttribute("r",r),l[d]&&l[d].setAttribute("cy",c)):l[d]&&l[d].setAttribute("r",0))}this.moveXCrosshairs(s),e.fixedTooltip||(i=a||i.globals.gridHeight,this.moveTooltip(s,i,r))}},{key:"moveStickyTooltipOverBars",value:function(t){var e=this.w,i=this.ttCtx,s=(a=e.globals.dom.baseEl.querySelector(".apexcharts-bar-series .apexcharts-series[rel='1'] path[j='".concat(t,"'], .apexcharts-candlestick-series .apexcharts-series[rel='1'] path[j='").concat(t,"'], .apexcharts-rangebar-series .apexcharts-series[rel='1'] path[j='").concat(t,"']")))?parseFloat(a.getAttribute("cx")):0,a=a?parseFloat(a.getAttribute("barWidth")):0;e.globals.isXNumeric?s-=a/2:(s=i.xAxisTicksPositions[t-1]+i.dataPointsDividedWidth/2,isNaN(s)&&(s=i.xAxisTicksPositions[t]-i.dataPointsDividedWidth/2)),t=i.getElGrid().getBoundingClientRect(),t=i.e.clientY-t.top-i.tooltipRect.ttHeight/2,this.moveXCrosshairs(s),i.fixedTooltip||(e=t||e.globals.gridHeight,this.moveTooltip(s,e))}}]),be),ae=(t(xe,[{key:"drawDynamicPoints",value:function(){for(var t=this.w,e=new tt(this.ctx),i=new st(this.ctx),s=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series"),a=0;a<s.length;a++){var n,o,r=parseInt(s[a].getAttribute("data:realIndex")),l=t.globals.dom.baseEl.querySelector(".apexcharts-series[data\\:realIndex='".concat(r,"'] .apexcharts-series-markers-wrap"));null!==l&&(n=void 0,o="apexcharts-marker w".concat((Math.random()+1).toString(36).substring(4)),"line"!==t.config.chart.type&&"area"!==t.config.chart.type||t.globals.comboCharts||t.config.tooltip.intersect||(o+=" no-pointer-events"),r=i.getMarkerConfig(o,r),(n=e.drawMarker(0,0,r)).node.setAttribute("default-marker-size",0),(r=document.createElementNS(t.globals.SVGNS,"g")).classList.add("apexcharts-series-markers"),r.appendChild(n.node),l.appendChild(r))}}},{key:"enlargeCurrentPoint",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null,s=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null,a=this.w;"bubble"!==a.config.chart.type&&this.newPointSize(t,e),t=e.getAttribute("cx"),e=e.getAttribute("cy"),null!==i&&null!==s&&(t=i,e=s),this.tooltipPosition.moveXCrosshairs(t),this.fixedTooltip||("radar"===a.config.chart.type&&(s=this.ttCtx.getElGrid().getBoundingClientRect(),t=this.ttCtx.e.clientX-s.left),this.tooltipPosition.moveTooltip(t,e,a.config.markers.hover.size))}},{key:"enlargePoints",value:function(t){for(var e=this.w,i=this.ttCtx,s=t,a=e.globals.dom.baseEl.querySelectorAll(".apexcharts-series:not(.apexcharts-series-collapsed) .apexcharts-marker"),n=e.config.markers.hover.size,o=0;o<a.length;o++){var r=a[o].getAttribute("rel"),l=a[o].getAttribute("index");void 0===n&&(n=e.globals.markers.size[l]+e.config.markers.hover.sizeOffset),s===parseInt(r)?(this.newPointSize(s,a[o]),l=a[o].getAttribute("cx"),r=a[o].getAttribute("cy"),this.tooltipPosition.moveXCrosshairs(l),i.fixedTooltip||this.tooltipPosition.moveTooltip(l,r,n)):this.oldPointSize(a[o])}}},{key:"newPointSize",value:function(t,e){var i=this.w,s=i.config.markers.hover.size,a=null,a=0===t?e.parentNode.firstChild:e.parentNode.lastChild,e=parseInt(a.getAttribute("index"));void 0===s&&(s=i.globals.markers.size[e]+i.config.markers.hover.sizeOffset),a.setAttribute("r",s)}},{key:"oldPointSize",value:function(t){var e=parseInt(t.getAttribute("default-marker-size"));t.setAttribute("r",e)}},{key:"resetPointsSize",value:function(){for(var t=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series:not(.apexcharts-series-collapsed) .apexcharts-marker"),e=0;e<t.length;e++){var i=parseInt(t[e].getAttribute("default-marker-size"));K.isNumber(i)?t[e].setAttribute("r",i):t[e].setAttribute("r",0)}}}]),xe),ne=(t(fe,[{key:"getAttr",value:function(t,e){return parseFloat(t.target.getAttribute(e))}},{key:"handleHeatTooltip",value:function(t){var e,i,s,a,n=t.e,o=t.opt,r=t.x,l=t.y,h=this.ttCtx,c=this.w;return n.target.classList.contains("apexcharts-heatmap-rect")&&(e=this.getAttr(n,"i"),i=this.getAttr(n,"j"),s=this.getAttr(n,"cx"),a=this.getAttr(n,"cy"),t=this.getAttr(n,"width"),n=this.getAttr(n,"height"),h.tooltipLabels.drawSeriesTexts({ttItems:o.ttItems,i:e,j:i,shared:!1}),r=s+h.tooltipRect.ttWidth/2+t,l=a+h.tooltipRect.ttHeight/2-n/2,h.tooltipPosition.moveXCrosshairs(s+t/2),r>c.globals.gridWidth/2&&(r=s-h.tooltipRect.ttWidth/2+t),h.w.config.tooltip.followCursor)&&(t=h.getElGrid().getBoundingClientRect(),l=h.e.clientY-t.top+c.globals.translateY/2-10),{x:r,y:l}}},{key:"handleMarkerTooltip",value:function(t){var e,i,s,a,n,o=t.e,r=t.opt,l=t.x,h=t.y,c=this.w,d=this.ttCtx;return o.target.classList.contains("apexcharts-marker")&&(n=parseInt(r.paths.getAttribute("cx")),e=parseInt(r.paths.getAttribute("cy")),i=parseFloat(r.paths.getAttribute("val")),a=parseInt(r.paths.getAttribute("rel")),t=parseInt(r.paths.parentNode.parentNode.parentNode.getAttribute("rel"))-1,d.intersect&&(s=K.findAncestor(r.paths,"apexcharts-series"))&&(t=parseInt(s.getAttribute("data:realIndex"))),d.tooltipLabels.drawSeriesTexts({ttItems:r.ttItems,i:t,j:a,shared:!d.showOnIntersect&&c.config.tooltip.shared}),"mouseup"===o.type&&d.markerClick(o,t,a),l=n,h=e+c.globals.translateY-1.4*d.tooltipRect.ttHeight,d.w.config.tooltip.followCursor&&(n=d.getElGrid().getBoundingClientRect(),h=d.e.clientY+c.globals.translateY-n.top),d.marker.enlargeCurrentPoint(a,r.paths,l,h=i<0?e:h)),{x:l,y:h}}},{key:"handleBarTooltip",value:function(t){var e,i=t.e,s=t.opt,a=this.w,n=this.ttCtx,o=n.getElTooltip(),r=0,l=0,h=0,c=this.getBarTooltipXY({e:i,opt:s}),d=c.i,t=c.barHeight,i=c.j;a.globals.isBarHorizontal&&n.hasBars()||!a.config.tooltip.shared?(l=c.x,h=c.y,e=Array.isArray(a.config.stroke.width)?a.config.stroke.width[d]:a.config.stroke.width,r=l):a.globals.comboCharts||a.config.tooltip.shared||(r/=2),isNaN(h)&&(h=a.globals.svgHeight-n.tooltipRect.ttHeight),l+n.tooltipRect.ttWidth>a.globals.gridWidth?l-=n.tooltipRect.ttWidth:l<0&&(l+=n.tooltipRect.ttWidth),n.w.config.tooltip.followCursor&&(c=n.getElGrid().getBoundingClientRect(),h=n.e.clientY-c.top),null===n.tooltip&&(n.tooltip=a.globals.dom.baseEl.querySelector(".apexcharts-tooltip")),a.config.tooltip.shared||(a.globals.comboChartsHasBars?n.tooltipPosition.moveXCrosshairs(r+e/2):n.tooltipPosition.moveXCrosshairs(r)),!n.fixedTooltip&&(!a.config.tooltip.shared||a.globals.isBarHorizontal&&n.hasBars())&&(o.style.left=l+a.globals.translateX+"px",s=parseInt(s.paths.parentNode.getAttribute("data:realIndex")),!(a.globals.isMultipleYAxis?a.config.yaxis[s]&&a.config.yaxis[s].reversed:a.config.yaxis[0].reversed)||a.globals.isBarHorizontal&&n.hasBars()||(h=h+t-2*(a.globals.series[d][i]<0?t:0)),n.tooltipRect.ttHeight+h>a.globals.gridHeight?(h=a.globals.gridHeight-n.tooltipRect.ttHeight+a.globals.translateY,o.style.top=h+"px"):o.style.top=h+a.globals.translateY-n.tooltipRect.ttHeight/2+"px")}},{key:"getBarTooltipXY",value:function(t){var e,i,s,a,n,o=t.e,r=t.opt,l=this.w,h=null,c=this.ttCtx,d=0,u=0,g=0,p=0,f=0,x=o.target.classList;return(x.contains("apexcharts-bar-area")||x.contains("apexcharts-candlestick-area")||x.contains("apexcharts-rangebar-area"))&&(n=(e=o.target).getBoundingClientRect(),i=r.elGrid.getBoundingClientRect(),s=n.height,f=n.height,a=n.width,t=parseInt(e.getAttribute("cx")),x=parseInt(e.getAttribute("cy")),p=parseFloat(e.getAttribute("barWidth")),n=("touchmove"===o.type?o.touches[0]:o).clientX,h=parseInt(e.getAttribute("j")),d=parseInt(e.parentNode.getAttribute("rel"))-1,l.globals.comboCharts&&(d=parseInt(e.parentNode.getAttribute("data:realIndex"))),c.tooltipLabels.drawSeriesTexts({ttItems:r.ttItems,i:d,j:h,shared:!c.showOnIntersect&&l.config.tooltip.shared}),g=l.config.tooltip.followCursor?l.globals.isBarHorizontal?(u=n-i.left+15,x-c.dataPointsDividedHeight+s/2-c.tooltipRect.ttHeight/2):(u=l.globals.isXNumeric?t-a/2:t-c.dataPointsDividedWidth+a/2,o.clientY-i.top-c.tooltipRect.ttHeight/2-15):l.globals.isBarHorizontal?((u=t)<c.xyRatios.baseLineInvertedY&&(u=t-c.tooltipRect.ttWidth),x-c.dataPointsDividedHeight+s/2-c.tooltipRect.ttHeight/2):(u=l.globals.isXNumeric?t-a/2:t-c.dataPointsDividedWidth+a/2,x)),{x:u,y:g,barHeight:f,barWidth:p,i:d,j:h}}}]),fe),oe=(t(pe,[{key:"drawXaxisTooltip",value:function(){var t=this.w,e=this.ttCtx,i="bottom"===t.config.xaxis.position,s=(e.xaxisOffY=i?t.globals.gridHeight+1:1,i?"apexcharts-xaxistooltip apexcharts-xaxistooltip-bottom":"apexcharts-xaxistooltip apexcharts-xaxistooltip-top"),i=t.globals.dom.elWrap;e.blxaxisTooltip&&null===t.globals.dom.baseEl.querySelector(".apexcharts-xaxistooltip")&&(e.xaxisTooltip=document.createElement("div"),e.xaxisTooltip.setAttribute("class",s+" "+t.config.tooltip.theme),i.appendChild(e.xaxisTooltip),e.xaxisTooltipText=document.createElement("div"),e.xaxisTooltipText.classList.add("apexcharts-xaxistooltip-text"),e.xaxisTooltipText.style.fontFamily=t.config.xaxis.tooltip.style.fontFamily||t.config.chart.fontFamily,e.xaxisTooltipText.style.fontSize=t.config.xaxis.tooltip.style.fontSize,e.xaxisTooltip.appendChild(e.xaxisTooltipText))}},{key:"drawYaxisTooltip",value:function(){for(var a=this.w,e=this.ttCtx,t=0;t<a.config.yaxis.length;t++)!function(i){var t=a.config.yaxis[i].opposite||a.config.yaxis[i].crosshairs.opposite,s=(e.yaxisOffX=t?a.globals.gridWidth+1:1,"apexcharts-yaxistooltip apexcharts-yaxistooltip-".concat(i,t?" apexcharts-yaxistooltip-right":" apexcharts-yaxistooltip-left"));a.globals.yAxisSameScaleIndices.map(function(t,e){t.map(function(t,e){e===i&&(s+=a.config.yaxis[e].show?" ":" apexcharts-yaxistooltip-hidden")})}),t=a.globals.dom.elWrap,e.blyaxisTooltip&&null===a.globals.dom.baseEl.querySelector(".apexcharts-yaxistooltip apexcharts-yaxistooltip-".concat(i))&&(e.yaxisTooltip=document.createElement("div"),e.yaxisTooltip.setAttribute("class",s+" "+a.config.tooltip.theme),t.appendChild(e.yaxisTooltip),0===i&&(e.yaxisTooltipText=[]),e.yaxisTooltipText.push(document.createElement("div")),e.yaxisTooltipText[i].classList.add("apexcharts-yaxistooltip-text"),e.yaxisTooltip.appendChild(e.yaxisTooltipText[i]))}(t)}},{key:"setXCrosshairWidth",value:function(){var t,e,i,s=this.w,a=this.ttCtx,n=a.getElXCrosshairs();a.xcrosshairsWidth=parseInt(s.config.xaxis.crosshairs.width),s.globals.comboCharts?null!==(t=s.globals.dom.baseEl.querySelector(".apexcharts-bar-area"))&&"barWidth"===s.config.xaxis.crosshairs.width?(e=parseFloat(t.getAttribute("barWidth")),a.xcrosshairsWidth=e):"tickWidth"===s.config.xaxis.crosshairs.width&&(e=s.globals.labels.length,a.xcrosshairsWidth=s.globals.gridWidth/e):"tickWidth"===s.config.xaxis.crosshairs.width?(i=s.globals.labels.length,a.xcrosshairsWidth=s.globals.gridWidth/i):"barWidth"===s.config.xaxis.crosshairs.width&&(null!==(i=s.globals.dom.baseEl.querySelector(".apexcharts-bar-area"))?(i=parseFloat(i.getAttribute("barWidth")),a.xcrosshairsWidth=i):a.xcrosshairsWidth=1),s.globals.isBarHorizontal&&(a.xcrosshairsWidth=0),null!==n&&0<a.xcrosshairsWidth&&n.setAttribute("width",a.xcrosshairsWidth)}},{key:"handleYCrosshair",value:function(){var t=this.w,e=this.ttCtx;e.ycrosshairs=t.globals.dom.baseEl.querySelector(".apexcharts-ycrosshairs"),e.ycrosshairsHidden=t.globals.dom.baseEl.querySelector(".apexcharts-ycrosshairs-hidden")}},{key:"drawYaxisTooltipText",value:function(t,e,i){var s,a,n=this.ttCtx,o=this.w,r=o.globals.yLabelFormatters[t];n.blyaxisTooltip&&(a=(e-(s=n.getElGrid().getBoundingClientRect()).top)*i.yRatio[t],i=o.globals.maxYArr[t]-o.globals.minYArr[t],a=o.globals.minYArr[t]+(i-a),n.tooltipPosition.moveYCrosshairs(e-s.top),n.yaxisTooltipText[t].innerHTML=r(a),n.tooltipPosition.moveYAxisTooltip(t))}}]),pe),re=(t(ge,[{key:"getElTooltip",value:function(t){return(t=t||this).w.globals.dom.baseEl.querySelector(".apexcharts-tooltip")}},{key:"getElXCrosshairs",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-xcrosshairs")}},{key:"getElGrid",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-grid")}},{key:"drawTooltip",value:function(t){var e=this.w,i=(this.xyRatios=t,this.blxaxisTooltip=e.config.xaxis.tooltip.enabled&&e.globals.axisCharts,this.blyaxisTooltip=e.config.yaxis[0].tooltip.enabled&&e.globals.axisCharts,this.allTooltipSeriesGroups=[],e.globals.axisCharts||(this.showTooltipTitle=!1),document.createElement("div"));i.classList.add("apexcharts-tooltip"),i.classList.add(this.tConfig.theme),e.globals.dom.elWrap.appendChild(i),e.globals.axisCharts&&(this.axesTooltip.drawXaxisTooltip(),this.axesTooltip.drawYaxisTooltip(),this.axesTooltip.setXCrosshairWidth(),this.axesTooltip.handleYCrosshair(),t=new X(this.ctx),this.xAxisTicksPositions=t.getXAxisTicksPositions()),(e.globals.comboCharts&&!this.tConfig.shared||this.tConfig.intersect&&!this.tConfig.shared||("bar"===e.config.chart.type||"rangeBar"===e.config.chart.type)&&!this.tConfig.shared)&&(this.showOnIntersect=!0),0!==e.config.markers.size&&0!==e.globals.markers.largestSize||this.marker.drawDynamicPoints(this),e.globals.collapsedSeries.length!==e.globals.series.length&&(this.dataPointsDividedHeight=e.globals.gridHeight/e.globals.dataPoints,this.dataPointsDividedWidth=e.globals.gridWidth/e.globals.dataPoints,this.showTooltipTitle&&(this.tooltipTitle=document.createElement("div"),this.tooltipTitle.classList.add("apexcharts-tooltip-title"),this.tooltipTitle.style.fontFamily=this.tConfig.style.fontFamily||e.config.chart.fontFamily,this.tooltipTitle.style.fontSize=this.tConfig.style.fontSize,i.appendChild(this.tooltipTitle)),i=e.globals.series.length,(e.globals.xyCharts||e.globals.comboCharts)&&this.tConfig.shared&&(i=this.showOnIntersect?1:e.globals.series.length),this.ttItems=this.createTTElements(i),this.addSVGEvents())}},{key:"createTTElements",value:function(t){for(var e=this.w,i=[],s=this.getElTooltip(),a=0;a<t;a++){var n=document.createElement("div"),o=(n.classList.add("apexcharts-tooltip-series-group"),this.tConfig.shared&&this.tConfig.enabledOnSeries&&Array.isArray(this.tConfig.enabledOnSeries)&&this.tConfig.enabledOnSeries.indexOf(a)<0&&n.classList.add("apexcharts-tooltip-series-group-hidden"),document.createElement("span")),r=(o.classList.add("apexcharts-tooltip-marker"),o.style.backgroundColor=e.globals.colors[a],n.appendChild(o),document.createElement("div")),l=(r.classList.add("apexcharts-tooltip-text"),r.style.fontFamily=this.tConfig.style.fontFamily||e.config.chart.fontFamily,r.style.fontSize=this.tConfig.style.fontSize,document.createElement("div")),h=(l.classList.add("apexcharts-tooltip-y-group"),document.createElement("span"));h.classList.add("apexcharts-tooltip-text-label"),l.appendChild(h),(o=document.createElement("span")).classList.add("apexcharts-tooltip-text-value"),l.appendChild(o),(h=document.createElement("div")).classList.add("apexcharts-tooltip-z-group"),(o=document.createElement("span")).classList.add("apexcharts-tooltip-text-z-label"),h.appendChild(o),(o=document.createElement("span")).classList.add("apexcharts-tooltip-text-z-value"),h.appendChild(o),r.appendChild(l),r.appendChild(h),n.appendChild(r),s.appendChild(n),i.push(n)}return i}},{key:"addSVGEvents",value:function(){var t=this.w,e=t.config.chart.type,i=this.getElTooltip(),s=!("bar"!==e&&"candlestick"!==e&&"rangeBar"!==e),a=t.globals.dom.Paper.node,n=this.getElGrid();n&&(this.seriesBound=n.getBoundingClientRect());var o,r,l=[],h=[],i={hoverArea:a,elGrid:n,tooltipEl:i,tooltipY:l,tooltipX:h,ttItems:this.ttItems};if(t.globals.axisCharts&&("area"===e||"line"===e||"scatter"===e||"bubble"===e?o=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series[data\\:longestSeries='true'] .apexcharts-marker"):s?o=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series .apexcharts-bar-area, .apexcharts-series .apexcharts-candlestick-area, .apexcharts-series .apexcharts-rangebar-area"):"heatmap"===e?o=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series .apexcharts-heatmap"):"radar"===e&&(o=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series .apexcharts-marker")),o)&&o.length)for(var c=0;c<o.length;c++)l.push(o[c].getAttribute("cy")),h.push(o[c].getAttribute("cx"));t.globals.xyCharts&&!this.showOnIntersect||t.globals.comboCharts&&!this.showOnIntersect||s&&this.hasBars()&&this.tConfig.shared?this.addPathsEventListeners([a],i):s&&!t.globals.comboCharts?this.addBarsEventListeners(i):"bubble"===e||"scatter"===e||"radar"===e||this.showOnIntersect&&("area"===e||"line"===e)?this.addPointsEventsListeners(i):t.globals.axisCharts&&"heatmap"!==e||(r=t.globals.dom.baseEl.querySelectorAll(".apexcharts-series"),this.addPathsEventListeners(r,i)),this.showOnIntersect&&(0<(r=t.globals.dom.baseEl.querySelectorAll(".apexcharts-line-series .apexcharts-marker")).length&&this.addPathsEventListeners(r,i),0<(t=t.globals.dom.baseEl.querySelectorAll(".apexcharts-area-series .apexcharts-marker")).length&&this.addPathsEventListeners(t,i),this.hasBars())&&!this.tConfig.shared&&this.addBarsEventListeners(i)}},{key:"drawFixedTooltipRect",value:function(){var t=this.w,e=this.getElTooltip(),i=(n=e.getBoundingClientRect()).width+10,s=n.height+10,a=this.tConfig.fixed.offsetX,n=this.tConfig.fixed.offsetY;return-1<this.tConfig.fixed.position.toLowerCase().indexOf("right")&&(a=a+t.globals.svgWidth-i+10),-1<this.tConfig.fixed.position.toLowerCase().indexOf("bottom")&&(n=n+t.globals.svgHeight-s-10),e.style.left=a+"px",e.style.top=n+"px",{x:a,y:n,ttWidth:i,ttHeight:s}}},{key:"addPointsEventsListeners",value:function(t){var e=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series-markers .apexcharts-marker");this.addPathsEventListeners(e,t)}},{key:"addBarsEventListeners",value:function(t){var e=this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-bar-area, .apexcharts-candlestick-area, .apexcharts-rangebar-area");this.addPathsEventListeners(e,t)}},{key:"addPathsEventListeners",value:function(s,t){for(var a=this,n=this,e=0;e<s.length;e++)!function(e){var i={paths:s[e],tooltipEl:t.tooltipEl,tooltipY:t.tooltipY,tooltipX:t.tooltipX,elGrid:t.elGrid,hoverArea:t.hoverArea,ttItems:t.ttItems};a.w.globals.tooltipOpts=i,["mousemove","mouseup","touchmove","mouseout","touchend"].map(function(t){return s[e].addEventListener(t,n.seriesHover.bind(n,i),{capture:!1,passive:!0})})}(e)}},{key:"seriesHover",value:function(i,s){var a=this,t=[],e=this.w;e.config.chart.group&&(t=this.ctx.getGroupedCharts()),e.globals.axisCharts&&(e.globals.minX===-1/0&&e.globals.maxX===1/0||0===e.globals.dataPoints)||(t.length?t.forEach(function(t){var e=a.getElTooltip(t),e={paths:i.paths,tooltipEl:e,tooltipY:i.tooltipY,tooltipX:i.tooltipX,elGrid:i.elGrid,hoverArea:i.hoverArea,ttItems:t.w.globals.tooltip.ttItems};t.w.globals.minX===a.w.globals.minX&&t.w.globals.maxX===a.w.globals.maxX&&t.w.globals.tooltip.seriesHoverByContext({chartCtx:t,ttCtx:t.w.globals.tooltip,opt:e,e:s})}):this.seriesHoverByContext({chartCtx:this.ctx,ttCtx:this.w.globals.tooltip,opt:i,e:s}))}},{key:"seriesHoverByContext",value:function(t){var e=t.chartCtx,i=t.ttCtx,s=t.opt,a=t.e,n=e.w,t=this.getElTooltip();i.tooltipRect={x:0,y:0,ttWidth:t.getBoundingClientRect().width,ttHeight:t.getBoundingClientRect().height},i.e=a,!i.hasBars()||n.globals.comboCharts||i.isBarShared||this.tConfig.onDatasetHover.highlightDataSeries&&new g(e).toggleSeriesOnHover(a,a.target.parentNode),i.fixedTooltip&&i.drawFixedTooltipRect(),n.globals.axisCharts?i.axisChartsTooltips({e:a,opt:s,tooltipRect:i.tooltipRect}):i.nonAxisChartsTooltips({e:a,opt:s,tooltipRect:i.tooltipRect})}},{key:"axisChartsTooltips",value:function(t){var e=t.e,i=t.opt,s=this.w,a=i.elGrid.getBoundingClientRect(),n=("touchmove"===e.type?e.touches[0]:e).clientX,o=("touchmove"===e.type?e.touches[0]:e).clientY;if(this.clientY=o,this.clientX=n,o<a.top||o>a.top+a.height)this.handleMouseOut(i);else{if(Array.isArray(this.tConfig.enabledOnSeries)&&!s.config.tooltip.shared){var r=parseInt(i.paths.getAttribute("index"));if(this.tConfig.enabledOnSeries.indexOf(r)<0)return void this.handleMouseOut(i)}var l,h,t=this.getElTooltip(),a=this.getElXCrosshairs(),r=s.globals.xyCharts||"bar"===s.config.chart.type&&!s.globals.isBarHorizontal&&this.hasBars()&&this.tConfig.shared||s.globals.comboCharts&&this.hasBars;if(s.globals.isBarHorizontal&&this.hasBars()&&(r=!1),"mousemove"===e.type||"touchmove"===e.type||"mouseup"===e.type){if(null!==a&&a.classList.add("active"),null!==this.ycrosshairs&&this.blyaxisTooltip&&this.ycrosshairs.classList.add("active"),r&&!this.showOnIntersect){var c=(r=this.tooltipUtil.getNearestValues({context:this,hoverArea:i.hoverArea,elGrid:i.elGrid,clientX:n,clientY:o,hasBars:this.hasBars})).j,n=r.capturedSeries;if(r.hoverX<0||r.hoverX>s.globals.gridWidth)return void this.handleMouseOut(i);if(null!==n){if(null===s.globals.series[n][c])return void i.tooltipEl.classList.remove("active");void 0!==s.globals.series[n][c]?this.tConfig.shared&&this.tooltipUtil.isXoverlap(c)&&this.tooltipUtil.isinitialSeriesSameLen()?this.create(e,this,n,c,i.ttItems):this.create(e,this,n,c,i.ttItems,!1):this.tooltipUtil.isXoverlap(c)&&this.create(e,this,0,c,i.ttItems)}else this.tooltipUtil.isXoverlap(c)&&this.create(e,this,0,c,i.ttItems)}else"heatmap"===s.config.chart.type?(l=(c=this.intersect.handleHeatTooltip({e:e,opt:i,x:l,y:h})).x,h=c.y,t.style.left=l+"px",t.style.top=h+"px"):(this.hasBars&&this.intersect.handleBarTooltip({e:e,opt:i}),this.hasMarkers&&this.intersect.handleMarkerTooltip({e:e,opt:i,x:l,y:h}));if(this.blyaxisTooltip)for(var d=0;d<s.config.yaxis.length;d++)this.axesTooltip.drawYaxisTooltipText(d,o,this.xyRatios);i.tooltipEl.classList.add("active")}else"mouseout"!==e.type&&"touchend"!==e.type||this.handleMouseOut(i)}}},{key:"nonAxisChartsTooltips",value:function(t){var e=t.e,i=t.opt,s=t.tooltipRect,a=this.w,n=i.paths.getAttribute("rel"),o=this.getElTooltip(),t=a.globals.dom.elWrap.getBoundingClientRect();"mousemove"===e.type||"touchmove"===e.type?(o.classList.add("active"),this.tooltipLabels.drawSeriesTexts({ttItems:i.ttItems,i:parseInt(n)-1,shared:!1}),n=a.globals.clientX-t.left-s.ttWidth/2,s=a.globals.clientY-t.top-s.ttHeight-10,o.style.left=n+"px",o.style.top=s+"px"):"mouseout"!==e.type&&"touchend"!==e.type||o.classList.remove("active")}},{key:"deactivateHoverFilter",value:function(){for(var t=this.w,e=new tt(this.ctx),i=t.globals.dom.Paper.select(".apexcharts-bar-area"),s=0;s<i.length;s++)e.pathMouseLeave(i[s])}},{key:"handleMouseOut",value:function(t){var e=this.w,i=this.getElXCrosshairs();if(t.tooltipEl.classList.remove("active"),this.deactivateHoverFilter(),"bubble"!==e.config.chart.type&&this.marker.resetPointsSize(),null!==i&&i.classList.remove("active"),null!==this.ycrosshairs&&this.ycrosshairs.classList.remove("active"),this.blxaxisTooltip&&this.xaxisTooltip.classList.remove("active"),this.blyaxisTooltip){null===this.yaxisTTEls&&(this.yaxisTTEls=e.globals.dom.baseEl.querySelectorAll(".apexcharts-yaxistooltip"));for(var s=0;s<this.yaxisTTEls.length;s++)this.yaxisTTEls[s].classList.remove("active")}}},{key:"getElMarkers",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(" .apexcharts-series-markers")}},{key:"getAllMarkers",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-series-markers .apexcharts-marker")}},{key:"hasMarkers",value:function(){return 0<this.getElMarkers().length}},{key:"getElBars",value:function(){return this.w.globals.dom.baseEl.querySelectorAll(".apexcharts-bar-series,  .apexcharts-candlestick-series, .apexcharts-rangebar-series")}},{key:"hasBars",value:function(){return 0<this.getElBars().length}},{key:"markerClick",value:function(t,e,i){var s=this.w;"function"==typeof s.config.chart.events.markerClick&&s.config.chart.events.markerClick(t,this.ctx,{seriesIndex:e,dataPointIndex:i,w:s}),this.ctx.fireEvent("markerClick",[t,this.ctx,{seriesIndex:e,dataPointIndex:i,w:s}])}},{key:"create",value:function(t,e,i,s,a){var n=5<arguments.length&&void 0!==arguments[5]?arguments[5]:null,o=this.w,r=e;if("mouseup"===t.type&&this.markerClick(t,i,s),null===n&&(n=this.tConfig.shared),e=this.hasMarkers(),t=this.getElBars(),n){if(r.tooltipLabels.drawSeriesTexts({ttItems:a,i:i,j:s,shared:!this.showOnIntersect&&this.tConfig.shared}),e&&(0<o.globals.markers.largestSize?r.marker.enlargePoints(s):r.tooltipPosition.moveDynamicPointsOnHover(s)),this.hasBars()&&(this.barSeriesHeight=this.tooltipUtil.getBarsHeight(t),0<this.barSeriesHeight)){var l=new tt(this.ctx),h=o.globals.dom.Paper.select(".apexcharts-bar-area[j='".concat(s,"']"));this.deactivateHoverFilter(),this.tooltipPosition.moveStickyTooltipOverBars(s);for(var c=0;c<h.length;c++)l.pathMouseEnter(h[c])}}else r.tooltipLabels.drawSeriesTexts({shared:!1,ttItems:a,i:i,j:s}),this.hasBars()&&r.tooltipPosition.moveStickyTooltipOverBars(s),e&&r.tooltipPosition.moveMarkers(i,s)}}]),ge),le=(t(ue,[{key:"createToolbar",value:function(){var t=this.w,e=document.createElement("div");if(e.setAttribute("class","apexcharts-toolbar"),t.globals.dom.elWrap.appendChild(e),this.elZoom=document.createElement("div"),this.elZoomIn=document.createElement("div"),this.elZoomOut=document.createElement("div"),this.elPan=document.createElement("div"),this.elSelection=document.createElement("div"),this.elZoomReset=document.createElement("div"),this.elMenuIcon=document.createElement("div"),this.elMenu=document.createElement("div"),this.elCustomIcons=[],this.t=t.config.chart.toolbar.tools,Array.isArray(this.t.customIcons))for(var i=0;i<this.t.customIcons.length;i++)this.elCustomIcons.push(document.createElement("div"));this.elMenuItems=[];var s=[];this.t.zoomin&&t.config.chart.zoom.enabled&&s.push({el:this.elZoomIn,icon:"string"==typeof this.t.zoomin?this.t.zoomin:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n    <path d="M0 0h24v24H0z" fill="none"/>\n    <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>\n</svg>\n',title:this.localeValues.zoomIn,class:"apexcharts-zoom-in-icon"}),this.t.zoomout&&t.config.chart.zoom.enabled&&s.push({el:this.elZoomOut,icon:"string"==typeof this.t.zoomout?this.t.zoomout:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">\n    <path d="M0 0h24v24H0z" fill="none"/>\n    <path d="M7 11v2h10v-2H7zm5-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/>\n</svg>\n',title:this.localeValues.zoomOut,class:"apexcharts-zoom-out-icon"}),this.t.zoom&&t.config.chart.zoom.enabled&&s.push({el:this.elZoom,icon:"string"==typeof this.t.zoom?this.t.zoom:'<svg xmlns="http://www.w3.org/2000/svg" fill="#000000" height="24" viewBox="0 0 24 24" width="24">\n    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>\n    <path d="M0 0h24v24H0V0z" fill="none"/>\n    <path d="M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"/>\n</svg>',title:this.localeValues.selectionZoom,class:t.globals.isTouchDevice?"hidden":"apexcharts-zoom-icon"}),this.t.selection&&t.config.chart.selection.enabled&&s.push({el:this.elSelection,icon:"string"==typeof this.t.selection?this.t.selection:'<svg fill="#6E8192" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <path d="M0 0h24v24H0z" fill="none"/>\n    <path d="M3 5h2V3c-1.1 0-2 .9-2 2zm0 8h2v-2H3v2zm4 8h2v-2H7v2zM3 9h2V7H3v2zm10-6h-2v2h2V3zm6 0v2h2c0-1.1-.9-2-2-2zM5 21v-2H3c0 1.1.9 2 2 2zm-2-4h2v-2H3v2zM9 3H7v2h2V3zm2 18h2v-2h-2v2zm8-8h2v-2h-2v2zm0 8c1.1 0 2-.9 2-2h-2v2zm0-12h2V7h-2v2zm0 8h2v-2h-2v2zm-4 4h2v-2h-2v2zm0-16h2V3h-2v2z"/>\n</svg>',title:this.localeValues.selection,class:t.globals.isTouchDevice?"hidden":"apexcharts-selection-icon"}),this.t.pan&&t.config.chart.zoom.enabled&&s.push({el:this.elPan,icon:"string"==typeof this.t.pan?this.t.pan:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#000000" height="24" viewBox="0 0 24 24" width="24">\n    <defs>\n        <path d="M0 0h24v24H0z" id="a"/>\n    </defs>\n    <clipPath id="b">\n        <use overflow="visible" xlink:href="#a"/>\n    </clipPath>\n    <path clip-path="url(#b)" d="M23 5.5V20c0 2.2-1.8 4-4 4h-7.3c-1.08 0-2.1-.43-2.85-1.19L1 14.83s1.26-1.23 1.3-1.25c.22-.19.49-.29.79-.29.22 0 .**********.04.01 4.31 2.46 4.31 2.46V4c0-.83.67-1.5 1.5-1.5S11 3.17 11 4v7h1V1.5c0-.83.67-1.5 1.5-1.5S15 .67 15 1.5V11h1V2.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5V11h1V5.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5z"/>\n</svg>',title:this.localeValues.pan,class:t.globals.isTouchDevice?"hidden":"apexcharts-pan-icon"}),this.t.reset&&t.config.chart.zoom.enabled&&s.push({el:this.elZoomReset,icon:"string"==typeof this.t.reset?this.t.reset:'<svg fill="#000000" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg">\n    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>\n    <path d="M0 0h24v24H0z" fill="none"/>\n</svg>',title:this.localeValues.reset,class:"apexcharts-reset-zoom-icon"}),this.t.download&&s.push({el:this.elMenuIcon,icon:"string"==typeof this.t.download?this.t.download:'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="none" d="M0 0h24v24H0V0z"/><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>',title:this.localeValues.menu,class:"apexcharts-menu-icon"});for(var a=0;a<this.elCustomIcons.length;a++)s.push({el:this.elCustomIcons[a],icon:this.t.customIcons[a].icon,title:this.t.customIcons[a].title,index:this.t.customIcons[a].index,class:"apexcharts-toolbar-custom-icon "+this.t.customIcons[a].class});s.forEach(function(t,e){t.index&&K.moveIndexInArray(s,e,t.index)});for(var n=0;n<s.length;n++)tt.setAttrs(s[n].el,{class:s[n].class,title:s[n].title}),s[n].el.innerHTML=s[n].icon,e.appendChild(s[n].el);e.appendChild(this.elMenu),tt.setAttrs(this.elMenu,{class:"apexcharts-menu"});for(var o=[{name:"exportSVG",title:this.localeValues.exportToSVG},{name:"exportPNG",title:this.localeValues.exportToPNG}],r=0;r<o.length;r++)this.elMenuItems.push(document.createElement("div")),this.elMenuItems[r].innerHTML=o[r].title,tt.setAttrs(this.elMenuItems[r],{class:"apexcharts-menu-item ".concat(o[r].name),title:o[r].title}),this.elMenu.appendChild(this.elMenuItems[r]);t.globals.zoomEnabled?this.elZoom.classList.add("selected"):t.globals.panEnabled?this.elPan.classList.add("selected"):t.globals.selectionEnabled&&this.elSelection.classList.add("selected"),this.addToolbarEventListeners()}},{key:"addToolbarEventListeners",value:function(){var e=this;this.elZoomReset.addEventListener("click",this.handleZoomReset.bind(this)),this.elSelection.addEventListener("click",this.toggleSelection.bind(this)),this.elZoom.addEventListener("click",this.toggleZooming.bind(this)),this.elZoomIn.addEventListener("click",this.handleZoomIn.bind(this)),this.elZoomOut.addEventListener("click",this.handleZoomOut.bind(this)),this.elPan.addEventListener("click",this.togglePanning.bind(this)),this.elMenuIcon.addEventListener("click",this.toggleMenu.bind(this)),this.elMenuItems.forEach(function(t){t.classList.contains("exportSVG")?t.addEventListener("click",e.downloadSVG.bind(e)):t.classList.contains("exportPNG")&&t.addEventListener("click",e.downloadPNG.bind(e))});for(var t=0;t<this.t.customIcons.length;t++)this.elCustomIcons[t].addEventListener("click",this.t.customIcons[t].click)}},{key:"toggleSelection",value:function(){this.toggleOtherControls(),this.w.globals.selectionEnabled=!this.w.globals.selectionEnabled,this.elSelection.classList.contains("selected")?this.elSelection.classList.remove("selected"):this.elSelection.classList.add("selected")}},{key:"toggleZooming",value:function(){this.toggleOtherControls(),this.w.globals.zoomEnabled=!this.w.globals.zoomEnabled,this.elZoom.classList.contains("selected")?this.elZoom.classList.remove("selected"):this.elZoom.classList.add("selected")}},{key:"getToolbarIconsReference",value:function(){var t=this.w;this.elZoom||(this.elZoom=t.globals.dom.baseEl.querySelector(".apexcharts-zoom-icon")),this.elPan||(this.elPan=t.globals.dom.baseEl.querySelector(".apexcharts-pan-icon")),this.elSelection||(this.elSelection=t.globals.dom.baseEl.querySelector(".apexcharts-selection-icon"))}},{key:"enableZooming",value:function(){this.toggleOtherControls(),this.w.globals.zoomEnabled=!0,this.elZoom&&this.elZoom.classList.add("selected"),this.elPan&&this.elPan.classList.remove("selected")}},{key:"enablePanning",value:function(){this.toggleOtherControls(),this.w.globals.panEnabled=!0,this.elPan&&this.elPan.classList.add("selected"),this.elZoom&&this.elZoom.classList.remove("selected")}},{key:"togglePanning",value:function(){this.toggleOtherControls(),this.w.globals.panEnabled=!this.w.globals.panEnabled,this.elPan.classList.contains("selected")?this.elPan.classList.remove("selected"):this.elPan.classList.add("selected")}},{key:"toggleOtherControls",value:function(){var t=this.w;t.globals.panEnabled=!1,t.globals.zoomEnabled=!1,t.globals.selectionEnabled=!1,this.getToolbarIconsReference(),this.elPan&&this.elPan.classList.remove("selected"),this.elSelection&&this.elSelection.classList.remove("selected"),this.elZoom&&this.elZoom.classList.remove("selected")}},{key:"handleZoomIn",value:function(){var t=this.w,e=(t.globals.minX+t.globals.maxX)/2,i=(t.globals.minX+e)/2,e=(t.globals.maxX+e)/2;t.globals.disableZoomIn||this.zoomUpdateOptions(i,e)}},{key:"handleZoomOut",value:function(){var t,e,i=this.w;"datetime"===i.config.xaxis.type&&new Date(i.globals.minX).getUTCFullYear()<1e3||(e=(i.globals.minX+i.globals.maxX)/2,t=i.globals.minX-(e-i.globals.minX),e=i.globals.maxX-(e-i.globals.maxX),i.globals.disableZoomOut)||this.zoomUpdateOptions(t,e)}},{key:"zoomUpdateOptions",value:function(t,e){(e=this.getBeforeZoomRange(t={min:t,max:e}))&&(t=e.xaxis),this.w.globals.zoomed=!0,this.ctx._updateOptions({xaxis:t},!1,this.w.config.chart.animations.dynamicAnimation.enabled),this.zoomCallback(t)}},{key:"zoomCallback",value:function(t,e){"function"==typeof this.ev.zoomed&&this.ev.zoomed(this.ctx,{xaxis:t,yaxis:e})}},{key:"getBeforeZoomRange",value:function(t,e){return"function"==typeof this.ev.beforeZoom?this.ev.beforeZoom(this,{xaxis:t,yaxis:e}):null}},{key:"toggleMenu",value:function(){this.elMenu.classList.contains("open")?this.elMenu.classList.remove("open"):this.elMenu.classList.add("open")}},{key:"downloadPNG",value:function(){new Jt(this.ctx).exportToPng(this.ctx),this.toggleMenu()}},{key:"downloadSVG",value:function(){new Jt(this.ctx).exportToSVG(),this.toggleMenu()}},{key:"handleZoomReset",value:function(t){var i=this;this.ctx.getSyncedCharts().forEach(function(t){var e=t.w;e.globals.minX!==e.globals.initialminX&&e.globals.maxX!==e.globals.initialmaxX&&(t.revertDefaultAxisMinMax(),"function"==typeof e.config.chart.events.zoomed&&i.zoomCallback({min:e.config.xaxis.min,max:e.config.xaxis.max}),e.globals.zoomed=!1,t._updateSeries(e.globals.initialSeries,e.config.chart.animations.dynamicAnimation.enabled))})}},{key:"destroy",value:function(){this.elZoom=null,this.elZoomIn=null,this.elZoomOut=null,this.elPan=null,this.elSelection=null,this.elZoomReset=null,this.elMenuIcon=null}}]),ue),he=(e(v,le),t(v,[{key:"init",value:function(t){var e=this,i=t.xyRatios,t=this.w,s=this;this.xyRatios=i,this.zoomRect=this.graphics.drawRect(0,0,0,0),this.selectionRect=this.graphics.drawRect(0,0,0,0),this.gridRect=t.globals.dom.baseEl.querySelector(".apexcharts-grid"),this.zoomRect.node.classList.add("apexcharts-zoom-rect"),this.selectionRect.node.classList.add("apexcharts-selection-rect"),t.globals.dom.elGraphical.add(this.zoomRect),t.globals.dom.elGraphical.add(this.selectionRect),"x"===t.config.chart.selection.type?this.slDraggableRect=this.selectionRect.draggable({minX:0,minY:0,maxX:t.globals.gridWidth,maxY:t.globals.gridHeight}).on("dragmove",this.selectionDragging.bind(this,"dragging")):"y"===t.config.chart.selection.type?this.slDraggableRect=this.selectionRect.draggable({minX:0,maxX:t.globals.gridWidth}).on("dragmove",this.selectionDragging.bind(this,"dragging")):this.slDraggableRect=this.selectionRect.draggable().on("dragmove",this.selectionDragging.bind(this,"dragging")),this.preselectedSelection(),this.hoverArea=t.globals.dom.baseEl.querySelector(t.globals.chartClass),this.hoverArea.classList.add("zoomable"),this.eventList.forEach(function(t){e.hoverArea.addEventListener(t,s.svgMouseEvents.bind(s,i),{capture:!1,passive:!0})})}},{key:"destroy",value:function(){this.slDraggableRect&&(this.slDraggableRect.draggable(!1),this.slDraggableRect.off(),this.selectionRect.off()),this.selectionRect=null,this.zoomRect=null,this.gridRect=null}},{key:"svgMouseEvents",value:function(t,e){var i=this.w,s=this,a=this.ctx.toolbar,n=(i.globals.zoomEnabled?i.config.chart.zoom:i.config.chart.selection).type;e.shiftKey?(this.shiftWasPressed=!0,a.enablePanning()):this.shiftWasPressed&&(a.enableZooming(),this.shiftWasPressed=!1),e.target.classList.contains("apexcharts-selection-rect")||e.target.parentNode.classList.contains("apexcharts-toolbar")||(s.clientX=("touchmove"===e.type||"touchstart"===e.type?e.touches[0]:"touchend"===e.type?e.changedTouches[0]:e).clientX,s.clientY=("touchmove"===e.type||"touchstart"===e.type?e.touches[0]:"touchend"===e.type?e.changedTouches[0]:e).clientY,"mousedown"===e.type&&1===e.which&&(a=s.gridRect.getBoundingClientRect(),s.startX=s.clientX-a.left,s.startY=s.clientY-a.top,s.dragged=!1,s.w.globals.mousedown=!0),("mousemove"===e.type&&1===e.which||"touchmove"===e.type)&&(s.dragged=!0,i.globals.panEnabled?(i.globals.selection=null,s.w.globals.mousedown&&s.panDragging({context:s,zoomtype:n,xyRatios:t})):(s.w.globals.mousedown&&i.globals.zoomEnabled||s.w.globals.mousedown&&i.globals.selectionEnabled)&&(s.selection=s.selectionDrawing({context:s,zoomtype:n}))),"mouseup"!==e.type&&"touchend"!==e.type&&"mouseleave"!==e.type||(e=s.gridRect.getBoundingClientRect(),s.w.globals.mousedown&&(s.endX=s.clientX-e.left,s.endY=s.clientY-e.top,s.dragX=Math.abs(s.endX-s.startX),s.dragY=Math.abs(s.endY-s.startY),i.globals.zoomEnabled||i.globals.selectionEnabled)&&s.selectionDrawn({context:s,zoomtype:n}),i.globals.zoomEnabled&&s.hideSelectionRect(this.selectionRect),s.dragged=!1,s.w.globals.mousedown=!1),this.makeSelectionRectDraggable())}},{key:"makeSelectionRectDraggable",value:function(){var t,e=this.w;this.selectionRect&&0<(t=this.selectionRect.node.getBoundingClientRect()).width&&0<t.height&&this.slDraggableRect.selectize().resize({constraint:{minX:0,minY:0,maxX:e.globals.gridWidth,maxY:e.globals.gridHeight}}).on("resizing",this.selectionDragging.bind(this,"resizing"))}},{key:"preselectedSelection",value:function(){var t,e=this.w,i=this.xyRatios;e.globals.zoomEnabled||(void 0!==e.globals.selection&&null!==e.globals.selection?this.drawSelectionRect(e.globals.selection):void 0!==e.config.chart.selection.xaxis.min&&void 0!==e.config.chart.selection.xaxis.max&&(t={x:t=(e.config.chart.selection.xaxis.min-e.globals.minX)/i.xRatio,y:0,width:e.globals.gridWidth-(e.globals.maxX-e.config.chart.selection.xaxis.max)/i.xRatio-t,height:e.globals.gridHeight,translateX:0,translateY:0,selectionEnabled:!0},this.drawSelectionRect(t),this.makeSelectionRectDraggable(),"function"==typeof e.config.chart.events.selection)&&e.config.chart.events.selection(this.ctx,{xaxis:{min:e.config.chart.selection.xaxis.min,max:e.config.chart.selection.xaxis.max},yaxis:{}}))}},{key:"drawSelectionRect",value:function(t){var e=t.x,i=t.y,s=t.width,a=t.height,n=t.translateX,o=t.translateY,r=this.w,l=this.zoomRect,t=this.selectionRect;!this.dragged&&null===r.globals.selection||(o={transform:"translate("+n+", "+o+")"},r.globals.zoomEnabled&&this.dragged&&(l.attr({x:e,y:i,width:s,height:a,fill:r.config.chart.zoom.zoomedArea.fill.color,"fill-opacity":r.config.chart.zoom.zoomedArea.fill.opacity,stroke:r.config.chart.zoom.zoomedArea.stroke.color,"stroke-width":r.config.chart.zoom.zoomedArea.stroke.width,"stroke-opacity":r.config.chart.zoom.zoomedArea.stroke.opacity}),tt.setAttrs(l.node,o)),r.globals.selectionEnabled&&(t.attr({x:e,y:i,width:0<s?s:0,height:0<a?a:0,fill:r.config.chart.selection.fill.color,"fill-opacity":r.config.chart.selection.fill.opacity,stroke:r.config.chart.selection.stroke.color,"stroke-width":r.config.chart.selection.stroke.width,"stroke-dasharray":r.config.chart.selection.stroke.dashArray,"stroke-opacity":r.config.chart.selection.stroke.opacity}),tt.setAttrs(t.node,o)))}},{key:"hideSelectionRect",value:function(t){t&&t.attr({x:0,y:0,width:0,height:0})}},{key:"selectionDrawing",value:function(t){var e=t.context,i=t.zoomtype,s=this.w,a=e,n=this.gridRect.getBoundingClientRect(),o=a.startX-1,r=a.startY,l=a.clientX-n.left-o,h=a.clientY-n.top-r,c=0,t=0;return Math.abs(l+o)>s.globals.gridWidth?l=s.globals.gridWidth-o:a.clientX-n.left<0&&(l=o),o>a.clientX-n.left&&(c=-(l=Math.abs(l))),r>a.clientY-n.top&&(t=-(h=Math.abs(h))),e="x"===i?{x:o,y:0,width:l,height:s.globals.gridHeight,translateX:c,translateY:0}:"y"===i?{x:0,y:r,width:s.globals.gridWidth,height:h,translateX:0,translateY:t}:{x:o,y:r,width:l,height:h,translateX:c,translateY:t},a.drawSelectionRect(e),a.selectionDragging("resizing"),e}},{key:"selectionDragging",value:function(t,e){var n=this,o=this.w,r=this.xyRatios,l=this.selectionRect,t="resizing"===t?30:0;"function"==typeof o.config.chart.events.selection&&o.globals.selectionEnabled&&(clearTimeout(this.w.globals.selectionResizeTimer),this.w.globals.selectionResizeTimer=window.setTimeout(function(){var t=n.gridRect.getBoundingClientRect(),e=l.node.getBoundingClientRect(),i=o.globals.xAxisScale.niceMin+(e.left-t.left)*r.xRatio,s=o.globals.xAxisScale.niceMin+(e.right-t.left)*r.xRatio,a=o.globals.yAxisScale[0].niceMin+(t.bottom-e.bottom)*r.yRatio[0],t=o.globals.yAxisScale[0].niceMax-(e.top-t.top)*r.yRatio[0];o.config.chart.events.selection(n.ctx,{xaxis:{min:i,max:s},yaxis:{min:a,max:t}})},t))}},{key:"selectionDrawn",value:function(t){var e=t.context,i=t.zoomtype,s=this.w,a=e,n=this.xyRatios,t=this.ctx.toolbar;a.startX>a.endX&&(c=a.startX,a.startX=a.endX,a.endX=c),a.startY>a.endY&&(o=a.startY,a.startY=a.endY,a.endY=o);var o,r,l,h,e=s.globals.xAxisScale.niceMin+a.startX*n.xRatio,c=s.globals.xAxisScale.niceMin+a.endX*n.xRatio,d=[],u=[];s.config.yaxis.forEach(function(t,e){d.push(Math.floor(s.globals.yAxisScale[e].niceMax-n.yRatio[e]*a.startY)),u.push(Math.floor(s.globals.yAxisScale[e].niceMax-n.yRatio[e]*a.endY))}),a.dragged&&(10<a.dragX||10<a.dragY)&&e!==c&&(s.globals.zoomEnabled?(r=K.clone(s.globals.initialConfig.yaxis),s.globals.zoomed||(s.globals.lastXAxis=K.clone(s.config.xaxis),s.globals.lastYAxis=K.clone(s.config.yaxis)),o={min:e,max:c},"xy"!==i&&"y"!==i||r.forEach(function(t,e){r[e].min=u[e],r[e].max=d[e]}),s.config.chart.zoom.autoScaleYaxis&&(r=new ct(a.ctx).autoScaleY(a.ctx,r,{xaxis:o})),t&&(l=t.getBeforeZoomRange(o,r))&&(o=l.xaxis||o,r=l.yaxis?l.yaxe:r),l={xaxis:o},s.config.chart.group||(l[r]=r),a.ctx._updateOptions(l,!1,a.w.config.chart.animations.dynamicAnimation.enabled),"function"==typeof s.config.chart.events.zoomed&&t.zoomCallback(o,r),s.globals.zoomed=!0):s.globals.selectionEnabled&&(h=null,c={min:e,max:c},"xy"!==i&&"y"!==i||(h=K.clone(s.config.yaxis)).forEach(function(t,e){h[e].min=u[e],h[e].max=d[e]}),s.globals.selection=a.selection,"function"==typeof s.config.chart.events.selection)&&s.config.chart.events.selection(a.ctx,{xaxis:c,yaxis:h}))}},{key:"panDragging",value:function(t){var e,i,t=t.context,s=(void 0!==(a=this.w).globals.lastClientPosition.x&&(i=a.globals.lastClientPosition.x-t.clientX,s=a.globals.lastClientPosition.y-t.clientY,Math.abs(i)>Math.abs(s)&&0<i?e="left":Math.abs(i)>Math.abs(s)&&i<0?e="right":Math.abs(s)>Math.abs(i)&&0<s?e="up":Math.abs(s)>Math.abs(i)&&s<0&&(e="down")),a.globals.lastClientPosition={x:t.clientX,y:t.clientY},a.globals.minX),a=a.globals.maxX;t.panScrolled(e,s,a)}},{key:"panScrolled",value:function(t,e,i){var s=this.w,a=this.xyRatios,n=K.clone(s.globals.initialConfig.yaxis);"left"===t?(e=s.globals.minX+s.globals.gridWidth/15*a.xRatio,i=s.globals.maxX+s.globals.gridWidth/15*a.xRatio):"right"===t&&(e=s.globals.minX-s.globals.gridWidth/15*a.xRatio,i=s.globals.maxX-s.globals.gridWidth/15*a.xRatio),(e<s.globals.initialminX||i>s.globals.initialmaxX)&&(e=s.globals.minX,i=s.globals.maxX),a={min:e,max:i},s.config.chart.zoom.autoScaleYaxis&&(n=new ct(this.ctx).autoScaleY(this.ctx,n,{xaxis:a})),a={xaxis:{min:e,max:i}},s.config.chart.group||(a[n]=n),this.ctx._updateOptions(a,!1,!1),"function"==typeof s.config.chart.events.scrolled&&s.config.chart.events.scrolled(this.ctx,{xaxis:{min:e,max:i}})}}]),v),ce=(t(de,[{key:"draw",value:function(){this.drawTitleSubtitle("title"),this.drawTitleSubtitle("subtitle")}},{key:"drawTitleSubtitle",value:function(t){var e=this.w,i="title"===t?e.config.title:e.config.subtitle,s=e.globals.svgWidth/2,a=i.offsetY,n="middle";"left"===i.align?(s=10,n="start"):"right"===i.align&&(s=e.globals.svgWidth-10,n="end"),s+=i.offsetX,a=a+parseInt(i.style.fontSize)+2,void 0!==i.text&&((i=new tt(this.ctx).drawText({x:s,y:a,text:i.text,textAnchor:n,fontSize:i.style.fontSize,fontFamily:i.style.fontFamily,foreColor:i.style.color,opacity:1})).node.setAttribute("class","apexcharts-".concat(t,"-text")),e.globals.dom.Paper.add(i))}}]),de);function de(t){s(this,de),this.ctx=t,this.w=t.w}function v(t){var e;return s(this,v),(e=a(this,i(v).call(this,t))).ctx=t,e.w=t.w,e.dragged=!1,e.graphics=new tt(e.ctx),e.eventList=["mousedown","mouseleave","mousemove","touchstart","touchmove","mouseup","touchend"],e.clientX=0,e.clientY=0,e.startX=0,e.endX=0,e.dragX=0,e.startY=0,e.endY=0,e.dragY=0,e}function ue(t){s(this,ue),this.ctx=t,this.w=t.w,this.ev=this.w.config.chart.events,this.localeValues=this.w.globals.locale.toolbar}function ge(t){s(this,ge),this.ctx=t,this.w=t.w,t=this.w,this.tConfig=t.config.tooltip,this.tooltipUtil=new ee(this),this.tooltipLabels=new ie(this),this.tooltipPosition=new se(this),this.marker=new ae(this),this.intersect=new ne(this),this.axesTooltip=new oe(this),this.showOnIntersect=this.tConfig.intersect,this.showTooltipTitle=this.tConfig.x.show,this.fixedTooltip=this.tConfig.fixed.enabled,this.xaxisTooltip=null,this.yaxisTTEls=null,this.isBarShared=!t.globals.isBarHorizontal&&this.tConfig.shared}function pe(t){s(this,pe),this.w=t.w,this.ttCtx=t}function fe(t){s(this,fe),this.w=t.w,this.ttCtx=t}function xe(t){s(this,xe),this.w=t.w,this.ttCtx=t,this.ctx=t.ctx,this.tooltipPosition=new se(t)}function be(t){s(this,be),this.ttCtx=t,this.ctx=t.ctx,this.w=t.w}function me(t){s(this,me),this.w=t.w,this.ctx=t.ctx,this.ttCtx=t,this.tooltipUtil=new ee(t)}function ye(t){s(this,ye),this.w=t.w,this.ttCtx=t,this.ctx=t.ctx}function ve(t){s(this,ve),this.ctx=t,this.w=t.w,this.colors=[]}function we(t){s(this,we),this.ctx=t,this.w=t.w}function ke(t){s(this,ke),this.ctx=t,this.w=t.w,t=this.w,this.anim=new z(this.ctx),this.xaxisLabels=t.globals.labels.slice(),this.animX=t.config.grid.xaxis.lines.animate&&t.config.chart.animations.enabled,this.animY=t.config.grid.yaxis.lines.animate&&t.config.chart.animations.enabled,0<t.globals.timelineLabels.length&&(this.xaxisLabels=t.globals.timelineLabels.slice())}function Ae(t){s(this,Ae),this.ctx=t,this.w=t.w}function w(t){(this.el=t).remember("_selectHandler",this),this.pointSelection={isSelected:!1},this.rectSelection={isSelected:!1}}function Se(t){switch(t[0]){case"z":case"Z":t[0]="L",t[1]=this.start[0],t[2]=this.start[1];break;case"H":t[0]="L",t[2]=this.pos[1];break;case"V":t[0]="L",t[2]=t[1],t[1]=this.pos[0];break;case"T":t[0]="Q",t[3]=t[1],t[4]=t[2],t[1]=this.reflection[1],t[2]=this.reflection[0];break;case"S":t[0]="C",t[6]=t[4],t[5]=t[3],t[4]=t[2],t[3]=t[1],t[2]=this.reflection[1],t[1]=this.reflection[0]}return t}function Ce(t){var e=t.length;return this.pos=[t[e-2],t[e-1]],-1!="SCQT".indexOf(t[0])&&(this.reflection=[2*this.pos[0]-t[e-4],2*this.pos[1]-t[e-3]]),t}function Le(t){var e=[t];switch(t[0]){case"M":return this.pos=this.start=[t[1],t[2]],e;case"L":t[5]=t[3]=t[1],t[6]=t[4]=t[2],t[1]=this.pos[0],t[2]=this.pos[1];break;case"Q":t[6]=t[4],t[5]=t[3],t[4]=+t[4]/3+2*t[2]/3,t[3]=+t[3]/3+2*t[1]/3,t[2]=+this.pos[1]/3+2*t[2]/3,t[1]=+this.pos[0]/3+2*t[1]/3;break;case"A":t=(e=function(t,e){var i,s,a,n,o,r,l,h,c,d,u,g,p,f,x,b,m,y=Math.abs(e[1]),v=Math.abs(e[2]),w=e[3]%360,k=e[4],A=e[5],S=e[6],C=e[7],L=new SVG.Point(t),z=new SVG.Point(S,C),P=[];if(0===y||0===v||L.x===z.x&&L.y===z.y)return[["C",L.x,L.y,z.x,z.y,z.x,z.y]];for(1<(e=(s=new SVG.Point((L.x-z.x)/2,(L.y-z.y)/2).transform((new SVG.Matrix).rotate(w))).x*s.x/(y*y)+s.y*s.y/(v*v))&&(y*=e=Math.sqrt(e),v*=e),i=(new SVG.Matrix).rotate(w).scale(1/y,1/v).rotate(-w),L=L.transform(i),s=(t=[(z=z.transform(i)).x-L.x,z.y-L.y])[0]*t[0]+t[1]*t[1],e=Math.sqrt(s),t[0]/=e,t[1]/=e,s=s<4?Math.sqrt(1-s/4):0,k===A&&(s*=-1),a=new SVG.Point((z.x+L.x)/2+s*-t[1],(z.y+L.y)/2+s*t[0]),t=new SVG.Point(L.x-a.x,L.y-a.y),L=new SVG.Point(z.x-a.x,z.y-a.y),z=Math.acos(t.x/Math.sqrt(t.x*t.x+t.y*t.y)),t.y<0&&(z*=-1),t=Math.acos(L.x/Math.sqrt(L.x*L.x+L.y*L.y)),L.y<0&&(t*=-1),A&&t<z&&(t+=2*Math.PI),!A&&z<t&&(t-=2*Math.PI),l=[],n=(t-(h=z))/(o=Math.ceil(2*Math.abs(z-t)/Math.PI)),r=4*Math.tan(n/4)/3,g=0;g<=o;g++)d=Math.cos(h),c=Math.sin(h),u=new SVG.Point(a.x+d,a.y+c),l[g]=[new SVG.Point(u.x+r*c,u.y-r*d),u,new SVG.Point(u.x-r*c,u.y+r*d)],h+=n;for(l[0][0]=l[0][1].clone(),l[l.length-1][2]=l[l.length-1][1].clone(),i=(new SVG.Matrix).rotate(w).scale(y,v).rotate(-w),g=0,p=l.length;g<p;g++)l[g][0]=l[g][0].transform(i),l[g][1]=l[g][1].transform(i),l[g][2]=l[g][2].transform(i);for(g=1,p=l.length;g<p;g++)f=(u=l[g-1][2]).x,x=u.y,b=(u=l[g][0]).x,m=u.y,S=(u=l[g][1]).x,C=u.y,P.push(["C",f,x,b,m,S,C]);return P}(this.pos,t))[0]}return t[0]="C",this.pos=[t[5],t[6]],this.reflection=[2*t[5]-t[3],2*t[6]-t[4]],e}function ze(t,e){if(!1!==e)for(var i=e,s=t.length;i<s;++i)if("M"==t[i][0])return i;return!1}if(f="undefined"!=typeof window?window:void 0,x=function(a,n){var d=(void 0!==this?this:a).SVG=function(t){if(d.supported)return t=new d.Doc(t),d.parser.draw||d.prepare(),t};if(d.ns="http://www.w3.org/2000/svg",d.xmlns="http://www.w3.org/2000/xmlns/",d.xlink="http://www.w3.org/1999/xlink",d.svgjs="http://svgjs.com/svgjs",d.supported=!0,!d.supported)return!1;d.did=1e3,d.eid=function(t){return"Svgjs"+r(t)+d.did++},d.create=function(t){var e=n.createElementNS(this.ns,t);return e.setAttribute("id",this.eid(t)),e},d.extend=function(){for(var t,e,i=(t=[].slice.call(arguments)).pop(),s=t.length-1;0<=s;s--)if(t[s])for(e in i)t[s].prototype[e]=i[e];d.Set&&d.Set.inherit&&d.Set.inherit()},d.invent=function(t){var e="function"==typeof t.create?t.create:function(){this.constructor.call(this,d.create(t.create))};return t.inherit&&(e.prototype=new t.inherit),t.extend&&d.extend(e,t.extend),t.construct&&d.extend(t.parent||d.Container,t.construct),e},d.adopt=function(t){return t?t.instance||((e="svg"==t.nodeName?new(t.parentNode instanceof a.SVGElement?d.Nested:d.Doc):"linearGradient"==t.nodeName?new d.Gradient("linear"):"radialGradient"==t.nodeName?new d.Gradient("radial"):d[r(t.nodeName)]?new d[r(t.nodeName)]:new d.Element(t)).type=t.nodeName,((e.node=t).instance=e)instanceof d.Doc&&e.namespace().defs(),e.setData(JSON.parse(t.getAttribute("svgjs:data"))||{}),e):null;var e},d.prepare=function(){var t=n.getElementsByTagName("body")[0],e=(t?new d.Doc(t):d.adopt(n.documentElement).nested()).size(2,0);d.parser={body:t||n.documentElement,draw:e.style("opacity:0;position:absolute;left:-100%;top:-100%;overflow:hidden").node,poly:e.polyline().node,path:e.path().node,native:d.create("svg")}},d.parser={native:d.create("svg")},n.addEventListener("DOMContentLoaded",function(){d.parser.draw||d.prepare()},!1),d.regex={numberAndUnit:/^([+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?)([a-z%]*)$/i,hex:/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,rgb:/rgb\((\d+),(\d+),(\d+)\)/,reference:/#([a-z0-9\-_]+)/i,transforms:/\)\s*,?\s*/,whitespace:/\s/g,isHex:/^#[a-f0-9]{3,6}$/i,isRgb:/^rgb\(/,isCss:/[^:]+:[^;]+;?/,isBlank:/^(\s+)?$/,isNumber:/^[+-]?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,isPercent:/^-?[\d\.]+%$/,isImage:/\.(jpg|jpeg|png|gif|svg)(\?[^=]+.*)?/i,delimiter:/[\s,]+/,hyphen:/([^e])\-/gi,pathLetters:/[MLHVCSQTAZ]/gi,isPathLetter:/[MLHVCSQTAZ]/i,numbersWithDots:/((\d?\.\d+(?:e[+-]?\d+)?)((?:\.\d+(?:e[+-]?\d+)?)+))+/gi,dots:/\./g},d.utils={map:function(t,e){for(var i=t.length,s=[],a=0;a<i;a++)s.push(e(t[a]));return s},filter:function(t,e){for(var i=t.length,s=[],a=0;a<i;a++)e(t[a])&&s.push(t[a]);return s},radians:function(t){return t%360*Math.PI/180},degrees:function(t){return 180*t/Math.PI%360},filterSVGElements:function(t){return this.filter(t,function(t){return t instanceof a.SVGElement})}},d.defaults={attrs:{"fill-opacity":1,"stroke-opacity":1,"stroke-width":0,"stroke-linejoin":"miter","stroke-linecap":"butt",fill:"#000000",stroke:"#000000",opacity:1,x:0,y:0,cx:0,cy:0,width:0,height:0,r:0,rx:0,ry:0,offset:0,"stop-opacity":1,"stop-color":"#000000","font-size":16,"font-family":"Helvetica, Arial, sans-serif","text-anchor":"start"}},d.Color=function(t){var e,i;this.r=0,this.g=0,this.b=0,t&&("string"==typeof t?d.regex.isRgb.test(t)?(e=d.regex.rgb.exec(t.replace(d.regex.whitespace,"")),this.r=parseInt(e[1]),this.g=parseInt(e[2]),this.b=parseInt(e[3])):d.regex.isHex.test(t)&&(e=d.regex.hex.exec(4==(i=t).length?["#",i.substring(1,2),i.substring(1,2),i.substring(2,3),i.substring(2,3),i.substring(3,4),i.substring(3,4)].join(""):i),this.r=parseInt(e[1],16),this.g=parseInt(e[2],16),this.b=parseInt(e[3],16)):"object"===C(t)&&(this.r=t.r,this.g=t.g,this.b=t.b))},d.extend(d.Color,{toString:function(){return this.toHex()},toHex:function(){return"#"+c(this.r)+c(this.g)+c(this.b)},toRgb:function(){return"rgb("+[this.r,this.g,this.b].join()+")"},brightness:function(){return this.r/255*.3+this.g/255*.59+this.b/255*.11},morph:function(t){return this.destination=new d.Color(t),this},at:function(t){return this.destination?new d.Color({r:~~(this.r+(this.destination.r-this.r)*(t=t<0?0:1<t?1:t)),g:~~(this.g+(this.destination.g-this.g)*t),b:~~(this.b+(this.destination.b-this.b)*t)}):this}}),d.Color.test=function(t){return d.regex.isHex.test(t+="")||d.regex.isRgb.test(t)},d.Color.isRgb=function(t){return t&&"number"==typeof t.r&&"number"==typeof t.g&&"number"==typeof t.b},d.Color.isColor=function(t){return d.Color.isRgb(t)||d.Color.test(t)},d.Array=function(t,e){0==(t=(t||[]).valueOf()).length&&e&&(t=e.valueOf()),this.value=this.parse(t)},d.extend(d.Array,{morph:function(t){if(this.destination=this.parse(t),this.value.length!=this.destination.length){for(var e=this.value[this.value.length-1],i=this.destination[this.destination.length-1];this.value.length>this.destination.length;)this.destination.push(i);for(;this.value.length<this.destination.length;)this.value.push(e)}return this},settle:function(){for(var t=0,e=this.value.length,i=[];t<e;t++)-1==i.indexOf(this.value[t])&&i.push(this.value[t]);return this.value=i},at:function(t){if(!this.destination)return this;for(var e=0,i=this.value.length,s=[];e<i;e++)s.push(this.value[e]+(this.destination[e]-this.value[e])*t);return new d.Array(s)},toString:function(){return this.value.join(" ")},valueOf:function(){return this.value},parse:function(t){return t=t.valueOf(),Array.isArray(t)?t:this.split(t)},split:function(t){return t.trim().split(d.regex.delimiter).map(parseFloat)},reverse:function(){return this.value.reverse(),this},clone:function(){var t=new this.constructor;return t.value=function t(e){for(var i=e.slice(0),s=i.length;s--;)Array.isArray(i[s])&&(i[s]=t(i[s]));return i}(this.value),t}}),d.PointArray=function(t,e){d.Array.call(this,t,e||[[0,0]])},d.PointArray.prototype=new d.Array,d.PointArray.prototype.constructor=d.PointArray,d.extend(d.PointArray,{toString:function(){for(var t=0,e=this.value.length,i=[];t<e;t++)i.push(this.value[t].join(","));return i.join(" ")},toLine:function(){return{x1:this.value[0][0],y1:this.value[0][1],x2:this.value[1][0],y2:this.value[1][1]}},at:function(t){if(!this.destination)return this;for(var e=0,i=this.value.length,s=[];e<i;e++)s.push([this.value[e][0]+(this.destination[e][0]-this.value[e][0])*t,this.value[e][1]+(this.destination[e][1]-this.value[e][1])*t]);return new d.PointArray(s)},parse:function(t){var e=[];if(t=t.valueOf(),Array.isArray(t)){if(Array.isArray(t[0]))return t.map(function(t){return t.slice()});if(null!=t[0].x)return t.map(function(t){return[t.x,t.y]})}else t=t.trim().split(d.regex.delimiter).map(parseFloat);t.length%2!=0&&t.pop();for(var i=0,s=t.length;i<s;i+=2)e.push([t[i],t[i+1]]);return e},move:function(t,e){var i=this.bbox();if(t-=i.x,e-=i.y,!isNaN(t)&&!isNaN(e))for(var s=this.value.length-1;0<=s;s--)this.value[s]=[this.value[s][0]+t,this.value[s][1]+e];return this},size:function(t,e){for(var i=this.bbox(),s=this.value.length-1;0<=s;s--)i.width&&(this.value[s][0]=(this.value[s][0]-i.x)*t/i.width+i.x),i.height&&(this.value[s][1]=(this.value[s][1]-i.y)*e/i.height+i.y);return this},bbox:function(){return d.parser.draw||d.prepare(),d.parser.poly.setAttribute("points",this.toString()),d.parser.poly.getBBox()}});for(var l={M:function(t,e,i){return e.x=i.x=t[0],e.y=i.y=t[1],["M",e.x,e.y]},L:function(t,e){return e.x=t[0],e.y=t[1],["L",t[0],t[1]]},H:function(t,e){return e.x=t[0],["H",t[0]]},V:function(t,e){return e.y=t[0],["V",t[0]]},C:function(t,e){return e.x=t[4],e.y=t[5],["C",t[0],t[1],t[2],t[3],t[4],t[5]]},S:function(t,e){return e.x=t[2],e.y=t[3],["S",t[0],t[1],t[2],t[3]]},Q:function(t,e){return e.x=t[2],e.y=t[3],["Q",t[0],t[1],t[2],t[3]]},T:function(t,e){return e.x=t[0],e.y=t[1],["T",t[0],t[1]]},Z:function(t,e,i){return e.x=i.x,e.y=i.y,["Z"]},A:function(t,e){return e.x=t[5],e.y=t[6],["A",t[0],t[1],t[2],t[3],t[4],t[5],t[6]]}},t="mlhvqtcsaz".split(""),e=0,i=t.length;e<i;++e)l[t[e]]=function(n){return function(t,e,i){if("H"==n)t[0]=t[0]+e.x;else if("V"==n)t[0]=t[0]+e.y;else if("A"==n)t[5]=t[5]+e.x,t[6]=t[6]+e.y;else for(var s=0,a=t.length;s<a;++s)t[s]=t[s]+(s%2?e.y:e.x);return l[n](t,e,i)}}(t[e].toUpperCase());d.PathArray=function(t,e){d.Array.call(this,t,e||[["M",0,0]])},d.PathArray.prototype=new d.Array,d.PathArray.prototype.constructor=d.PathArray,d.extend(d.PathArray,{toString:function(){for(var t=this.value,e=0,i=t.length,s="";e<i;e++)s+=t[e][0],null!=t[e][1]&&(s+=t[e][1],null!=t[e][2])&&(s=s+" "+t[e][2],null!=t[e][3])&&(s=(s=s+" "+t[e][3])+" "+t[e][4],null!=t[e][5])&&(s=(s=s+" "+t[e][5])+" "+t[e][6],null!=t[e][7])&&(s=s+" "+t[e][7]);return s+" "},move:function(t,e){var i=this.bbox();if(t-=i.x,e-=i.y,!isNaN(t)&&!isNaN(e))for(var s,a=this.value.length-1;0<=a;a--)"M"==(s=this.value[a][0])||"L"==s||"T"==s?(this.value[a][1]+=t,this.value[a][2]+=e):"H"==s?this.value[a][1]+=t:"V"==s?this.value[a][1]+=e:"C"==s||"S"==s||"Q"==s?(this.value[a][1]+=t,this.value[a][2]+=e,this.value[a][3]+=t,this.value[a][4]+=e,"C"==s&&(this.value[a][5]+=t,this.value[a][6]+=e)):"A"==s&&(this.value[a][6]+=t,this.value[a][7]+=e);return this},size:function(t,e){for(var i,s=this.bbox(),a=this.value.length-1;0<=a;a--)"M"==(i=this.value[a][0])||"L"==i||"T"==i?(this.value[a][1]=(this.value[a][1]-s.x)*t/s.width+s.x,this.value[a][2]=(this.value[a][2]-s.y)*e/s.height+s.y):"H"==i?this.value[a][1]=(this.value[a][1]-s.x)*t/s.width+s.x:"V"==i?this.value[a][1]=(this.value[a][1]-s.y)*e/s.height+s.y:"C"==i||"S"==i||"Q"==i?(this.value[a][1]=(this.value[a][1]-s.x)*t/s.width+s.x,this.value[a][2]=(this.value[a][2]-s.y)*e/s.height+s.y,this.value[a][3]=(this.value[a][3]-s.x)*t/s.width+s.x,this.value[a][4]=(this.value[a][4]-s.y)*e/s.height+s.y,"C"==i&&(this.value[a][5]=(this.value[a][5]-s.x)*t/s.width+s.x,this.value[a][6]=(this.value[a][6]-s.y)*e/s.height+s.y)):"A"==i&&(this.value[a][1]=this.value[a][1]*t/s.width,this.value[a][2]=this.value[a][2]*e/s.height,this.value[a][6]=(this.value[a][6]-s.x)*t/s.width+s.x,this.value[a][7]=(this.value[a][7]-s.y)*e/s.height+s.y);return this},equalCommands:function(t){var e,i,s;for(t=new d.PathArray(t),s=this.value.length===t.value.length,e=0,i=this.value.length;s&&e<i;e++)s=this.value[e][0]===t.value[e][0];return s},morph:function(t){return t=new d.PathArray(t),this.equalCommands(t)?this.destination=t:this.destination=null,this},at:function(t){if(!this.destination)return this;for(var e,i,s=this.value,a=this.destination.value,n=[],o=new d.PathArray,r=0,l=s.length;r<l;r++){for(n[r]=[s[r][0]],e=1,i=s[r].length;e<i;e++)n[r][e]=s[r][e]+(a[r][e]-s[r][e])*t;"A"===n[r][0]&&(n[r][4]=+(0!=n[r][4]),n[r][5]=+(0!=n[r][5]))}return o.value=n,o},parse:function(t){if(t instanceof d.PathArray)return t.valueOf();var e,i={M:2,L:2,H:1,V:1,C:6,S:4,Q:4,T:2,A:7,Z:0};t="string"==typeof t?t.replace(d.regex.numbersWithDots,h).replace(d.regex.pathLetters," $& ").replace(d.regex.hyphen,"$1 -").trim().split(d.regex.delimiter):t.reduce(function(t,e){return[].concat.call(t,e)},[]);for(var s=[],a=new d.Point,n=new d.Point,o=0,r=t.length;d.regex.isPathLetter.test(t[o])?(e=t[o],++o):"M"==e?e="L":"m"==e&&(e="l"),s.push(l[e].call(null,t.slice(o,o+=i[e.toUpperCase()]).map(parseFloat),a,n)),o<r;);return s},bbox:function(){return d.parser.draw||d.prepare(),d.parser.path.setAttribute("d",this.toString()),d.parser.path.getBBox()}}),d.Number=d.invent({create:function(t,e){this.value=0,this.unit=e||"","number"==typeof t?this.value=isNaN(t)?0:isFinite(t)?t:t<0?-34e37:34e37:"string"==typeof t?(e=t.match(d.regex.numberAndUnit))&&(this.value=parseFloat(e[1]),"%"==e[5]?this.value/=100:"s"==e[5]&&(this.value*=1e3),this.unit=e[5]):t instanceof d.Number&&(this.value=t.valueOf(),this.unit=t.unit)},extend:{toString:function(){return("%"==this.unit?~~(1e8*this.value)/1e6:"s"==this.unit?this.value/1e3:this.value)+this.unit},toJSON:function(){return this.toString()},valueOf:function(){return this.value},plus:function(t){return t=new d.Number(t),new d.Number(this+t,this.unit||t.unit)},minus:function(t){return t=new d.Number(t),new d.Number(this-t,this.unit||t.unit)},times:function(t){return t=new d.Number(t),new d.Number(this*t,this.unit||t.unit)},divide:function(t){return t=new d.Number(t),new d.Number(this/t,this.unit||t.unit)},to:function(t){var e=new d.Number(this);return"string"==typeof t&&(e.unit=t),e},morph:function(t){return this.destination=new d.Number(t),t.relative&&(this.destination.value+=this.value),this},at:function(t){return this.destination?new d.Number(this.destination).minus(this).times(t).plus(this):this}}}),d.Element=d.invent({create:function(t){this._stroke=d.defaults.attrs.stroke,this._event=null,this.dom={},(this.node=t)&&(this.type=t.nodeName,(this.node.instance=this)._stroke=t.getAttribute("stroke")||this._stroke)},extend:{x:function(t){return this.attr("x",t)},y:function(t){return this.attr("y",t)},cx:function(t){return null==t?this.x()+this.width()/2:this.x(t-this.width()/2)},cy:function(t){return null==t?this.y()+this.height()/2:this.y(t-this.height()/2)},move:function(t,e){return this.x(t).y(e)},center:function(t,e){return this.cx(t).cy(e)},width:function(t){return this.attr("width",t)},height:function(t){return this.attr("height",t)},size:function(t,e){return e=u(this,t,e),this.width(new d.Number(e.width)).height(new d.Number(e.height))},clone:function(t){this.writeDataToDom();var e=x(this.node.cloneNode(!0));return t?t.add(e):this.after(e),e},remove:function(){return this.parent()&&this.parent().removeElement(this),this},replace:function(t){return this.after(t).remove(),t},addTo:function(t){return t.put(this)},putIn:function(t){return t.add(this)},id:function(t){return this.attr("id",t)},inside:function(t,e){var i=this.bbox();return t>i.x&&e>i.y&&t<i.x+i.width&&e<i.y+i.height},show:function(){return this.style("display","")},hide:function(){return this.style("display","none")},visible:function(){return"none"!=this.style("display")},toString:function(){return this.attr("id")},classes:function(){var t=this.attr("class");return null==t?[]:t.trim().split(d.regex.delimiter)},hasClass:function(t){return-1!=this.classes().indexOf(t)},addClass:function(t){var e;return this.hasClass(t)||((e=this.classes()).push(t),this.attr("class",e.join(" "))),this},removeClass:function(e){return this.hasClass(e)&&this.attr("class",this.classes().filter(function(t){return t!=e}).join(" ")),this},toggleClass:function(t){return this.hasClass(t)?this.removeClass(t):this.addClass(t)},reference:function(t){return d.get(this.attr(t))},parent:function(t){var e=this;if(!e.node.parentNode)return null;if(e=d.adopt(e.node.parentNode),!t)return e;for(;e&&e.node instanceof a.SVGElement;){if("string"==typeof t?e.matches(t):e instanceof t)return e;if(!e.node.parentNode||"#document"==e.node.parentNode.nodeName)return null;e=d.adopt(e.node.parentNode)}},doc:function(){return this instanceof d.Doc?this:this.parent(d.Doc)},parents:function(t){for(var e=[],i=this;(i=i.parent(t))&&i.node&&(e.push(i),i.parent););return e},matches:function(t){return((e=this.node).matches||e.matchesSelector||e.msMatchesSelector||e.mozMatchesSelector||e.webkitMatchesSelector||e.oMatchesSelector).call(e,t);var e},native:function(){return this.node},svg:function(t){var e=n.createElement("svg");if(!(t&&this instanceof d.Parent))return e.appendChild(t=n.createElement("svg")),this.writeDataToDom(),t.appendChild(this.node.cloneNode(!0)),e.innerHTML.replace(/^<svg>/,"").replace(/<\/svg>$/,"");e.innerHTML="<svg>"+t.replace(/\n/,"").replace(/<([\w:-]+)([^<]+?)\/>/g,"<$1$2></$1>")+"</svg>";for(var i=0,s=e.firstChild.childNodes.length;i<s;i++)this.node.appendChild(e.firstChild.firstChild);return this},writeDataToDom:function(){return(this.each||this.lines)&&(this.each?this:this.lines()).each(function(){this.writeDataToDom()}),this.node.removeAttribute("svgjs:data"),Object.keys(this.dom).length&&this.node.setAttribute("svgjs:data",JSON.stringify(this.dom)),this},setData:function(t){return this.dom=t,this},is:function(t){return this instanceof t}}}),d.easing={"-":function(t){return t},"<>":function(t){return-Math.cos(t*Math.PI)/2+.5},">":function(t){return Math.sin(t*Math.PI/2)},"<":function(t){return 1-Math.cos(t*Math.PI/2)}},d.morph=function(i){return function(t,e){return new d.MorphObj(t,e).at(i)}},d.Situation=d.invent({create:function(t){this.init=!1,this.reversed=!1,this.reversing=!1,this.duration=new d.Number(t.duration).valueOf(),this.delay=new d.Number(t.delay).valueOf(),this.start=+new Date+this.delay,this.finish=this.start+this.duration,this.ease=t.ease,this.loop=0,this.loops=!1,this.animations={},this.attrs={},this.styles={},this.transforms=[],this.once={}}}),d.FX=d.invent({create:function(t){this._target=t,this.situations=[],this.active=!1,this.situation=null,this.paused=!1,this.lastPos=0,this.pos=0,this.absPos=0,this._speed=1},extend:{animate:function(t,e,i){return"object"===C(t)&&(e=t.ease,i=t.delay,t=t.duration),e=new d.Situation({duration:t||1e3,delay:i||0,ease:d.easing[e||"-"]||e}),this.queue(e),this},delay:function(t){return t=new d.Situation({duration:t,delay:0,ease:d.easing["-"]}),this.queue(t)},target:function(t){return t&&t instanceof d.Element?(this._target=t,this):this._target},timeToAbsPos:function(t){return(t-this.situation.start)/(this.situation.duration/this._speed)},absPosToTime:function(t){return this.situation.duration/this._speed*t+this.situation.start},startAnimFrame:function(){this.stopAnimFrame(),this.animationFrame=a.requestAnimationFrame(function(){this.step()}.bind(this))},stopAnimFrame:function(){a.cancelAnimationFrame(this.animationFrame)},start:function(){return!this.active&&this.situation&&(this.active=!0,this.startCurrent()),this},startCurrent:function(){return this.situation.start=+new Date+this.situation.delay/this._speed,this.situation.finish=this.situation.start+this.situation.duration/this._speed,this.initAnimations().step()},queue:function(t){return("function"==typeof t||t instanceof d.Situation)&&this.situations.push(t),this.situation||(this.situation=this.situations.shift()),this},dequeue:function(){return this.stop(),this.situation=this.situations.shift(),this.situation&&(this.situation instanceof d.Situation?this.start():this.situation.call(this)),this},initAnimations:function(){var t,e,i,s=this.situation;if(!s.init){for(t in s.animations)for(i=this.target()[t](),Array.isArray(i)||(i=[i]),Array.isArray(s.animations[t])||(s.animations[t]=[s.animations[t]]),e=i.length;e--;)s.animations[t][e]instanceof d.Number&&(i[e]=new d.Number(i[e])),s.animations[t][e]=i[e].morph(s.animations[t][e]);for(t in s.attrs)s.attrs[t]=new d.MorphObj(this.target().attr(t),s.attrs[t]);for(t in s.styles)s.styles[t]=new d.MorphObj(this.target().style(t),s.styles[t]);s.initialTransformation=this.target().matrixify(),s.init=!0}return this},clearQueue:function(){return this.situations=[],this},clearCurrent:function(){return this.situation=null,this},stop:function(t,e){var i=this.active;return this.active=!1,e&&this.clearQueue(),t&&this.situation&&(i||this.startCurrent(),this.atEnd()),this.stopAnimFrame(),this.clearCurrent()},reset:function(){var t;return this.situation&&(t=this.situation,this.stop(),this.situation=t,this.atStart()),this},finish:function(){for(this.stop(!0,!1);this.dequeue().situation&&this.stop(!0,!1););return this.clearQueue().clearCurrent(),this},atStart:function(){return this.at(0,!0)},atEnd:function(){return!0===this.situation.loops&&(this.situation.loops=this.situation.loop+1),"number"==typeof this.situation.loops?this.at(this.situation.loops,!0):this.at(1,!0)},at:function(t,e){var i=this.situation.duration/this._speed;return this.absPos=t,e||(this.situation.reversed&&(this.absPos=1-this.absPos),this.absPos+=this.situation.loop),this.situation.start=+new Date-this.absPos*i,this.situation.finish=this.situation.start+i,this.step(!0)},speed:function(t){return 0===t?this.pause():t?(this._speed=t,this.at(this.absPos,!0)):this._speed},loop:function(t,e){var i=this.last();return i.loops=null==t||t,i.loop=0,e&&(i.reversing=!0),this},pause:function(){return this.paused=!0,this.stopAnimFrame(),this},play:function(){return this.paused?(this.paused=!1,this.at(this.absPos,!0)):this},reverse:function(t){var e=this.last();return e.reversed=void 0===t?!e.reversed:t,this},progress:function(t){return t?this.situation.ease(this.pos):this.pos},after:function(i){var s=this.last();return this.target().on("finished.fx",function t(e){e.detail.situation==s&&(i.call(this,s),this.off("finished.fx",t))}),this._callStart()},during:function(e){function t(t){t.detail.situation==i&&e.call(this,t.detail.pos,d.morph(t.detail.pos),t.detail.eased,i)}var i=this.last();return this.target().off("during.fx",t).on("during.fx",t),this.after(function(){this.off("during.fx",t)}),this._callStart()},afterAll:function(e){function i(t){e.call(this),this.off("allfinished.fx",i)}return this.target().off("allfinished.fx",i).on("allfinished.fx",i),this._callStart()},duringAll:function(e){function t(t){e.call(this,t.detail.pos,d.morph(t.detail.pos),t.detail.eased,t.detail.situation)}return this.target().off("during.fx",t).on("during.fx",t),this.afterAll(function(){this.off("during.fx",t)}),this._callStart()},last:function(){return this.situations.length?this.situations[this.situations.length-1]:this.situation},add:function(t,e,i){return this.last()[i||"animations"][t]=e,this._callStart()},step:function(t){var e,i;t||(this.absPos=this.timeToAbsPos(+new Date)),!1!==this.situation.loops?(e=Math.max(this.absPos,0),t=Math.floor(e),!0===this.situation.loops||t<this.situation.loops?(this.pos=e-t,i=this.situation.loop,this.situation.loop=t):(this.absPos=this.situation.loops,this.pos=1,i=this.situation.loop-1,this.situation.loop=this.situation.loops),this.situation.reversing&&(this.situation.reversed=this.situation.reversed!=Boolean((this.situation.loop-i)%2))):(this.absPos=Math.min(this.absPos,1),this.pos=this.absPos),this.pos<0&&(this.pos=0),this.situation.reversed&&(this.pos=1-this.pos);var s,a=this.situation.ease(this.pos);for(s in this.situation.once)s>this.lastPos&&s<=a&&(this.situation.once[s].call(this.target(),this.pos,a),delete this.situation.once[s]);return this.active&&this.target().fire("during",{pos:this.pos,eased:a,fx:this,situation:this.situation}),this.situation&&(this.eachAt(),1==this.pos&&!this.situation.reversed||this.situation.reversed&&0==this.pos?(this.stopAnimFrame(),this.target().fire("finished",{fx:this,situation:this.situation}),this.situations.length||(this.target().fire("allfinished"),this.situations.length)||(this.target().off(".fx"),this.active=!1),this.active?this.dequeue():this.clearCurrent()):!this.paused&&this.active&&this.startAnimFrame(),this.lastPos=a),this},eachAt:function(){var t,e,i=this,s=this.target(),a=this.situation;for(t in a.animations)o=[].concat(a.animations[t]).map(function(t){return"string"!=typeof t&&t.at?t.at(a.ease(i.pos),i.pos):t}),s[t].apply(s,o);for(t in a.attrs)o=[t].concat(a.attrs[t]).map(function(t){return"string"!=typeof t&&t.at?t.at(a.ease(i.pos),i.pos):t}),s.attr.apply(s,o);for(t in a.styles)o=[t].concat(a.styles[t]).map(function(t){return"string"!=typeof t&&t.at?t.at(a.ease(i.pos),i.pos):t}),s.style.apply(s,o);if(a.transforms.length){for(o=a.initialTransformation,t=0,e=a.transforms.length;t<e;t++)var n=a.transforms[t],o=n instanceof d.Matrix?n.relative?o.multiply((new d.Matrix).morph(n).at(a.ease(this.pos))):o.morph(n).at(a.ease(this.pos)):(n.relative||n.undo(o.extract()),o.multiply(n.at(a.ease(this.pos))));s.matrix(o)}return this},once:function(t,e,i){var s=this.last();return i||(t=s.ease(t)),s.once[t]=e,this},_callStart:function(){return setTimeout(function(){this.start()}.bind(this),0),this}},parent:d.Element,construct:{animate:function(t,e,i){return(this.fx||(this.fx=new d.FX(this))).animate(t,e,i)},delay:function(t){return(this.fx||(this.fx=new d.FX(this))).delay(t)},stop:function(t,e){return this.fx&&this.fx.stop(t,e),this},finish:function(){return this.fx&&this.fx.finish(),this},pause:function(){return this.fx&&this.fx.pause(),this},play:function(){return this.fx&&this.fx.play(),this},speed:function(t){if(this.fx){if(null==t)return this.fx.speed();this.fx.speed(t)}return this}}}),d.MorphObj=d.invent({create:function(t,e){return d.Color.isColor(e)?new d.Color(t).morph(e):d.regex.delimiter.test(t)?new(d.regex.pathLetters.test(t)?d.PathArray:d.Array)(t).morph(e):d.regex.numberAndUnit.test(e)?new d.Number(t).morph(e):(this.value=t,void(this.destination=e))},extend:{at:function(t,e){return e<1?this.value:this.destination},valueOf:function(){return this.value}}}),d.extend(d.FX,{attr:function(t,e,i){if("object"===C(t))for(var s in t)this.attr(s,t[s]);else this.add(t,e,"attrs");return this},style:function(t,e){if("object"===C(t))for(var i in t)this.style(i,t[i]);else this.add(t,e,"styles");return this},x:function(t,e){return this.target()instanceof d.G?(this.transform({x:t},e),this):((t=new d.Number(t)).relative=e,this.add("x",t))},y:function(t,e){return this.target()instanceof d.G?(this.transform({y:t},e),this):((t=new d.Number(t)).relative=e,this.add("y",t))},cx:function(t){return this.add("cx",new d.Number(t))},cy:function(t){return this.add("cy",new d.Number(t))},move:function(t,e){return this.x(t).y(e)},center:function(t,e){return this.cx(t).cy(e)},size:function(t,e){var i;return this.target()instanceof d.Text?this.attr("font-size",t):(t&&e||(i=this.target().bbox()),t=t||i.width/i.height*e,e=e||i.height/i.width*t,this.add("width",new d.Number(t)).add("height",new d.Number(e))),this},width:function(t){return this.add("width",new d.Number(t))},height:function(t){return this.add("height",new d.Number(t))},plot:function(t,e,i,s){return 4==arguments.length?this.plot([t,e,i,s]):this.add("plot",new(this.target().morphArray)(t))},leading:function(t){return this.target().leading?this.add("leading",new d.Number(t)):this},viewbox:function(t,e,i,s){return this.target()instanceof d.Container&&this.add("viewbox",new d.ViewBox(t,e,i,s)),this},update:function(t){if(this.target()instanceof d.Stop){if("number"==typeof t||t instanceof d.Number)return this.update({offset:t,color:arguments[1],opacity:arguments[2]});null!=t.opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",t.offset)}return this}}),d.Box=d.invent({create:function(t,e,i,s){if(!("object"!==C(t)||t instanceof d.Element))return d.Box.call(this,null!=t.left?t.left:t.x,null!=t.top?t.top:t.y,t.width,t.height);4==arguments.length&&(this.x=t,this.y=e,this.width=i,this.height=s),b(this)},extend:{merge:function(t){var e=new this.constructor;return e.x=Math.min(this.x,t.x),e.y=Math.min(this.y,t.y),e.width=Math.max(this.x+this.width,t.x+t.width)-e.x,e.height=Math.max(this.y+this.height,t.y+t.height)-e.y,b(e)},transform:function(e){var t,i=1/0,s=-1/0,a=1/0,n=-1/0;return[new d.Point(this.x,this.y),new d.Point(this.x2,this.y),new d.Point(this.x,this.y2),new d.Point(this.x2,this.y2)].forEach(function(t){t=t.transform(e),i=Math.min(i,t.x),s=Math.max(s,t.x),a=Math.min(a,t.y),n=Math.max(n,t.y)}),(t=new this.constructor).x=i,t.width=s-i,t.y=a,t.height=n-a,b(t),t}}}),d.BBox=d.invent({create:function(t){if(d.Box.apply(this,[].slice.call(arguments)),t instanceof d.Element){var e,i;try{if(!n.documentElement.contains){for(var s=t.node;s.parentNode;)s=s.parentNode;if(s!=n)throw new Error("Element not in the dom")}i=t.node.getBBox()}catch(s){t instanceof d.Shape?(d.parser.draw||d.prepare(),i=(e=t.clone(d.parser.draw.instance).show()).node.getBBox(),e.remove()):i={x:t.node.clientLeft,y:t.node.clientTop,width:t.node.clientWidth,height:t.node.clientHeight}}d.Box.call(this,i)}},inherit:d.Box,parent:d.Element,construct:{bbox:function(){return new d.BBox(this)}}}),d.BBox.prototype.constructor=d.BBox,d.extend(d.Element,{tbox:function(){return console.warn("Use of TBox is deprecated and mapped to RBox. Use .rbox() instead."),this.rbox(this.doc())}}),d.RBox=d.invent({create:function(t){d.Box.apply(this,[].slice.call(arguments)),t instanceof d.Element&&d.Box.call(this,t.node.getBoundingClientRect())},inherit:d.Box,parent:d.Element,extend:{addOffset:function(){return this.x+=a.pageXOffset,this.y+=a.pageYOffset,this}},construct:{rbox:function(t){return t?new d.RBox(this).transform(t.screenCTM().inverse()):new d.RBox(this).addOffset()}}}),d.RBox.prototype.constructor=d.RBox,d.Matrix=d.invent({create:function(t){var e,i=p([1,0,0,1,0,0]);for(t=t instanceof d.Element?t.matrixify():"string"==typeof t?p(t.split(d.regex.delimiter).map(parseFloat)):6==arguments.length?p([].slice.call(arguments)):Array.isArray(t)?p(t):"object"===C(t)?t:i,e=v.length-1;0<=e;--e)this[v[e]]=(null!=t[v[e]]?t:i)[v[e]]},extend:{extract:function(){var t=g(this,0,1),e=g(this,1,0),t=180/Math.PI*Math.atan2(t.y,t.x)-90;return{x:this.e,y:this.f,transformedX:(this.e*Math.cos(t*Math.PI/180)+this.f*Math.sin(t*Math.PI/180))/Math.sqrt(this.a*this.a+this.b*this.b),transformedY:(this.f*Math.cos(t*Math.PI/180)+this.e*Math.sin(-t*Math.PI/180))/Math.sqrt(this.c*this.c+this.d*this.d),skewX:-t,skewY:180/Math.PI*Math.atan2(e.y,e.x),scaleX:Math.sqrt(this.a*this.a+this.b*this.b),scaleY:Math.sqrt(this.c*this.c+this.d*this.d),rotation:t,a:this.a,b:this.b,c:this.c,d:this.d,e:this.e,f:this.f,matrix:new d.Matrix(this)}},clone:function(){return new d.Matrix(this)},morph:function(t){return this.destination=new d.Matrix(t),this},at:function(t){return this.destination?new d.Matrix({a:this.a+(this.destination.a-this.a)*t,b:this.b+(this.destination.b-this.b)*t,c:this.c+(this.destination.c-this.c)*t,d:this.d+(this.destination.d-this.d)*t,e:this.e+(this.destination.e-this.e)*t,f:this.f+(this.destination.f-this.f)*t}):this},multiply:function(t){return new d.Matrix(this.native().multiply((t=t instanceof d.Matrix?t:new d.Matrix(t)).native()))},inverse:function(){return new d.Matrix(this.native().inverse())},translate:function(t,e){return new d.Matrix(this.native().translate(t||0,e||0))},scale:function(t,e,i,s){return 1==arguments.length?e=t:3==arguments.length&&(s=i,i=e,e=t),this.around(i,s,new d.Matrix(t,0,0,e,0,0))},rotate:function(t,e,i){return t=d.utils.radians(t),this.around(e,i,new d.Matrix(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0))},flip:function(t,e){return"x"==t?this.scale(-1,1,e,0):"y"==t?this.scale(1,-1,0,e):this.scale(-1,-1,t,null!=e?e:t)},skew:function(t,e,i,s){return 1==arguments.length?e=t:3==arguments.length&&(s=i,i=e,e=t),t=d.utils.radians(t),e=d.utils.radians(e),this.around(i,s,new d.Matrix(1,Math.tan(e),Math.tan(t),1,0,0))},skewX:function(t,e,i){return this.skew(t,0,e,i)},skewY:function(t,e,i){return this.skew(0,t,e,i)},around:function(t,e,i){return this.multiply(new d.Matrix(1,0,0,1,t||0,e||0)).multiply(i).multiply(new d.Matrix(1,0,0,1,-t||0,-e||0))},native:function(){for(var t=d.parser.native.createSVGMatrix(),e=v.length-1;0<=e;e--)t[v[e]]=this[v[e]];return t},toString:function(){return"matrix("+m(this.a)+","+m(this.b)+","+m(this.c)+","+m(this.d)+","+m(this.e)+","+m(this.f)+")"}},parent:d.Element,construct:{ctm:function(){return new d.Matrix(this.node.getCTM())},screenCTM:function(){var t,e;return this instanceof d.Nested?(e=(t=this.rect(1,1)).node.getScreenCTM(),t.remove(),new d.Matrix(e)):new d.Matrix(this.node.getScreenCTM())}}}),d.Point=d.invent({create:function(t,e){t=Array.isArray(t)?{x:t[0],y:t[1]}:"object"===C(t)?{x:t.x,y:t.y}:null!=t?{x:t,y:null!=e?e:t}:{x:0,y:0},this.x=t.x,this.y=t.y},extend:{clone:function(){return new d.Point(this)},morph:function(t,e){return this.destination=new d.Point(t,e),this},at:function(t){return this.destination?new d.Point({x:this.x+(this.destination.x-this.x)*t,y:this.y+(this.destination.y-this.y)*t}):this},native:function(){var t=d.parser.native.createSVGPoint();return t.x=this.x,t.y=this.y,t},transform:function(t){return new d.Point(this.native().matrixTransform(t.native()))}}}),d.extend(d.Element,{point:function(t,e){return new d.Point(t,e).transform(this.screenCTM().inverse())}}),d.extend(d.Element,{attr:function(t,e,i){if(null==t){for(t={},i=(e=this.node.attributes).length-1;0<=i;i--)t[e[i].nodeName]=d.regex.isNumber.test(e[i].nodeValue)?parseFloat(e[i].nodeValue):e[i].nodeValue;return t}if("object"===C(t))for(e in t)this.attr(e,t[e]);else if(null===e)this.node.removeAttribute(t);else{if(null==e)return null==(e=this.node.getAttribute(t))?d.defaults.attrs[t]:d.regex.isNumber.test(e)?parseFloat(e):e;"stroke-width"==t?this.attr("stroke",0<parseFloat(e)?this._stroke:null):"stroke"==t&&(this._stroke=e),"fill"!=t&&"stroke"!=t||(e=d.regex.isImage.test(e)?this.doc().defs().image(e,0,0):e)instanceof d.Image&&(e=this.doc().defs().pattern(0,0,function(){this.add(e)})),"number"==typeof e?e=new d.Number(e):d.Color.isColor(e)?e=new d.Color(e):Array.isArray(e)&&(e=new d.Array(e)),"leading"==t?this.leading&&this.leading(e):"string"==typeof i?this.node.setAttributeNS(i,t,e.toString()):this.node.setAttribute(t,e.toString()),!this.rebuild||"font-size"!=t&&"x"!=t||this.rebuild(t,e)}return this}}),d.extend(d.Element,{transform:function(t,e){var i,s;return"object"!==C(t)?(i=new d.Matrix(this).extract(),"string"==typeof t?i[t]:i):(i=new d.Matrix(this),e=!!e||!!t.relative,null!=t.a?i=e?i.multiply(new d.Matrix(t)):new d.Matrix(t):null!=t.rotation?(f(t,this),i=e?i.rotate(t.rotation,t.cx,t.cy):i.rotate(t.rotation-i.extract().rotation,t.cx,t.cy)):null!=t.scale||null!=t.scaleX||null!=t.scaleY?(f(t,this),t.scaleX=null!=t.scale?t.scale:null!=t.scaleX?t.scaleX:1,t.scaleY=null!=t.scale?t.scale:null!=t.scaleY?t.scaleY:1,e||(s=i.extract(),t.scaleX=+t.scaleX/s.scaleX,t.scaleY=+t.scaleY/s.scaleY),i=i.scale(t.scaleX,t.scaleY,t.cx,t.cy)):null!=t.skew||null!=t.skewX||null!=t.skewY?(f(t,this),t.skewX=null!=t.skew?t.skew:null!=t.skewX?t.skewX:0,t.skewY=null!=t.skew?t.skew:null!=t.skewY?t.skewY:0,e||(s=i.extract(),i=i.multiply((new d.Matrix).skew(s.skewX,s.skewY,t.cx,t.cy).inverse())),i=i.skew(t.skewX,t.skewY,t.cx,t.cy)):t.flip?("x"==t.flip||"y"==t.flip?t.offset=null==t.offset?this.bbox()["c"+t.flip]:t.offset:null==t.offset?(s=this.bbox(),t.flip=s.cx,t.offset=s.cy):t.flip=t.offset,i=(new d.Matrix).flip(t.flip,t.offset)):null==t.x&&null==t.y||(e?i=i.translate(t.x,t.y):(null!=t.x&&(i.e=t.x),null!=t.y&&(i.f=t.y))),this.attr("transform",i))}}),d.extend(d.FX,{transform:function(t,e){var i,s=this.target();return"object"!==C(t)?(i=new d.Matrix(s).extract(),"string"==typeof t?i[t]:i):(e=!!e||!!t.relative,null!=t.a?i=new d.Matrix(t):null!=t.rotation?(f(t,s),i=new d.Rotate(t.rotation,t.cx,t.cy)):null!=t.scale||null!=t.scaleX||null!=t.scaleY?(f(t,s),t.scaleX=null!=t.scale?t.scale:null!=t.scaleX?t.scaleX:1,t.scaleY=null!=t.scale?t.scale:null!=t.scaleY?t.scaleY:1,i=new d.Scale(t.scaleX,t.scaleY,t.cx,t.cy)):null!=t.skewX||null!=t.skewY?(f(t,s),t.skewX=null!=t.skewX?t.skewX:0,t.skewY=null!=t.skewY?t.skewY:0,i=new d.Skew(t.skewX,t.skewY,t.cx,t.cy)):t.flip?("x"==t.flip||"y"==t.flip?t.offset=null==t.offset?s.bbox()["c"+t.flip]:t.offset:null==t.offset?(s=s.bbox(),t.flip=s.cx,t.offset=s.cy):t.flip=t.offset,i=(new d.Matrix).flip(t.flip,t.offset)):null==t.x&&null==t.y||(i=new d.Translate(t.x,t.y)),i?(i.relative=e,this.last().transforms.push(i),this._callStart()):this)}}),d.extend(d.Element,{untransform:function(){return this.attr("transform",null)},matrixify:function(){return(this.attr("transform")||"").split(d.regex.transforms).slice(0,-1).map(function(t){return[(t=t.trim().split("("))[0],t[1].split(d.regex.delimiter).map(function(t){return parseFloat(t)})]}).reduce(function(t,e){return"matrix"==e[0]?t.multiply(p(e[1])):t[e[0]].apply(t,e[1])},new d.Matrix)},toParent:function(t){var e,i;return this!=t&&(e=this.screenCTM(),i=t.screenCTM().inverse(),this.addTo(t).untransform().transform(i.multiply(e))),this},toDoc:function(){return this.toParent(this.doc())}}),d.Transformation=d.invent({create:function(t,e){if(1<arguments.length&&"boolean"!=typeof e)return this.constructor.call(this,[].slice.call(arguments));if(Array.isArray(t))for(var i=0,s=this.arguments.length;i<s;++i)this[this.arguments[i]]=t[i];else if("object"===C(t))for(i=0,s=this.arguments.length;i<s;++i)this[this.arguments[i]]=t[this.arguments[i]];!(this.inversed=!1)===e&&(this.inversed=!0)},extend:{arguments:[],method:"",at:function(t){for(var e=[],i=0,s=this.arguments.length;i<s;++i)e.push(this[this.arguments[i]]);var a=this._undo||new d.Matrix,a=(new d.Matrix).morph(d.Matrix.prototype[this.method].apply(a,e)).at(t);return this.inversed?a.inverse():a},undo:function(t){for(var e=0,i=this.arguments.length;e<i;++e)t[this.arguments[e]]=void 0===this[this.arguments[e]]?0:t[this.arguments[e]];return t.cx=this.cx,t.cy=this.cy,this._undo=new d[r(this.method)](t,!0).at(1),this}}}),d.Translate=d.invent({parent:d.Matrix,inherit:d.Transformation,create:function(t,e){this.constructor.apply(this,[].slice.call(arguments))},extend:{arguments:["transformedX","transformedY"],method:"translate"}}),d.Rotate=d.invent({parent:d.Matrix,inherit:d.Transformation,create:function(t,e){this.constructor.apply(this,[].slice.call(arguments))},extend:{arguments:["rotation","cx","cy"],method:"rotate",at:function(t){return t=(new d.Matrix).rotate((new d.Number).morph(this.rotation-(this._undo?this._undo.rotation:0)).at(t),this.cx,this.cy),this.inversed?t.inverse():t},undo:function(t){return this._undo=t,this}}}),d.Scale=d.invent({parent:d.Matrix,inherit:d.Transformation,create:function(t,e){this.constructor.apply(this,[].slice.call(arguments))},extend:{arguments:["scaleX","scaleY","cx","cy"],method:"scale"}}),d.Skew=d.invent({parent:d.Matrix,inherit:d.Transformation,create:function(t,e){this.constructor.apply(this,[].slice.call(arguments))},extend:{arguments:["skewX","skewY","cx","cy"],method:"skew"}}),d.extend(d.Element,{style:function(t,e){if(0==arguments.length)return this.node.style.cssText||"";if(arguments.length<2)if("object"===C(t))for(e in t)this.style(e,t[e]);else{if(!d.regex.isCss.test(t))return this.node.style[o(t)];for(t=t.split(/\s*;\s*/).filter(function(t){return!!t}).map(function(t){return t.split(/\s*:\s*/)});e=t.pop();)this.style(e[0],e[1])}else this.node.style[o(t)]=null===e||d.regex.isBlank.test(e)?"":e;return this}}),d.Parent=d.invent({create:function(t){this.constructor.call(this,t)},inherit:d.Element,extend:{children:function(){return d.utils.map(d.utils.filterSVGElements(this.node.childNodes),function(t){return d.adopt(t)})},add:function(t,e){return null==e?this.node.appendChild(t.node):t.node!=this.node.childNodes[e]&&this.node.insertBefore(t.node,this.node.childNodes[e]),this},put:function(t,e){return this.add(t,e),t},has:function(t){return 0<=this.index(t)},index:function(t){return[].slice.call(this.node.childNodes).indexOf(t.node)},get:function(t){return d.adopt(this.node.childNodes[t])},first:function(){return this.get(0)},last:function(){return this.get(this.node.childNodes.length-1)},each:function(t,e){for(var i=this.children(),s=0,a=i.length;s<a;s++)i[s]instanceof d.Element&&t.apply(i[s],[s,i]),e&&i[s]instanceof d.Container&&i[s].each(t,e);return this},removeElement:function(t){return this.node.removeChild(t.node),this},clear:function(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return delete this._defs,this},defs:function(){return this.doc().defs()}}}),d.extend(d.Parent,{ungroup:function(t,e){return 0===e||this instanceof d.Defs||this.node==d.parser.draw||(t=t||(this instanceof d.Doc?this:this.parent(d.Parent)),e=e||1/0,this.each(function(){return this instanceof d.Defs?this:this instanceof d.Parent?this.ungroup(t,e-1):this.toParent(t)}),this.node.firstChild)||this.remove(),this},flatten:function(t,e){return this.ungroup(t,e)}}),d.Container=d.invent({create:function(t){this.constructor.call(this,t)},inherit:d.Parent}),d.ViewBox=d.invent({create:function(t){var e,i,s,a,n,o,r,l=1,h=1,c=/[+-]?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?/gi;if(t instanceof d.Element){for(n=((r=o=t).attr("viewBox")||"").match(c),t.bbox,s=new d.Number(t.width()),a=new d.Number(t.height());"%"==s.unit;)l*=s.value,s=new d.Number(o instanceof d.Doc?o.parent().offsetWidth:o.parent().width()),o=o.parent();for(;"%"==a.unit;)h*=a.value,a=new d.Number(r instanceof d.Doc?r.parent().offsetHeight:r.parent().height()),r=r.parent();this.x=0,this.y=0,this.width=s*l,this.height=a*h,this.zoom=1,n&&(e=parseFloat(n[0]),i=parseFloat(n[1]),s=parseFloat(n[2]),a=parseFloat(n[3]),this.zoom=this.width/this.height>s/a?this.height/a:this.width/s,this.x=e,this.y=i,this.width=s,this.height=a)}else t="string"==typeof t?t.match(c).map(function(t){return parseFloat(t)}):Array.isArray(t)?t:"object"===C(t)?[t.x,t.y,t.width,t.height]:4==arguments.length?[].slice.call(arguments):[0,0,0,0],this.x=t[0],this.y=t[1],this.width=t[2],this.height=t[3]},extend:{toString:function(){return this.x+" "+this.y+" "+this.width+" "+this.height},morph:function(t,e,i,s){return this.destination=new d.ViewBox(t,e,i,s),this},at:function(t){return this.destination?new d.ViewBox([this.x+(this.destination.x-this.x)*t,this.y+(this.destination.y-this.y)*t,this.width+(this.destination.width-this.width)*t,this.height+(this.destination.height-this.height)*t]):this}},parent:d.Container,construct:{viewbox:function(t,e,i,s){return 0==arguments.length?new d.ViewBox(this):this.attr("viewBox",new d.ViewBox(t,e,i,s))}}}),["click","dblclick","mousedown","mouseup","mouseover","mouseout","mousemove","touchstart","touchmove","touchleave","touchend","touchcancel"].forEach(function(e){d.Element.prototype[e]=function(t){return d.on(this.node,e,t),this}}),d.listeners=[],d.handlerMap=[],d.listenerId=0,d.on=function(t,e,i,s,a){var n=i.bind(s||t.instance||t),o=(d.handlerMap.indexOf(t)+1||d.handlerMap.push(t))-1,s=e.split(".")[0],e=e.split(".")[1]||"*";d.listeners[o]=d.listeners[o]||{},d.listeners[o][s]=d.listeners[o][s]||{},d.listeners[o][s][e]=d.listeners[o][s][e]||{},i._svgjsListenerId||(i._svgjsListenerId=++d.listenerId),d.listeners[o][s][e][i._svgjsListenerId]=n,t.addEventListener(s,n,a||!1)},d.off=function(t,e,i){var s=d.handlerMap.indexOf(t),a=e&&e.split(".")[0],n=e&&e.split(".")[1],o="";if(-1!=s)if(i)(i="function"==typeof i?i._svgjsListenerId:i)&&d.listeners[s][a]&&d.listeners[s][a][n||"*"]&&(t.removeEventListener(a,d.listeners[s][a][n||"*"][i],!1),delete d.listeners[s][a][n||"*"][i]);else if(n&&a){if(d.listeners[s][a]&&d.listeners[s][a][n]){for(i in d.listeners[s][a][n])d.off(t,[a,n].join("."),i);delete d.listeners[s][a][n]}}else if(n)for(e in d.listeners[s])for(o in d.listeners[s][e])n===o&&d.off(t,[e,n].join("."));else if(a){if(d.listeners[s][a]){for(o in d.listeners[s][a])d.off(t,[a,o].join("."));delete d.listeners[s][a]}}else{for(e in d.listeners[s])d.off(t,e);delete d.listeners[s],delete d.handlerMap[s]}},d.extend(d.Element,{on:function(t,e,i,s){return d.on(this.node,t,e,i,s),this},off:function(t,e){return d.off(this.node,t,e),this},fire:function(t,e){return t instanceof a.Event?this.node.dispatchEvent(t):this.node.dispatchEvent(t=new d.CustomEvent(t,{detail:e,cancelable:!0})),this._event=t,this},event:function(){return this._event}}),d.Defs=d.invent({create:"defs",inherit:d.Container}),d.G=d.invent({create:"g",inherit:d.Container,extend:{x:function(t){return null==t?this.transform("x"):this.transform({x:t-this.x()},!0)},y:function(t){return null==t?this.transform("y"):this.transform({y:t-this.y()},!0)},cx:function(t){return null==t?this.gbox().cx:this.x(t-this.gbox().width/2)},cy:function(t){return null==t?this.gbox().cy:this.y(t-this.gbox().height/2)},gbox:function(){var t=this.bbox(),e=this.transform();return t.x+=e.x,t.x2+=e.x,t.cx+=e.x,t.y+=e.y,t.y2+=e.y,t.cy+=e.y,t}},construct:{group:function(){return this.put(new d.G)}}}),d.Doc=d.invent({create:function(t){t&&("svg"==(t="string"==typeof t?n.getElementById(t):t).nodeName?this.constructor.call(this,t):(this.constructor.call(this,d.create("svg")),t.appendChild(this.node),this.size("100%","100%")),this.namespace().defs())},inherit:d.Container,extend:{namespace:function(){return this.attr({xmlns:d.ns,version:"1.1"}).attr("xmlns:xlink",d.xlink,d.xmlns).attr("xmlns:svgjs",d.svgjs,d.xmlns)},defs:function(){var t;return this._defs||((t=this.node.getElementsByTagName("defs")[0])?this._defs=d.adopt(t):this._defs=new d.Defs,this.node.appendChild(this._defs.node)),this._defs},parent:function(){return this.node.parentNode&&"#document"!=this.node.parentNode.nodeName?this.node.parentNode:null},spof:function(){var t=this.node.getScreenCTM();return t&&this.style("left",-t.e%1+"px").style("top",-t.f%1+"px"),this},remove:function(){return this.parent()&&this.parent().removeChild(this.node),this},clear:function(){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return delete this._defs,d.parser.draw&&!d.parser.draw.parentNode&&this.node.appendChild(d.parser.draw),this},clone:function(t){this.writeDataToDom();var e=this.node,i=x(e.cloneNode(!0));return t?(t.node||t).appendChild(i.node):e.parentNode.insertBefore(i.node,e.nextSibling),i}}}),d.extend(d.Element,{siblings:function(){return this.parent().children()},position:function(){return this.parent().index(this)},next:function(){return this.siblings()[this.position()+1]},previous:function(){return this.siblings()[this.position()-1]},forward:function(){var t=this.position()+1,e=this.parent();return e.removeElement(this).add(this,t),e instanceof d.Doc&&e.node.appendChild(e.defs().node),this},backward:function(){var t=this.position();return 0<t&&this.parent().removeElement(this).add(this,t-1),this},front:function(){var t=this.parent();return t.node.appendChild(this.node),t instanceof d.Doc&&t.node.appendChild(t.defs().node),this},back:function(){return 0<this.position()&&this.parent().removeElement(this).add(this,0),this},before:function(t){t.remove();var e=this.position();return this.parent().add(t,e),this},after:function(t){t.remove();var e=this.position();return this.parent().add(t,e+1),this}}),d.Mask=d.invent({create:function(){this.constructor.call(this,d.create("mask")),this.targets=[]},inherit:d.Container,extend:{remove:function(){for(var t=this.targets.length-1;0<=t;t--)this.targets[t]&&this.targets[t].unmask();return this.targets=[],d.Element.prototype.remove.call(this),this}},construct:{mask:function(){return this.defs().put(new d.Mask)}}}),d.extend(d.Element,{maskWith:function(t){return this.masker=t instanceof d.Mask?t:this.parent().mask().add(t),this.masker.targets.push(this),this.attr("mask",'url("#'+this.masker.attr("id")+'")')},unmask:function(){return delete this.masker,this.attr("mask",null)}}),d.ClipPath=d.invent({create:function(){this.constructor.call(this,d.create("clipPath")),this.targets=[]},inherit:d.Container,extend:{remove:function(){for(var t=this.targets.length-1;0<=t;t--)this.targets[t]&&this.targets[t].unclip();return this.targets=[],this.parent().removeElement(this),this}},construct:{clip:function(){return this.defs().put(new d.ClipPath)}}}),d.extend(d.Element,{clipWith:function(t){return this.clipper=t instanceof d.ClipPath?t:this.parent().clip().add(t),this.clipper.targets.push(this),this.attr("clip-path",'url("#'+this.clipper.attr("id")+'")')},unclip:function(){return delete this.clipper,this.attr("clip-path",null)}}),d.Gradient=d.invent({create:function(t){this.constructor.call(this,d.create(t+"Gradient")),this.type=t},inherit:d.Container,extend:{at:function(t,e,i){return this.put(new d.Stop).update(t,e,i)},update:function(t){return this.clear(),"function"==typeof t&&t.call(this,this),this},fill:function(){return"url(#"+this.id()+")"},toString:function(){return this.fill()},attr:function(t,e,i){return d.Container.prototype.attr.call(this,t="transform"==t?"gradientTransform":t,e,i)}},construct:{gradient:function(t,e){return this.defs().gradient(t,e)}}}),d.extend(d.Gradient,d.FX,{from:function(t,e){return"radial"==(this._target||this).type?this.attr({fx:new d.Number(t),fy:new d.Number(e)}):this.attr({x1:new d.Number(t),y1:new d.Number(e)})},to:function(t,e){return"radial"==(this._target||this).type?this.attr({cx:new d.Number(t),cy:new d.Number(e)}):this.attr({x2:new d.Number(t),y2:new d.Number(e)})}}),d.extend(d.Defs,{gradient:function(t,e){return this.put(new d.Gradient(t)).update(e)}}),d.Stop=d.invent({create:"stop",inherit:d.Element,extend:{update:function(t){return null!=(t="number"==typeof t||t instanceof d.Number?{offset:arguments[0],color:arguments[1],opacity:arguments[2]}:t).opacity&&this.attr("stop-opacity",t.opacity),null!=t.color&&this.attr("stop-color",t.color),null!=t.offset&&this.attr("offset",new d.Number(t.offset)),this}}}),d.Pattern=d.invent({create:"pattern",inherit:d.Container,extend:{fill:function(){return"url(#"+this.id()+")"},update:function(t){return this.clear(),"function"==typeof t&&t.call(this,this),this},toString:function(){return this.fill()},attr:function(t,e,i){return d.Container.prototype.attr.call(this,t="transform"==t?"patternTransform":t,e,i)}},construct:{pattern:function(t,e,i){return this.defs().pattern(t,e,i)}}}),d.extend(d.Defs,{pattern:function(t,e,i){return this.put(new d.Pattern).update(i).attr({x:0,y:0,width:t,height:e,patternUnits:"userSpaceOnUse"})}}),d.Shape=d.invent({create:function(t){this.constructor.call(this,t)},inherit:d.Element}),d.Bare=d.invent({create:function(t,e){if(this.constructor.call(this,d.create(t)),e)for(var i in e.prototype)"function"==typeof e.prototype[i]&&(this[i]=e.prototype[i])},inherit:d.Element,extend:{words:function(t){for(;this.node.hasChildNodes();)this.node.removeChild(this.node.lastChild);return this.node.appendChild(n.createTextNode(t)),this}}}),d.extend(d.Parent,{element:function(t,e){return this.put(new d.Bare(t,e))}}),d.Symbol=d.invent({create:"symbol",inherit:d.Container,construct:{symbol:function(){return this.put(new d.Symbol)}}}),d.Use=d.invent({create:"use",inherit:d.Shape,extend:{element:function(t,e){return this.attr("href",(e||"")+"#"+t,d.xlink)}},construct:{use:function(t,e){return this.put(new d.Use).element(t,e)}}}),d.Rect=d.invent({create:"rect",inherit:d.Shape,construct:{rect:function(t,e){return this.put(new d.Rect).size(t,e)}}}),d.Circle=d.invent({create:"circle",inherit:d.Shape,construct:{circle:function(t){return this.put(new d.Circle).rx(new d.Number(t).divide(2)).move(0,0)}}}),d.extend(d.Circle,d.FX,{rx:function(t){return this.attr("r",t)},ry:function(t){return this.rx(t)}}),d.Ellipse=d.invent({create:"ellipse",inherit:d.Shape,construct:{ellipse:function(t,e){return this.put(new d.Ellipse).size(t,e).move(0,0)}}}),d.extend(d.Ellipse,d.Rect,d.FX,{rx:function(t){return this.attr("rx",t)},ry:function(t){return this.attr("ry",t)}}),d.extend(d.Circle,d.Ellipse,{x:function(t){return null==t?this.cx()-this.rx():this.cx(t+this.rx())},y:function(t){return null==t?this.cy()-this.ry():this.cy(t+this.ry())},cx:function(t){return null==t?this.attr("cx"):this.attr("cx",t)},cy:function(t){return null==t?this.attr("cy"):this.attr("cy",t)},width:function(t){return null==t?2*this.rx():this.rx(new d.Number(t).divide(2))},height:function(t){return null==t?2*this.ry():this.ry(new d.Number(t).divide(2))},size:function(t,e){return e=u(this,t,e),this.rx(new d.Number(e.width).divide(2)).ry(new d.Number(e.height).divide(2))}}),d.Line=d.invent({create:"line",inherit:d.Shape,extend:{array:function(){return new d.PointArray([[this.attr("x1"),this.attr("y1")],[this.attr("x2"),this.attr("y2")]])},plot:function(t,e,i,s){return null==t?this.array():(t=void 0!==e?{x1:t,y1:e,x2:i,y2:s}:new d.PointArray(t).toLine(),this.attr(t))},move:function(t,e){return this.attr(this.array().move(t,e).toLine())},size:function(t,e){return e=u(this,t,e),this.attr(this.array().size(e.width,e.height).toLine())}},construct:{line:function(t,e,i,s){return d.Line.prototype.plot.apply(this.put(new d.Line),null!=t?[t,e,i,s]:[0,0,0,0])}}}),d.Polyline=d.invent({create:"polyline",inherit:d.Shape,construct:{polyline:function(t){return this.put(new d.Polyline).plot(t||new d.PointArray)}}}),d.Polygon=d.invent({create:"polygon",inherit:d.Shape,construct:{polygon:function(t){return this.put(new d.Polygon).plot(t||new d.PointArray)}}}),d.extend(d.Polyline,d.Polygon,{array:function(){return this._array||(this._array=new d.PointArray(this.attr("points")))},plot:function(t){return null==t?this.array():this.clear().attr("points","string"==typeof t?t:this._array=new d.PointArray(t))},clear:function(){return delete this._array,this},move:function(t,e){return this.attr("points",this.array().move(t,e))},size:function(t,e){return e=u(this,t,e),this.attr("points",this.array().size(e.width,e.height))}}),d.extend(d.Line,d.Polyline,d.Polygon,{morphArray:d.PointArray,x:function(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)},y:function(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)},width:function(t){var e=this.bbox();return null==t?e.width:this.size(t,e.height)},height:function(t){var e=this.bbox();return null==t?e.height:this.size(e.width,t)}}),d.Path=d.invent({create:"path",inherit:d.Shape,extend:{morphArray:d.PathArray,array:function(){return this._array||(this._array=new d.PathArray(this.attr("d")))},plot:function(t){return null==t?this.array():this.clear().attr("d","string"==typeof t?t:this._array=new d.PathArray(t))},clear:function(){return delete this._array,this},move:function(t,e){return this.attr("d",this.array().move(t,e))},x:function(t){return null==t?this.bbox().x:this.move(t,this.bbox().y)},y:function(t){return null==t?this.bbox().y:this.move(this.bbox().x,t)},size:function(t,e){return e=u(this,t,e),this.attr("d",this.array().size(e.width,e.height))},width:function(t){return null==t?this.bbox().width:this.size(t,this.bbox().height)},height:function(t){return null==t?this.bbox().height:this.size(this.bbox().width,t)}},construct:{path:function(t){return this.put(new d.Path).plot(t||new d.PathArray)}}}),d.Image=d.invent({create:"image",inherit:d.Shape,extend:{load:function(e){var i,s;return e?(i=this,s=new a.Image,d.on(s,"load",function(){d.off(s);var t=i.parent(d.Pattern);null!==t&&(0==i.width()&&0==i.height()&&i.size(s.width,s.height),t&&0==t.width()&&0==t.height()&&t.size(i.width(),i.height()),"function"==typeof i._loaded)&&i._loaded.call(i,{width:s.width,height:s.height,ratio:s.width/s.height,url:e})}),d.on(s,"error",function(t){d.off(s),"function"==typeof i._error&&i._error.call(i,t)}),this.attr("href",s.src=this.src=e,d.xlink)):this},loaded:function(t){return this._loaded=t,this},error:function(t){return this._error=t,this}},construct:{image:function(t,e,i){return this.put(new d.Image).load(t).size(e||0,i||e||0)}}}),d.Text=d.invent({create:function(){this.constructor.call(this,d.create("text")),this.dom.leading=new d.Number(1.3),this._rebuild=!0,this._build=!1,this.attr("font-family",d.defaults.attrs["font-family"])},inherit:d.Shape,extend:{x:function(t){return null==t?this.attr("x"):this.attr("x",t)},y:function(t){var e=this.attr("y"),i="number"==typeof e?e-this.bbox().y:0;return null==t?"number"==typeof e?e-i:e:this.attr("y","number"==typeof t.valueOf()?t+i:t)},cx:function(t){return null==t?this.bbox().cx:this.x(t-this.bbox().width/2)},cy:function(t){return null==t?this.bbox().cy:this.y(t-this.bbox().height/2)},text:function(t){if(void 0===t){t="";for(var e=this.node.childNodes,i=0,s=e.length;i<s;++i)0!=i&&3!=e[i].nodeType&&1==d.adopt(e[i]).dom.newLined&&(t+="\n"),t+=e[i].textContent;return t}if(this.clear().build(!0),"function"==typeof t)t.call(this,this);else for(var i=0,a=(t=t.split("\n")).length;i<a;i++)this.tspan(t[i]).newLine();return this.build(!1).rebuild()},size:function(t){return this.attr("font-size",t).rebuild()},leading:function(t){return null==t?this.dom.leading:(this.dom.leading=new d.Number(t),this.rebuild())},lines:function(){var t=(this.textPath&&this.textPath()||this).node,t=d.utils.map(d.utils.filterSVGElements(t.childNodes),function(t){return d.adopt(t)});return new d.Set(t)},rebuild:function(t){var e,i,s;return"boolean"==typeof t&&(this._rebuild=t),this._rebuild&&(i=0,s=(e=this).dom.leading*new d.Number(this.attr("font-size")),this.lines().each(function(){this.dom.newLined&&(e.textPath()||this.attr("x",e.attr("x")),"\n"==this.text()?i+=s:(this.attr("dy",s+i),i=0))}),this.fire("rebuild")),this},build:function(t){return this._build=!!t,this},setData:function(t){return this.dom=t,this.dom.leading=new d.Number(t.leading||1.3),this}},construct:{text:function(t){return this.put(new d.Text).text(t)},plain:function(t){return this.put(new d.Text).plain(t)}}}),d.Tspan=d.invent({create:"tspan",inherit:d.Shape,extend:{text:function(t){return null==t?this.node.textContent+(this.dom.newLined?"\n":""):("function"==typeof t?t.call(this,this):this.plain(t),this)},dx:function(t){return this.attr("dx",t)},dy:function(t){return this.attr("dy",t)},newLine:function(){var t=this.parent(d.Text);return this.dom.newLined=!0,this.dy(t.dom.leading*t.attr("font-size")).attr("x",t.x())}}}),d.extend(d.Text,d.Tspan,{plain:function(t){return!1===this._build&&this.clear(),this.node.appendChild(n.createTextNode(t)),this},tspan:function(t){var e=(this.textPath&&this.textPath()||this).node,i=new d.Tspan;return!1===this._build&&this.clear(),e.appendChild(i.node),i.text(t)},clear:function(){for(var t=(this.textPath&&this.textPath()||this).node;t.hasChildNodes();)t.removeChild(t.lastChild);return this},length:function(){return this.node.getComputedTextLength()}}),d.TextPath=d.invent({create:"textPath",inherit:d.Parent,parent:d.Text,construct:{morphArray:d.PathArray,path:function(t){for(var e=new d.TextPath,t=this.doc().defs().path(t);this.node.hasChildNodes();)e.node.appendChild(this.node.firstChild);return this.node.appendChild(e.node),e.attr("href","#"+t,d.xlink),this},array:function(){var t=this.track();return t?t.array():null},plot:function(t){var e=this.track(),i=null;return e&&(i=e.plot(t)),null==t?i:this},track:function(){var t=this.textPath();if(t)return t.reference("href")},textPath:function(){if(this.node.firstChild&&"textPath"==this.node.firstChild.nodeName)return d.adopt(this.node.firstChild)}}}),d.Nested=d.invent({create:function(){this.constructor.call(this,d.create("svg")),this.style("overflow","visible")},inherit:d.Container,construct:{nested:function(){return this.put(new d.Nested)}}}),d.A=d.invent({create:"a",inherit:d.Container,extend:{to:function(t){return this.attr("href",t,d.xlink)},show:function(t){return this.attr("show",t,d.xlink)},target:function(t){return this.attr("target",t)}},construct:{link:function(t){return this.put(new d.A).to(t)}}}),d.extend(d.Element,{linkTo:function(t){var e=new d.A;return"function"==typeof t?t.call(e,e):e.to(t),this.parent().put(e).put(this)}}),d.Marker=d.invent({create:"marker",inherit:d.Container,extend:{width:function(t){return this.attr("markerWidth",t)},height:function(t){return this.attr("markerHeight",t)},ref:function(t,e){return this.attr("refX",t).attr("refY",e)},update:function(t){return this.clear(),"function"==typeof t&&t.call(this,this),this},toString:function(){return"url(#"+this.id()+")"}},construct:{marker:function(t,e,i){return this.defs().marker(t,e,i)}}}),d.extend(d.Defs,{marker:function(t,e,i){return this.put(new d.Marker).size(t,e).ref(t/2,e/2).viewbox(0,0,t,e).attr("orient","auto").update(i)}}),d.extend(d.Line,d.Polyline,d.Polygon,d.Path,{marker:function(t,e,i,s){var a=["marker"];return"all"!=t&&a.push(t),a=a.join("-"),t=e instanceof d.Marker?e:this.doc().marker(e,i,s),this.attr(a,t)}});var s={stroke:["color","width","opacity","linecap","linejoin","miterlimit","dasharray","dashoffset"],fill:["color","opacity","rule"],prefix:function(t,e){return"color"==e?t:t+"-"+e}};function h(t,e,i,s){return i+s.replace(d.regex.dots," .")}function o(t){return t.toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()})}function r(t){return t.charAt(0).toUpperCase()+t.slice(1)}function c(t){return 1==(t=t.toString(16)).length?"0"+t:t}function u(t,e,i){return null!=e&&null!=i||(t=t.bbox(),null==e?e=t.width/t.height*i:null==i&&(i=t.height/t.width*e)),{width:e,height:i}}function g(t,e,i){return{x:e*t.a+i*t.c,y:e*t.b+i*t.d}}function p(t){return{a:t[0],b:t[1],c:t[2],d:t[3],e:t[4],f:t[5]}}function f(t,e){t.cx=(null==t.cx?e.bbox():t).cx,t.cy=(null==t.cy?e.bbox():t).cy}function x(t){for(var e=t.childNodes.length-1;0<=e;e--)t.childNodes[e]instanceof a.SVGElement&&x(t.childNodes[e]);return d.adopt(t).id(d.eid(t.nodeName))}function b(t){return null==t.x&&(t.x=0,t.y=0,t.width=0,t.height=0),t.w=t.width,t.h=t.height,t.x2=t.x+t.width,t.y2=t.y+t.height,t.cx=t.x+t.width/2,t.cy=t.y+t.height/2,t}function m(t){return 1e-37<Math.abs(t)?t:0}["fill","stroke"].forEach(function(e){var i,t={};t[e]=function(t){if(void 0!==t)if("string"==typeof t||d.Color.isRgb(t)||t&&"function"==typeof t.fill)this.attr(e,t);else for(i=s[e].length-1;0<=i;i--)null!=t[s[e][i]]&&this.attr(s.prefix(e,s[e][i]),t[s[e][i]]);return this},d.extend(d.Element,d.FX,t)}),d.extend(d.Element,d.FX,{rotate:function(t,e,i){return this.transform({rotation:t,cx:e,cy:i})},skew:function(t,e,i,s){return 1==arguments.length||3==arguments.length?this.transform({skew:t,cx:e,cy:i}):this.transform({skewX:t,skewY:e,cx:i,cy:s})},scale:function(t,e,i,s){return 1==arguments.length||3==arguments.length?this.transform({scale:t,cx:e,cy:i}):this.transform({scaleX:t,scaleY:e,cx:i,cy:s})},translate:function(t,e){return this.transform({x:t,y:e})},flip:function(t,e){return this.transform({flip:t||"both",offset:e="number"==typeof t?t:e})},matrix:function(t){return this.attr("transform",new d.Matrix(6==arguments.length?[].slice.call(arguments):t))},opacity:function(t){return this.attr("opacity",t)},dx:function(t){return this.x(new d.Number(t).plus(this instanceof d.FX?0:this.x()),!0)},dy:function(t){return this.y(new d.Number(t).plus(this instanceof d.FX?0:this.y()),!0)},dmove:function(t,e){return this.dx(t).dy(e)}}),d.extend(d.Rect,d.Ellipse,d.Circle,d.Gradient,d.FX,{radius:function(t,e){var i=(this._target||this).type;return"radial"==i||"circle"==i?this.attr("r",new d.Number(t)):this.rx(t).ry(null==e?t:e)}}),d.extend(d.Path,{length:function(){return this.node.getTotalLength()},pointAt:function(t){return this.node.getPointAtLength(t)}}),d.extend(d.Parent,d.Text,d.Tspan,d.FX,{font:function(t,e){if("object"===C(t))for(e in t)this.font(e,t[e]);return"leading"==t?this.leading(e):"anchor"==t?this.attr("text-anchor",e):"size"==t||"family"==t||"weight"==t||"stretch"==t||"variant"==t||"style"==t?this.attr("font-"+t,e):this.attr(t,e)}}),d.Set=d.invent({create:function(t){Array.isArray(t)?this.members=t:this.clear()},extend:{add:function(){for(var t=[].slice.call(arguments),e=0,i=t.length;e<i;e++)this.members.push(t[e]);return this},remove:function(t){return-1<(t=this.index(t))&&this.members.splice(t,1),this},each:function(t){for(var e=0,i=this.members.length;e<i;e++)t.apply(this.members[e],[e,this.members]);return this},clear:function(){return this.members=[],this},length:function(){return this.members.length},has:function(t){return 0<=this.index(t)},index:function(t){return this.members.indexOf(t)},get:function(t){return this.members[t]},first:function(){return this.get(0)},last:function(){return this.get(this.members.length-1)},valueOf:function(){return this.members},bbox:function(){var t;return 0==this.members.length?new d.RBox:(t=this.members[0].rbox(this.members[0].doc()),this.each(function(){t=t.merge(this.rbox(this.doc()))}),t)}},construct:{set:function(t){return new d.Set(t)}}}),d.FX.Set=d.invent({create:function(t){this.set=t}}),d.Set.inherit=function(){var t,e=[];for(t in d.Shape.prototype)"function"==typeof d.Shape.prototype[t]&&"function"!=typeof d.Set.prototype[t]&&e.push(t);for(t in e.forEach(function(i){d.Set.prototype[i]=function(){for(var t=0,e=this.members.length;t<e;t++)this.members[t]&&"function"==typeof this.members[t][i]&&this.members[t][i].apply(this.members[t],arguments);return"animate"==i?this.fx||(this.fx=new d.FX.Set(this)):this}}),e=[],d.FX.prototype)"function"==typeof d.FX.prototype[t]&&"function"!=typeof d.FX.Set.prototype[t]&&e.push(t);e.forEach(function(i){d.FX.Set.prototype[i]=function(){for(var t=0,e=this.set.members.length;t<e;t++)this.set.members[t].fx[i].apply(this.set.members[t].fx,arguments);return this}})},d.extend(d.Element,{data:function(t,e,i){if("object"===C(t))for(e in t)this.data(e,t[e]);else if(arguments.length<2)try{return JSON.parse(this.attr("data-"+t))}catch(e){return this.attr("data-"+t)}else this.attr("data-"+t,null===e?null:!0===i||"string"==typeof e||"number"==typeof e?e:JSON.stringify(e));return this}}),d.extend(d.Element,{remember:function(t,e){if("object"===C(t))for(var e in t)this.remember(e,t[e]);else{if(1==arguments.length)return this.memory()[t];this.memory()[t]=e}return this},forget:function(){if(0==arguments.length)this._memory={};else for(var t=arguments.length-1;0<=t;t--)delete this.memory()[arguments[t]];return this},memory:function(){return this._memory||(this._memory={})}}),d.get=function(e){var t=n.getElementById(function(){var t=(e||"").toString().match(d.regex.reference);if(t)return t[1]}()||e);return d.adopt(t)},d.select=function(t,e){return new d.Set(d.utils.map((e||n).querySelectorAll(t),function(t){return d.adopt(t)}))},d.extend(d.Parent,{select:function(t){return d.select(t,this.node)}});for(var y,v="abcdef".split(""),w=("function"!=typeof a.CustomEvent?((y=function(t,e){e=e||{bubbles:!1,cancelable:!1,detail:void 0};var i=n.createEvent("CustomEvent");return i.initCustomEvent(t,e.bubbles,e.cancelable,e.detail),i}).prototype=a.Event.prototype,d.CustomEvent=y):d.CustomEvent=a.CustomEvent,a),k=0,A=["moz","webkit"],S=0;S<A.length&&!a.requestAnimationFrame;++S)w.requestAnimationFrame=w[A[S]+"RequestAnimationFrame"],w.cancelAnimationFrame=w[A[S]+"CancelAnimationFrame"]||w[A[S]+"CancelRequestAnimationFrame"];return w.requestAnimationFrame=w.requestAnimationFrame||function(t){var e=(new Date).getTime(),i=Math.max(0,16-(e-k)),s=w.setTimeout(function(){t(e+i)},i);return k=e+i,s},w.cancelAnimationFrame=w.cancelAnimationFrame||w.clearTimeout,d},"function"==typeof define&&define.amd?define(function(){return x(f,f.document)}):"object"===("undefined"==typeof exports?"undefined":C(exports))&&"undefined"!=typeof module?module.exports=f.document?x(f,f.document):function(t){return x(t,t.document)}:f.SVG=x(f,f.document),function(){SVG.Filter=SVG.invent({create:"filter",inherit:SVG.Parent,extend:{source:"SourceGraphic",sourceAlpha:"SourceAlpha",background:"BackgroundImage",backgroundAlpha:"BackgroundAlpha",fill:"FillPaint",stroke:"StrokePaint",autoSetIn:!0,put:function(t,e){return this.add(t,e),!t.attr("in")&&this.autoSetIn&&t.attr("in",this.source),t.attr("result")||t.attr("result",t),t},blend:function(t,e,i){return this.put(new SVG.BlendEffect(t,e,i))},colorMatrix:function(t,e){return this.put(new SVG.ColorMatrixEffect(t,e))},convolveMatrix:function(t){return this.put(new SVG.ConvolveMatrixEffect(t))},componentTransfer:function(t){return this.put(new SVG.ComponentTransferEffect(t))},composite:function(t,e,i){return this.put(new SVG.CompositeEffect(t,e,i))},flood:function(t,e){return this.put(new SVG.FloodEffect(t,e))},offset:function(t,e){return this.put(new SVG.OffsetEffect(t,e))},image:function(t){return this.put(new SVG.ImageEffect(t))},merge:function(){var t,e=[void 0];for(t in arguments)e.push(arguments[t]);return this.put(new(SVG.MergeEffect.bind.apply(SVG.MergeEffect,e)))},gaussianBlur:function(t,e){return this.put(new SVG.GaussianBlurEffect(t,e))},morphology:function(t,e){return this.put(new SVG.MorphologyEffect(t,e))},diffuseLighting:function(t,e,i){return this.put(new SVG.DiffuseLightingEffect(t,e,i))},displacementMap:function(t,e,i,s,a){return this.put(new SVG.DisplacementMapEffect(t,e,i,s,a))},specularLighting:function(t,e,i,s){return this.put(new SVG.SpecularLightingEffect(t,e,i,s))},tile:function(){return this.put(new SVG.TileEffect)},turbulence:function(t,e,i,s,a){return this.put(new SVG.TurbulenceEffect(t,e,i,s,a))},toString:function(){return"url(#"+this.attr("id")+")"}}}),SVG.extend(SVG.Defs,{filter:function(t){var e=this.put(new SVG.Filter);return"function"==typeof t&&t.call(e,e),e}}),SVG.extend(SVG.Container,{filter:function(t){return this.defs().filter(t)}}),SVG.extend(SVG.Element,SVG.G,SVG.Nested,{filter:function(t){return this.filterer=t instanceof SVG.Element?t:this.doc().filter(t),this.doc()&&this.filterer.doc()!==this.doc()&&this.doc().defs().add(this.filterer),this.attr("filter",this.filterer),this.filterer},unfilter:function(t){return this.filterer&&!0===t&&this.filterer.remove(),delete this.filterer,this.attr("filter",null)}}),SVG.Effect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Element,extend:{in:function(t){return null==t?this.parent()&&this.parent().select('[result="'+this.attr("in")+'"]').get(0)||this.attr("in"):this.attr("in",t)},result:function(t){return null==t?this.attr("result"):this.attr("result",t)},toString:function(){return this.result()}}}),SVG.ParentEffect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Parent,extend:{in:function(t){return null==t?this.parent()&&this.parent().select('[result="'+this.attr("in")+'"]').get(0)||this.attr("in"):this.attr("in",t)},result:function(t){return null==t?this.attr("result"):this.attr("result",t)},toString:function(){return this.result()}}});var t={blend:function(t,e){return this.parent()&&this.parent().blend(this,t,e)},colorMatrix:function(t,e){return this.parent()&&this.parent().colorMatrix(t,e).in(this)},convolveMatrix:function(t){return this.parent()&&this.parent().convolveMatrix(t).in(this)},componentTransfer:function(t){return this.parent()&&this.parent().componentTransfer(t).in(this)},composite:function(t,e){return this.parent()&&this.parent().composite(this,t,e)},flood:function(t,e){return this.parent()&&this.parent().flood(t,e)},offset:function(t,e){return this.parent()&&this.parent().offset(t,e).in(this)},image:function(t){return this.parent()&&this.parent().image(t)},merge:function(){return this.parent()&&this.parent().merge.apply(this.parent(),[this].concat(arguments))},gaussianBlur:function(t,e){return this.parent()&&this.parent().gaussianBlur(t,e).in(this)},morphology:function(t,e){return this.parent()&&this.parent().morphology(t,e).in(this)},diffuseLighting:function(t,e,i){return this.parent()&&this.parent().diffuseLighting(t,e,i).in(this)},displacementMap:function(t,e,i,s){return this.parent()&&this.parent().displacementMap(this,t,e,i,s)},specularLighting:function(t,e,i,s){return this.parent()&&this.parent().specularLighting(t,e,i,s).in(this)},tile:function(){return this.parent()&&this.parent().tile().in(this)},turbulence:function(t,e,i,s,a){return this.parent()&&this.parent().turbulence(t,e,i,s,a).in(this)}},e=(SVG.extend(SVG.Effect,t),SVG.extend(SVG.ParentEffect,t),SVG.ChildEffect=SVG.invent({create:function(){this.constructor.call(this)},inherit:SVG.Element,extend:{in:function(t){this.attr("in",t)}}}),{blend:function(t,e,i){this.attr({in:t,in2:e,mode:i||"normal"})},colorMatrix:function(t,e){"matrix"==t&&(e=s(e)),this.attr({type:t,values:void 0===e?null:e})},convolveMatrix:function(t){t=s(t),this.attr({order:Math.sqrt(t.split(" ").length),kernelMatrix:t})},composite:function(t,e,i){this.attr({in:t,in2:e,operator:i})},flood:function(t,e){this.attr("flood-color",t),null!=e&&this.attr("flood-opacity",e)},offset:function(t,e){this.attr({dx:t,dy:e})},image:function(t){this.attr("href",t,SVG.xlink)},displacementMap:function(t,e,i,s,a){this.attr({in:t,in2:e,scale:i,xChannelSelector:s,yChannelSelector:a})},gaussianBlur:function(t,e){null!=t||null!=e?this.attr("stdDeviation",function(t){if(!Array.isArray(t))return t;for(var e=0,i=t.length,s=[];e<i;e++)s.push(t[e]);return s.join(" ")}(Array.prototype.slice.call(arguments))):this.attr("stdDeviation","0 0")},morphology:function(t,e){this.attr({operator:t,radius:e})},tile:function(){},turbulence:function(t,e,i,s,a){this.attr({numOctaves:e,seed:i,stitchTiles:s,baseFrequency:t,type:a})}}),t={merge:function(){if(arguments[0]instanceof SVG.Set){var e=this;arguments[0].each(function(t){this instanceof SVG.MergeNode?e.put(this):(this instanceof SVG.Effect||this instanceof SVG.ParentEffect)&&e.put(new SVG.MergeNode(this))})}else for(var t=Array.isArray(arguments[0])?arguments[0]:arguments,i=0;i<t.length;i++)t[i]instanceof SVG.MergeNode?this.put(t[i]):this.put(new SVG.MergeNode(t[i]))},componentTransfer:function(e){if(this.rgb=new SVG.Set,["r","g","b","a"].forEach(function(t){this[t]=new SVG["Func"+t.toUpperCase()]("identity"),this.rgb.add(this[t]),this.node.appendChild(this[t].node)}.bind(this)),e)for(var t in e.rgb&&(["r","g","b"].forEach(function(t){this[t].attr(e.rgb)}.bind(this)),delete e.rgb),e)this[t].attr(e[t])},diffuseLighting:function(t,e,i){this.attr({surfaceScale:t,diffuseConstant:e,kernelUnitLength:i})},specularLighting:function(t,e,i,s){this.attr({surfaceScale:t,diffuseConstant:e,specularExponent:i,kernelUnitLength:s})}},i={distantLight:function(t,e){this.attr({azimuth:t,elevation:e})},pointLight:function(t,e,i){this.attr({x:t,y:e,z:i})},spotLight:function(t,e,i,s,a,n){this.attr({x:t,y:e,z:i,pointsAtX:s,pointsAtY:a,pointsAtZ:n})},mergeNode:function(t){this.attr("in",t)}};function s(t){return(t=Array.isArray(t)?new SVG.Array(t):t).toString().replace(/^\s+/,"").replace(/\s+$/,"").replace(/\s+/g," ")}function a(){var t,e=function(){};for(t in"function"==typeof arguments[arguments.length-1]&&(e=arguments[arguments.length-1],Array.prototype.splice.call(arguments,arguments.length-1,1)),arguments)for(var i in arguments[t])e(arguments[t][i],i,arguments[t])}["r","g","b","a"].forEach(function(t){i["Func"+t.toUpperCase()]=function(t){switch(this.attr("type",t),t){case"table":this.attr("tableValues",arguments[1]);break;case"linear":this.attr("slope",arguments[1]),this.attr("intercept",arguments[2]);break;case"gamma":this.attr("amplitude",arguments[1]),this.attr("exponent",arguments[2]),this.attr("offset",arguments[2])}}}),a(e,function(t,e){var i=e.charAt(0).toUpperCase()+e.slice(1);SVG[i+"Effect"]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+i)),t.apply(this,arguments),this.result(this.attr("id")+"Out")},inherit:SVG.Effect,extend:{}})}),a(t,function(t,e){var i=e.charAt(0).toUpperCase()+e.slice(1);SVG[i+"Effect"]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+i)),t.apply(this,arguments),this.result(this.attr("id")+"Out")},inherit:SVG.ParentEffect,extend:{}})}),a(i,function(t,e){var i=e.charAt(0).toUpperCase()+e.slice(1);SVG[i]=SVG.invent({create:function(){this.constructor.call(this,SVG.create("fe"+i)),t.apply(this,arguments)},inherit:SVG.ChildEffect,extend:{}})}),SVG.extend(SVG.MergeEffect,{in:function(t){return t instanceof SVG.MergeNode?this.add(t,0):this.add(new SVG.MergeNode(t),0),this}}),SVG.extend(SVG.CompositeEffect,SVG.BlendEffect,SVG.DisplacementMapEffect,{in2:function(t){return null==t?this.parent()&&this.parent().select('[result="'+this.attr("in2")+'"]').get(0)||this.attr("in2"):this.attr("in2",t)}}),SVG.filter={sepiatone:[.343,.669,.119,0,0,.249,.626,.13,0,0,.172,.334,.111,0,0,0,0,0,1,0]}}.call(void 0),SVG.extend(SVG.PathArray,{morph:function(t){for(var l=this.value,e=this.parse(t),h=0,i=0;!1!==h||!1!==i;)var s,c=ze(l,!1!==h&&h+1),a=ze(e,!1!==i&&i+1),n=(!1===h&&(h=0==(s=new SVG.PathArray(n.start).bbox()).height||0==s.width?l.push(l[0])-1:l.push(["M",s.x+s.width/2,s.y+s.height/2])-1),!1===i&&(i=0==(s=new SVG.PathArray(n.dest).bbox()).height||0==s.width?e.push(e[0])-1:e.push(["M",s.x+s.width/2,s.y+s.height/2])-1),function(t,e,i){for(var s=l.slice(h,c||void 0),a=t.slice(e,i||void 0),n=0,o={pos:[0,0],start:[0,0]},r={pos:[0,0],start:[0,0]};s[n]=Se.call(o,s[n]),a[n]=Se.call(r,a[n]),s[n][0]!=a[n][0]||"M"==s[n][0]||"A"==s[n][0]&&(s[n][4]!=a[n][4]||s[n][5]!=a[n][5])?(Array.prototype.splice.apply(s,[n,1].concat(Le.call(o,s[n]))),Array.prototype.splice.apply(a,[n,1].concat(Le.call(r,a[n])))):(s[n]=Ce.call(o,s[n]),a[n]=Ce.call(r,a[n])),++n!=s.length||n!=a.length;)n==s.length&&s.push(["C",o.pos[0],o.pos[1],o.pos[0],o.pos[1],o.pos[0],o.pos[1]]),n==a.length&&a.push(["C",r.pos[0],r.pos[1],r.pos[0],r.pos[1],r.pos[0],r.pos[1]]);return{start:s,dest:a}}(e,i,a)),l=l.slice(0,h).concat(n.start,!1===c?[]:l.slice(c)),e=e.slice(0,i).concat(n.dest,!1===a?[]:e.slice(a)),h=!1!==c&&h+n.start.length,i=!1!==a&&i+n.dest.length;return this.value=l,this.destination=new SVG.PathArray,this.destination.value=e,this}}),function(){function s(t){t.remember("_draggable",this),this.el=t}s.prototype.init=function(t,e){var i=this;this.constraint=t,this.value=e,this.el.on("mousedown.drag",function(t){i.start(t)}),this.el.on("touchstart.drag",function(t){i.start(t)})},s.prototype.transformPoint=function(t,e){return t=(t=t||window.event).changedTouches&&t.changedTouches[0]||t,this.p.x=t.clientX-(e||0),this.p.y=t.clientY,this.p.matrixTransform(this.m)},s.prototype.getBBox=function(){var t=this.el.bbox();return this.el instanceof SVG.Nested&&(t=this.el.rbox()),(this.el instanceof SVG.G||this.el instanceof SVG.Use||this.el instanceof SVG.Nested)&&(t.x=this.el.x(),t.y=this.el.y()),t},s.prototype.start=function(t){if("click"!=t.type&&"mousedown"!=t.type&&"mousemove"!=t.type||1==(t.which||t.buttons)){var e=this;if(this.el.fire("beforedrag",{event:t,handler:this}),!this.el.event().defaultPrevented){t.preventDefault(),t.stopPropagation(),this.parent=this.parent||this.el.parent(SVG.Nested)||this.el.parent(SVG.Doc),this.p=this.parent.node.createSVGPoint(),this.m=this.el.node.getScreenCTM().inverse();var i,s=this.getBBox();if(this.el instanceof SVG.Text)switch(i=this.el.node.getComputedTextLength(),this.el.attr("text-anchor")){case"middle":i/=2;break;case"start":i=0}this.startPoints={point:this.transformPoint(t,i),box:s,transform:this.el.transform()},SVG.on(window,"mousemove.drag",function(t){e.drag(t)}),SVG.on(window,"touchmove.drag",function(t){e.drag(t)}),SVG.on(window,"mouseup.drag",function(t){e.end(t)}),SVG.on(window,"touchend.drag",function(t){e.end(t)}),this.el.fire("dragstart",{event:t,p:this.startPoints.point,m:this.m,handler:this})}}},s.prototype.drag=function(t){var e=this.getBBox(),i=this.transformPoint(t),s=this.startPoints.box.x+i.x-this.startPoints.point.x,a=this.startPoints.box.y+i.y-this.startPoints.point.y,n=this.constraint,o=i.x-this.startPoints.point.x,r=i.y-this.startPoints.point.y;return this.el.fire("dragmove",{event:t,p:i,m:this.m,handler:this}),this.el.event().defaultPrevented||("function"==typeof n?(!0===(t="boolean"==typeof(t=n.call(this.el,s,a,this.m))?{x:t,y:t}:t).x?this.el.x(s):!1!==t.x&&this.el.x(t.x),!0===t.y?this.el.y(a):!1!==t.y&&this.el.y(t.y)):"object"==typeof n&&(null!=n.minX&&s<n.minX?o=(s=n.minX)-this.startPoints.box.x:null!=n.maxX&&s>n.maxX-e.width&&(o=(s=n.maxX-e.width)-this.startPoints.box.x),null!=n.minY&&a<n.minY?r=(a=n.minY)-this.startPoints.box.y:null!=n.maxY&&a>n.maxY-e.height&&(r=(a=n.maxY-e.height)-this.startPoints.box.y),null!=n.snapToGrid&&(s-=s%n.snapToGrid,a-=a%n.snapToGrid,o-=o%n.snapToGrid,r-=r%n.snapToGrid),this.el instanceof SVG.G?this.el.matrix(this.startPoints.transform).transform({x:o,y:r},!0):this.el.move(s,a))),i},s.prototype.end=function(t){var e=this.drag(t);this.el.fire("dragend",{event:t,p:e,m:this.m,handler:this}),SVG.off(window,"mousemove.drag"),SVG.off(window,"touchmove.drag"),SVG.off(window,"mouseup.drag"),SVG.off(window,"touchend.drag")},SVG.extend(SVG.Element,{draggable:function(t,e){"function"!=typeof t&&"object"!=typeof t||(e=t,t=!0);var i=this.remember("_draggable")||new s(this);return(t=void 0===t||t)?i.init(e||{},t):(this.off("mousedown.drag"),this.off("touchstart.drag")),this}})}.call(void 0),w.prototype.init=function(t,e){var i,s=this.el.bbox();for(i in this.options={},this.el.selectize.defaults)this.options[i]=this.el.selectize.defaults[i],void 0!==e[i]&&(this.options[i]=e[i]);this.parent=this.el.parent(),this.nested=this.nested||this.parent.group(),this.nested.matrix(new SVG.Matrix(this.el).translate(s.x,s.y)),this.options.deepSelect&&-1!==["line","polyline","polygon"].indexOf(this.el.type)?this.selectPoints(t):this.selectRect(t),this.observe(),this.cleanup()},w.prototype.selectPoints=function(t){return this.pointSelection.isSelected=t,this.pointSelection.set||(this.pointSelection.set=this.parent.set(),this.drawCircles()),this},w.prototype.getPointArray=function(){var e=this.el.bbox();return this.el.array().valueOf().map(function(t){return[t[0]-e.x,t[1]-e.y]})},w.prototype.drawCircles=function(){for(var a=this,t=this.getPointArray(),e=0,i=t.length;e<i;++e){var s=function(s){return function(t){(t=t||window.event).preventDefault?t.preventDefault():t.returnValue=!1,t.stopPropagation();var e=t.pageX||t.touches[0].pageX,i=t.pageY||t.touches[0].pageY;a.el.fire("point",{x:e,y:i,i:s,event:t})}}(e);this.pointSelection.set.add(this.nested.circle(this.options.radius).center(t[e][0],t[e][1]).addClass(this.options.classPoints).addClass(this.options.classPoints+"_point").on("touchstart",s).on("mousedown",s))}},w.prototype.updatePointSelection=function(){var e=this.getPointArray();this.pointSelection.set.each(function(t){this.cx()===e[t][0]&&this.cy()===e[t][1]||this.center(e[t][0],e[t][1])})},w.prototype.updateRectSelection=function(){var t=this.el.bbox();this.rectSelection.set.get(0).attr({width:t.width,height:t.height}),this.options.points&&(this.rectSelection.set.get(2).center(t.width,0),this.rectSelection.set.get(3).center(t.width,t.height),this.rectSelection.set.get(4).center(0,t.height),this.rectSelection.set.get(5).center(t.width/2,0),this.rectSelection.set.get(6).center(t.width,t.height/2),this.rectSelection.set.get(7).center(t.width/2,t.height),this.rectSelection.set.get(8).center(0,t.height/2)),this.options.rotationPoint&&(this.options.points?this.rectSelection.set.get(9):this.rectSelection.set.get(1)).center(t.width/2,20)},w.prototype.selectRect=function(t){var e,a=this,i=this.el.bbox();function s(s){return function(t){(t=t||window.event).preventDefault?t.preventDefault():t.returnValue=!1,t.stopPropagation();var e=t.pageX||t.touches[0].pageX,i=t.pageY||t.touches[0].pageY;a.el.fire(s,{x:e,y:i,event:t})}}this.rectSelection.isSelected=t,this.rectSelection.set=this.rectSelection.set||this.parent.set(),this.rectSelection.set.get(0)||this.rectSelection.set.add(this.nested.rect(i.width,i.height).addClass(this.options.classRect)),this.options.points&&!this.rectSelection.set.get(1)&&(e="touchstart",t="mousedown",this.rectSelection.set.add(this.nested.circle(this.options.radius).center(0,0).attr("class",this.options.classPoints+"_lt").on(t,s("lt")).on(e,s("lt"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width,0).attr("class",this.options.classPoints+"_rt").on(t,s("rt")).on(e,s("rt"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width,i.height).attr("class",this.options.classPoints+"_rb").on(t,s("rb")).on(e,s("rb"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(0,i.height).attr("class",this.options.classPoints+"_lb").on(t,s("lb")).on(e,s("lb"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width/2,0).attr("class",this.options.classPoints+"_t").on(t,s("t")).on(e,s("t"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width,i.height/2).attr("class",this.options.classPoints+"_r").on(t,s("r")).on(e,s("r"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width/2,i.height).attr("class",this.options.classPoints+"_b").on(t,s("b")).on(e,s("b"))),this.rectSelection.set.add(this.nested.circle(this.options.radius).center(0,i.height/2).attr("class",this.options.classPoints+"_l").on(t,s("l")).on(e,s("l"))),this.rectSelection.set.each(function(){this.addClass(a.options.classPoints)})),this.options.rotationPoint&&(this.options.points&&!this.rectSelection.set.get(9)||!this.options.points&&!this.rectSelection.set.get(1))&&(e=function(t){(t=t||window.event).preventDefault?t.preventDefault():t.returnValue=!1,t.stopPropagation();var e=t.pageX||t.touches[0].pageX,i=t.pageY||t.touches[0].pageY;a.el.fire("rot",{x:e,y:i,event:t})},this.rectSelection.set.add(this.nested.circle(this.options.radius).center(i.width/2,20).attr("class",this.options.classPoints+"_rot").on("touchstart",e).on("mousedown",e)))},w.prototype.handler=function(){var t=this.el.bbox();this.nested.matrix(new SVG.Matrix(this.el).translate(t.x,t.y)),this.rectSelection.isSelected&&this.updateRectSelection(),this.pointSelection.isSelected&&this.updatePointSelection()},w.prototype.observe=function(){var t=this;if(MutationObserver)if(this.rectSelection.isSelected||this.pointSelection.isSelected)this.observerInst=this.observerInst||new MutationObserver(function(){t.handler()}),this.observerInst.observe(this.el.node,{attributes:!0});else try{this.observerInst.disconnect(),delete this.observerInst}catch(t){}else this.el.off("DOMAttrModified.select"),(this.rectSelection.isSelected||this.pointSelection.isSelected)&&this.el.on("DOMAttrModified.select",function(){t.handler()})},w.prototype.cleanup=function(){!this.rectSelection.isSelected&&this.rectSelection.set&&(this.rectSelection.set.each(function(){this.remove()}),this.rectSelection.set.clear(),delete this.rectSelection.set),!this.pointSelection.isSelected&&this.pointSelection.set&&(this.pointSelection.set.each(function(){this.remove()}),this.pointSelection.set.clear(),delete this.pointSelection.set),this.pointSelection.isSelected||this.rectSelection.isSelected||(this.nested.remove(),delete this.nested)},SVG.extend(SVG.Element,{selectize:function(t,e){return"object"==typeof t&&(e=t,t=!0),(this.remember("_selectHandler")||new w(this)).init(void 0===t||t,e||{}),this}}),SVG.Element.prototype.selectize.defaults={points:!0,classRect:"svg_select_boundingRect",classPoints:"svg_select_points",radius:7,rotationPoint:!0,deepSelect:!1},function(){!function(){function e(t){t.remember("_resizeHandler",this),this.el=t,this.parameters={},this.lastUpdateCall=null,this.p=t.doc().node.createSVGPoint()}e.prototype.transformPoint=function(t,e,i){return this.p.x=t-(this.offset.x-window.pageXOffset),this.p.y=e-(this.offset.y-window.pageYOffset),this.p.matrixTransform(i||this.m)},e.prototype._extractPosition=function(t){return{x:(null!=t.clientX?t:t.touches[0]).clientX,y:(null!=t.clientY?t:t.touches[0]).clientY}},e.prototype.init=function(t){var e=this;if(this.stop(),"stop"!==t){for(var i in this.options={},this.el.resize.defaults)this.options[i]=this.el.resize.defaults[i],void 0!==t[i]&&(this.options[i]=t[i]);this.el.on("lt.resize",function(t){e.resize(t||window.event)}),this.el.on("rt.resize",function(t){e.resize(t||window.event)}),this.el.on("rb.resize",function(t){e.resize(t||window.event)}),this.el.on("lb.resize",function(t){e.resize(t||window.event)}),this.el.on("t.resize",function(t){e.resize(t||window.event)}),this.el.on("r.resize",function(t){e.resize(t||window.event)}),this.el.on("b.resize",function(t){e.resize(t||window.event)}),this.el.on("l.resize",function(t){e.resize(t||window.event)}),this.el.on("rot.resize",function(t){e.resize(t||window.event)}),this.el.on("point.resize",function(t){e.resize(t||window.event)}),this.update()}},e.prototype.stop=function(){return this.el.off("lt.resize"),this.el.off("rt.resize"),this.el.off("rb.resize"),this.el.off("lb.resize"),this.el.off("t.resize"),this.el.off("r.resize"),this.el.off("b.resize"),this.el.off("l.resize"),this.el.off("rot.resize"),this.el.off("point.resize"),this},e.prototype.resize=function(t){var e=this,i=(this.m=this.el.node.getScreenCTM().inverse(),this.offset={x:window.pageXOffset,y:window.pageYOffset},this._extractPosition(t.detail.event));switch(this.parameters={type:this.el.type,p:this.transformPoint(i.x,i.y),x:t.detail.x,y:t.detail.y,box:this.el.bbox(),rotation:this.el.transform().rotation},"text"===this.el.type&&(this.parameters.fontSize=this.el.attr()["font-size"]),void 0!==t.detail.i&&(i=this.el.array().valueOf(),this.parameters.i=t.detail.i,this.parameters.pointCoords=[i[t.detail.i][0],i[t.detail.i][1]]),t.type){case"lt":this.calc=function(t,e){e=this.snapToGrid(t,e),0<this.parameters.box.width-e[0]&&0<this.parameters.box.height-e[1]&&("text"===this.parameters.type?(this.el.move(this.parameters.box.x+e[0],this.parameters.box.y),this.el.attr("font-size",this.parameters.fontSize-e[0])):(e=this.checkAspectRatio(e),this.el.move(this.parameters.box.x+e[0],this.parameters.box.y+e[1]).size(this.parameters.box.width-e[0],this.parameters.box.height-e[1])))};break;case"rt":this.calc=function(t,e){e=this.snapToGrid(t,e,2),0<this.parameters.box.width+e[0]&&0<this.parameters.box.height-e[1]&&("text"===this.parameters.type?(this.el.move(this.parameters.box.x-e[0],this.parameters.box.y),this.el.attr("font-size",this.parameters.fontSize+e[0])):(e=this.checkAspectRatio(e,!0),this.el.move(this.parameters.box.x,this.parameters.box.y+e[1]).size(this.parameters.box.width+e[0],this.parameters.box.height-e[1])))};break;case"rb":this.calc=function(t,e){e=this.snapToGrid(t,e,0),0<this.parameters.box.width+e[0]&&0<this.parameters.box.height+e[1]&&("text"===this.parameters.type?(this.el.move(this.parameters.box.x-e[0],this.parameters.box.y),this.el.attr("font-size",this.parameters.fontSize+e[0])):(e=this.checkAspectRatio(e),this.el.move(this.parameters.box.x,this.parameters.box.y).size(this.parameters.box.width+e[0],this.parameters.box.height+e[1])))};break;case"lb":this.calc=function(t,e){e=this.snapToGrid(t,e,1),0<this.parameters.box.width-e[0]&&0<this.parameters.box.height+e[1]&&("text"===this.parameters.type?(this.el.move(this.parameters.box.x+e[0],this.parameters.box.y),this.el.attr("font-size",this.parameters.fontSize-e[0])):(e=this.checkAspectRatio(e,!0),this.el.move(this.parameters.box.x+e[0],this.parameters.box.y).size(this.parameters.box.width-e[0],this.parameters.box.height+e[1])))};break;case"t":this.calc=function(t,e){e=this.snapToGrid(t,e,2),0<this.parameters.box.height-e[1]&&"text"!==this.parameters.type&&this.el.move(this.parameters.box.x,this.parameters.box.y+e[1]).height(this.parameters.box.height-e[1])};break;case"r":this.calc=function(t,e){e=this.snapToGrid(t,e,0),0<this.parameters.box.width+e[0]&&"text"!==this.parameters.type&&this.el.move(this.parameters.box.x,this.parameters.box.y).width(this.parameters.box.width+e[0])};break;case"b":this.calc=function(t,e){e=this.snapToGrid(t,e,0),0<this.parameters.box.height+e[1]&&"text"!==this.parameters.type&&this.el.move(this.parameters.box.x,this.parameters.box.y).height(this.parameters.box.height+e[1])};break;case"l":this.calc=function(t,e){e=this.snapToGrid(t,e,1),0<this.parameters.box.width-e[0]&&"text"!==this.parameters.type&&this.el.move(this.parameters.box.x+e[0],this.parameters.box.y).width(this.parameters.box.width-e[0])};break;case"rot":this.calc=function(t,e){var i=t+this.parameters.p.x,t=e+this.parameters.p.y,e=Math.atan2(this.parameters.p.y-this.parameters.box.y-this.parameters.box.height/2,this.parameters.p.x-this.parameters.box.x-this.parameters.box.width/2),i=Math.atan2(t-this.parameters.box.y-this.parameters.box.height/2,i-this.parameters.box.x-this.parameters.box.width/2),e=this.parameters.rotation+180*(i-e)/Math.PI+this.options.snapToAngle/2;this.el.center(this.parameters.box.cx,this.parameters.box.cy).rotate(e-e%this.options.snapToAngle,this.parameters.box.cx,this.parameters.box.cy)};break;case"point":this.calc=function(t,e){t=this.snapToGrid(t,e,this.parameters.pointCoords[0],this.parameters.pointCoords[1]),(e=this.el.array().valueOf())[this.parameters.i][0]=this.parameters.pointCoords[0]+t[0],e[this.parameters.i][1]=this.parameters.pointCoords[1]+t[1],this.el.plot(e)}}this.el.fire("resizestart",{dx:this.parameters.x,dy:this.parameters.y,event:t}),SVG.on(window,"touchmove.resize",function(t){e.update(t||window.event)}),SVG.on(window,"touchend.resize",function(){e.done()}),SVG.on(window,"mousemove.resize",function(t){e.update(t||window.event)}),SVG.on(window,"mouseup.resize",function(){e.done()})},e.prototype.update=function(t){var e,i;t?(e=this._extractPosition(t),e=(i=this.transformPoint(e.x,e.y)).x-this.parameters.p.x,i=i.y-this.parameters.p.y,this.lastUpdateCall=[e,i],this.calc(e,i),this.el.fire("resizing",{dx:e,dy:i,event:t})):this.lastUpdateCall&&this.calc(this.lastUpdateCall[0],this.lastUpdateCall[1])},e.prototype.done=function(){this.lastUpdateCall=null,SVG.off(window,"mousemove.resize"),SVG.off(window,"mouseup.resize"),SVG.off(window,"touchmove.resize"),SVG.off(window,"touchend.resize"),this.el.fire("resizedone")},e.prototype.snapToGrid=function(t,e,i,s){var a=void 0!==s?[(i+t)%this.options.snapToGrid,(s+e)%this.options.snapToGrid]:[(this.parameters.box.x+t+(1&(i=null==i?3:i)?0:this.parameters.box.width))%this.options.snapToGrid,(this.parameters.box.y+e+(2&i?0:this.parameters.box.height))%this.options.snapToGrid];return t<0&&(a[0]-=this.options.snapToGrid),e<0&&(a[1]-=this.options.snapToGrid),t-=Math.abs(a[0])<this.options.snapToGrid/2?a[0]:a[0]-(t<0?-this.options.snapToGrid:this.options.snapToGrid),e-=Math.abs(a[1])<this.options.snapToGrid/2?a[1]:a[1]-(e<0?-this.options.snapToGrid:this.options.snapToGrid),this.constraintToBox(t,e,i,s)},e.prototype.constraintToBox=function(t,e,i,s){var a,n=this.options.constraint||{},i=void 0!==s?(a=i,s):(a=this.parameters.box.x+(1&i?0:this.parameters.box.width),this.parameters.box.y+(2&i?0:this.parameters.box.height));return void 0!==n.minX&&a+t<n.minX&&(t=n.minX-a),void 0!==n.minY&&i+e<n.minY&&(e=n.minY-i),[t=void 0!==n.maxX&&n.maxX<a+t?n.maxX-a:t,e=void 0!==n.maxY&&n.maxY<i+e?n.maxY-i:e]},e.prototype.checkAspectRatio=function(t,e){var i,s,a,n;return this.options.saveAspectRatio?(i=t.slice(),s=this.parameters.box.width/this.parameters.box.height,(t=(a=this.parameters.box.width+t[0])/(n=this.parameters.box.height-t[1]))<s?(i[1]=a/s-this.parameters.box.height,e&&(i[1]=-i[1])):s<t&&(i[0]=this.parameters.box.width-n*s,e)&&(i[0]=-i[0]),i):t},SVG.extend(SVG.Element,{resize:function(t){return(this.remember("_resizeHandler")||new e(this)).init(t||{}),this}}),SVG.Element.prototype.resize.defaults={snapToAngle:.1,snapToGrid:1,constraint:{},saveAspectRatio:!1}}.call(this)}(),b='.apexcharts-canvas {\n  position: relative;\n  user-select: none;\n  /* cannot give overflow: hidden as it will crop tooltips which overflow outside chart area */\n}\n\n/* scrollbar is not visible by default for legend, hence forcing the visibility */\n.apexcharts-canvas ::-webkit-scrollbar {\n  -webkit-appearance: none;\n  width: 6px;\n}\n.apexcharts-canvas ::-webkit-scrollbar-thumb {\n  border-radius: 4px;\n  background-color: rgba(0,0,0,.5);\n  box-shadow: 0 0 1px rgba(255,255,255,.5);\n  -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);\n}\n.apexcharts-canvas.dark {\n  background: #343F57;\n}\n\n.apexcharts-inner {\n  position: relative;\n}\n\n.legend-mouseover-inactive {\n  transition: 0.15s ease all;\n  opacity: 0.20;\n}\n\n.apexcharts-series-collapsed {\n  opacity: 0;\n}\n\n.apexcharts-gridline, .apexcharts-text {\n  pointer-events: none;\n}\n\n.apexcharts-tooltip {\n  border-radius: 5px;\n  box-shadow: 2px 2px 6px -4px #999;\n  cursor: default;\n  font-size: 14px;\n  left: 62px;\n  opacity: 0;\n  pointer-events: none;\n  position: absolute;\n  top: 20px;\n  overflow: hidden;\n  white-space: nowrap;\n  z-index: 12;\n  transition: 0.15s ease all;\n}\n.apexcharts-tooltip.light {\n  border: 1px solid #e3e3e3;\n  background: rgba(255, 255, 255, 0.96);\n}\n.apexcharts-tooltip.dark {\n  color: #fff;\n  background: rgba(30,30,30, 0.8);\n}\n.apexcharts-tooltip * {\n  font-family: inherit;\n}\n\n.apexcharts-tooltip .apexcharts-marker,\n.apexcharts-area-series .apexcharts-area,\n.apexcharts-line {\n  pointer-events: none;\n}\n\n.apexcharts-tooltip.active {\n  opacity: 1;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-tooltip-title {\n  padding: 6px;\n  font-size: 15px;\n  margin-bottom: 4px;\n}\n.apexcharts-tooltip.light .apexcharts-tooltip-title {\n  background: #ECEFF1;\n  border-bottom: 1px solid #ddd;\n}\n.apexcharts-tooltip.dark .apexcharts-tooltip-title {\n  background: rgba(0, 0, 0, 0.7);\n  border-bottom: 1px solid #333;\n}\n\n.apexcharts-tooltip-text-value,\n.apexcharts-tooltip-text-z-value {\n  display: inline-block;\n  font-weight: 600;\n  margin-left: 5px;\n}\n\n.apexcharts-tooltip-text-z-label:empty,\n.apexcharts-tooltip-text-z-value:empty {\n  display: none;\n}\n\n.apexcharts-tooltip-text-value, \n.apexcharts-tooltip-text-z-value {\n  font-weight: 600;\n}\n\n.apexcharts-tooltip-marker {\n  width: 12px;\n  height: 12px;\n  position: relative;\n  top: 0px;\n  margin-right: 10px;\n  border-radius: 50%;\n}\n\n.apexcharts-tooltip-series-group {\n  padding: 0 10px;\n  display: none;\n  text-align: left;\n  justify-content: left;\n  align-items: center;\n}\n\n.apexcharts-tooltip-series-group.active .apexcharts-tooltip-marker {\n  opacity: 1;\n}\n.apexcharts-tooltip-series-group.active, .apexcharts-tooltip-series-group:last-child {\n  padding-bottom: 4px;\n}\n.apexcharts-tooltip-series-group-hidden {\n  opacity: 0;\n  height: 0;\n  line-height: 0;\n  padding: 0 !important;\n}\n.apexcharts-tooltip-y-group {\n  padding: 6px 0 5px;\n}\n.apexcharts-tooltip-candlestick {\n  padding: 4px 8px;\n}\n.apexcharts-tooltip-candlestick > div {\n  margin: 4px 0;\n}\n.apexcharts-tooltip-candlestick span.value {\n  font-weight: bold;\n}\n\n.apexcharts-tooltip-rangebar {\n  padding: 5px 8px;\n}\n\n.apexcharts-tooltip-rangebar .category {\n  font-weight: 600;\n  color: #777;\n}\n\n.apexcharts-tooltip-rangebar .series-name {\n  font-weight: bold;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.apexcharts-xaxistooltip {\n  opacity: 0;\n  padding: 9px 10px;\n  pointer-events: none;\n  color: #373d3f;\n  font-size: 13px;\n  text-align: center;\n  border-radius: 2px;\n  position: absolute;\n  z-index: 10;\n\tbackground: #ECEFF1;\n  border: 1px solid #90A4AE;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-xaxistooltip.dark {\n  background: rgba(0, 0, 0, 0.7);\n  border: 1px solid rgba(0, 0, 0, 0.5);\n  color: #fff;\n}\n\n.apexcharts-xaxistooltip:after, .apexcharts-xaxistooltip:before {\n\tleft: 50%;\n\tborder: solid transparent;\n\tcontent: " ";\n\theight: 0;\n\twidth: 0;\n\tposition: absolute;\n\tpointer-events: none;\n}\n\n.apexcharts-xaxistooltip:after {\n\tborder-color: rgba(236, 239, 241, 0);\n\tborder-width: 6px;\n\tmargin-left: -6px;\n}\n.apexcharts-xaxistooltip:before {\n\tborder-color: rgba(144, 164, 174, 0);\n\tborder-width: 7px;\n\tmargin-left: -7px;\n}\n\n.apexcharts-xaxistooltip-bottom:after, .apexcharts-xaxistooltip-bottom:before {\n  bottom: 100%;\n}\n\n.apexcharts-xaxistooltip-top:after, .apexcharts-xaxistooltip-top:before {\n  top: 100%;\n}\n\n.apexcharts-xaxistooltip-bottom:after {\n  border-bottom-color: #ECEFF1;\n}\n.apexcharts-xaxistooltip-bottom:before {\n  border-bottom-color: #90A4AE;\n}\n\n.apexcharts-xaxistooltip-bottom.dark:after {\n  border-bottom-color: rgba(0, 0, 0, 0.5);\n}\n.apexcharts-xaxistooltip-bottom.dark:before {\n  border-bottom-color: rgba(0, 0, 0, 0.5);\n}\n\n.apexcharts-xaxistooltip-top:after {\n  border-top-color:#ECEFF1\n}\n.apexcharts-xaxistooltip-top:before {\n  border-top-color: #90A4AE;\n}\n.apexcharts-xaxistooltip-top.dark:after {\n  border-top-color:rgba(0, 0, 0, 0.5);\n}\n.apexcharts-xaxistooltip-top.dark:before {\n  border-top-color: rgba(0, 0, 0, 0.5);\n}\n\n\n.apexcharts-xaxistooltip.active {\n  opacity: 1;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-yaxistooltip {\n  opacity: 0;\n  padding: 4px 10px;\n  pointer-events: none;\n  color: #373d3f;\n  font-size: 13px;\n  text-align: center;\n  border-radius: 2px;\n  position: absolute;\n  z-index: 10;\n\tbackground: #ECEFF1;\n  border: 1px solid #90A4AE;\n}\n\n.apexcharts-yaxistooltip.dark {\n  background: rgba(0, 0, 0, 0.7);\n  border: 1px solid rgba(0, 0, 0, 0.5);\n  color: #fff;\n}\n\n.apexcharts-yaxistooltip:after, .apexcharts-yaxistooltip:before {\n\ttop: 50%;\n\tborder: solid transparent;\n\tcontent: " ";\n\theight: 0;\n\twidth: 0;\n\tposition: absolute;\n\tpointer-events: none;\n}\n.apexcharts-yaxistooltip:after {\n\tborder-color: rgba(236, 239, 241, 0);\n\tborder-width: 6px;\n\tmargin-top: -6px;\n}\n.apexcharts-yaxistooltip:before {\n\tborder-color: rgba(144, 164, 174, 0);\n\tborder-width: 7px;\n\tmargin-top: -7px;\n}\n\n.apexcharts-yaxistooltip-left:after, .apexcharts-yaxistooltip-left:before {\n  left: 100%;\n}\n\n.apexcharts-yaxistooltip-right:after, .apexcharts-yaxistooltip-right:before {\n  right: 100%;\n}\n\n.apexcharts-yaxistooltip-left:after {\n  border-left-color: #ECEFF1;\n}\n.apexcharts-yaxistooltip-left:before {\n  border-left-color: #90A4AE;\n}\n.apexcharts-yaxistooltip-left.dark:after {\n  border-left-color: rgba(0, 0, 0, 0.5);\n}\n.apexcharts-yaxistooltip-left.dark:before {\n  border-left-color: rgba(0, 0, 0, 0.5);\n}\n\n.apexcharts-yaxistooltip-right:after {\n  border-right-color: #ECEFF1;\n}\n.apexcharts-yaxistooltip-right:before {\n  border-right-color: #90A4AE;\n}\n.apexcharts-yaxistooltip-right.dark:after {\n  border-right-color: rgba(0, 0, 0, 0.5);\n}\n.apexcharts-yaxistooltip-right.dark:before {\n  border-right-color: rgba(0, 0, 0, 0.5);\n}\n\n.apexcharts-yaxistooltip.active {\n  opacity: 1;\n}\n.apexcharts-yaxistooltip-hidden {\n  display: none;\n}\n\n.apexcharts-xcrosshairs, .apexcharts-ycrosshairs {\n  pointer-events: none;\n  opacity: 0;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-xcrosshairs.active, .apexcharts-ycrosshairs.active {\n  opacity: 1;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-ycrosshairs-hidden {\n  opacity: 0;\n}\n\n.apexcharts-zoom-rect {\n  pointer-events: none;\n}\n.apexcharts-selection-rect {\n  cursor: move;\n}\n\n.svg_select_points, .svg_select_points_rot {\n  opacity: 0;\n  visibility: hidden;\n}\n.svg_select_points_l, .svg_select_points_r {\n  cursor: ew-resize;\n  opacity: 1;\n  visibility: visible;\n  fill: #888;\n}\n.apexcharts-canvas.zoomable .hovering-zoom {\n  cursor: crosshair\n}\n.apexcharts-canvas.zoomable .hovering-pan {\n  cursor: move\n}\n\n.apexcharts-xaxis,\n.apexcharts-yaxis {\n  pointer-events: none;\n}\n\n.apexcharts-zoom-icon, \n.apexcharts-zoom-in-icon,\n.apexcharts-zoom-out-icon,\n.apexcharts-reset-zoom-icon, \n.apexcharts-pan-icon, \n.apexcharts-selection-icon,\n.apexcharts-menu-icon, \n.apexcharts-toolbar-custom-icon {\n  cursor: pointer;\n  width: 20px;\n  height: 20px;\n  line-height: 24px;\n  color: #6E8192;\n  text-align: center;\n}\n\n\n.apexcharts-zoom-icon svg, \n.apexcharts-zoom-in-icon svg,\n.apexcharts-zoom-out-icon svg,\n.apexcharts-reset-zoom-icon svg,\n.apexcharts-menu-icon svg {\n  fill: #6E8192;\n}\n.apexcharts-selection-icon svg {\n  fill: #444;\n  transform: scale(0.76)\n}\n\n.dark .apexcharts-zoom-icon svg, \n.dark .apexcharts-zoom-in-icon svg,\n.dark .apexcharts-zoom-out-icon svg,\n.dark .apexcharts-reset-zoom-icon svg, \n.dark .apexcharts-pan-icon svg, \n.dark .apexcharts-selection-icon svg,\n.dark .apexcharts-menu-icon svg, \n.dark .apexcharts-toolbar-custom-icon svg{\n  fill: #f3f4f5;\n}\n\n.apexcharts-canvas .apexcharts-zoom-icon.selected svg, \n.apexcharts-canvas .apexcharts-selection-icon.selected svg, \n.apexcharts-canvas .apexcharts-reset-zoom-icon.selected svg {\n  fill: #008FFB;\n}\n.light .apexcharts-selection-icon:not(.selected):hover svg,\n.light .apexcharts-zoom-icon:not(.selected):hover svg, \n.light .apexcharts-zoom-in-icon:hover svg, \n.light .apexcharts-zoom-out-icon:hover svg, \n.light .apexcharts-reset-zoom-icon:hover svg, \n.light .apexcharts-menu-icon:hover svg {\n  fill: #333;\n}\n\n.apexcharts-selection-icon, .apexcharts-menu-icon {\n  position: relative;\n}\n.apexcharts-reset-zoom-icon {\n  margin-left: 5px;\n}\n.apexcharts-zoom-icon, .apexcharts-reset-zoom-icon, .apexcharts-menu-icon {\n  transform: scale(0.85);\n}\n\n.apexcharts-zoom-in-icon, .apexcharts-zoom-out-icon {\n  transform: scale(0.7)\n}\n\n.apexcharts-zoom-out-icon {\n  margin-right: 3px;\n}\n\n.apexcharts-pan-icon {\n  transform: scale(0.62);\n  position: relative;\n  left: 1px;\n  top: 0px;\n}\n.apexcharts-pan-icon svg {\n  fill: #fff;\n  stroke: #6E8192;\n  stroke-width: 2;\n}\n.apexcharts-pan-icon.selected svg {\n  stroke: #008FFB;\n}\n.apexcharts-pan-icon:not(.selected):hover svg {\n  stroke: #333;\n}\n\n.apexcharts-toolbar {\n  position: absolute;\n  z-index: 11;\n  top: 0px;\n  right: 3px;\n  max-width: 176px;\n  text-align: right;\n  border-radius: 3px;\n  padding: 0px 6px 2px 6px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center; \n}\n\n.apexcharts-toolbar svg {\n  pointer-events: none;\n}\n\n.apexcharts-menu {\n  background: #fff;\n  position: absolute;\n  top: 100%;\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  padding: 3px;\n  right: 10px;\n  opacity: 0;\n  min-width: 110px;\n  transition: 0.15s ease all;\n  pointer-events: none;\n}\n\n.apexcharts-menu.open {\n  opacity: 1;\n  pointer-events: all;\n  transition: 0.15s ease all;\n}\n\n.apexcharts-menu-item {\n  padding: 6px 7px;\n  font-size: 12px;\n  cursor: pointer;\n}\n.light .apexcharts-menu-item:hover {\n  background: #eee;\n}\n.dark .apexcharts-menu {\n  background: rgba(0, 0, 0, 0.7);\n  color: #fff;\n}\n\n@media screen and (min-width: 768px) {\n  .apexcharts-toolbar {\n    /*opacity: 0;*/\n  }\n\n  .apexcharts-canvas:hover .apexcharts-toolbar {\n    opacity: 1;\n  } \n}\n\n.apexcharts-datalabel.hidden {\n  opacity: 0;\n}\n\n.apexcharts-pie-label,\n.apexcharts-datalabel, .apexcharts-datalabel-label, .apexcharts-datalabel-value {\n  cursor: default;\n  pointer-events: none;\n}\n\n.apexcharts-pie-label-delay {\n  opacity: 0;\n  animation-name: opaque;\n  animation-duration: 0.3s;\n  animation-fill-mode: forwards;\n  animation-timing-function: ease;\n}\n\n.apexcharts-canvas .hidden {\n  opacity: 0;\n}\n\n.apexcharts-hide .apexcharts-series-points {\n  opacity: 0;\n}\n\n.apexcharts-area-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events,\n.apexcharts-line-series .apexcharts-series-markers .apexcharts-marker.no-pointer-events, .apexcharts-radar-series path, .apexcharts-radar-series polygon {\n  pointer-events: none;\n}\n\n/* markers */\n\n.apexcharts-marker {\n  transition: 0.15s ease all;\n}\n\n@keyframes opaque {\n  0% {\n    opacity: 0;\n  }\n  100% {\n    opacity: 1;\n  }\n}',Pe=(m=void 0===m?{}:m).insertAt,"undefined"!=typeof document&&(A=document.head||document.getElementsByTagName("head")[0],(m=document.createElement("style")).type="text/css","top"===Pe&&A.firstChild?A.insertBefore(m,A.firstChild):A.appendChild(m),m.styleSheet?m.styleSheet.cssText=b:m.appendChild(document.createTextNode(b))),"document"in self){if(!("classList"in document.createElement("_")&&(!document.createElementNS||"classList"in document.createElementNS("http://www.w3.org/2000/svg","g")))){var k=self;if("Element"in k){var Pe=k.Element.prototype,A=Object,Ee=String.prototype.trim||function(){return this.replace(/^\s+|\s+$/g,"")},Me=Array.prototype.indexOf||function(t){for(var e=0,i=this.length;e<i;e++)if(e in this&&this[e]===t)return e;return-1},Te=function(t,e){this.name=t,this.code=DOMException[t],this.message=e},Xe=function(t,e){if(""===e)throw new Te("SYNTAX_ERR","The token must not be empty.");if(/\s/.test(e))throw new Te("INVALID_CHARACTER_ERR","The token must not contain space characters.");return Me.call(t,e)},Ie=function(t){for(var e=Ee.call(t.getAttribute("class")||""),i=e?e.split(/\s+/):[],s=0,a=i.length;s<a;s++)this.push(i[s]);this._updateClassName=function(){t.setAttribute("class",this.toString())}},S=Ie.prototype=[],Ye=function(){return new Ie(this)};if(Te.prototype=Error.prototype,S.item=function(t){return this[t]||null},S.contains=function(t){return~Xe(this,t+"")},S.add=function(){for(var t,e=arguments,i=0,s=e.length,a=!1;~Xe(this,t=e[i]+"")||(this.push(t),a=!0),++i<s;);a&&this._updateClassName()},S.remove=function(){var t,e,i=arguments,s=0,a=i.length,n=!1;do{for(e=Xe(this,t=i[s]+"");~e;)this.splice(e,1),n=!0,e=Xe(this,t)}while(++s<a);n&&this._updateClassName()},S.toggle=function(t,e){var i=this.contains(t),s=i?!0!==e&&"remove":!1!==e&&"add";return s&&this[s](t),!0===e||!1===e?e:!i},S.replace=function(t,e){~(t=Xe(t+""))&&(this.splice(t,1,e),this._updateClassName())},S.toString=function(){return this.join(" ")},A.defineProperty){S={get:Ye,enumerable:!0,configurable:!0};try{A.defineProperty(Pe,"classList",S)}catch(k){void 0!==k.number&&-2146823252!==k.number||(S.enumerable=!1,A.defineProperty(Pe,"classList",S))}}else A.prototype.__defineGetter__&&Pe.__defineGetter__("classList",Ye)}}(m=document.createElement("_")).classList.add("c1","c2"),m.classList.contains("c2")||((b=function(t){var s=DOMTokenList.prototype[t];DOMTokenList.prototype[t]=function(t){for(var e=arguments.length,i=0;i<e;i++)s.call(this,arguments[i])}})("add"),b("remove")),m.classList.toggle("c3",!1),m.classList.contains("c3")&&($t=DOMTokenList.prototype.toggle,DOMTokenList.prototype.toggle=function(t,e){return 1 in arguments&&!this.contains(t)==!e?e:$t.call(this,t)}),"replace"in document.createElement("_").classList||(DOMTokenList.prototype.replace=function(t,e){var i=this.toString().split(" ");~(t=i.indexOf(t+""))&&(i=i.slice(t),this.remove.apply(this,i),this.add(e),this.add.apply(this,i.slice(1)))}),m=null}var Fe=!1;function Re(t){var e=(i=t.__resizeTriggers__).firstElementChild,t=i.lastElementChild,i=e.firstElementChild;t.scrollLeft=t.scrollWidth,t.scrollTop=t.scrollHeight,i.style.width=e.offsetWidth+1+"px",i.style.height=e.offsetHeight+1+"px",e.scrollLeft=e.scrollWidth,e.scrollTop=e.scrollHeight}function De(e){var t,i=this;Re(this),this.__resizeRAF__&&(t=this.__resizeRAF__,Oe(t)),this.__resizeRAF__=Ne(function(){i.offsetWidth==i.__resizeLast__.width&&i.offsetHeight==i.__resizeLast__.height||(i.__resizeLast__.width=i.offsetWidth,i.__resizeLast__.height=i.offsetHeight,i.__resizeListeners__.forEach(function(t){t.call(e)}))})}Ne=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||function(t){return window.setTimeout(t,20)},Oe=window.cancelAnimationFrame||window.mozCancelAnimationFrame||window.webkitCancelAnimationFrame||window.clearTimeout;var Ne,Oe,He="",We="animationstart",Be="Webkit Moz O ms".split(" "),Ve="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),Ge=document.createElement("fakeelement");if(!1==(void 0!==Ge.style.animationName||!1))for(var _e=0;_e<Be.length;_e++)if(void 0!==Ge.style[Be[_e]+"AnimationName"]){He="-"+Be[_e].toLowerCase()+"-",We=Ve[_e];break}var je="@"+He+"keyframes resizeanim { from { opacity: 0; } to { opacity: 0; } } ",Ue=He+"animation: 1ms resizeanim; ";return window.addResizeListener=function(e,t){var i,s,a;e.__resizeTriggers__||("static"==getComputedStyle(e).position&&(e.style.position="relative"),Fe||(i=(je||"")+".resize-triggers { "+(Ue||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',s=document.head||document.getElementsByTagName("head")[0],(a=document.createElement("style")).type="text/css",a.styleSheet?a.styleSheet.cssText=i:a.appendChild(document.createTextNode(i)),s.appendChild(a),Fe=!0),e.__resizeLast__={},e.__resizeListeners__=[],(e.__resizeTriggers__=document.createElement("div")).className="resize-triggers",e.__resizeTriggers__.innerHTML='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>',e.appendChild(e.__resizeTriggers__),Re(e),e.addEventListener("scroll",De,!0),We&&e.__resizeTriggers__.addEventListener(We,function(t){"resizeanim"==t.animationName&&Re(e)})),e.__resizeListeners__.push(t)},window.removeResizeListener=function(t,e){t&&(t.__resizeListeners__.splice(t.__resizeListeners__.indexOf(e),1),t.__resizeListeners__.length||(t.removeEventListener("scroll",De),t.__resizeTriggers__=!t.removeChild(t.__resizeTriggers__)))},window.Apex={},t(qe,[{key:"render",value:function(){var a=this;return new c(function(t,e){if(null!==a.el){void 0===Apex._chartInstances&&(Apex._chartInstances=[]),a.w.config.chart.id&&Apex._chartInstances.push({id:a.w.globals.chartID,group:a.w.config.chart.group,chart:a}),a.setLocale(a.w.config.chart.defaultLocale);var i=a.w.config.chart.events.beforeMount,s=("function"==typeof i&&i(a,a.w),a.fireEvent("beforeMount",[a,a.w]),window.addEventListener("resize",a.windowResizeHandler),window.addResizeListener(a.el.parentNode,a.parentResizeCallback.bind(a)),a.create(a.w.config.series,{}));if(!s)return t(a);a.mount(s).then(function(){t(s),"function"==typeof a.w.config.chart.events.mounted&&a.w.config.chart.events.mounted(a,a.w),a.fireEvent("mounted",[a,a.w])}).catch(function(t){e(t)})}else e(new Error("Element not found"))})}},{key:"initModules",value:function(){this.animations=new z(this),this.core=new gt(this.el,this),this.grid=new Qt(this),this.coreUtils=new et(this),this.config=new l({}),this.crosshairs=new _(this),this.options=new d,this.responsive=new Kt(this),this.series=new g(this),this.theme=new te(this),this.formatters=new J(this),this.titleSubtitle=new ce(this),this.legend=new lt(this),this.toolbar=new le(this),this.dimensions=new rt(this),this.zoomPanSelection=new he(this),this.w.globals.tooltip=new re(this)}},{key:"addEventListener",value:function(t,e){var i=this.w;i.globals.events.hasOwnProperty(t)?i.globals.events[t].push(e):i.globals.events[t]=[e]}},{key:"removeEventListener",value:function(t,e){var i=this.w;i.globals.events.hasOwnProperty(t)&&-1!==(e=i.globals.events[t].indexOf(e))&&i.globals.events[t].splice(e,1)}},{key:"fireEvent",value:function(t,e){var i=this.w;if(i.globals.events.hasOwnProperty(t)){e&&e.length||(e=[]);for(var s=i.globals.events[t],a=s.length,n=0;n<a;n++)s[n].apply(null,e)}}},{key:"create",value:function(t,e){var i=this.w,s=(this.initModules(),this.w.globals);return s.noData=!1,s.animationEnded=!1,this.responsive.checkResponsiveConfig(e),null===this.el||(this.core.setupElements(),0===s.svgWidth)?(s.animationEnded=!0,null):(e=et.checkComboSeries(t),s.comboCharts=e.comboCharts,s.comboChartsHasBars=e.comboChartsHasBars,(0===t.length||1===t.length&&t[0].data&&0===t[0].data.length)&&this.series.handleNoData(),this.setupEventHandlers(),this.core.parseData(t),this.theme.init(),new st(this).setGlobalMarkerSize(),this.formatters.setLabelFormatters(),this.titleSubtitle.draw(),i.globals.noData||this.legend.init(),this.series.hasAllSeriesEqualX(),s.axisCharts&&(this.core.coreCalculations(),"category"!==i.config.xaxis.type)&&this.formatters.setLabelFormatters(),this.formatters.heatmapLabelFormatters(),this.dimensions.plotCoords(),e=this.core.xySettings(),this.grid.createGridMask(),s=this.core.plotChartType(t,e),this.core.shiftGraphPosition(),t={plot:{left:i.globals.translateX,top:i.globals.translateY,width:i.globals.gridWidth,height:i.globals.gridHeight}},{elGraph:s,xyRatios:e,elInner:i.globals.dom.elGraphical,dimensions:t})}},{key:"mount",value:function(){var s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null,a=this,n=a.w;return new c(function(t,e){if(null===a.el)return e(new Error("Not enough data to display or target element not found"));if(null!==s&&!n.globals.allSeriesCollapsed||a.series.handleNoData(),a.annotations=new N(a),a.core.drawAxis(n.config.chart.type,s.xyRatios),a.grid=new Qt(a),"back"===n.config.grid.position&&a.grid.drawGrid(),"back"===n.config.annotations.position&&a.annotations.drawAnnotations(),s.elGraph instanceof Array)for(var i=0;i<s.elGraph.length;i++)n.globals.dom.elGraphical.add(s.elGraph[i]);else n.globals.dom.elGraphical.add(s.elGraph);"front"===n.config.grid.position&&a.grid.drawGrid(),"front"===n.config.xaxis.crosshairs.position&&a.crosshairs.drawXCrosshairs(),"front"===n.config.yaxis[0].crosshairs.position&&a.crosshairs.drawYCrosshairs(),"front"===n.config.annotations.position&&a.annotations.drawAnnotations(),n.globals.noData||(n.config.tooltip.enabled&&!n.globals.noData&&a.w.globals.tooltip.drawTooltip(s.xyRatios),n.globals.axisCharts&&n.globals.isXNumeric?(n.config.chart.zoom.enabled||n.config.chart.selection&&n.config.chart.selection.enabled||n.config.chart.pan&&n.config.chart.pan.enabled)&&a.zoomPanSelection.init({xyRatios:s.xyRatios}):((e=n.config.chart.toolbar.tools).zoom=!1,e.zoomin=!1,e.zoomout=!1,e.selection=!1,e.pan=!1,e.reset=!1),n.config.chart.toolbar.show&&!n.globals.allSeriesCollapsed&&a.toolbar.createToolbar()),0<n.globals.memory.methodsToExec.length&&n.globals.memory.methodsToExec.forEach(function(t){t.method(t.params,!1,t.context)}),n.globals.axisCharts||n.globals.noData||a.core.resizeNonAxisCharts(),t(a)})}},{key:"clearPreviousPaths",value:function(){var t=this.w;t.globals.previousPaths=[],t.globals.allSeriesCollapsed=!1,t.globals.collapsedSeries=[],t.globals.collapsedSeriesIndices=[]}},{key:"updateOptions",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1],i=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],s=!(3<arguments.length&&void 0!==arguments[3])||arguments[3],a=!(4<arguments.length&&void 0!==arguments[4])||arguments[4],n=this.w;return t.series&&(this.resetSeries(!1),t.series[0].data&&(t.series=t.series.map(function(t,e){return Q({},n.config.series[e],{name:t.name||n.config.series[e]&&n.config.series[e].name,type:t.type||n.config.series[e]&&n.config.series[e].type,data:t.data||n.config.series[e]&&n.config.series[e].data})})),this.revertDefaultAxisMinMax()),t.xaxis&&((t.xaxis.min||t.xaxis.max)&&this.forceXAxisUpdate(t),t.xaxis.categories)&&t.xaxis.categories.length&&n.config.xaxis.convertedCatToNumeric&&(t=O.convertCatToNumeric(t)),0<n.globals.collapsedSeriesIndices.length&&this.clearPreviousPaths(),t.theme&&(t=this.theme.updateThemeOptions(t)),this._updateOptions(t,e,i,s,a)}},{key:"_updateOptions",value:function(i){var s=1<arguments.length&&void 0!==arguments[1]&&arguments[1],a=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],t=!(3<arguments.length&&void 0!==arguments[3])||arguments[3],n=4<arguments.length&&void 0!==arguments[4]&&arguments[4],e=[this];t&&(e=this.getSyncedCharts()),this.w.globals.isExecCalled&&(this.w.globals.isExecCalled=!(e=[this])),e.forEach(function(t){var e=t.w;return e.globals.shouldAnimate=a,s||(e.globals.resized=!0,e.globals.dataChanged=!0,a&&t.series.getPreviousPaths()),i&&"object"===C(i)&&(t.config=new l(i),i=et.extendArrayProps(t.config,i),e.config=K.extend(e.config,i),n)&&(e.globals.lastXAxis=[],e.globals.lastYAxis=[],e.globals.initialConfig=K.extend({},e.config),e.globals.initialSeries=JSON.parse(JSON.stringify(e.config.series))),t.update(i)})}},{key:"updateSeries",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=!(2<arguments.length&&void 0!==arguments[2])||arguments[2];return this.resetSeries(!1),this.revertDefaultAxisMinMax(),this._updateSeries(t,e,i)}},{key:"appendSeries",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],s=this.w.config.series.slice();return s.push(t),this.resetSeries(!1),this.revertDefaultAxisMinMax(),this._updateSeries(s,e,i)}},{key:"_updateSeries",value:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]&&arguments[2],s=this.w;return this.w.globals.shouldAnimate=e,s.globals.dataChanged=!0,s.globals.allSeriesCollapsed&&(s.globals.allSeriesCollapsed=!1),e&&this.series.getPreviousPaths(),s.globals.axisCharts?(0===(e=t.map(function(t,e){return Q({},s.config.series[e],{name:t.name||s.config.series[e]&&s.config.series[e].name,type:t.type||s.config.series[e]&&s.config.series[e].type,data:t.data||s.config.series[e]&&s.config.series[e].data})})).length&&(e=[{data:[]}]),s.config.series=e):s.config.series=t.slice(),i&&(s.globals.initialConfig.series=JSON.parse(JSON.stringify(s.config.series)),s.globals.initialSeries=JSON.parse(JSON.stringify(s.config.series))),this.update()}},{key:"getSyncedCharts",value:function(){var t=this.getGroupedCharts(),e=[this];return t.length&&(e=[],t.forEach(function(t){e.push(t)})),e}},{key:"getGroupedCharts",value:function(){var e=this;return Apex._chartInstances.filter(function(t){if(t.group)return!0}).map(function(t){return e.w.config.chart.group===t.group?t.chart:e})}},{key:"appendData",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];this.w.globals.dataChanged=!0,this.series.getPreviousPaths();for(var i=this.w.config.series.slice(),s=0;s<i.length;s++)if(void 0!==t[s])for(var a=0;a<t[s].data.length;a++)i[s].data.push(t[s].data[a]);return this.w.config.series=i,e&&(this.w.globals.initialSeries=JSON.parse(JSON.stringify(this.w.config.series))),this.update()}},{key:"update",value:function(s){var a=this;return new c(function(t,e){a.clear();var i=a.create(a.w.config.series,s);if(!i)return t(a);a.mount(i).then(function(){"function"==typeof a.w.config.chart.events.updated&&a.w.config.chart.events.updated(a,a.w),a.fireEvent("updated",[a,a.w]),a.w.globals.isDirty=!0,t(a)}).catch(function(t){e(t)})})}},{key:"forceXAxisUpdate",value:function(t){var e=this.w;void 0!==t.xaxis.min&&(e.config.xaxis.min=t.xaxis.min,e.globals.lastXAxis.min=t.xaxis.min),void 0!==t.xaxis.max&&(e.config.xaxis.max=t.xaxis.max,e.globals.lastXAxis.max=t.xaxis.max)}},{key:"revertDefaultAxisMinMax",value:function(){var i=this.w;i.config.xaxis.min=i.globals.lastXAxis.min,i.config.xaxis.max=i.globals.lastXAxis.max,i.config.yaxis.map(function(t,e){i.globals.zoomed&&void 0!==i.globals.lastYAxis[e]&&(t.min=i.globals.lastYAxis[e].min,t.max=i.globals.lastYAxis[e].max)})}},{key:"clear",value:function(){this.zoomPanSelection&&this.zoomPanSelection.destroy(),this.toolbar&&this.toolbar.destroy(),this.animations=null,this.annotations=null,this.core=null,this.grid=null,this.series=null,this.responsive=null,this.theme=null,this.formatters=null,this.titleSubtitle=null,this.legend=null,this.dimensions=null,this.options=null,this.crosshairs=null,this.zoomPanSelection=null,this.toolbar=null,this.w.globals.tooltip=null,this.clearDomElements()}},{key:"killSVG",value:function(i){return new c(function(t,e){i.each(function(t,e){this.removeClass("*"),this.off(),this.stop()},!0),i.ungroup(),i.clear(),t("done")})}},{key:"clearDomElements",value:function(){var e=this,t=(this.eventList.forEach(function(t){document.removeEventListener(t,e.documentEvent)}),this.w.globals.dom);if(null!==this.el)for(;this.el.firstChild;)this.el.removeChild(this.el.firstChild);this.killSVG(t.Paper),t.Paper.remove(),t.elWrap=null,t.elGraphical=null,t.elLegendWrap=null,t.baseEl=null,t.elGridRect=null,t.elGridRectMask=null,t.elGridRectMarkerMask=null,t.elDefs=null}},{key:"destroy",value:function(){this.clear();var i=this.w.config.chart.id;i&&Apex._chartInstances.forEach(function(t,e){t.id===i&&Apex._chartInstances.splice(e,1)}),window.removeEventListener("resize",this.windowResizeHandler),window.removeResizeListener(this.el.parentNode,this.parentResizeCallback.bind(this))}},{key:"toggleSeries",value:function(t){var e=this.series.getSeriesByName(t),t=parseInt(e.getAttribute("data:realIndex")),e=e.classList.contains("apexcharts-series-collapsed");this.legend.toggleDataSeries(t,e)}},{key:"resetSeries",value:function(){this.series.resetSeries(!(0<arguments.length&&void 0!==arguments[0])||arguments[0])}},{key:"setupEventHandlers",value:function(){var e=this,i=this.w,s=this,a=i.globals.dom.baseEl.querySelector(i.globals.chartClass);this.eventListHandlers=[],this.eventList.forEach(function(t){a.addEventListener(t,function(t){"mousedown"===t.type&&1===t.which||("mouseup"===t.type&&1===t.which||"touchend"===t.type)&&("function"==typeof i.config.chart.events.click&&i.config.chart.events.click(t,s,i),s.fireEvent("click",[t,s,i]))},{capture:!1,passive:!0})}),this.eventList.forEach(function(t){document.addEventListener(t,e.documentEvent)}),this.core.setupBrushHandler()}},{key:"documentEvent",value:function(t){var e=this.w;e.globals.clientX=("touchmove"===t.type?t.touches[0]:t).clientX,e.globals.clientY=("touchmove"===t.type?t.touches[0]:t).clientY}},{key:"addXaxisAnnotation",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;(i=i||this).annotations.addXaxisAnnotationExternal(t,e,i)}},{key:"addYaxisAnnotation",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;(i=i||this).annotations.addYaxisAnnotationExternal(t,e,i)}},{key:"addPointAnnotation",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;(i=i||this).annotations.addPointAnnotationExternal(t,e,i)}},{key:"clearAnnotations",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:void 0;(t=t||this).annotations.clearAnnotations(t)}},{key:"addText",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:void 0;(i=i||this).annotations.addText(t,e,i)}},{key:"getChartArea",value:function(){return this.w.globals.dom.baseEl.querySelector(".apexcharts-inner")}},{key:"getSeriesTotalXRange",value:function(t,e){return this.coreUtils.getSeriesTotalsXRange(t,e)}},{key:"getHighestValueInSeries",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;return new dt(this.ctx).getMinYMaxY(t).highestY}},{key:"getLowestValueInSeries",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:0;return new dt(this.ctx).getMinYMaxY(t).lowestY}},{key:"getSeriesTotal",value:function(){return this.w.globals.seriesTotals}},{key:"setLocale",value:function(t){this.setCurrentLocaleValues(t)}},{key:"toggleDataPointSelection",value:function(t,e){var i=this.w;return(t=(i.globals.axisCharts?i.globals.dom.Paper.select(".apexcharts-series[data\\:realIndex='".concat(t,"'] path[j='").concat(e,"'], .apexcharts-series[data\\:realIndex='").concat(t,"'] circle[j='").concat(e,"'], .apexcharts-series[data\\:realIndex='").concat(t,"'] rect[j='").concat(e,"']")):i.globals.dom.Paper.select(".apexcharts-series[data\\:realIndex='".concat(t,"']"))).members[0])?new tt(this.ctx).pathMouseDown(t,null):console.warn("toggleDataPointSelection: Element not found"),t.node||null}},{key:"setCurrentLocaleValues",value:function(e){var t=this.w.config.chart.locales;if(!(t=(t=window.Apex.chart&&window.Apex.chart.locales&&0<window.Apex.chart.locales.length?this.w.config.chart.locales.concat(window.Apex.chart.locales):t).filter(function(t){return t.name===e})[0]))throw new Error("Wrong locale name provided. Please make sure you set the correct locale name in options");t=K.extend(D,t),this.w.globals.locale=t.options}},{key:"dataURI",value:function(){return new Jt(this.ctx).dataURI()}},{key:"paper",value:function(){return this.w.globals.dom.Paper}},{key:"parentResizeCallback",value:function(){this.w.globals.animationEnded&&this.windowResize()}},{key:"windowResize",value:function(){var t=this;clearTimeout(this.w.globals.resizeTimer),this.w.globals.resizeTimer=window.setTimeout(function(){t.w.globals.resized=!0,t.w.globals.dataChanged=!1,t.update()},150)}}],[{key:"initOnLoad",value:function(){for(var t=document.querySelectorAll("[data-apexcharts]"),e=0;e<t.length;e++)new qe(t[e],JSON.parse(t[e].getAttribute("data-options"))).render()}},{key:"exec",value:function(t,e){var i=this.getChartByID(t);if(i){i.w.globals.isExecCalled=!0;for(var s=arguments.length,a=new Array(2<s?s-2:0),n=2;n<s;n++)a[n-2]=arguments[n];switch(e){case"updateOptions":return i.updateOptions.apply(i,a);case"updateSeries":return i.updateSeries.apply(i,a);case"appendData":return i.appendData.apply(i,a);case"appendSeries":return i.appendSeries.apply(i,a);case"toggleSeries":return i.toggleSeries.apply(i,a);case"resetSeries":return i.resetSeries.apply(i,a);case"toggleDataPointSelection":return i.toggleDataPointSelection.apply(i,a);case"dataURI":return i.dataURI.apply(i,a);case"addXaxisAnnotation":return i.addXaxisAnnotation.apply(i,a);case"addYaxisAnnotation":return i.addYaxisAnnotation.apply(i,a);case"addPointAnnotation":return i.addPointAnnotation.apply(i,a);case"addText":return i.addText.apply(i,a);case"clearAnnotations":return i.clearAnnotations.apply(i,a);case"paper":return i.paper.apply(i,a);case"destroy":return i.destroy()}}}},{key:"merge",value:function(t,e){return K.extend(t,e)}},{key:"getChartByID",value:function(e){return Apex._chartInstances.filter(function(t){return t.id===e})[0].chart}}]),qe;function qe(t,e){s(this,qe),this.opts=e,(this.ctx=this).w=new W(e).init(),this.el=t,this.w.globals.cuid=(Math.random()+1).toString(36).substring(4),this.w.globals.chartID=this.w.config.chart.id||this.w.globals.cuid,this.eventList=["mousedown","mousemove","touchstart","touchmove","mouseup","touchend"],this.initModules(),this.create=K.bind(this.create,this),this.documentEvent=K.bind(this.documentEvent,this),this.windowResizeHandler=this.windowResize.bind(this)}});
//# sourceMappingURL=apexcharts.min.js.map

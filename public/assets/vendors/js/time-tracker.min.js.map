{"version": 3, "file": "time-tracker.min.js", "sources": ["time-tracker.min.js"], "sourcesContent": ["!(function (e) {\r\n\tvar n,\r\n\t\ts = { format: \"H:i:s\", countdown: !1, passed: 0, passedStop: -1, timestart: 0, timestop: 0, delayInterval: 1e3, stop: null },\r\n\t\ta = null,\r\n\t\ti = null,\r\n\t\tp = {\r\n\t\t\tinit: function (t) {\r\n\t\t\t\t(i = this), (n = e.extend({}, s, t)).timestop && (n.passedStop = 1e3 * n.timestop), n.countdown && 0 === n.passedStop && (n.countdown = !1), 0 !== n.passed && (n.passed = 1e3 * n.passed), p.display.apply(), p.stop.apply();\r\n\t\t\t},\r\n\t\t\tstart: function () {\r\n\t\t\t\t(n.timestart = Date.now()), (a = setTimeout(p.make, n.delayInterval));\r\n\t\t\t},\r\n\t\t\tstop: function () {\r\n\t\t\t\ta && clearTimeout(a);\r\n\t\t\t},\r\n\t\t\tmake: function () {\r\n\t\t\t\tvar t = Date.now();\r\n\t\t\t\t(n.passed += t - n.timestart), (n.timestart = t), p.display.apply(), n.passed <= n.passedStop || n.passedStop < 0 ? (a = setTimeout(p.make, n.delayInterval)) : ((n.passed = n.passedStop), n.stop && n.stop.apply());\r\n\t\t\t},\r\n\t\t\tdisplay: function () {\r\n\t\t\t\tvar t = 0,\r\n\t\t\t\t\te = 0,\r\n\t\t\t\t\ts = 0,\r\n\t\t\t\t\ta = n.format,\r\n\t\t\t\t\tp = n.passed,\r\n\t\t\t\t\to = Math.floor;\r\n\t\t\t\tn.countdown && ((p = n.passedStop - p), (o = Math.ceil)), (s = o(p / 1e3) % 60), (e = (e = Math.floor(p / 6e4) % 60) < 10 ? \"0\" + e : e), (s = s < 10 ? \"0\" + s : s), (t = (t = Math.floor(p / 36e5)) < 10 ? \"0\" + t : t), (a = a.replace(\"H\", t).replace(\"i\", e).replace(\"s\", s)), i.text(a);\r\n\t\t\t},\r\n\t\t\treset: function () {\r\n\t\t\t\t(n.passed = 0), n.timestop && (n.passedStop = 1e3 * n.timestop), n.countdown && 0 === n.passedStop && (n.countdown = !1), p.display.apply();\r\n\t\t\t},\r\n\t\t};\r\n\te.fn.timetracker = function (t) {\r\n\t\treturn p[t] ? p[t].apply(this, Array.prototype.slice.call(arguments, 1)) : \"object\" != typeof t && t ? void e.error(t + \" don't exists\") : p.init.apply(this, arguments);\r\n\t};\r\n})(jQuery);\r\n"], "names": ["e", "n", "s", "format", "countdown", "passed", "passedStop", "timestart", "timestop", "delayInterval", "stop", "a", "i", "p", "init", "t", "this", "extend", "display", "apply", "start", "Date", "now", "setTimeout", "make", "clearTimeout", "o", "Math", "floor", "ceil", "replace", "text", "reset", "fn", "timetracker", "Array", "prototype", "slice", "call", "arguments", "error", "j<PERSON><PERSON><PERSON>"], "mappings": "AAAA,CAAC,SAAWA,GACX,IAAIC,EACHC,EAAI,CAAEC,OAAQ,QAASC,UAAW,CAAA,EAAIC,OAAQ,EAAGC,WAAY,CAAC,EAAGC,UAAW,EAAGC,SAAU,EAAGC,cAAe,IAAKC,KAAM,IAAK,EAC3HC,EAAI,KACJC,EAAI,KACJC,EAAI,CACHC,KAAM,SAAUC,GACdH,EAAII,MAAQf,EAAID,EAAEiB,OAAO,GAAIf,EAAGa,CAAC,GAAGP,WAAaP,EAAEK,WAAa,IAAML,EAAEO,UAAWP,EAAEG,WAAa,IAAMH,EAAEK,aAAeL,EAAEG,UAAY,CAAA,GAAK,IAAMH,EAAEI,SAAWJ,EAAEI,OAAS,IAAMJ,EAAEI,QAASQ,EAAEK,QAAQC,MAAM,EAAGN,EAAEH,KAAKS,MAAM,CAC7N,EACAC,MAAO,WACLnB,EAAEM,UAAYc,KAAKC,IAAI,EAAKX,EAAIY,WAAWV,EAAEW,KAAMvB,EAAEQ,aAAa,CACpE,EACAC,KAAM,WACLC,GAAKc,aAAad,CAAC,CACpB,EACAa,KAAM,WACL,IAAIT,EAAIM,KAAKC,IAAI,EAChBrB,EAAEI,QAAUU,EAAId,EAAEM,UAAaN,EAAEM,UAAYQ,EAAIF,EAAEK,QAAQC,MAAM,EAAGlB,EAAEI,QAAUJ,EAAEK,YAAcL,EAAEK,WAAa,EAAKK,EAAIY,WAAWV,EAAEW,KAAMvB,EAAEQ,aAAa,GAAOR,EAAEI,OAASJ,EAAEK,WAAaL,EAAES,MAAQT,EAAES,KAAKS,MAAM,EACpN,EACAD,QAAS,WACR,IACClB,EAEAW,EAAIV,EAAEE,OACNU,EAAIZ,EAAEI,OACNqB,EAAIC,KAAKC,MACV3B,EAAEG,YAAeS,EAAIZ,EAAEK,WAAaO,EAAKa,EAAIC,KAAKE,MAAS3B,EAAIwB,EAAEb,EAAI,GAAG,EAAI,GAAMb,GAAKA,EAAI2B,KAAKC,MAAMf,EAAI,GAAG,EAAI,IAAM,GAAK,IAAMb,EAAIA,EAAKE,EAAIA,EAAI,GAAK,IAAMA,EAAIA,EAAKa,GAAKA,EAAIY,KAAKC,MAAMf,EAAI,IAAI,GAAK,GAAK,IAAME,EAAIA,EAAKJ,EAAIA,EAAEmB,QAAQ,IAAKf,CAAC,EAAEe,QAAQ,IAAK9B,CAAC,EAAE8B,QAAQ,IAAK5B,CAAC,EAAIU,EAAEmB,KAAKpB,CAAC,CAC7R,EACAqB,MAAO,WACL/B,EAAEI,OAAS,EAAIJ,EAAEO,WAAaP,EAAEK,WAAa,IAAML,EAAEO,UAAWP,EAAEG,WAAa,IAAMH,EAAEK,aAAeL,EAAEG,UAAY,CAAA,GAAKS,EAAEK,QAAQC,MAAM,CAC3I,CACD,EACDnB,EAAEiC,GAAGC,YAAc,SAAUnB,GAC5B,OAAOF,EAAEE,GAAKF,EAAEE,GAAGI,MAAMH,KAAMmB,MAAMC,UAAUC,MAAMC,KAAKC,UAAW,CAAC,CAAC,EAAI,UAAY,OAAOxB,GAAKA,EAAI,KAAKf,EAAEwC,MAAMzB,EAAI,eAAe,EAAIF,EAAEC,KAAKK,MAAMH,KAAMuB,SAAS,CACxK,CACA,EAAEE,MAAM"}
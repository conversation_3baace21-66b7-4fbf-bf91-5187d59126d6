{"version": 3, "file": "lslstrength.min.js", "sources": ["lslstrength.min.js"], "sourcesContent": ["var allBars = document.querySelectorAll(\"div.progress-bar > div\");\n//var currentPercent = document.querySelector(\"div.percentage > div.digit\");\n\nvar inputPasswordField = document.querySelector(\"input.password\");\ninputPasswordField.addEventListener(\"keyup\", (e) => {\n\tdetPasswordStrength(inputPasswordField.value);\n});\n\nfunction detPasswordStrength(password) {\n\tallBars.forEach((bar) => {\n\t\tbar.style.background = \"#e9ecef\";\n\t\tbar.style.border = \"1px solid #e9ecef\";\n\t});\n\tvar pwdPercent = getStrengthPercent(password);\n\tif (pwdPercent == 100) {\n\t\tallBars.forEach((bar) => {\n\t\t\tbar.style.background = \"#25b865\";\n\t\t\tbar.style.border = \"1px solid #25b865\";\n\t\t});\n\t} else if (pwdPercent >= 75) {\n\t\tfor (var i = 0; i < 3; i++) {\n\t\t\tallBars[i].style.background = \"#41b2c4\";\n\t\t\tallBars[i].style.border = \"1px solid #41b2c4\";\n\t\t}\n\t} else if (pwdPercent >= 50) {\n\t\tfor (var i = 0; i < 2; i++) {\n\t\t\tallBars[i].style.background = \"#e49e3d\";\n\t\t\tallBars[i].style.border = \"1px solid #e49e3d\";\n\t\t}\n\t} else if (pwdPercent >= 25) {\n\t\tallBars[0].style.background = \"#d13b4c\";\n\t\tallBars[0].style.border = \"#d13b4c\";\n\t}\n\n\t//displayPercent(pwdPercent);\n}\n\n/* \nfunction displayPercent(pwdPercent) {\n\t//var count = currentPercent.textContent;\n\tvar total = pwdPercent;\n\n\tdocument.querySelector(\"div.percentage\").style = \"\";\n\n\tvar x = setInterval(function () {\n\t\tif (count > total) currentPercent.innerHTML = --count;\n\t\tif (count < total) currentPercent.innerHTML = ++count;\n\t\tif (count == total) clearInterval(x);\n\t}, 5);\n}\n*/\n\nfunction getStrengthPercent(inputPassword) {\n\tvar percent = 0;\n\tpercent = percent + percentByLength(inputPassword);\n\tpercent = percent + percentByUppercase(inputPassword);\n\tpercent = percent + percentByChar(inputPassword);\n\tpercent = percent + percentByNum(inputPassword);\n\tpercent = charRepitition(percent, inputPassword);\n\n\treturn percent;\n}\n\nfunction percentByLength(inputPassword) {\n\tif (inputPassword.length >= 16) return 25;\n\telse if (inputPassword.length >= 8) return 15;\n\telse if (inputPassword.length > 0) return 5;\n\telse return 0;\n}\n\nfunction percentByUppercase(inputPassword) {\n\tvar letters = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n\tvar noOfUpperCase = [];\n\n\tinputPassword.split(\"\").forEach((char) => {\n\t\tif (letters.includes(char)) noOfUpperCase.push(char);\n\t});\n\n\tif (inputPassword.length - noOfUpperCase.length >= inputPassword.length) return 0;\n\telse if (inputPassword.length - noOfUpperCase.length >= 16) return 25;\n\telse if (inputPassword.length - noOfUpperCase.length >= 8) return 15;\n\telse if (inputPassword.length - noOfUpperCase.length > 0) return 5;\n\telse return 0;\n}\n\nfunction percentByChar(inputPassword) {\n\tvar allChar = \"`,.~{}()[]/+_=-!@#$%^&*|\\\\'\\\":?\";\n\tvar noOfChar = [];\n\n\tinputPassword.split(\"\").forEach((char) => {\n\t\tif (allChar.includes(char)) noOfChar.push(char);\n\t});\n\n\tif (inputPassword.length - noOfChar.length >= inputPassword.length) return 0;\n\telse if (inputPassword.length - noOfChar.length >= 16) return 25;\n\telse if (inputPassword.length - noOfChar.length >= 8) return 15;\n\telse if (inputPassword.length - noOfChar.length > 0) return 5;\n\telse return 0;\n}\n\nfunction percentByNum(inputPassword) {\n\tvar allChar = \"1234567890\";\n\tvar noOfChar = [];\n\n\tinputPassword.split(\"\").forEach((char) => {\n\t\tif (allChar.includes(char)) noOfChar.push(char);\n\t});\n\n\tif (inputPassword.length - noOfChar.length >= inputPassword.length) return 0;\n\telse if (inputPassword.length - noOfChar.length >= 16) return 25;\n\telse if (inputPassword.length - noOfChar.length >= 8) return 15;\n\telse if (inputPassword.length - noOfChar.length > 0) return 5;\n\telse return 0;\n}\n\nconst showPasswordBtn = document.querySelector(\"div.show-pass\");\nshowPasswordBtn.addEventListener(\"click\", (event) => {\n\tif (inputPasswordField.getAttribute(\"type\") == \"password\") {\n\t\tinputPasswordField.setAttribute(\"type\", \"text\");\n\t\tshowPasswordBtn.children[0].innerHTML = '<i class=\"feather feather-eye-off\"></i>';\n\t} else {\n\t\tinputPasswordField.setAttribute(\"type\", \"password\");\n\t\tshowPasswordBtn.children[0].innerHTML = '<i class=\"feather feather-eye\"></i>';\n\t}\n});\n\nconst generatePasswordBtn = document.querySelector(\"div.gen-pass\");\ngeneratePasswordBtn.addEventListener(\"click\", (event) => {\n\tvar upperCaseLetters = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\n\tvar lowerCaseLetters = upperCaseLetters.toLowerCase();\n\tvar numbers = \"1234567890\";\n\tvar chars = \"`,.~{}()[]/+_=-!@#$%^&*|\\\\'\\\":?\";\n\tvar passwordLength = 16;\n\n\tvar newPassword = [];\n\n\t// 16 characters all together\n\t// 12 letters: 7 uc, 5lc\n\t// 2 numbers\n\t// 1 chars\n\n\tfor (var i = 0; i < 3; i++) {\n\t\tvar letterPosition = Math.floor(Math.random() * upperCaseLetters.length);\n\n\t\tif (newPassword[newPassword.length - 1] != upperCaseLetters[letterPosition] && newPassword[newPassword.length - 2] != upperCaseLetters[letterPosition]) {\n\t\t\tnewPassword.push(upperCaseLetters[letterPosition]);\n\t\t\tconsole.log(newPassword.length - 1 + \" = \" + upperCaseLetters[letterPosition]);\n\t\t} else --i;\n\t}\n\n\tfor (var i = 0; i < 13; i++) {\n\t\tvar letterPosition = Math.floor(Math.random() * lowerCaseLetters.length);\n\n\t\tif (newPassword[newPassword.length - 1].toLowerCase() != lowerCaseLetters[letterPosition] && newPassword[newPassword.length - 2].toLowerCase() != lowerCaseLetters[letterPosition]) {\n\t\t\tnewPassword.push(lowerCaseLetters[letterPosition]);\n\t\t\tconsole.log(newPassword.length - 1 + \" = \" + lowerCaseLetters[letterPosition]);\n\t\t} else --i;\n\t}\n\n\tfor (var i = 0; i < 2; i++) {\n\t\tvar letterPosition = Math.floor(Math.random() * numbers.length);\n\n\t\tif (newPassword[newPassword.length - 1] != numbers[letterPosition] && parseInt(newPassword[newPassword.length - 1]) + 1 != numbers[letterPosition] && parseInt(newPassword[newPassword.length - 1]) - 1 != numbers[letterPosition] && parseInt(newPassword[newPassword.length - 1]) + 2 != numbers[letterPosition] && parseInt(newPassword[newPassword.length - 1]) - 2 != numbers[letterPosition]) {\n\t\t\tnewPassword.push(numbers[letterPosition]);\n\t\t\tconsole.log(newPassword.length - 1 + \" = \" + numbers[letterPosition]);\n\t\t} else --i;\n\t}\n\n\tvar letterPosition = Math.floor(Math.random() * chars.length);\n\tnewPassword.push(chars[letterPosition]);\n\n\t// randomly sort the generated password\n\t// ISSUE: it scatters the calculated arrangement of the elements\n\n\t// for (var i = 0; i < newPassword.length; i++) {\n\t//     var randomIndex = Math.floor(Math.random() * newPassword.length);\n\t//     var hold = newPassword[randomIndex];\n\t//     newPassword[randomIndex] = newPassword[i];\n\t//     newPassword[i] = hold;\n\t// }\n\n\tinputPasswordField.value = newPassword.join(\"\");\n\tdetPasswordStrength(newPassword.join(\"\"));\n});\n\nfunction charRepitition(percent, inputPassword) {\n\tvar allChar = inputPassword.split(\"\");\n\tconsole.log(allChar);\n\tvar reps = [];\n\n\tfor (var currentPosition = 0; currentPosition < allChar.length; currentPosition++) {\n\t\tfor (var inc = 1; inc <= 2; inc++) {\n\t\t\tvar nextPosition = currentPosition + inc;\n\t\t\tif (allChar[currentPosition] == allChar[nextPosition] || allChar[nextPosition] == parseInt(allChar[currentPosition]) + 1) {\n\t\t\t\tif (!reps.includes(allChar[currentPosition])) reps.push(allChar[currentPosition]);\n\t\t\t\telse break;\n\t\t\t}\n\t\t}\n\t}\n\n\tconsole.log(reps);\n\n\tif (reps.length >= 3) return percent - 25;\n\tif (reps.length == 2) return percent - 15;\n\tif (reps.length == 1) return percent - 5;\n\treturn percent;\n}\n"], "names": ["allBars", "document", "querySelectorAll", "inputPasswordField", "querySelector", "detPasswordStrength", "password", "for<PERSON>ach", "bar", "style", "background", "border", "pwd<PERSON><PERSON><PERSON>", "getStrengthPercent", "i", "inputPassword", "percent", "percentByLength", "charRepitition", "percentByUppercase", "percentByChar", "percentByNum", "length", "noOfUpperCase", "split", "includes", "char", "push", "noOfChar", "addEventListener", "value", "showPasswordBtn", "generatePasswordBtn", "getAttribute", "setAttribute", "children", "innerHTML", "allChar", "reps", "console", "log", "currentPosition", "inc", "nextPosition", "parseInt", "upperCaseLetters", "lowerCaseLetters", "toLowerCase", "numbers", "chars", "newPassword", "letterPosition", "Math", "floor", "random", "join"], "mappings": "AAAA,IAAIA,QAAUC,SAASC,iBAAiB,wBAAwB,EAG5DC,mBAAqBF,SAASG,cAAc,gBAAgB,EAKhE,SAASC,oBAAoBC,GAC5BN,QAAQO,QAAQ,IACfC,EAAIC,MAAMC,WAAa,UACvBF,EAAIC,MAAME,OAAS,mBACpB,CAAC,EACGC,EAAaC,mBAAmBP,CAAQ,EAC5C,GAAkB,KAAdM,EACHZ,QAAQO,QAAQ,IACfC,EAAIC,MAAMC,WAAa,UACvBF,EAAIC,MAAME,OAAS,mBACpB,CAAC,OACK,GAAkB,IAAdC,EACV,IAAK,IAAIE,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACvBd,QAAQc,GAAGL,MAAMC,WAAa,UAC9BV,QAAQc,GAAGL,MAAME,OAAS,yBAErB,GAAkB,IAAdC,EACV,IAASE,EAAI,EAAGA,EAAI,EAAGA,CAAC,GACvBd,QAAQc,GAAGL,MAAMC,WAAa,UAC9BV,QAAQc,GAAGL,MAAME,OAAS,yBAEH,IAAdC,IACVZ,QAAQ,GAAGS,MAAMC,WAAa,UAC9BV,QAAQ,GAAGS,MAAME,OAAS,UAI5B,CAiBA,SAASE,mBAAmBE,GAC3B,IAAIC,EAAU,EACdA,EADc,EACMC,gBAAgBF,CAAa,EAMjD,OAFAC,EAAUE,eADVF,GADAA,GADAA,GAAoBG,mBAAmBJ,CAAa,GAChCK,cAAcL,CAAa,GAC3BM,aAAaN,CAAa,EACZA,CAAa,CAGhD,CAEA,SAASE,gBAAgBF,GACxB,OAA4B,IAAxBA,EAAcO,OAAqB,GACN,GAAxBP,EAAcO,OAAoB,GACX,EAAvBP,EAAcO,OAAmB,EAC9B,CACb,CAEA,SAASH,mBAAmBJ,GAC3B,IACIQ,EAAgB,GAMpB,OAJAR,EAAcS,MAAM,EAAE,EAAEjB,QAAQ,IAHlB,6BAIDkB,SAASC,CAAI,GAAGH,EAAcI,KAAKD,CAAI,CACpD,CAAC,EAEGX,EAAcO,OAASC,EAAcD,QAAUP,EAAcO,OAAe,EACxB,IAA/CP,EAAcO,OAASC,EAAcD,OAAqB,GACX,GAA/CP,EAAcO,OAASC,EAAcD,OAAoB,GACX,EAA9CP,EAAcO,OAASC,EAAcD,OAAmB,EACrD,CACb,CAEA,SAASF,cAAcL,GACtB,IACIa,EAAW,GAMf,OAJAb,EAAcS,MAAM,EAAE,EAAEjB,QAAQ,IAHlB,kCAIDkB,SAASC,CAAI,GAAGE,EAASD,KAAKD,CAAI,CAC/C,CAAC,EAEGX,EAAcO,OAASM,EAASN,QAAUP,EAAcO,OAAe,EACxB,IAA1CP,EAAcO,OAASM,EAASN,OAAqB,GACX,GAA1CP,EAAcO,OAASM,EAASN,OAAoB,GACX,EAAzCP,EAAcO,OAASM,EAASN,OAAmB,EAChD,CACb,CAEA,SAASD,aAAaN,GACrB,IACIa,EAAW,GAMf,OAJAb,EAAcS,MAAM,EAAE,EAAEjB,QAAQ,IAHlB,aAIDkB,SAASC,CAAI,GAAGE,EAASD,KAAKD,CAAI,CAC/C,CAAC,EAEGX,EAAcO,OAASM,EAASN,QAAUP,EAAcO,OAAe,EACxB,IAA1CP,EAAcO,OAASM,EAASN,OAAqB,GACX,GAA1CP,EAAcO,OAASM,EAASN,OAAoB,GACX,EAAzCP,EAAcO,OAASM,EAASN,OAAmB,EAChD,CACb,CA7GAnB,mBAAmB0B,iBAAiB,QAAS,IAC5CxB,oBAAoBF,mBAAmB2B,KAAK,CAC7C,CAAC,EA6GD,MAAMC,gBAAkB9B,SAASG,cAAc,eAAe,EAWxD4B,qBAVND,gBAAgBF,iBAAiB,QAAS,IACM,YAA3C1B,mBAAmB8B,aAAa,MAAM,GACzC9B,mBAAmB+B,aAAa,OAAQ,MAAM,EAC9CH,gBAAgBI,SAAS,GAAGC,UAAY,4CAExCjC,mBAAmB+B,aAAa,OAAQ,UAAU,EAClDH,gBAAgBI,SAAS,GAAGC,UAAY,sCAE1C,CAAC,EAE2BnC,SAASG,cAAc,cAAc,GA2DjE,SAASc,eAAeF,EAASD,GAKhC,IAJA,IAAIsB,EAAUtB,EAAcS,MAAM,EAAE,EAEhCc,GADJC,QAAQC,IAAIH,CAAO,EACR,IAEFI,EAAkB,EAAGA,EAAkBJ,EAAQf,OAAQmB,CAAe,GAC9E,IAAK,IAAIC,EAAM,EAAGA,GAAO,EAAGA,CAAG,GAAI,CAClC,IAAIC,EAAeF,EAAkBC,EACrC,GAAIL,EAAQI,IAAoBJ,EAAQM,IAAiBN,EAAQM,IAAiBC,SAASP,EAAQI,EAAgB,EAAI,EAAG,CACzH,GAAKH,EAAKb,SAASY,EAAQI,EAAgB,EACtC,MADyCH,EAAKX,KAAKU,EAAQI,EAAgB,CAEjF,CACD,CAKD,OAFAF,QAAQC,IAAIF,CAAI,EAEG,GAAfA,EAAKhB,OAAoBN,EAAU,GACpB,GAAfsB,EAAKhB,OAAoBN,EAAU,GACpB,GAAfsB,EAAKhB,OAAoBN,EAAU,EAChCA,CACR,CA/EAgB,oBAAoBH,iBAAiB,QAAS,IAc7C,IAbA,IAAIgB,EAAmB,6BACnBC,EAAmBD,EAAiBE,YAAY,EAChDC,EAAU,aACVC,EAAQ,kCAGRC,EAAc,GAOTpC,EAAI,EAAGA,EAAI,EAAGA,CAAC,GAAI,CAC3B,IAAIqC,EAAiBC,KAAKC,MAAMD,KAAKE,OAAO,EAAIT,EAAiBvB,MAAM,EAEnE4B,EAAYA,EAAY5B,OAAS,IAAMuB,EAAiBM,IAAmBD,EAAYA,EAAY5B,OAAS,IAAMuB,EAAiBM,IACtID,EAAYvB,KAAKkB,EAAiBM,EAAe,EACjDZ,QAAQC,IAAIU,EAAY5B,OAAS,EAAI,MAAQuB,EAAiBM,EAAe,GACvE,EAAErC,CACV,CAEA,IAASA,EAAI,EAAGA,EAAI,GAAIA,CAAC,GAAI,CACxBqC,EAAiBC,KAAKC,MAAMD,KAAKE,OAAO,EAAIR,EAAiBxB,MAAM,EAEnE4B,EAAYA,EAAY5B,OAAS,GAAGyB,YAAY,GAAKD,EAAiBK,IAAmBD,EAAYA,EAAY5B,OAAS,GAAGyB,YAAY,GAAKD,EAAiBK,IAClKD,EAAYvB,KAAKmB,EAAiBK,EAAe,EACjDZ,QAAQC,IAAIU,EAAY5B,OAAS,EAAI,MAAQwB,EAAiBK,EAAe,GACvE,EAAErC,CACV,CAEA,IAASA,EAAI,EAAGA,EAAI,EAAGA,CAAC,GAAI,CACvBqC,EAAiBC,KAAKC,MAAMD,KAAKE,OAAO,EAAIN,EAAQ1B,MAAM,EAE1D4B,EAAYA,EAAY5B,OAAS,IAAM0B,EAAQG,IAAmBP,SAASM,EAAYA,EAAY5B,OAAS,EAAE,EAAI,GAAK0B,EAAQG,IAAmBP,SAASM,EAAYA,EAAY5B,OAAS,EAAE,EAAI,GAAK0B,EAAQG,IAAmBP,SAASM,EAAYA,EAAY5B,OAAS,EAAE,EAAI,GAAK0B,EAAQG,IAAmBP,SAASM,EAAYA,EAAY5B,OAAS,EAAE,EAAI,GAAK0B,EAAQG,IAClXD,EAAYvB,KAAKqB,EAAQG,EAAe,EACxCZ,QAAQC,IAAIU,EAAY5B,OAAS,EAAI,MAAQ0B,EAAQG,EAAe,GAC9D,EAAErC,CACV,CAEIqC,EAAiBC,KAAKC,MAAMD,KAAKE,OAAO,EAAIL,EAAM3B,MAAM,EAC5D4B,EAAYvB,KAAKsB,EAAME,EAAe,EAYtChD,mBAAmB2B,MAAQoB,EAAYK,KAAK,EAAE,EAC9ClD,oBAAoB6C,EAAYK,KAAK,EAAE,CAAC,CACzC,CAAC"}
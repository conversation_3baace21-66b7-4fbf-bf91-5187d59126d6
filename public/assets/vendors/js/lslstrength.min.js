var allBars=document.querySelectorAll("div.progress-bar > div"),inputPasswordField=document.querySelector("input.password");function detPasswordStrength(e){allBars.forEach(e=>{e.style.background="#e9ecef",e.style.border="1px solid #e9ecef"});e=getStrengthPercent(e);if(100==e)allBars.forEach(e=>{e.style.background="#25b865",e.style.border="1px solid #25b865"});else if(75<=e)for(var t=0;t<3;t++)allBars[t].style.background="#41b2c4",allBars[t].style.border="1px solid #41b2c4";else if(50<=e)for(t=0;t<2;t++)allBars[t].style.background="#e49e3d",allBars[t].style.border="1px solid #e49e3d";else 25<=e&&(allBars[0].style.background="#d13b4c",allBars[0].style.border="#d13b4c")}function getStrengthPercent(e){var t=0,t=0+percentByLength(e);return t=charRepitition(t=(t=(t+=percentByUppercase(e))+percentByChar(e))+percentByNum(e),e)}function percentByLength(e){return 16<=e.length?25:8<=e.length?15:0<e.length?5:0}function percentByUppercase(e){var t=[];return e.split("").forEach(e=>{"ABCDEFGHIJKLMNOPQRSTUVWXYZ".includes(e)&&t.push(e)}),e.length-t.length>=e.length?0:16<=e.length-t.length?25:8<=e.length-t.length?15:0<e.length-t.length?5:0}function percentByChar(e){var t=[];return e.split("").forEach(e=>{"`,.~{}()[]/+_=-!@#$%^&*|\\'\":?".includes(e)&&t.push(e)}),e.length-t.length>=e.length?0:16<=e.length-t.length?25:8<=e.length-t.length?15:0<e.length-t.length?5:0}function percentByNum(e){var t=[];return e.split("").forEach(e=>{"1234567890".includes(e)&&t.push(e)}),e.length-t.length>=e.length?0:16<=e.length-t.length?25:8<=e.length-t.length?15:0<e.length-t.length?5:0}inputPasswordField.addEventListener("keyup",e=>{detPasswordStrength(inputPasswordField.value)});const showPasswordBtn=document.querySelector("div.show-pass"),generatePasswordBtn=(showPasswordBtn.addEventListener("click",e=>{"password"==inputPasswordField.getAttribute("type")?(inputPasswordField.setAttribute("type","text"),showPasswordBtn.children[0].innerHTML='<i class="feather feather-eye-off"></i>'):(inputPasswordField.setAttribute("type","password"),showPasswordBtn.children[0].innerHTML='<i class="feather feather-eye"></i>')}),document.querySelector("div.gen-pass"));function charRepitition(e,t){for(var n=t.split(""),r=(console.log(n),[]),l=0;l<n.length;l++)for(var s=1;s<=2;s++){var o=l+s;if(n[l]==n[o]||n[o]==parseInt(n[l])+1){if(r.includes(n[l]))break;r.push(n[l])}}return console.log(r),3<=r.length?e-25:2==r.length?e-15:1==r.length?e-5:e}generatePasswordBtn.addEventListener("click",e=>{for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZ",n=t.toLowerCase(),r="1234567890",l="`,.~{}()[]/+_=-!@#$%^&*|\\'\":?",s=[],o=0;o<3;o++){var a=Math.floor(Math.random()*t.length);s[s.length-1]!=t[a]&&s[s.length-2]!=t[a]?(s.push(t[a]),console.log(s.length-1+" = "+t[a])):--o}for(o=0;o<13;o++){a=Math.floor(Math.random()*n.length);s[s.length-1].toLowerCase()!=n[a]&&s[s.length-2].toLowerCase()!=n[a]?(s.push(n[a]),console.log(s.length-1+" = "+n[a])):--o}for(o=0;o<2;o++){a=Math.floor(Math.random()*r.length);s[s.length-1]!=r[a]&&parseInt(s[s.length-1])+1!=r[a]&&parseInt(s[s.length-1])-1!=r[a]&&parseInt(s[s.length-1])+2!=r[a]&&parseInt(s[s.length-1])-2!=r[a]?(s.push(r[a]),console.log(s.length-1+" = "+r[a])):--o}a=Math.floor(Math.random()*l.length);s.push(l[a]),inputPasswordField.value=s.join(""),detPasswordStrength(s.join(""))});
//# sourceMappingURL=lslstrength.min.js.map

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap=t()}(this,function(){"use strict";const d={find(e,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne(e,t=document.documentElement){return Element.prototype.querySelector.call(t,e)},children(e,t){return[].concat(...e.children).filter(e=>e.matches(t))},parents(e,t){var i=[];let n=e.parentNode;for(;n&&n.nodeType===Node.ELEMENT_NODE&&3!==n.nodeType;)n.matches(t)&&i.push(n),n=n.parentNode;return i},prev(e,t){let i=e.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(e,t){let i=e.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]}},P=1e3,H="transitionend",R=e=>{for(;e+=Math.floor(1e6*Math.random()),document.getElementById(e););return e},B=t=>{let i=t.getAttribute("data-bs-target");if(!i||"#"===i){let e=t.getAttribute("href");if(!e||!e.includes("#")&&!e.startsWith("."))return null;e.includes("#")&&!e.startsWith("#")&&(e="#"+e.split("#")[1]),i=e&&"#"!==e?e.trim():null}return i},W=e=>{e=B(e);return e&&document.querySelector(e)?e:null},s=e=>{e=B(e);return e?document.querySelector(e):null},q=e=>{e.dispatchEvent(new Event(H))},r=e=>!(!e||"object"!=typeof e)&&void 0!==(e=void 0!==e.jquery?e[0]:e).nodeType,$=e=>r(e)?e.jquery?e[0]:e:"string"==typeof e&&0<e.length?d.findOne(e):null,i=(n,s,o)=>{Object.keys(o).forEach(e=>{var t=o[e],i=s[e],i=i&&r(i)?"element":null==(i=i)?""+i:{}.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(t).test(i))throw new TypeError(n.toUpperCase()+`: Option "${e}" provided type "${i}" but expected type "${t}".`)})},z=e=>!(!r(e)||0===e.getClientRects().length)&&"visible"===getComputedStyle(e).getPropertyValue("visibility"),F=e=>!e||e.nodeType!==Node.ELEMENT_NODE||!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),U=e=>{var t;return document.documentElement.attachShadow?"function"==typeof e.getRootNode?(t=e.getRootNode())instanceof ShadowRoot?t:null:e instanceof ShadowRoot?e:e.parentNode?U(e.parentNode):null:null},V=()=>{},K=e=>e.offsetHeight,X=()=>{var e=window["jQuery"];return e&&!document.body.hasAttribute("data-bs-no-jquery")?e:null},Y=[],n=()=>"rtl"===document.documentElement.dir;var e=n=>{var e;e=()=>{const e=X();if(e){const t=n.NAME,i=e.fn[t];e.fn[t]=n.jQueryInterface,e.fn[t].Constructor=n,e.fn[t].noConflict=()=>(e.fn[t]=i,n.jQueryInterface)}},"loading"===document.readyState?(Y.length||document.addEventListener("DOMContentLoaded",()=>{Y.forEach(e=>e())}),Y.push(e)):e()};const o=e=>{"function"==typeof e&&e()},Q=(i,n,e=!0)=>{if(e){e=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(e);var e=Number.parseFloat(t),n=Number.parseFloat(i);return e||n?(t=t.split(",")[0],i=i.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(i))*P):0})(n)+5;let t=!1;const s=({target:e})=>{e===n&&(t=!0,n.removeEventListener(H,s),o(i))};n.addEventListener(H,s),setTimeout(()=>{t||q(n)},e)}else o(i)},G=(e,t,i,n)=>{let s=e.indexOf(t);return-1===s?e[!i&&n?e.length-1:0]:(t=e.length,s+=i?1:-1,n&&(s=(s+t)%t),e[Math.max(0,Math.min(s,t-1))])},Z=/[^.]*(?=\..*)\.|.*/,J=/\..*/,ee=/::\d+$/,te={};let ie=1;const ne={mouseenter:"mouseover",mouseleave:"mouseout"},se=/^(mouseenter|mouseleave)/i,oe=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function re(e,t){return t&&t+"::"+ie++||e.uidEvent||ie++}function ae(e){var t=re(e);return e.uidEvent=t,te[t]=te[t]||{},te[t]}function le(i,n,s=null){var o=Object.keys(i);for(let e=0,t=o.length;e<t;e++){var r=i[o[e]];if(r.originalHandler===n&&r.delegationSelector===s)return r}return null}function ce(e,t,i){var n="string"==typeof t,i=n?i:t;let s=ue(e);t=oe.has(s);return[n,i,s=t?s:e]}function he(e,t,i,n,s){var o,r,a,l,c,h,d,u,f,p;"string"==typeof t&&e&&([o,r,a]=(i||(i=n,n=null),se.test(t)&&(o=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)},n?n=o(n):i=o(i)),ce(t,i,n)),(c=le(l=(l=ae(e))[a]||(l[a]={}),r,o?i:null))?c.oneOff=c.oneOff&&s:(c=re(r,t.replace(Z,"")),(t=o?(u=e,f=i,p=n,function i(n){var s=u.querySelectorAll(f);for(let t=n["target"];t&&t!==this;t=t.parentNode)for(let e=s.length;e--;)if(s[e]===t)return n.delegateTarget=t,i.oneOff&&m.off(u,n.type,f,p),p.apply(t,[n]);return null}):(h=e,d=i,function e(t){return t.delegateTarget=h,e.oneOff&&m.off(h,t.type,d),d.apply(h,[t])})).delegationSelector=o?i:null,t.originalHandler=r,t.oneOff=s,l[t.uidEvent=c]=t,e.addEventListener(a,t,o)))}function de(e,t,i,n,s){n=le(t[i],n,s);n&&(e.removeEventListener(i,n,Boolean(s)),delete t[i][n.uidEvent])}function ue(e){return e=e.replace(J,""),ne[e]||e}const m={on(e,t,i,n){he(e,t,i,n,!1)},one(e,t,i,n){he(e,t,i,n,!0)},off(r,a,e,t){if("string"==typeof a&&r){const[i,n,s]=ce(a,e,t),o=s!==a,l=ae(r);t=a.startsWith(".");if(void 0!==n)return l&&l[s]?void de(r,l,s,n,i?e:null):void 0;t&&Object.keys(l).forEach(e=>{{var t=r,i=l,n=e,s=a.slice(1);const o=i[n]||{};Object.keys(o).forEach(e=>{e.includes(s)&&(e=o[e],de(t,i,n,e.originalHandler,e.delegationSelector))})}});const c=l[s]||{};Object.keys(c).forEach(e=>{var t=e.replace(ee,"");o&&!a.includes(t)||(t=c[e],de(r,l,s,t.originalHandler,t.delegationSelector))})}},trigger(e,t,i){if("string"!=typeof t||!e)return null;var n=X(),s=ue(t),o=t!==s,r=oe.has(s);let a,l=!0,c=!0,h=!1,d=null;return o&&n&&(a=n.Event(t,i),n(e).trigger(a),l=!a.isPropagationStopped(),c=!a.isImmediatePropagationStopped(),h=a.isDefaultPrevented()),r?(d=document.createEvent("HTMLEvents")).initEvent(s,l,!0):d=new CustomEvent(t,{bubbles:l,cancelable:!0}),void 0!==i&&Object.keys(i).forEach(e=>{Object.defineProperty(d,e,{get(){return i[e]}})}),h&&d.preventDefault(),c&&e.dispatchEvent(d),d.defaultPrevented&&void 0!==a&&a.preventDefault(),d}},a=new Map;var fe=function(e,t,i){a.has(e)||a.set(e,new Map);e=a.get(e);e.has(t)||0===e.size?e.set(t,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(e.keys())[0]}.`)},pe=function(e,t){return a.has(e)&&a.get(e).get(t)||null},me=function(e,t){var i;a.has(e)&&((i=a.get(e)).delete(t),0===i.size)&&a.delete(e)};class t{constructor(e){(e=$(e))&&(this._element=e,fe(this._element,this.constructor.DATA_KEY,this))}dispose(){me(this._element,this.constructor.DATA_KEY),m.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this).forEach(e=>{this[e]=null})}_queueCallback(e,t,i=!0){Q(e,t,i)}static getInstance(e){return pe(e,this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.0.2"}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}static get DATA_KEY(){return"bs."+this.NAME}static get EVENT_KEY(){return"."+this.DATA_KEY}}class ge extends t{static get NAME(){return"alert"}close(e){var e=e?this._getRootElement(e):this._element,t=this._triggerCloseEvent(e);null===t||t.defaultPrevented||this._removeElement(e)}_getRootElement(e){return s(e)||e.closest(".alert")}_triggerCloseEvent(e){return m.trigger(e,"close.bs.alert")}_removeElement(e){e.classList.remove("show");var t=e.classList.contains("fade");this._queueCallback(()=>this._destroyElement(e),e,t)}_destroyElement(e){e.remove(),m.trigger(e,"closed.bs.alert")}static jQueryInterface(t){return this.each(function(){var e=ge.getOrCreateInstance(this);"close"===t&&e[t](this)})}static handleDismiss(t){return function(e){e&&e.preventDefault(),t.close(this)}}}m.on(document,"click.bs.alert.data-api",'[data-bs-dismiss="alert"]',ge.handleDismiss(new ge)),e(ge);const _e='[data-bs-toggle="button"]';class ve extends t{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each(function(){var e=ve.getOrCreateInstance(this);"toggle"===t&&e[t]()})}}function be(e){return"true"===e||"false"!==e&&(e===Number(e).toString()?Number(e):""===e||"null"===e?null:e)}function ye(e){return e.replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}m.on(document,"click.bs.button.data-api",_e,e=>{e.preventDefault();e=e.target.closest(_e);ve.getOrCreateInstance(e).toggle()}),e(ve);const l={setDataAttribute(e,t,i){e.setAttribute("data-bs-"+ye(t),i)},removeDataAttribute(e,t){e.removeAttribute("data-bs-"+ye(t))},getDataAttributes(i){if(!i)return{};const n={};return Object.keys(i.dataset).filter(e=>e.startsWith("bs")).forEach(e=>{let t=e.replace(/^bs/,"");t=t.charAt(0).toLowerCase()+t.slice(1,t.length),n[t]=be(i.dataset[e])}),n},getDataAttribute(e,t){return be(e.getAttribute("data-bs-"+ye(t)))},offset(e){e=e.getBoundingClientRect();return{top:e.top+document.body.scrollTop,left:e.left+document.body.scrollLeft}},position(e){return{top:e.offsetTop,left:e.offsetLeft}}},we="carousel";var Ee=".bs.carousel";const Ae={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},Te={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},u="next",c="prev",h="left",Oe="right",Ce={ArrowLeft:Oe,ArrowRight:h},Le="slid"+Ee;const f="active",ke=".active.carousel-item";class p extends t{constructor(e,t){super(e),this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._indicatorsElement=d.findOne(".carousel-indicators",this._element),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent),this._addEventListeners()}static get Default(){return Ae}static get NAME(){return we}next(){this._slide(u)}nextWhenVisible(){!document.hidden&&z(this._element)&&this.next()}prev(){this._slide(c)}pause(e){e||(this._isPaused=!0),d.findOne(".carousel-item-next, .carousel-item-prev",this._element)&&(q(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null}cycle(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config&&this._config.interval&&!this._isPaused&&(this._updateInterval(),this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))}to(e){this._activeElement=d.findOne(ke,this._element);var t=this._getItemIndex(this._activeElement);e>this._items.length-1||e<0||(this._isSliding?m.one(this._element,Le,()=>this.to(e)):t===e?(this.pause(),this.cycle()):(t=t<e?u:c,this._slide(t,this._items[e])))}_getConfig(e){return e={...Ae,...l.getDataAttributes(this._element),..."object"==typeof e?e:{}},i(we,e,Te),e}_handleSwipe(){var e=Math.abs(this.touchDeltaX);e<=40||(e=e/this.touchDeltaX,this.touchDeltaX=0,e&&this._slide(0<e?Oe:h))}_addEventListeners(){this._config.keyboard&&m.on(this._element,"keydown.bs.carousel",e=>this._keydown(e)),"hover"===this._config.pause&&(m.on(this._element,"mouseenter.bs.carousel",e=>this.pause(e)),m.on(this._element,"mouseleave.bs.carousel",e=>this.cycle(e))),this._config.touch&&this._touchSupported&&this._addTouchEventListeners()}_addTouchEventListeners(){const t=e=>{!this._pointerEvent||"pen"!==e.pointerType&&"touch"!==e.pointerType?this._pointerEvent||(this.touchStartX=e.touches[0].clientX):this.touchStartX=e.clientX},i=e=>{this.touchDeltaX=e.touches&&1<e.touches.length?0:e.touches[0].clientX-this.touchStartX},n=e=>{!this._pointerEvent||"pen"!==e.pointerType&&"touch"!==e.pointerType||(this.touchDeltaX=e.clientX-this.touchStartX),this._handleSwipe(),"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(e=>this.cycle(e),500+this._config.interval))};d.find(".carousel-item img",this._element).forEach(e=>{m.on(e,"dragstart.bs.carousel",e=>e.preventDefault())}),this._pointerEvent?(m.on(this._element,"pointerdown.bs.carousel",e=>t(e)),m.on(this._element,"pointerup.bs.carousel",e=>n(e)),this._element.classList.add("pointer-event")):(m.on(this._element,"touchstart.bs.carousel",e=>t(e)),m.on(this._element,"touchmove.bs.carousel",e=>i(e)),m.on(this._element,"touchend.bs.carousel",e=>n(e)))}_keydown(e){var t;/input|textarea/i.test(e.target.tagName)||(t=Ce[e.key])&&(e.preventDefault(),this._slide(t))}_getItemIndex(e){return this._items=e&&e.parentNode?d.find(".carousel-item",e.parentNode):[],this._items.indexOf(e)}_getItemByOrder(e,t){e=e===u;return G(this._items,t,e,this._config.wrap)}_triggerSlideEvent(e,t){var i=this._getItemIndex(e),n=this._getItemIndex(d.findOne(ke,this._element));return m.trigger(this._element,"slide.bs.carousel",{relatedTarget:e,direction:t,from:n,to:i})}_setActiveIndicatorElement(t){if(this._indicatorsElement){var e=d.findOne(".active",this._indicatorsElement),i=(e.classList.remove(f),e.removeAttribute("aria-current"),d.find("[data-bs-target]",this._indicatorsElement));for(let e=0;e<i.length;e++)if(Number.parseInt(i[e].getAttribute("data-bs-slide-to"),10)===this._getItemIndex(t)){i[e].classList.add(f),i[e].setAttribute("aria-current","true");break}}}_updateInterval(){var e=this._activeElement||d.findOne(ke,this._element);e&&((e=Number.parseInt(e.getAttribute("data-bs-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=e):this._config.interval=this._config.defaultInterval||this._config.interval)}_slide(e,t){e=this._directionToOrder(e);const i=d.findOne(ke,this._element),n=this._getItemIndex(i),s=t||this._getItemByOrder(e,i),o=this._getItemIndex(s);var t=Boolean(this._interval),r=e===u;const a=r?"carousel-item-start":"carousel-item-end",l=r?"carousel-item-next":"carousel-item-prev",c=this._orderToDirection(e);if(s&&s.classList.contains(f))this._isSliding=!1;else if(!this._isSliding){r=this._triggerSlideEvent(s,c);if(!r.defaultPrevented&&i&&s){this._isSliding=!0,t&&this.pause(),this._setActiveIndicatorElement(s),this._activeElement=s;const h=()=>{m.trigger(this._element,Le,{relatedTarget:s,direction:c,from:n,to:o})};this._element.classList.contains("slide")?(s.classList.add(l),K(s),i.classList.add(a),s.classList.add(a),this._queueCallback(()=>{s.classList.remove(a,l),s.classList.add(f),i.classList.remove(f,l,a),this._isSliding=!1,setTimeout(h,0)},i,!0)):(i.classList.remove(f),s.classList.add(f),this._isSliding=!1,h()),t&&this.cycle()}}}_directionToOrder(e){return[Oe,h].includes(e)?n()?e===h?c:u:e===h?u:c:e}_orderToDirection(e){return[u,c].includes(e)?n()?e===c?h:Oe:e===c?Oe:h:e}static carouselInterface(e,t){e=p.getOrCreateInstance(e,t);let i=e["_config"];"object"==typeof t&&(i={...i,...t});var n="string"==typeof t?t:i.slide;if("number"==typeof t)e.to(t);else if("string"==typeof n){if(void 0===e[n])throw new TypeError(`No method named "${n}"`);e[n]()}else i.interval&&i.ride&&(e.pause(),e.cycle())}static jQueryInterface(e){return this.each(function(){p.carouselInterface(this,e)})}static dataApiClickHandler(e){var t,i,n=s(this);n&&n.classList.contains("carousel")&&(t={...l.getDataAttributes(n),...l.getDataAttributes(this)},(i=this.getAttribute("data-bs-slide-to"))&&(t.interval=!1),p.carouselInterface(n,t),i&&p.getInstance(n).to(i),e.preventDefault())}}m.on(document,"click.bs.carousel.data-api","[data-bs-slide], [data-bs-slide-to]",p.dataApiClickHandler),m.on(window,"load.bs.carousel.data-api",()=>{var i=d.find('[data-bs-ride="carousel"]');for(let e=0,t=i.length;e<t;e++)p.carouselInterface(i[e],p.getInstance(i[e]))}),e(p);const xe="collapse",De="bs.collapse";De;const Se={toggle:!0,parent:""},Ie={toggle:"boolean",parent:"(string|element)"};const g="show",Ne="collapse",je="collapsing",Me="collapsed",Pe='[data-bs-toggle="collapse"]';class _ extends t{constructor(e,t){super(e),this._isTransitioning=!1,this._config=this._getConfig(t),this._triggerArray=d.find(Pe+`[href="#${this._element.id}"],`+Pe+`[data-bs-target="#${this._element.id}"]`);var i=d.find(Pe);for(let e=0,t=i.length;e<t;e++){var n=i[e],s=W(n),o=d.find(s).filter(e=>e===this._element);null!==s&&o.length&&(this._selector=s,this._triggerArray.push(n))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}static get Default(){return Se}static get NAME(){return xe}toggle(){this._element.classList.contains(g)?this.hide():this.show()}show(){if(!this._isTransitioning&&!this._element.classList.contains(g)){let e,t;this._parent&&0===(e=d.find(".show, .collapsing",this._parent).filter(e=>"string"==typeof this._config.parent?e.getAttribute("data-bs-parent")===this._config.parent:e.classList.contains(Ne))).length&&(e=null);const n=d.findOne(this._selector);if(e){var i=e.find(e=>n!==e);if((t=i?_.getInstance(i):null)&&t._isTransitioning)return}i=m.trigger(this._element,"show.bs.collapse");if(!i.defaultPrevented){e&&e.forEach(e=>{n!==e&&_.collapseInterface(e,"hide"),t||fe(e,De,null)});const s=this._getDimension();this._element.classList.remove(Ne),this._element.classList.add(je),this._element.style[s]=0,this._triggerArray.length&&this._triggerArray.forEach(e=>{e.classList.remove(Me),e.setAttribute("aria-expanded",!0)}),this.setTransitioning(!0);i="scroll"+(s[0].toUpperCase()+s.slice(1));this._queueCallback(()=>{this._element.classList.remove(je),this._element.classList.add(Ne,g),this._element.style[s]="",this.setTransitioning(!1),m.trigger(this._element,"shown.bs.collapse")},this._element,!0),this._element.style[s]=this._element[i]+"px"}}}hide(){if(!this._isTransitioning&&this._element.classList.contains(g)){var e=m.trigger(this._element,"hide.bs.collapse");if(!e.defaultPrevented){var e=this._getDimension(),t=(this._element.style[e]=this._element.getBoundingClientRect()[e]+"px",K(this._element),this._element.classList.add(je),this._element.classList.remove(Ne,g),this._triggerArray.length);if(0<t)for(let e=0;e<t;e++){var i=this._triggerArray[e],n=s(i);n&&!n.classList.contains(g)&&(i.classList.add(Me),i.setAttribute("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[e]="",this._queueCallback(()=>{this.setTransitioning(!1),this._element.classList.remove(je),this._element.classList.add(Ne),m.trigger(this._element,"hidden.bs.collapse")},this._element,!0)}}}setTransitioning(e){this._isTransitioning=e}_getConfig(e){return(e={...Se,...e}).toggle=Boolean(e.toggle),i(xe,e,Ie),e}_getDimension(){return this._element.classList.contains("width")?"width":"height"}_getParent(){var e=this._config["parent"],e=$(e),t=Pe+`[data-bs-parent="${e}"]`;return d.find(t,e).forEach(e=>{var t=s(e);this._addAriaAndCollapsedClass(t,[e])}),e}_addAriaAndCollapsedClass(e,t){if(e&&t.length){const i=e.classList.contains(g);t.forEach(e=>{i?e.classList.remove(Me):e.classList.add(Me),e.setAttribute("aria-expanded",i)})}}static collapseInterface(e,t){let i=_.getInstance(e);var n={...Se,...l.getDataAttributes(e),..."object"==typeof t&&t?t:{}};if(!i&&n.toggle&&"string"==typeof t&&/show|hide/.test(t)&&(n.toggle=!1),i=i||new _(e,n),"string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}static jQueryInterface(e){return this.each(function(){_.collapseInterface(this,e)})}}m.on(document,"click.bs.collapse.data-api",Pe,function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();const n=l.getDataAttributes(this);e=W(this);d.find(e).forEach(e=>{var t=_.getInstance(e);let i;i=t?(null===t._parent&&"string"==typeof n.parent&&(t._config.parent=n.parent,t._parent=t._getParent()),"toggle"):n,_.collapseInterface(e,i)})}),e(_);var L="top",k="bottom",x="right",D="left",He="auto",Re=[L,k,x,D],S="start",Be="end",We="clippingParents",qe="viewport",$e="popper",ze="reference",Fe=Re.reduce(function(e,t){return e.concat([t+"-"+S,t+"-"+Be])},[]),Ue=[].concat(Re,[He]).reduce(function(e,t){return e.concat([t,t+"-"+S,t+"-"+Be])},[]),Ee="beforeRead",Ve="afterRead",Ke="beforeMain",Xe="afterMain",Ye="beforeWrite",Qe="afterWrite",Ge=[Ee,"read",Ve,Ke,"main",Xe,Ye,"write",Qe];function v(e){return e?(e.nodeName||"").toLowerCase():null}function b(e){var t;return null==e?window:"[object Window]"!==e.toString()?(t=e.ownerDocument)&&t.defaultView||window:e}function Ze(e){return e instanceof b(e).Element||e instanceof Element}function y(e){return e instanceof b(e).HTMLElement||e instanceof HTMLElement}function Je(e){return"undefined"!=typeof ShadowRoot&&(e instanceof b(e).ShadowRoot||e instanceof ShadowRoot)}var w={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var s=e.state;Object.keys(s.elements).forEach(function(e){var t=s.styles[e]||{},i=s.attributes[e]||{},n=s.elements[e];y(n)&&v(n)&&(Object.assign(n.style,t),Object.keys(i).forEach(function(e){var t=i[e];!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var n=e.state,s={popper:{position:n.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(n.elements.popper.style,s.popper),n.styles=s,n.elements.arrow&&Object.assign(n.elements.arrow.style,s.arrow),function(){Object.keys(n.elements).forEach(function(e){var t=n.elements[e],i=n.attributes[e]||{},e=Object.keys((n.styles.hasOwnProperty(e)?n.styles:s)[e]).reduce(function(e,t){return e[t]="",e},{});y(t)&&v(t)&&(Object.assign(t.style,e),Object.keys(i).forEach(function(e){t.removeAttribute(e)}))})}},requires:["computeStyles"]};function I(e){return e.split("-")[0]}function et(e){e=e.getBoundingClientRect();return{width:e.width,height:e.height,top:e.top,right:e.right,bottom:e.bottom,left:e.left,x:e.left,y:e.top}}function tt(e){var t=et(e),i=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-i)<=1&&(i=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:i,height:n}}function it(e,t){var i=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(i&&Je(i)){var n=t;do{if(n&&e.isSameNode(n))return!0}while(n=n.parentNode||n.host)}return!1}function E(e){return b(e).getComputedStyle(e)}function A(e){return((Ze(e)?e.ownerDocument:e.document)||window.document).documentElement}function nt(e){return"html"===v(e)?e:e.assignedSlot||e.parentNode||(Je(e)?e.host:null)||A(e)}function st(e){return y(e)&&"fixed"!==E(e).position?e.offsetParent:null}function ot(e){for(var t,i=b(e),n=st(e);n&&(t=n,0<=["table","td","th"].indexOf(v(t)))&&"static"===E(n).position;)n=st(n);return(!n||"html"!==v(n)&&("body"!==v(n)||"static"!==E(n).position))&&(n||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox"),i=-1!==navigator.userAgent.indexOf("Trident");if(!i||!y(e)||"fixed"!==E(e).position)for(var n=nt(e);y(n)&&["html","body"].indexOf(v(n))<0;){var s=E(n);if("none"!==s.transform||"none"!==s.perspective||"paint"===s.contain||-1!==["transform","perspective"].indexOf(s.willChange)||t&&"filter"===s.willChange||t&&s.filter&&"none"!==s.filter)return n;n=n.parentNode}return null}(e))||i}function rt(e){return 0<=["top","bottom"].indexOf(e)?"x":"y"}var T=Math.max,at=Math.min,lt=Math.round;function ct(e,t,i){return T(e,at(t,i))}function ht(){return{top:0,right:0,bottom:0,left:0}}function dt(e){return Object.assign({},ht(),e)}function ut(i,e){return e.reduce(function(e,t){return e[t]=i,e},{})}var O={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,i,n,s,o=e.state,r=e.name,e=e.options,a=o.elements.arrow,l=o.modifiersData.popperOffsets,c=rt(h=I(o.placement)),h=0<=[D,x].indexOf(h)?"height":"width";a&&l&&(e=e.padding,i=o,i=dt("number"!=typeof(e="function"==typeof e?e(Object.assign({},i.rects,{placement:i.placement})):e)?e:ut(e,Re)),e=tt(a),s="y"===c?L:D,n="y"===c?k:x,t=o.rects.reference[h]+o.rects.reference[c]-l[c]-o.rects.popper[h],l=l[c]-o.rects.reference[c],a=(a=ot(a))?"y"===c?a.clientHeight||0:a.clientWidth||0:0,s=i[s],i=a-e[h]-i[n],s=ct(s,n=a/2-e[h]/2+(t/2-l/2),i),o.modifiersData[r]=((a={})[c]=s,a.centerOffset=s-n,a))},effect:function(e){var t=e.state;null!=(e=void 0===(e=e.options.element)?"[data-popper-arrow]":e)&&("string"!=typeof e||(e=t.elements.popper.querySelector(e)))&&it(t.elements.popper,e)&&(t.elements.arrow=e)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},ft={top:"auto",right:"auto",bottom:"auto",left:"auto"};function pt(e){var t,i,n,s=e.popper,o=e.popperRect,r=e.placement,a=e.offsets,l=e.position,c=e.gpuAcceleration,h=e.adaptive,e=e.roundOffsets,d=!0===e?(d=(u=a).x,u=a.y,f=window.devicePixelRatio||1,{x:lt(lt(d*f)/f)||0,y:lt(lt(u*f)/f)||0}):"function"==typeof e?e(a):a,u=d.x,f=void 0===u?0:u,e=d.y,e=void 0===e?0:e,p=a.hasOwnProperty("x"),a=a.hasOwnProperty("y"),m=D,g=L,_=window,s=(h&&(n="clientHeight",i="clientWidth",(t=ot(s))===b(s)&&"static"!==E(t=A(s)).position&&(n="scrollHeight",i="scrollWidth"),r===L&&(g=k,e=(e-(t[n]-o.height))*(c?1:-1)),r===D)&&(m=x,f=(f-(t[i]-o.width))*(c?1:-1)),Object.assign({position:l},h&&ft));return c?Object.assign({},s,((n={})[g]=a?"0":"",n[m]=p?"0":"",n.transform=(_.devicePixelRatio||1)<2?"translate("+f+"px, "+e+"px)":"translate3d("+f+"px, "+e+"px, 0)",n)):Object.assign({},s,((r={})[g]=a?e+"px":"",r[m]=p?f+"px":"",r.transform="",r))}var mt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,e=e.options,i=void 0===(i=e.gpuAcceleration)||i,n=void 0===(n=e.adaptive)||n,e=void 0===(e=e.roundOffsets)||e,i={placement:I(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,pt(Object.assign({},i,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:n,roundOffsets:e})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,pt(Object.assign({},i,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:e})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},gt={passive:!0};var _t={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,i=e.instance,n=(e=e.options).scroll,s=void 0===n||n,o=void 0===(n=e.resize)||n,r=b(t.elements.popper),a=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&a.forEach(function(e){e.addEventListener("scroll",i.update,gt)}),o&&r.addEventListener("resize",i.update,gt),function(){s&&a.forEach(function(e){e.removeEventListener("scroll",i.update,gt)}),o&&r.removeEventListener("resize",i.update,gt)}},data:{}},vt={left:"right",right:"left",bottom:"top",top:"bottom"};function bt(e){return e.replace(/left|right|bottom|top/g,function(e){return vt[e]})}var yt={start:"end",end:"start"};function wt(e){return e.replace(/start|end/g,function(e){return yt[e]})}function Et(e){e=b(e);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function At(e){return et(A(e)).left+Et(e).scrollLeft}function Tt(e){var e=E(e),t=e.overflow,i=e.overflowX,e=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+e+i)}function Ot(e,t){void 0===t&&(t=[]);var i=function e(t){return 0<=["html","body","#document"].indexOf(v(t))?t.ownerDocument.body:y(t)&&Tt(t)?t:e(nt(t))}(e),e=i===(null==(e=e.ownerDocument)?void 0:e.body),n=b(i),n=e?[n].concat(n.visualViewport||[],Tt(i)?i:[]):i,i=t.concat(n);return e?i:i.concat(Ot(nt(n)))}function Ct(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Lt(e,t){return t===qe?Ct((n=b(i=e),s=A(i),n=n.visualViewport,o=s.clientWidth,s=s.clientHeight,a=r=0,n&&(o=n.width,s=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(r=n.offsetLeft,a=n.offsetTop)),{width:o,height:s,x:r+At(i),y:a})):y(t)?((o=et(n=t)).top=o.top+n.clientTop,o.left=o.left+n.clientLeft,o.bottom=o.top+n.clientHeight,o.right=o.left+n.clientWidth,o.width=n.clientWidth,o.height=n.clientHeight,o.x=o.left,o.y=o.top,o):Ct((s=A(e),r=A(s),i=Et(s),a=null==(a=s.ownerDocument)?void 0:a.body,t=T(r.scrollWidth,r.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),e=T(r.scrollHeight,r.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),s=-i.scrollLeft+At(s),i=-i.scrollTop,"rtl"===E(a||r).direction&&(s+=T(r.clientWidth,a?a.clientWidth:0)-t),{width:t,height:e,x:s,y:i}));var i,n,s,o,r,a}function kt(i,e,t){var n,s="clippingParents"===e?(o=Ot(nt(s=i)),Ze(n=0<=["absolute","fixed"].indexOf(E(s).position)&&y(s)?ot(s):s)?o.filter(function(e){return Ze(e)&&it(e,n)&&"body"!==v(e)}):[]):[].concat(e),o=[].concat(s,[t]),e=o[0],t=o.reduce(function(e,t){t=Lt(i,t);return e.top=T(t.top,e.top),e.right=at(t.right,e.right),e.bottom=at(t.bottom,e.bottom),e.left=T(t.left,e.left),e},Lt(i,e));return t.width=t.right-t.left,t.height=t.bottom-t.top,t.x=t.left,t.y=t.top,t}function xt(e){return e.split("-")[1]}function Dt(e){var t,i=e.reference,n=e.element,e=e.placement,s=e?I(e):null,e=e?xt(e):null,o=i.x+i.width/2-n.width/2,r=i.y+i.height/2-n.height/2;switch(s){case L:t={x:o,y:i.y-n.height};break;case k:t={x:o,y:i.y+i.height};break;case x:t={x:i.x+i.width,y:r};break;case D:t={x:i.x-n.width,y:r};break;default:t={x:i.x,y:i.y}}var a=s?rt(s):null;if(null!=a){var l="y"===a?"height":"width";switch(e){case S:t[a]=t[a]-(i[l]/2-n[l]/2);break;case Be:t[a]=t[a]+(i[l]/2-n[l]/2)}}return t}function St(e,t){var n,t=t=void 0===t?{}:t,i=t.placement,i=void 0===i?e.placement:i,s=t.boundary,s=void 0===s?We:s,o=t.rootBoundary,o=void 0===o?qe:o,r=t.elementContext,r=void 0===r?$e:r,a=t.altBoundary,a=void 0!==a&&a,t=t.padding,t=void 0===t?0:t,t=dt("number"!=typeof t?t:ut(t,Re)),l=e.elements.reference,c=e.rects.popper,a=e.elements[a?r===$e?ze:$e:r],a=kt(Ze(a)?a:a.contextElement||A(e.elements.popper),s,o),s=et(l),o=Dt({reference:s,element:c,strategy:"absolute",placement:i}),l=Ct(Object.assign({},c,o)),c=r===$e?l:s,h={top:a.top-c.top+t.top,bottom:c.bottom-a.bottom+t.bottom,left:a.left-c.left+t.left,right:c.right-a.right+t.right},o=e.modifiersData.offset;return r===$e&&o&&(n=o[i],Object.keys(h).forEach(function(e){var t=0<=[x,k].indexOf(e)?1:-1,i=0<=[L,k].indexOf(e)?"y":"x";h[e]+=n[i]*t})),h}var It={name:"flip",enabled:!0,phase:"main",fn:function(e){var d=e.state,t=e.options,e=e.name;if(!d.modifiersData[e]._skip){for(var i=t.mainAxis,n=void 0===i||i,i=t.altAxis,s=void 0===i||i,i=t.fallbackPlacements,u=t.padding,f=t.boundary,p=t.rootBoundary,o=t.altBoundary,r=t.flipVariations,m=void 0===r||r,g=t.allowedAutoPlacements,r=d.options.placement,t=I(r),i=i||(t===r||!m?[bt(r)]:I(i=r)===He?[]:(t=bt(i),[wt(i),t,wt(t)])),a=[r].concat(i).reduce(function(e,t){return e.concat(I(t)===He?(i=d,n=(e=e=void 0===(e={placement:t,boundary:f,rootBoundary:p,padding:u,flipVariations:m,allowedAutoPlacements:g})?{}:e).placement,s=e.boundary,o=e.rootBoundary,r=e.padding,a=e.flipVariations,l=void 0===(e=e.allowedAutoPlacements)?Ue:e,c=xt(n),e=c?a?Fe:Fe.filter(function(e){return xt(e)===c}):Re,h=(n=0===(n=e.filter(function(e){return 0<=l.indexOf(e)})).length?e:n).reduce(function(e,t){return e[t]=St(i,{placement:t,boundary:s,rootBoundary:o,padding:r})[I(t)],e},{}),Object.keys(h).sort(function(e,t){return h[e]-h[t]})):t);var i,n,s,o,r,a,l,c,h},[]),l=d.rects.reference,c=d.rects.popper,h=new Map,_=!0,v=a[0],b=0;b<a.length;b++){var y=a[b],w=I(y),E=xt(y)===S,A=0<=[L,k].indexOf(w),T=A?"width":"height",O=St(d,{placement:y,boundary:f,rootBoundary:p,altBoundary:o,padding:u}),A=A?E?x:D:E?k:L,E=(l[T]>c[T]&&(A=bt(A)),bt(A)),T=[];if(n&&T.push(O[w]<=0),s&&T.push(O[A]<=0,O[E]<=0),T.every(function(e){return e})){v=y,_=!1;break}h.set(y,T)}if(_)for(var C=m?3:1;0<C;C--)if("break"===function(t){var e=a.find(function(e){e=h.get(e);if(e)return e.slice(0,t).every(function(e){return e})});if(e)return v=e,"break"}(C))break;d.placement!==v&&(d.modifiersData[e]._skip=!0,d.placement=v,d.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Nt(e,t,i){return{top:e.top-t.height-(i=void 0===i?{x:0,y:0}:i).y,right:e.right-t.width+i.x,bottom:e.bottom-t.height+i.y,left:e.left-t.width-i.x}}function jt(t){return[L,x,k,D].some(function(e){return 0<=t[e]})}var Mt={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,e=e.name,i=t.rects.reference,n=t.rects.popper,s=t.modifiersData.preventOverflow,o=St(t,{elementContext:"reference"}),r=St(t,{altBoundary:!0}),o=Nt(o,i),i=Nt(r,n,s),r=jt(o),n=jt(i);t.modifiersData[e]={referenceClippingOffsets:o,popperEscapeOffsets:i,isReferenceHidden:r,hasPopperEscaped:n},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":r,"data-popper-escaped":n})}};var Pt={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var r=e.state,t=e.options,e=e.name,a=void 0===(t=t.offset)?[0,0]:t,t=Ue.reduce(function(e,t){var i,n,s,o;return e[t]=(t=t,i=r.rects,n=a,s=I(t),o=0<=[D,L].indexOf(s)?-1:1,t=(i="function"==typeof n?n(Object.assign({},i,{placement:t})):n)[0]||0,n=(i[1]||0)*o,0<=[D,x].indexOf(s)?{x:n,y:t}:{x:t,y:n}),e},{}),i=(n=t[r.placement]).x,n=n.y;null!=r.modifiersData.popperOffsets&&(r.modifiersData.popperOffsets.x+=i,r.modifiersData.popperOffsets.y+=n),r.modifiersData[e]=t}};var Ht={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,e=e.name;t.modifiersData[e]=Dt({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};var Rt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t,i,n,s,o,r,a,l,c,h=e.state,d=e.options,e=e.name,u=void 0===(u=d.mainAxis)||u,f=void 0!==(f=d.altAxis)&&f,p=d.boundary,m=d.rootBoundary,g=d.altBoundary,_=d.padding,v=void 0===(v=d.tether)||v,d=void 0===(d=d.tetherOffset)?0:d,p=St(h,{boundary:p,rootBoundary:m,padding:_,altBoundary:g}),m=I(h.placement),g=!(_=xt(h.placement)),b="x"===(m=rt(m))?"y":"x",y=h.modifiersData.popperOffsets,w=h.rects.reference,E=h.rects.popper,d="function"==typeof d?d(Object.assign({},h.rects,{placement:h.placement})):d,A={x:0,y:0};y&&((u||f)&&(o="y"===m?"height":"width",t=y[m],i=y[m]+p[c="y"===m?L:D],n=y[m]-p[a="y"===m?k:x],r=v?-E[o]/2:0,s=(_===S?w:E)[o],_=_===S?-E[o]:-w[o],E=h.elements.arrow,E=v&&E?tt(E):{width:0,height:0},c=(l=h.modifiersData["arrow#persistent"]?h.modifiersData["arrow#persistent"].padding:ht())[c],l=l[a],a=ct(0,w[o],E[o]),E=g?w[o]/2-r-a-c-d:s-a-c-d,s=g?-w[o]/2+r+a+l+d:_+a+l+d,g=(c=h.elements.arrow&&ot(h.elements.arrow))?"y"===m?c.clientTop||0:c.clientLeft||0:0,w=h.modifiersData.offset?h.modifiersData.offset[h.placement][m]:0,o=y[m]+E-w-g,r=y[m]+s-w,u&&(_=ct(v?at(i,o):i,t,v?T(n,r):n),y[m]=_,A[m]=_-t),f)&&(l=(a=y[b])+p["x"===m?L:D],d=a-p["x"===m?k:x],c=ct(v?at(l,o):l,a,v?T(d,r):d),y[b]=c,A[b]=c-a),h.modifiersData[e]=A)},requiresIfExists:["offset"]};function Bt(e,t,i){void 0===i&&(i=!1);var n=A(t),e=et(e),s=y(t),o={scrollLeft:0,scrollTop:0},r={x:0,y:0};return!s&&i||("body"===v(t)&&!Tt(n)||(o=(s=t)!==b(s)&&y(s)?{scrollLeft:s.scrollLeft,scrollTop:s.scrollTop}:Et(s)),y(t)?((r=et(t)).x+=t.clientLeft,r.y+=t.clientTop):n&&(r.x=At(n))),{x:e.left+o.scrollLeft-r.x,y:e.top+o.scrollTop-r.y,width:e.width,height:e.height}}function Wt(e){var i=new Map,n=new Set,s=[];return e.forEach(function(e){i.set(e.name,e)}),e.forEach(function(e){n.has(e.name)||!function t(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){n.has(e)||(e=i.get(e))&&t(e)}),s.push(e)}(e)}),s}var qt={placement:"bottom",modifiers:[],strategy:"absolute"};function $t(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function zt(e){var e=e=void 0===e?{}:e,t=e.defaultModifiers,d=void 0===t?[]:t,t=e.defaultOptions,u=void 0===t?qt:t;return function(n,s,t){void 0===t&&(t=u);var i,o,r={placement:"bottom",orderedModifiers:[],options:Object.assign({},qt,u),modifiersData:{},elements:{reference:n,popper:s},attributes:{},styles:{}},a=[],l=!1,c={state:r,setOptions:function(e){h(),r.options=Object.assign({},u,r.options,e),r.scrollParents={reference:Ze(n)?Ot(n):n.contextElement?Ot(n.contextElement):[],popper:Ot(s)};e=[].concat(d,r.options.modifiers),t=e.reduce(function(e,t){var i=e[t.name];return e[t.name]=i?Object.assign({},i,t,{options:Object.assign({},i.options,t.options),data:Object.assign({},i.data,t.data)}):t,e},{}),e=Object.keys(t).map(function(e){return t[e]}),i=Wt(e);var i,t,e=Ge.reduce(function(e,t){return e.concat(i.filter(function(e){return e.phase===t}))},[]);return r.orderedModifiers=e.filter(function(e){return e.enabled}),r.orderedModifiers.forEach(function(e){var t=e.name,i=e.options,e=e.effect;"function"==typeof e&&(e=e({state:r,name:t,instance:c,options:void 0===i?{}:i}),a.push(e||function(){}))}),c.update()},forceUpdate:function(){if(!l){var e=r.elements,t=e.reference,e=e.popper;if($t(t,e)){r.rects={reference:Bt(t,ot(e),"fixed"===r.options.strategy),popper:tt(e)},r.reset=!1,r.placement=r.options.placement,r.orderedModifiers.forEach(function(e){return r.modifiersData[e.name]=Object.assign({},e.data)});for(var i,n,s,o=0;o<r.orderedModifiers.length;o++)!0===r.reset?(r.reset=!1,o=-1):(i=(s=r.orderedModifiers[o]).fn,n=s.options,s=s.name,"function"==typeof i&&(r=i({state:r,options:void 0===n?{}:n,name:s,instance:c})||r))}}},update:(i=function(){return new Promise(function(e){c.forceUpdate(),e(r)})},function(){return o=o||new Promise(function(e){Promise.resolve().then(function(){o=void 0,e(i())})})}),destroy:function(){h(),l=!0}};return $t(n,s)&&c.setOptions(t).then(function(e){!l&&t.onFirstUpdate&&t.onFirstUpdate(e)}),c;function h(){a.forEach(function(e){return e()}),a=[]}}}var Ft=zt({defaultModifiers:[_t,Ht,mt,w,Pt,It,Rt,O,Mt]}),Ut=Object.freeze({__proto__:null,popperGenerator:zt,detectOverflow:St,createPopperBase:zt(),createPopper:Ft,createPopperLite:zt({defaultModifiers:[_t,Ht,mt,w]}),top:L,bottom:k,right:x,left:D,auto:He,basePlacements:Re,start:S,end:Be,clippingParents:We,viewport:qe,popper:$e,reference:ze,variationPlacements:Fe,placements:Ue,beforeRead:Ee,read:"read",afterRead:Ve,beforeMain:Ke,main:"main",afterMain:Xe,beforeWrite:Ye,write:"write",afterWrite:Qe,modifierPhases:Ge,applyStyles:w,arrow:O,computeStyles:mt,eventListeners:_t,flip:It,hide:Mt,offset:Pt,popperOffsets:Ht,preventOverflow:Rt});const Vt="dropdown";Ee=".bs.dropdown",Ve=".data-api";const Kt="Escape",Xt="ArrowUp",Yt="ArrowDown",Qt=new RegExp(Xt+`|${Yt}|`+Kt);Ke="click"+Ee+Ve,Xe="keydown"+Ee+Ve;const C="show",Gt='[data-bs-toggle="dropdown"]',Zt=".dropdown-menu",Jt=n()?"top-end":"top-start",ei=n()?"top-start":"top-end",ti=n()?"bottom-end":"bottom-start",ii=n()?"bottom-start":"bottom-end",ni=n()?"left-start":"right-start",si=n()?"right-start":"left-start",oi={offset:[0,2],boundary:"clippingParents",reference:"toggle",display:"dynamic",popperConfig:null,autoClose:!0},ri={offset:"(array|string|function)",boundary:"(string|element)",reference:"(string|element|object)",display:"string",popperConfig:"(null|object|function)",autoClose:"(boolean|string)"};class N extends t{constructor(e,t){super(e),this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}static get Default(){return oi}static get DefaultType(){return ri}static get NAME(){return Vt}toggle(){F(this._element)||(this._element.classList.contains(C)?this.hide():this.show())}show(){if(!F(this._element)&&!this._menu.classList.contains(C)){var t=N.getParentFromElement(this._element),e={relatedTarget:this._element},i=m.trigger(this._element,"show.bs.dropdown",e);if(!i.defaultPrevented){if(this._inNavbar)l.setDataAttribute(this._menu,"popper","none");else{if(void 0===Ut)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=t:r(this._config.reference)?e=$(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);var i=this._getPopperConfig(),n=i.modifiers.find(e=>"applyStyles"===e.name&&!1===e.enabled);this._popper=Ft(e,this._menu,i),n&&l.setDataAttribute(this._menu,"popper","static")}"ontouchstart"in document.documentElement&&!t.closest(".navbar-nav")&&[].concat(...document.body.children).forEach(e=>m.on(e,"mouseover",V)),this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.toggle(C),this._element.classList.toggle(C),m.trigger(this._element,"shown.bs.dropdown",e)}}}hide(){var e;!F(this._element)&&this._menu.classList.contains(C)&&(e={relatedTarget:this._element},this._completeHide(e))}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_addEventListeners(){m.on(this._element,"click.bs.dropdown",e=>{e.preventDefault(),this.toggle()})}_completeHide(e){m.trigger(this._element,"hide.bs.dropdown",e).defaultPrevented||("ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(e=>m.off(e,"mouseover",V)),this._popper&&this._popper.destroy(),this._menu.classList.remove(C),this._element.classList.remove(C),this._element.setAttribute("aria-expanded","false"),l.removeDataAttribute(this._menu,"popper"),m.trigger(this._element,"hidden.bs.dropdown",e))}_getConfig(e){if(e={...this.constructor.Default,...l.getDataAttributes(this._element),...e},i(Vt,e,this.constructor.DefaultType),"object"!=typeof e.reference||r(e.reference)||"function"==typeof e.reference.getBoundingClientRect)return e;throw new TypeError(Vt.toUpperCase()+': Option "reference" provided type "object" without a required "getBoundingClientRect" method.')}_getMenuElement(){return d.next(this._element,Zt)[0]}_getPlacement(){var e,t=this._element.parentNode;return t.classList.contains("dropend")?ni:t.classList.contains("dropstart")?si:(e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim(),t.classList.contains("dropup")?e?ei:Jt:e?ii:ti)}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const t=this._config["offset"];return"string"==typeof t?t.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){var e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return"static"===this._config.display&&(e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,..."function"==typeof this._config.popperConfig?this._config.popperConfig(e):this._config.popperConfig}}_selectMenuItem({key:e,target:t}){var i=d.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(z);i.length&&G(i,t,e===Yt,!i.includes(t)).focus()}static dropdownInterface(e,t){e=N.getOrCreateInstance(e,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}static jQueryInterface(e){return this.each(function(){N.dropdownInterface(this,e)})}static clearMenus(i){if(!i||2!==i.button&&("keyup"!==i.type||"Tab"===i.key)){var n=d.find(Gt);for(let e=0,t=n.length;e<t;e++){var s=N.getInstance(n[e]);if(s&&!1!==s._config.autoClose&&s._element.classList.contains(C)){var o={relatedTarget:s._element};if(i){var r=i.composedPath(),a=r.includes(s._menu);if(r.includes(s._element)||"inside"===s._config.autoClose&&!a||"outside"===s._config.autoClose&&a)continue;if(s._menu.contains(i.target)&&("keyup"===i.type&&"Tab"===i.key||/input|select|option|textarea|form/i.test(i.target.tagName)))continue;"click"===i.type&&(o.clickEvent=i)}s._completeHide(o)}}}}static getParentFromElement(e){return s(e)||e.parentNode}static dataApiKeydownHandler(e){var t,i;(/input|textarea/i.test(e.target.tagName)?"Space"===e.key||e.key!==Kt&&(e.key!==Yt&&e.key!==Xt||e.target.closest(Zt)):!Qt.test(e.key))||!(t=this.classList.contains(C))&&e.key===Kt||(e.preventDefault(),e.stopPropagation(),F(this))||(i=()=>this.matches(Gt)?this:d.prev(this,Gt)[0],e.key===Kt?(i().focus(),N.clearMenus()):e.key===Xt||e.key===Yt?(t||i().click(),N.getInstance(i())._selectMenuItem(e)):t&&"Space"!==e.key||N.clearMenus())}}m.on(document,Xe,Gt,N.dataApiKeydownHandler),m.on(document,Xe,Zt,N.dataApiKeydownHandler),m.on(document,Ke,N.clearMenus),m.on(document,"keyup.bs.dropdown.data-api",N.clearMenus),m.on(document,Ke,Gt,function(e){e.preventDefault(),N.dropdownInterface(this)}),e(N);const ai=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",li=".sticky-top";class ci{constructor(){this._element=document.body}getWidth(){var e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,"paddingRight",e=>e+t),this._setElementAttributes(ai,"paddingRight",e=>e+t),this._setElementAttributes(li,"marginRight",e=>e-t)}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,i,n){const s=this.getWidth();this._applyManipulationCallback(e,e=>{var t;e!==this._element&&window.innerWidth>e.clientWidth+s||(this._saveInitialAttribute(e,i),t=window.getComputedStyle(e)[i],e.style[i]=n(Number.parseFloat(t))+"px")})}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,"paddingRight"),this._resetElementAttributes(ai,"paddingRight"),this._resetElementAttributes(li,"marginRight")}_saveInitialAttribute(e,t){var i=e.style[t];i&&l.setDataAttribute(e,t,i)}_resetElementAttributes(e,i){this._applyManipulationCallback(e,e=>{var t=l.getDataAttribute(e,i);void 0===t?e.style.removeProperty(i):(l.removeDataAttribute(e,i),e.style[i]=t)})}_applyManipulationCallback(e,t){r(e)?t(e):d.find(e,this._element).forEach(t)}isOverflowing(){return 0<this.getWidth()}}const hi={isVisible:!0,isAnimated:!1,rootElement:"body",clickCallback:null},di={isVisible:"boolean",isAnimated:"boolean",rootElement:"(element|string)",clickCallback:"(function|null)"},ui="backdrop",fi="mousedown.bs."+ui;class pi{constructor(e){this._config=this._getConfig(e),this._isAppended=!1,this._element=null}show(e){this._config.isVisible?(this._append(),this._config.isAnimated&&K(this._getElement()),this._getElement().classList.add("show"),this._emulateAnimation(()=>{o(e)})):o(e)}hide(e){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation(()=>{this.dispose(),o(e)})):o(e)}_getElement(){var e;return this._element||((e=document.createElement("div")).className="modal-backdrop",this._config.isAnimated&&e.classList.add("fade"),this._element=e),this._element}_getConfig(e){return(e={...hi,..."object"==typeof e?e:{}}).rootElement=$(e.rootElement),i(ui,e,di),e}_append(){this._isAppended||(this._config.rootElement.appendChild(this._getElement()),m.on(this._getElement(),fi,()=>{o(this._config.clickCallback)}),this._isAppended=!0)}dispose(){this._isAppended&&(m.off(this._element,fi),this._element.remove(),this._isAppended=!1)}_emulateAnimation(e){Q(e,this._getElement(),this._config.isAnimated)}}const j=".bs.modal";const mi={backdrop:!0,keyboard:!0,focus:!0},gi={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean"},_i=(j,j,"hidden"+j),vi="show"+j,bi=(j,"focusin"+j),yi="resize"+j,wi="click.dismiss"+j,Ei="keydown.dismiss"+j,Ai=(j,"mousedown.dismiss"+j);j;const Ti="modal-open",Oi="modal-static";class Ci extends t{constructor(e,t){super(e),this._config=this._getConfig(t),this._dialog=d.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._isShown=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollBar=new ci}static get Default(){return mi}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||m.trigger(this._element,vi,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isAnimated()&&(this._isTransitioning=!0),this._scrollBar.hide(),document.body.classList.add(Ti),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),m.on(this._element,wi,'[data-bs-dismiss="modal"]',e=>this.hide(e)),m.on(this._dialog,Ai,()=>{m.one(this._element,"mouseup.dismiss.bs.modal",e=>{e.target===this._element&&(this._ignoreBackdropClick=!0)})}),this._showBackdrop(()=>this._showElement(e)))}hide(e){e&&["A","AREA"].includes(e.target.tagName)&&e.preventDefault(),!this._isShown||this._isTransitioning||m.trigger(this._element,"hide.bs.modal").defaultPrevented||(this._isShown=!1,(e=this._isAnimated())&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),m.off(document,bi),this._element.classList.remove("show"),m.off(this._element,wi),m.off(this._dialog,Ai),this._queueCallback(()=>this._hideModal(),this._element,e))}dispose(){[window,this._dialog].forEach(e=>m.off(e,j)),this._backdrop.dispose(),super.dispose(),m.off(document,bi)}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new pi({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_getConfig(e){return e={...mi,...l.getDataAttributes(this._element),..."object"==typeof e?e:{}},i("modal",e,gi),e}_showElement(e){var t=this._isAnimated(),i=d.findOne(".modal-body",this._dialog);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0,i&&(i.scrollTop=0),t&&K(this._element),this._element.classList.add("show"),this._config.focus&&this._enforceFocus();this._queueCallback(()=>{this._config.focus&&this._element.focus(),this._isTransitioning=!1,m.trigger(this._element,"shown.bs.modal",{relatedTarget:e})},this._dialog,t)}_enforceFocus(){m.off(document,bi),m.on(document,bi,e=>{document===e.target||this._element===e.target||this._element.contains(e.target)||this._element.focus()})}_setEscapeEvent(){this._isShown?m.on(this._element,Ei,e=>{this._config.keyboard&&"Escape"===e.key?(e.preventDefault(),this.hide()):this._config.keyboard||"Escape"!==e.key||this._triggerBackdropTransition()}):m.off(this._element,Ei)}_setResizeEvent(){this._isShown?m.on(window,yi,()=>this._adjustDialog()):m.off(window,yi)}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ti),this._resetAdjustments(),this._scrollBar.reset(),m.trigger(this._element,_i)})}_showBackdrop(e){m.on(this._element,wi,e=>{this._ignoreBackdropClick?this._ignoreBackdropClick=!1:e.target===e.currentTarget&&(!0===this._config.backdrop?this.hide():"static"===this._config.backdrop&&this._triggerBackdropTransition())}),this._backdrop.show(e)}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){var e=m.trigger(this._element,"hidePrevented.bs.modal");if(!e.defaultPrevented){const{classList:t,scrollHeight:i,style:n}=this._element,s=i>document.documentElement.clientHeight;!s&&"hidden"===n.overflowY||t.contains(Oi)||(s||(n.overflowY="hidden"),t.add(Oi),this._queueCallback(()=>{t.remove(Oi),s||this._queueCallback(()=>{n.overflowY=""},this._dialog)},this._dialog),this._element.focus())}}_adjustDialog(){var e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),i=0<t;(!i&&e&&!n()||i&&!e&&n())&&(this._element.style.paddingLeft=t+"px"),(i&&!e&&!n()||!i&&e&&n())&&(this._element.style.paddingRight=t+"px")}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,i){return this.each(function(){var e=Ci.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](i)}})}}m.on(document,"click.bs.modal.data-api",'[data-bs-toggle="modal"]',function(e){const t=s(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),m.one(t,vi,e=>{e.defaultPrevented||m.one(t,_i,()=>{z(this)&&this.focus()})}),Ci.getOrCreateInstance(t).toggle(this)}),e(Ci);const Li="offcanvas";Ye=".bs.offcanvas";const ki={backdrop:!0,keyboard:!0,scroll:!1},xi={backdrop:"boolean",keyboard:"boolean",scroll:"boolean"},Di=".offcanvas.show",Si="hidden"+Ye,Ii="focusin"+Ye;class Ni extends t{constructor(e,t){super(e),this._config=this._getConfig(t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._addEventListeners()}static get NAME(){return Li}static get Default(){return ki}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||m.trigger(this._element,"show.bs.offcanvas",{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._element.style.visibility="visible",this._backdrop.show(),this._config.scroll||((new ci).hide(),this._enforceFocusOnElement(this._element)),this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add("show"),this._queueCallback(()=>{m.trigger(this._element,"shown.bs.offcanvas",{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&!m.trigger(this._element,"hide.bs.offcanvas").defaultPrevented&&(m.off(document,Ii),this._element.blur(),this._isShown=!1,this._element.classList.remove("show"),this._backdrop.hide(),this._queueCallback(()=>{this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._element.style.visibility="hidden",this._config.scroll||(new ci).reset(),m.trigger(this._element,Si)},this._element,!0))}dispose(){this._backdrop.dispose(),super.dispose(),m.off(document,Ii)}_getConfig(e){return e={...ki,...l.getDataAttributes(this._element),..."object"==typeof e?e:{}},i(Li,e,xi),e}_initializeBackDrop(){return new pi({isVisible:this._config.backdrop,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:()=>this.hide()})}_enforceFocusOnElement(t){m.off(document,Ii),m.on(document,Ii,e=>{document===e.target||t===e.target||t.contains(e.target)||t.focus()}),t.focus()}_addEventListeners(){m.on(this._element,"click.dismiss.bs.offcanvas",'[data-bs-dismiss="offcanvas"]',()=>this.hide()),m.on(this._element,"keydown.dismiss.bs.offcanvas",e=>{this._config.keyboard&&"Escape"===e.key&&this.hide()})}static jQueryInterface(t){return this.each(function(){var e=Ni.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}})}}m.on(document,"click.bs.offcanvas.data-api",'[data-bs-toggle="offcanvas"]',function(e){var t=s(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),F(this)||(m.one(t,Si,()=>{z(this)&&this.focus()}),(e=d.findOne(Di))&&e!==t&&Ni.getInstance(e).hide(),Ni.getOrCreateInstance(t).toggle(this))}),m.on(window,"load.bs.offcanvas.data-api",()=>d.find(Di).forEach(e=>Ni.getOrCreateInstance(e).show())),e(Ni);const ji=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]);const Mi=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/i,Pi=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;Qe={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function Hi(e,i,t){if(!e.length)return e;if(t&&"function"==typeof t)return t(e);var t=(new window.DOMParser).parseFromString(e,"text/html"),n=Object.keys(i),s=[].concat(...t.body.querySelectorAll("*"));for(let e=0,t=s.length;e<t;e++){const a=s[e];var o=a.nodeName.toLowerCase();if(n.includes(o)){var r=[].concat(...a.attributes);const l=[].concat(i["*"]||[],i[o]||[]);r.forEach(e=>{((e,t)=>{var i=e.nodeName.toLowerCase();if(t.includes(i))return!ji.has(i)||Boolean(Mi.test(e.nodeValue)||Pi.test(e.nodeValue));var n=t.filter(e=>e instanceof RegExp);for(let e=0,t=n.length;e<t;e++)if(n[e].test(i))return!0;return!1})(e,l)||a.removeAttribute(e.nodeName)})}else a.remove()}return t.body.innerHTML}const Ri="tooltip";w=".bs.tooltip";const Bi="bs-tooltip",Wi=new RegExp(`(^|\\s)${Bi}\\S+`,"g"),qi=new Set(["sanitize","allowList","sanitizeFn"]),$i={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(array|string|function)",container:"(string|element|boolean)",fallbackPlacements:"array",boundary:"(string|element)",customClass:"(string|function)",sanitize:"boolean",sanitizeFn:"(null|function)",allowList:"object",popperConfig:"(null|object|function)"},zi={AUTO:"auto",TOP:"top",RIGHT:n()?"left":"right",BOTTOM:"bottom",LEFT:n()?"right":"left"},Fi={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:[0,0],container:!1,fallbackPlacements:["top","right","bottom","left"],boundary:"clippingParents",customClass:"",sanitize:!0,sanitizeFn:null,allowList:Qe,popperConfig:null},Ui={HIDE:"hide"+w,HIDDEN:"hidden"+w,SHOW:"show"+w,SHOWN:"shown"+w,INSERTED:"inserted"+w,CLICK:"click"+w,FOCUSIN:"focusin"+w,FOCUSOUT:"focusout"+w,MOUSEENTER:"mouseenter"+w,MOUSELEAVE:"mouseleave"+w},Vi="fade",Ki="show",Xi="show",Yi="hover",Qi="focus";class Gi extends t{constructor(e,t){if(void 0===Ut)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e),this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this._config=this._getConfig(t),this.tip=null,this._setListeners()}static get Default(){return Fi}static get NAME(){return Ri}static get Event(){return Ui}static get DefaultType(){return $i}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(e){this._isEnabled&&(e?((e=this._initializeOnDelegatedTarget(e))._activeTrigger.click=!e._activeTrigger.click,e._isWithActiveTrigger()?e._enter(null,e):e._leave(null,e)):this.getTipElement().classList.contains(Ki)?this._leave(null,this):this._enter(null,this))}dispose(){clearTimeout(this._timeout),m.off(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this.tip&&this.tip.remove(),this._popper&&this._popper.destroy(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");var e,t,i;this.isWithContent()&&this._isEnabled&&(e=m.trigger(this._element,this.constructor.Event.SHOW),i=(null===(i=U(this._element))?this._element.ownerDocument.documentElement:i).contains(this._element),!e.defaultPrevented)&&i&&(e=this.getTipElement(),i=R(this.constructor.NAME),e.setAttribute("id",i),this._element.setAttribute("aria-describedby",i),this.setContent(),this._config.animation&&e.classList.add(Vi),i="function"==typeof this._config.placement?this._config.placement.call(this,e,this._element):this._config.placement,i=this._getAttachment(i),t=(this._addAttachmentClass(i),this._config)["container"],fe(e,this.constructor.DATA_KEY,this),this._element.ownerDocument.documentElement.contains(this.tip)||(t.appendChild(e),m.trigger(this._element,this.constructor.Event.INSERTED)),this._popper?this._popper.update():this._popper=Ft(this._element,e,this._getPopperConfig(i)),e.classList.add(Ki),(t="function"==typeof this._config.customClass?this._config.customClass():this._config.customClass)&&e.classList.add(...t.split(" ")),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(e=>{m.on(e,"mouseover",V)}),i=this.tip.classList.contains(Vi),this._queueCallback(()=>{var e=this._hoverState;this._hoverState=null,m.trigger(this._element,this.constructor.Event.SHOWN),"out"===e&&this._leave(null,this)},this.tip,i))}hide(){if(this._popper){const t=this.getTipElement();var e;m.trigger(this._element,this.constructor.Event.HIDE).defaultPrevented||(t.classList.remove(Ki),"ontouchstart"in document.documentElement&&[].concat(...document.body.children).forEach(e=>m.off(e,"mouseover",V)),this._activeTrigger.click=!1,this._activeTrigger[Qi]=!1,this._activeTrigger[Yi]=!1,e=this.tip.classList.contains(Vi),this._queueCallback(()=>{this._isWithActiveTrigger()||(this._hoverState!==Xi&&t.remove(),this._cleanTipClass(),this._element.removeAttribute("aria-describedby"),m.trigger(this._element,this.constructor.Event.HIDDEN),this._popper&&(this._popper.destroy(),this._popper=null))},this.tip,e),this._hoverState="")}}update(){null!==this._popper&&this._popper.update()}isWithContent(){return Boolean(this.getTitle())}getTipElement(){var e;return this.tip||((e=document.createElement("div")).innerHTML=this._config.template,this.tip=e.children[0]),this.tip}setContent(){var e=this.getTipElement();this.setElementContent(d.findOne(".tooltip-inner",e),this.getTitle()),e.classList.remove(Vi,Ki)}setElementContent(e,t){null!==e&&(r(t)?(t=$(t),this._config.html?t.parentNode!==e&&(e.innerHTML="",e.appendChild(t)):e.textContent=t.textContent):this._config.html?(this._config.sanitize&&(t=Hi(t,this._config.allowList,this._config.sanitizeFn)),e.innerHTML=t):e.textContent=t)}getTitle(){let e=this._element.getAttribute("data-bs-original-title");return e=e||("function"==typeof this._config.title?this._config.title.call(this._element):this._config.title)}updateAttachment(e){return"right"===e?"end":"left"===e?"start":e}_initializeOnDelegatedTarget(e,t){var i=this.constructor.DATA_KEY;return(t=t||pe(e.delegateTarget,i))||(t=new this.constructor(e.delegateTarget,this._getDelegateConfig()),fe(e.delegateTarget,i,t)),t}_getOffset(){const t=this._config["offset"];return"string"==typeof t?t.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(e){e={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"onChange",enabled:!0,phase:"afterWrite",fn:e=>this._handlePopperPlacementChange(e)}],onFirstUpdate:e=>{e.options.placement!==e.placement&&this._handlePopperPlacementChange(e)}};return{...e,..."function"==typeof this._config.popperConfig?this._config.popperConfig(e):this._config.popperConfig}}_addAttachmentClass(e){this.getTipElement().classList.add(Bi+"-"+this.updateAttachment(e))}_getAttachment(e){return zi[e.toUpperCase()]}_setListeners(){this._config.trigger.split(" ").forEach(e=>{var t;"click"===e?m.on(this._element,this.constructor.Event.CLICK,this._config.selector,e=>this.toggle(e)):"manual"!==e&&(t=e===Yi?this.constructor.Event.MOUSEENTER:this.constructor.Event.FOCUSIN,e=e===Yi?this.constructor.Event.MOUSELEAVE:this.constructor.Event.FOCUSOUT,m.on(this._element,t,this._config.selector,e=>this._enter(e)),m.on(this._element,e,this._config.selector,e=>this._leave(e)))}),this._hideModalHandler=()=>{this._element&&this.hide()},m.on(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this._config.selector?this._config={...this._config,trigger:"manual",selector:""}:this._fixTitle()}_fixTitle(){var e=this._element.getAttribute("title"),t=typeof this._element.getAttribute("data-bs-original-title");!e&&"string"==t||(this._element.setAttribute("data-bs-original-title",e||""),!e||this._element.getAttribute("aria-label")||this._element.textContent||this._element.setAttribute("aria-label",e),this._element.setAttribute("title",""))}_enter(e,t){t=this._initializeOnDelegatedTarget(e,t),e&&(t._activeTrigger["focusin"===e.type?Qi:Yi]=!0),t.getTipElement().classList.contains(Ki)||t._hoverState===Xi?t._hoverState=Xi:(clearTimeout(t._timeout),t._hoverState=Xi,t._config.delay&&t._config.delay.show?t._timeout=setTimeout(()=>{t._hoverState===Xi&&t.show()},t._config.delay.show):t.show())}_leave(e,t){t=this._initializeOnDelegatedTarget(e,t),e&&(t._activeTrigger["focusout"===e.type?Qi:Yi]=t._element.contains(e.relatedTarget)),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t._config.delay&&t._config.delay.hide?t._timeout=setTimeout(()=>{"out"===t._hoverState&&t.hide()},t._config.delay.hide):t.hide())}_isWithActiveTrigger(){for(const e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1}_getConfig(e){const t=l.getDataAttributes(this._element);return Object.keys(t).forEach(e=>{qi.has(e)&&delete t[e]}),(e={...this.constructor.Default,...t,..."object"==typeof e&&e?e:{}}).container=!1===e.container?document.body:$(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),i(Ri,e,this.constructor.DefaultType),e.sanitize&&(e.template=Hi(e.template,e.allowList,e.sanitizeFn)),e}_getDelegateConfig(){var e={};if(this._config)for(const t in this._config)this.constructor.Default[t]!==this._config[t]&&(e[t]=this._config[t]);return e}_cleanTipClass(){const t=this.getTipElement();var e=t.getAttribute("class").match(Wi);null!==e&&0<e.length&&e.map(e=>e.trim()).forEach(e=>t.classList.remove(e))}_handlePopperPlacementChange(e){e=e.state;e&&(this.tip=e.elements.popper,this._cleanTipClass(),this._addAttachmentClass(this._getAttachment(e.placement)))}static jQueryInterface(t){return this.each(function(){var e=Gi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}e(Gi);O=".bs.popover";const Zi="bs-popover",Ji=new RegExp(`(^|\\s)${Zi}\\S+`,"g"),en={...Gi.Default,placement:"right",offset:[0,8],trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'},tn={...Gi.DefaultType,content:"(string|element|function)"},nn={HIDE:"hide"+O,HIDDEN:"hidden"+O,SHOW:"show"+O,SHOWN:"shown"+O,INSERTED:"inserted"+O,CLICK:"click"+O,FOCUSIN:"focusin"+O,FOCUSOUT:"focusout"+O,MOUSEENTER:"mouseenter"+O,MOUSELEAVE:"mouseleave"+O},sn=".popover-header",on=".popover-body";class rn extends Gi{static get Default(){return en}static get NAME(){return"popover"}static get Event(){return nn}static get DefaultType(){return tn}isWithContent(){return this.getTitle()||this._getContent()}getTipElement(){return this.tip||(this.tip=super.getTipElement(),this.getTitle()||d.findOne(sn,this.tip).remove(),this._getContent())||d.findOne(on,this.tip).remove(),this.tip}setContent(){var e=this.getTipElement();this.setElementContent(d.findOne(sn,e),this.getTitle());let t=this._getContent();"function"==typeof t&&(t=t.call(this._element)),this.setElementContent(d.findOne(on,e),t),e.classList.remove("fade","show")}_addAttachmentClass(e){this.getTipElement().classList.add(Zi+"-"+this.updateAttachment(e))}_getContent(){return this._element.getAttribute("data-bs-content")||this._config.content}_cleanTipClass(){const t=this.getTipElement();var e=t.getAttribute("class").match(Ji);null!==e&&0<e.length&&e.map(e=>e.trim()).forEach(e=>t.classList.remove(e))}static jQueryInterface(t){return this.each(function(){var e=rn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}e(rn);const an="scrollspy";const ln=".bs.scrollspy";const cn={offset:10,method:"auto",target:""},hn={offset:"number",method:"string",target:"(string|element)"};ln,ln;ln;const dn="dropdown-item",M="active",un=".nav-link",fn=".list-group-item",pn="position";class mn extends t{constructor(e,t){super(e),this._scrollElement="BODY"===this._element.tagName?window:this._element,this._config=this._getConfig(t),this._selector=`${this._config.target} ${un}, ${this._config.target} ${fn}, ${this._config.target} .`+dn,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,m.on(this._scrollElement,"scroll.bs.scrollspy",()=>this._process()),this.refresh(),this._process()}static get Default(){return cn}static get NAME(){return an}refresh(){var e=this._scrollElement===this._scrollElement.window?"offset":pn;const n="auto"===this._config.method?e:this._config.method,s=n===pn?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),d.find(this._selector).map(e=>{var e=W(e),t=e?d.findOne(e):null;if(t){var i=t.getBoundingClientRect();if(i.width||i.height)return[l[n](t).top+s,e]}return null}).filter(e=>e).sort((e,t)=>e[0]-t[0]).forEach(e=>{this._offsets.push(e[0]),this._targets.push(e[1])})}dispose(){m.off(this._scrollElement,ln),super.dispose()}_getConfig(t){if("string"!=typeof(t={...cn,...l.getDataAttributes(this._element),..."object"==typeof t&&t?t:{}}).target&&r(t.target)){let e=t.target["id"];e||(e=R(an),t.target.id=e),t.target="#"+e}return i(an,t,hn),t}_getScrollTop(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop}_getScrollHeight(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)}_getOffsetHeight(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height}_process(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),i=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),i<=t)e=this._targets[this._targets.length-1],this._activeTarget!==e&&this._activate(e);else if(this._activeTarget&&t<this._offsets[0]&&0<this._offsets[0])this._activeTarget=null,this._clear();else for(let e=this._offsets.length;e--;)this._activeTarget!==this._targets[e]&&t>=this._offsets[e]&&(void 0===this._offsets[e+1]||t<this._offsets[e+1])&&this._activate(this._targets[e])}_activate(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(e=>e+`[data-bs-target="${t}"],${e}[href="${t}"]`),e=d.findOne(e.join(","));e.classList.contains(dn)?(d.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(M),e.classList.add(M)):(e.classList.add(M),d.parents(e,".nav, .list-group").forEach(e=>{d.prev(e,un+", "+fn).forEach(e=>e.classList.add(M)),d.prev(e,".nav-item").forEach(e=>{d.children(e,un).forEach(e=>e.classList.add(M))})})),m.trigger(this._scrollElement,"activate.bs.scrollspy",{relatedTarget:t})}_clear(){d.find(this._selector).filter(e=>e.classList.contains(M)).forEach(e=>e.classList.remove(M))}static jQueryInterface(t){return this.each(function(){var e=mn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}m.on(window,"load.bs.scrollspy.data-api",()=>{d.find('[data-bs-spy="scroll"]').forEach(e=>new mn(e))}),e(mn);const gn="active",_n=".active",vn=":scope > li > .active";class bn extends t{static get NAME(){return"tab"}show(){if(!this._element.parentNode||this._element.parentNode.nodeType!==Node.ELEMENT_NODE||!this._element.classList.contains(gn)){let e;var t=s(this._element),i=this._element.closest(".nav, .list-group"),n=(i&&(n="UL"===i.nodeName||"OL"===i.nodeName?vn:_n,e=(e=d.find(n,i))[e.length-1]),e?m.trigger(e,"hide.bs.tab",{relatedTarget:this._element}):null);m.trigger(this._element,"show.bs.tab",{relatedTarget:e}).defaultPrevented||null!==n&&n.defaultPrevented||(this._activate(this._element,i),n=()=>{m.trigger(e,"hidden.bs.tab",{relatedTarget:this._element}),m.trigger(this._element,"shown.bs.tab",{relatedTarget:e})},t?this._activate(t,t.parentNode,n):n())}}_activate(e,t,i){const n=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?d.children(t,_n):d.find(vn,t))[0];var t=i&&n&&n.classList.contains("fade"),s=()=>this._transitionComplete(e,n,i);n&&t?(n.classList.remove("show"),this._queueCallback(s,e,!0)):s()}_transitionComplete(e,t,i){var n;t&&(t.classList.remove(gn),(n=d.findOne(":scope > .dropdown-menu .active",t.parentNode))&&n.classList.remove(gn),"tab"===t.getAttribute("role"))&&t.setAttribute("aria-selected",!1),e.classList.add(gn),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),K(e),e.classList.contains("fade")&&e.classList.add("show");let s=e.parentNode;(s=s&&"LI"===s.nodeName?s.parentNode:s)&&s.classList.contains("dropdown-menu")&&((n=e.closest(".dropdown"))&&d.find(".dropdown-toggle",n).forEach(e=>e.classList.add(gn)),e.setAttribute("aria-expanded",!0)),i&&i()}static jQueryInterface(t){return this.each(function(){var e=bn.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}})}}m.on(document,"click.bs.tab.data-api",'[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),F(this)||bn.getOrCreateInstance(this).show()}),e(bn);const yn="show",wn="showing",En={animation:"boolean",autohide:"boolean",delay:"number"},An={animation:!0,autohide:!0,delay:5e3};class Tn extends t{constructor(e,t){super(e),this._config=this._getConfig(t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get DefaultType(){return En}static get Default(){return An}static get NAME(){return"toast"}show(){m.trigger(this._element,"show.bs.toast").defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove("hide"),K(this._element),this._element.classList.add(wn),this._queueCallback(()=>{this._element.classList.remove(wn),this._element.classList.add(yn),m.trigger(this._element,"shown.bs.toast"),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this._element.classList.contains(yn)&&!m.trigger(this._element,"hide.bs.toast").defaultPrevented&&(this._element.classList.remove(yn),this._queueCallback(()=>{this._element.classList.add("hide"),m.trigger(this._element,"hidden.bs.toast")},this._element,this._config.animation))}dispose(){this._clearTimeout(),this._element.classList.contains(yn)&&this._element.classList.remove(yn),super.dispose()}_getConfig(e){return e={...An,...l.getDataAttributes(this._element),..."object"==typeof e&&e?e:{}},i("toast",e,this.constructor.DefaultType),e}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}t?this._clearTimeout():(e=e.relatedTarget,this._element===e||this._element.contains(e)||this._maybeScheduleHide())}_setListeners(){m.on(this._element,"click.dismiss.bs.toast",'[data-bs-dismiss="toast"]',()=>this.hide()),m.on(this._element,"mouseover.bs.toast",e=>this._onInteraction(e,!0)),m.on(this._element,"mouseout.bs.toast",e=>this._onInteraction(e,!1)),m.on(this._element,"focusin.bs.toast",e=>this._onInteraction(e,!0)),m.on(this._element,"focusout.bs.toast",e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){var e=Tn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}})}}return e(Tn),{Alert:ge,Button:ve,Carousel:p,Collapse:_,Dropdown:N,Modal:Ci,Offcanvas:Ni,Popover:rn,ScrollSpy:mn,Tab:bn,Toast:Tn,Tooltip:Gi}});
//# sourceMappingURL=bootstrap.min.js.map

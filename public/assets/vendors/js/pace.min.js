!function(){function _(t,e){for(var n in e)$.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype}function F(t,e){return function(){return t.apply(e,arguments)}}var u,U,W,X,s,n,m,D,H,y,I,z,a,t,G,v,w,e,r,c,i,o,B,l,p,J,b,k,h,f,K,S,g,d,q,L,x,P,T,R,j,O,Q,V,Y,Z,E,M=[].slice,$={}.hasOwnProperty,tt=[].indexOf||function(t){for(var e=0,n=this.length;e<n;e++)if(e in this&&this[e]===t)return e;return-1};function A(){}for(r={className:"",catchupTime:100,initialRate:.03,minTime:250,ghostTime:100,maxProgressPerFrame:20,easeFactor:1.25,startOnPageLoad:!0,restartOnPushState:!0,restartOnRequestAfter:500,target:"body",elements:{checkInterval:100,selectors:["body"]},eventLag:{minSamples:10,sampleCount:3,lagThreshold:3},ajax:{trackMethods:["GET"],trackWebSockets:!0,ignoreURLs:[]}},b=function(){var t;return null!=(t="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance.now():void 0)?t:+new Date},h=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,e=window.cancelAnimationFrame||window.mozCancelAnimationFrame,a=function(t,e,n){if("function"==typeof t.addEventListener)return t.addEventListener(e,n,!1);var r;"function"!=typeof t["on"+e]||"object"!=typeof t["on"+e].eventListeners?(r=new s,"function"==typeof t["on"+e]&&r.on(e,t["on"+e]),t["on"+e]=function(t){return r.trigger(e,t)},t["on"+e].eventListeners=r):r=t["on"+e].eventListeners,r.on(e,n)},null==h&&(h=function(t){return setTimeout(t,50)},e=function(t){return clearTimeout(t)}),K=function(e){var n=b(),r=function(){var t=b()-n;return 33<=t?(n=b(),e(t,function(){return h(r)})):setTimeout(r,33-t)};return r()},f=function(){var t=arguments[0],e=arguments[1],n=3<=arguments.length?M.call(arguments,2):[];return"function"==typeof t[e]?t[e].apply(t,n):t[e]},c=function(){for(var t,e,n,r=arguments[0],s=2<=arguments.length?M.call(arguments,1):[],o=0,i=s.length;o<i;o++)if(e=s[o])for(t in e)$.call(e,t)&&(n=e[t],null!=r[t]&&"object"==typeof r[t]&&null!=n&&"object"==typeof n?c(r[t],n):r[t]=n);return r},G=function(t){for(var e,n,r=e=0,s=0,o=t.length;s<o;s++)n=t[s],r+=Math.abs(n),e++;return r/e},o=function(t,e){var n;if(null==t&&(t="options"),null==e&&(e=!0),n=document.querySelector("[data-pace-"+t+"]")){if(n=n.getAttribute("data-pace-"+t),!e)return n;try{return JSON.parse(n)}catch(t){return"undefined"!=typeof console&&null!==console?console.error("Error parsing inline pace options",t):void 0}}},A.prototype.on=function(t,e,n,r){var s;return null==r&&(r=!1),null==this.bindings&&(this.bindings={}),null==(s=this.bindings)[t]&&(s[t]=[]),this.bindings[t].push({handler:e,ctx:n,once:r})},A.prototype.once=function(t,e,n){return this.on(t,e,n,!0)},A.prototype.off=function(t,e){var n,r,s;if(null!=(null!=(r=this.bindings)?r[t]:void 0)){if(null==e)return delete this.bindings[t];for(n=0,s=[];n<this.bindings[t].length;)this.bindings[t][n].handler===e?s.push(this.bindings[t].splice(n,1)):s.push(n++);return s}},A.prototype.trigger=function(){var t,e,n,r,s,o,i=arguments[0],a=2<=arguments.length?M.call(arguments,1):[];if(null!=(r=this.bindings)&&r[i]){for(n=0,o=[];n<this.bindings[i].length;)e=(s=this.bindings[i][n]).handler,t=s.ctx,s=s.once,e.apply(null!=t?t:this,a),o.push(s?this.bindings[i].splice(n,1):n++);return o}},E=A,m=window.Pace||{},window.Pace=m,c(m,E.prototype),k=m.options=c({},r,window.paceOptions,o()),j=0,Q=(Y=["ajax","document","eventLag","elements"]).length;j<Q;j++)!0===k[q=Y[j]]&&(k[q]=r[q]);function C(){return C.__super__.constructor.apply(this,arguments)}function N(){this.progress=0}function et(){this.bindings={}}function nt(){var e,o=this;nt.__super__.constructor.apply(this,arguments),e=function(r){var s=r.open;return r.open=function(t,e,n){return d(t)&&o.trigger("request",{type:t,url:e,request:r}),s.apply(r,arguments)}},window.XMLHttpRequest=function(t){t=new R(t);return e(t),t};try{i(window.XMLHttpRequest,R)}catch(t){}if(null!=T){window.XDomainRequest=function(){var t=new T;return e(t),t};try{i(window.XDomainRequest,T)}catch(t){}}if(null!=P&&k.ajax.trackWebSockets){window.WebSocket=function(t,e){var n=null!=e?new P(t,e):new P(t);return d("socket")&&o.trigger("request",{type:"socket",url:t,protocols:e,request:n}),n};try{i(window.WebSocket,P)}catch(t){}}}function rt(){this.complete=F(this.complete,this);var t=this;this.elements=[],B().on("request",function(){return t.watch.apply(t,arguments)})}function st(t){var e,n,r,s;for(null==t&&(t={}),this.complete=F(this.complete,this),this.elements=[],null==t.selectors&&(t.selectors=[]),n=0,r=(s=t.selectors).length;n<r;n++)e=s[n],this.elements.push(new W(e,this.complete))}function ot(t,e){this.selector=t,this.completeCallback=e,this.progress=0,this.check()}function it(){var t,e,n=this;this.progress=null!=(e=this.states[document.readyState])?e:100,t=document.onreadystatechange,document.onreadystatechange=function(){return null!=n.states[document.readyState]&&(n.progress=n.states[document.readyState]),"function"==typeof t?t.apply(null,arguments):void 0}}function at(t){this.source=t,this.last=this.sinceLastUpdate=0,this.rate=k.initialRate,this.catchup=0,this.progress=this.lastProgress=0,null!=this.source&&(this.progress=f(this.source,"progress"))}E=Error,_(C,E),n=C,N.prototype.getElement=function(){var t;if(null==this.el){if(!(t=document.querySelector(k.target)))throw new n;this.el=document.createElement("div"),this.el.className="pace pace-active",document.body.className=document.body.className.replace(/(pace-done )|/,"pace-running ");var e=""!==k.className?" "+k.className:"";this.el.innerHTML='<div class="pace-progress'+e+'">\n  <div class="pace-progress-inner"></div>\n</div>\n<div class="pace-activity"></div>',null!=t.firstChild?t.insertBefore(this.el,t.firstChild):t.appendChild(this.el)}return this.el},N.prototype.finish=function(){var t=this.getElement();return t.className=t.className.replace("pace-active","pace-inactive"),document.body.className=document.body.className.replace("pace-running ","pace-done ")},N.prototype.update=function(t){return this.progress=t,m.trigger("progress",t),this.render()},N.prototype.destroy=function(){try{this.getElement().parentNode.removeChild(this.getElement())}catch(t){n=t}return this.el=void 0},N.prototype.render=function(){var t,e,n,r,s,o;if(null==document.querySelector(k.target))return!1;for(t=this.getElement(),n="translate3d("+this.progress+"%, 0, 0)",r=0,s=(o=["webkitTransform","msTransform","transform"]).length;r<s;r++)t.children[0].style[o[r]]=n;return(!this.lastRenderedProgress||this.lastRenderedProgress|0!==this.progress|0)&&(t.children[0].setAttribute("data-progress-text",(0|this.progress)+"%"),100<=this.progress?e="99":(e=this.progress<10?"0":"",e+=0|this.progress),t.children[0].setAttribute("data-progress",""+e)),m.trigger("change",this.progress),this.lastRenderedProgress=this.progress},N.prototype.done=function(){return 100<=this.progress},U=N,et.prototype.trigger=function(t,e){var n,r,s,o,i;if(null!=this.bindings[t]){for(i=[],r=0,s=(o=this.bindings[t]).length;r<s;r++)n=o[r],i.push(n.call(this,e));return i}},et.prototype.on=function(t,e){var n;return null==(n=this.bindings)[t]&&(n[t]=[]),this.bindings[t].push(e)},s=et,R=window.XMLHttpRequest,T=window.XDomainRequest,P=window.WebSocket,i=function(t,e){var n,r=[];for(n in e.prototype)try{null==t[n]&&"function"!=typeof e[n]?"function"==typeof Object.defineProperty?r.push(Object.defineProperty(t,n,{get:function(t){return function(){return e.prototype[t]}}(n),configurable:!0,enumerable:!0})):r.push(t[n]=e.prototype[n]):r.push(void 0)}catch(t){0}return r},p=[],m.ignore=function(){var t=arguments[0],e=2<=arguments.length?M.call(arguments,1):[];return p.unshift("ignore"),t=t.apply(null,e),p.shift(),t},m.track=function(){var t=arguments[0],e=2<=arguments.length?M.call(arguments,1):[];return p.unshift("track"),t=t.apply(null,e),p.shift(),t},d=function(t){if(null==t&&(t="GET"),"track"===p[0])return"force";if(!p.length&&k.ajax){if("socket"===t&&k.ajax.trackWebSockets)return!0;if(t=t.toUpperCase(),0<=tt.call(k.ajax.trackMethods,t))return!0}return!1},_(nt,s),D=nt,O=null,g=function(t){for(var e,n=k.ajax.ignoreURLs,r=0,s=n.length;r<s;r++)if("string"==typeof(e=n[r])){if(-1!==t.indexOf(e))return!0}else if(e.test(t))return!0;return!1},(B=function(){return O=null==O?new D:O})().on("request",function(t){var o,i=t.type,a=t.request,e=t.url;return g(e)||m.running||!1===k.restartOnRequestAfter&&"force"!==d(i)?void 0:(o=arguments,"boolean"==typeof(e=k.restartOnRequestAfter||0)&&(e=0),setTimeout(function(){var t,e,n,r,s="socket"===i?a.readyState<1:0<(s=a.readyState)&&s<4;if(s){for(m.restart(),r=[],t=0,e=(n=m.sources).length;t<e;t++){if((q=n[t])instanceof u){q.watch.apply(q,o);break}r.push(void 0)}return r}},e))}),rt.prototype.watch=function(t){var e=t.type,n=t.request,t=t.url;if(!g(t))return t=new("socket"===e?I:z)(n,this.complete),this.elements.push(t)},rt.prototype.complete=function(e){return this.elements=this.elements.filter(function(t){return t!==e})},u=rt,z=function(e,n){var t,r,s,o,i=this;if(this.progress=0,null!=window.ProgressEvent)for(a(e,"progress",function(t){return t.lengthComputable?i.progress=100*t.loaded/t.total:i.progress=i.progress+(100-i.progress)/2}),t=0,r=(o=["load","abort","timeout","error"]).length;t<r;t++)a(e,o[t],function(){return n(i),i.progress=100});else s=e.onreadystatechange,e.onreadystatechange=function(){var t;return 0===(t=e.readyState)||4===t?(n(i),i.progress=100):3===e.readyState&&(i.progress=50),"function"==typeof s?s.apply(null,arguments):void 0}},I=function(t,e){for(var n,r=this,s=this.progress=0,o=(n=["error","open"]).length;s<o;s++)a(t,n[s],function(){return e(r),r.progress=100})},st.prototype.complete=function(e){return this.elements=this.elements.filter(function(t){return t!==e})},o=st,ot.prototype.check=function(){var t=this;return document.querySelector(this.selector)?this.done():setTimeout(function(){return t.check()},k.elements.checkInterval)},ot.prototype.done=function(){return this.completeCallback(this),this.completeCallback=null,this.progress=100},W=ot,it.prototype.states={loading:0,interactive:50,complete:100},E=it,X=function(){var e,n,r,s,o,i=this;this.progress=0,o=[],s=0,r=b(),n=setInterval(function(){var t=b()-r-50;return r=b(),o.push(t),o.length>k.eventLag.sampleCount&&o.shift(),e=G(o),++s>=k.eventLag.minSamples&&e<k.eventLag.lagThreshold?(i.progress=100,clearInterval(n)):i.progress=3/(e+3)*100},50)},at.prototype.tick=function(t,e){return 100<=(e=null==e?f(this.source,"progress"):e)&&(this.done=!0),e===this.last?this.sinceLastUpdate+=t:(this.sinceLastUpdate&&(this.rate=(e-this.last)/this.sinceLastUpdate),this.catchup=(e-this.progress)/k.catchupTime,this.sinceLastUpdate=0,this.last=e),e>this.progress&&(this.progress+=this.catchup*t),e=1-Math.pow(this.progress/100,k.easeFactor),this.progress+=e*this.rate*t,this.progress=Math.min(this.lastProgress+k.maxProgressPerFrame,this.progress),this.progress=Math.max(0,this.progress),this.progress=Math.min(100,this.progress),this.lastProgress=this.progress,this.progress},y=at,w=t=x=v=S=L=null,m.running=!1,l=function(){if(k.restartOnPushState)return m.restart()},null!=window.history.pushState&&(V=window.history.pushState,window.history.pushState=function(){return l(),V.apply(window.history,arguments)}),null!=window.history.replaceState&&(Z=window.history.replaceState,window.history.replaceState=function(){return l(),Z.apply(window.history,arguments)}),H={ajax:u,elements:o,document:E,eventLag:X},(J=function(){var t,e,n,r,s,o,i,a;for(m.sources=L=[],e=0,r=(o=["ajax","elements","document","eventLag"]).length;e<r;e++)!1!==k[t=o[e]]&&L.push(new H[t](k[t]));for(n=0,s=(a=null!=(i=k.extraSources)?i:[]).length;n<s;n++)q=a[n],L.push(new q(k));return m.bar=v=new U,S=[],x=new y})(),m.stop=function(){return m.trigger("stop"),m.running=!1,v.destroy(),w=!0,null!=t&&("function"==typeof e&&e(t),t=null),J()},m.restart=function(){return m.trigger("restart"),m.stop(),m.start()},m.go=function(){var d;return m.running=!0,v.render(),d=b(),w=!1,t=K(function(t,e){v.progress;for(var n,r,s,o,i,a,u,c,l,p=i=0,h=!0,f=a=0,g=L.length;a<g;f=++a)for(q=L[f],o=null!=S[f]?S[f]:S[f]=[],r=u=0,c=(n=null!=(l=q.elements)?l:[q]).length;u<c;r=++u)s=n[r],h&=(s=null!=o[r]?o[r]:o[r]=new y(s)).done,s.done||(p++,i+=s.tick(t));return v.update(x.tick(t,i/p)),v.done()||h||w?(v.update(100),m.trigger("done"),setTimeout(function(){return v.finish(),m.running=!1,m.trigger("hide")},Math.max(k.ghostTime,Math.max(k.minTime-(b()-d),0)))):e()})},m.start=function(t){c(k,t),m.running=!0;try{v.render()}catch(t){n=t}return document.querySelector(".pace")?(m.trigger("start"),m.go()):setTimeout(m.start,50)},"function"==typeof define&&define.amd?define(function(){return m}):"object"==typeof exports?module.exports=m:k.startOnPageLoad&&m.start()}.call(this);
//# sourceMappingURL=pace.min.js.map

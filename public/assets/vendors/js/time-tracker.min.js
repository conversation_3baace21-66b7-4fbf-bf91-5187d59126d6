!function(e){var p,s={format:"H:i:s",countdown:!1,passed:0,passedStop:-1,timestart:0,timestop:0,delayInterval:1e3,stop:null},a=null,o=null,n={init:function(t){o=this,(p=e.extend({},s,t)).timestop&&(p.passedStop=1e3*p.timestop),p.countdown&&0===p.passedStop&&(p.countdown=!1),0!==p.passed&&(p.passed=1e3*p.passed),n.display.apply(),n.stop.apply()},start:function(){p.timestart=Date.now(),a=setTimeout(n.make,p.delayInterval)},stop:function(){a&&clearTimeout(a)},make:function(){var t=Date.now();p.passed+=t-p.timestart,p.timestart=t,n.display.apply(),p.passed<=p.passedStop||p.passedStop<0?a=setTimeout(n.make,p.delayInterval):(p.passed=p.passedStop,p.stop&&p.stop.apply())},display:function(){var t,e=p.format,s=p.passed,a=Math.floor;p.countdown&&(s=p.passedStop-s,a=Math.ceil),a=a(s/1e3)%60,t=(t=Math.floor(s/6e4)%60)<10?"0"+t:t,a=a<10?"0"+a:a,s=(s=Math.floor(s/36e5))<10?"0"+s:s,e=e.replace("H",s).replace("i",t).replace("s",a),o.text(e)},reset:function(){p.passed=0,p.timestop&&(p.passedStop=1e3*p.timestop),p.countdown&&0===p.passedStop&&(p.countdown=!1),n.display.apply()}};e.fn.timetracker=function(t){return n[t]?n[t].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof t&&t?void e.error(t+" don't exists"):n.init.apply(this,arguments)}}(jQuery);
//# sourceMappingURL=time-tracker.min.js.map

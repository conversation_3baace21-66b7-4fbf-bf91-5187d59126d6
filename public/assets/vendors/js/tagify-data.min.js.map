{"version": 3, "file": "tagify-data.min.js", "sources": ["tagify-data.min.js"], "sourcesContent": ["\"use strict\";\r\n\r\nvar tomailmodal = document.querySelector(\"input[name=tomailmodal]\");\r\nvar ccmailmodal = document.querySelector(\"input[name=ccmailmodal]\");\r\nvar bccmailmodal = document.querySelector(\"input[name=bccmailmodal]\");\r\nvar tomailcontent = document.querySelector(\"input[name=tomailcontent]\");\r\nvar ccmailcontent = document.querySelector(\"input[name=ccmailcontent]\");\r\nvar bccmailcontent = document.querySelector(\"input[name=bccmailcontent]\");\r\n\r\nvar userlist = [\r\n\t{\r\n\t\tvalue: 1,\r\n\t\tname: \"theme_ocean\",\r\n\t\tavatar: \"https://en.gravatar.com/userimage/217650896/f011e8341437035d5063e32a126cb70d.png\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 2,\r\n\t\tname: \"Suraiya Parvin Swampa\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=1\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 3,\r\n\t\tname: \"<PERSON><PERSON><PERSON>\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=2\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 4,\r\n\t\tname: \"Ardeen Batisse\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=3\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 5,\r\n\t\tname: \"Graeme Yellowley\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=4\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 6,\r\n\t\tname: \"Dido Wilford\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=5\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 7,\r\n\t\tname: \"Celesta Orwin\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=6\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 8,\r\n\t\tname: \"Sally Main\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=7\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 9,\r\n\t\tname: \"Grethel Haysman\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=8\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 10,\r\n\t\tname: \"Marvin Mandrake\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=9\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 11,\r\n\t\tname: \"Corrie Tidey\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=10\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 12,\r\n\t\tname: \"Antons Esson\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=12\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 13,\r\n\t\tname: \"Archie Cantones\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=13\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 14,\r\n\t\tname: \"Holmes Cherryman\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=14\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 15,\r\n\t\tname: \"Malanie Hanvey\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=15\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 16,\r\n\t\tname: \"Kenneth Hune\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=16\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 17,\r\n\t\tname: \"Antons Esson\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=17\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 18,\r\n\t\tname: \"Jesse Ross\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=18\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 19,\r\n\t\tname: \"Madsen Daniel\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=19\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n\t{\r\n\t\tvalue: 20,\r\n\t\tname: \"Valentine Maton\",\r\n\t\tavatar: \"https://i.pravatar.cc/80?img=20\",\r\n\t\temail: \"<EMAIL>\",\r\n\t},\r\n];\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [tomailmodal] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(tomailmodal, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [ccmailmodal] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(ccmailmodal, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [bccmailmodal] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(bccmailmodal, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [tomailcontent] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(tomailcontent, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [ccmailcontent] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(ccmailcontent, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n\r\n/*\r\n<--!----------------------------------------------------------------!-->\r\n<--! Tagify [bccmailcontent] !-->\r\n<--!----------------------------------------------------------------!-->\r\n*/\r\n$(document).ready(function () {\r\n\tfunction tagTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<tag title=\"${tagData.email}\"\r\n\t\t\t\t\tcontenteditable='false'\r\n\t\t\t\t\tspellcheck='false'\r\n\t\t\t\t\ttabIndex=\"-1\"\r\n\t\t\t\t\tclass=\"tagify__tag ${tagData.class ? tagData.class : \"\"}\"\r\n\t\t\t\t\t${this.getAttributes(tagData)}>\r\n\t\t\t\t<x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>\r\n\t\t\t\t<div>\r\n\t\t\t\t\t<div class='tagify__tag__avatar-wrap'>\r\n\t\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<span class='tagify__tag-text'>${tagData.name}</span>\r\n\t\t\t\t</div>\r\n\t\t\t</tag>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction suggestionItemTemplate(tagData) {\r\n\t\treturn `\r\n\t\t\t<div ${this.getAttributes(tagData)}\r\n\t\t\t\tclass='tagify__dropdown__item ${tagData.class ? tagData.class : \"\"}'\r\n\t\t\t\ttabindex=\"0\"\r\n\t\t\t\trole=\"option\">\r\n\t\t\t\t${\r\n\t\t\t\t\ttagData.avatar\r\n\t\t\t\t\t\t? `\r\n\t\t\t\t<div class='tagify__dropdown__item__avatar-wrap'>\r\n\t\t\t\t\t<img onerror=\"this.style.visibility='hidden'\" src=\"${tagData.avatar}\">\r\n\t\t\t\t</div>`\r\n\t\t\t\t\t\t: \"\"\r\n\t\t\t\t}\r\n\t\t\t\t<strong>${tagData.name}</strong>\r\n\t\t\t\t<span class=\"text-gray-500\">${tagData.email}</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tfunction dropdownHeaderTemplate(suggestions) {\r\n\t\treturn `\r\n\t\t\t<div class=\"${this.settings.classNames.dropdownItem} ${this.settings.classNames.dropdownItem}__addAll\">\r\n\t\t\t\t<strong>${this.value.length ? `Add Remaining ${suggestions.length}` : \"Add All\"}</strong>\r\n\t\t\t\t<span>${suggestions.length} members</span>\r\n\t\t\t</div>\r\n\t\t`;\r\n\t}\r\n\r\n\tvar tagify = new Tagify(bccmailcontent, {\r\n\t\ttagTextProp: \"name\", // very important since a custom template is used with this property as text\r\n\t\tenforceWhitelist: true,\r\n\t\tskipInvalid: true, // do not remporarily add invalid tags\r\n\t\tdropdown: {\r\n\t\t\tcloseOnSelect: false,\r\n\t\t\tenabled: 0,\r\n\t\t\tclassname: \"users-list\",\r\n\t\t\tsearchKeys: [\"name\", \"email\"], // very important to set by which keys to search for suggesttions when typing\r\n\t\t},\r\n\t\ttemplates: {\r\n\t\t\ttag: tagTemplate,\r\n\t\t\tdropdownItem: suggestionItemTemplate,\r\n\t\t\tdropdownHeader: dropdownHeaderTemplate,\r\n\t\t},\r\n\t\twhitelist: userlist,\r\n\t});\r\n\r\n\t// listen to dropdown suggestion items selection\r\n\ttagify.on(\"dropdown:select\", onSelectSuggestion);\r\n\r\n\tfunction onSelectSuggestion(e) {\r\n\t\tconsole.log(this);\r\n\t\tconsole.log(e.detail.elm.className, e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`));\r\n\r\n\t\tif (e.detail.elm.classList.contains(`${tagify.settings.classNames.dropdownItem}__addAll`)) tagify.dropdown.selectAll();\r\n\t}\r\n});\r\n"], "names": ["tomailmodal", "document", "querySelector", "ccmailmodal", "bccmailmodal", "tomailcontent", "ccmailcontent", "bccmailcontent", "userlist", "value", "name", "avatar", "email", "$", "ready", "tagify", "Tagify", "tagTextProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "skipInvalid", "dropdown", "closeOnSelect", "enabled", "classname", "searchKeys", "templates", "tag", "tagData", "class", "this", "getAttributes", "dropdownItem", "dropdownHeader", "suggestions", "settings", "classNames", "length", "whitelist", "on", "e", "console", "log", "detail", "elm", "className", "classList", "contains", "selectAll"], "mappings": "AAAA,aAEA,IAAIA,YAAcC,SAASC,cAAc,yBAAyB,EAC9DC,YAAcF,SAASC,cAAc,yBAAyB,EAC9DE,aAAeH,SAASC,cAAc,0BAA0B,EAChEG,cAAgBJ,SAASC,cAAc,2BAA2B,EAClEI,cAAgBL,SAASC,cAAc,2BAA2B,EAClEK,eAAiBN,SAASC,cAAc,4BAA4B,EAEpEM,SAAW,CACd,CACCC,MAAO,EACPC,KAAM,aACNC,OAAQ,mFACRC,MAAO,yBACR,EACA,CACCH,MAAO,EACPC,KAAM,wBACNC,OAAQ,iCACRC,MAAO,4BACR,EACA,CACCH,MAAO,EACPC,KAAM,sBACNC,OAAQ,iCACRC,MAAO,wBACR,EACA,CACCH,MAAO,EACPC,KAAM,iBACNC,OAAQ,iCACRC,MAAO,mBACR,EACA,CACCH,MAAO,EACPC,KAAM,mBACNC,OAAQ,iCACRC,MAAO,yBACR,EACA,CACCH,MAAO,EACPC,KAAM,eACNC,OAAQ,iCACRC,MAAO,oBACR,EACA,CACCH,MAAO,EACPC,KAAM,gBACNC,OAAQ,iCACRC,MAAO,oBACR,EACA,CACCH,MAAO,EACPC,KAAM,aACNC,OAAQ,iCACRC,MAAO,uBACR,EACA,CACCH,MAAO,EACPC,KAAM,kBACNC,OAAQ,iCACRC,MAAO,wBACR,EACA,CACCH,MAAO,GACPC,KAAM,kBACNC,OAAQ,iCACRC,MAAO,4BACR,EACA,CACCH,MAAO,GACPC,KAAM,eACNC,OAAQ,kCACRC,MAAO,qBACR,EACA,CACCH,MAAO,GACPC,KAAM,eACNC,OAAQ,kCACRC,MAAO,qBACR,EACA,CACCH,MAAO,GACPC,KAAM,kBACNC,OAAQ,kCACRC,MAAO,2BACR,EACA,CACCH,MAAO,GACPC,KAAM,mBACNC,OAAQ,kCACRC,MAAO,4BACR,EACA,CACCH,MAAO,GACPC,KAAM,iBACNC,OAAQ,kCACRC,MAAO,yBACR,EACA,CACCH,MAAO,GACPC,KAAM,eACNC,OAAQ,kCACRC,MAAO,2BACR,EACA,CACCH,MAAO,GACPC,KAAM,eACNC,OAAQ,kCACRC,MAAO,mBACR,EACA,CACCH,MAAO,GACPC,KAAM,aACNC,OAAQ,kCACRC,MAAO,sBACR,EACA,CACCH,MAAO,GACPC,KAAM,gBACNC,OAAQ,kCACRC,MAAO,uBACR,EACA,CACCH,MAAO,GACPC,KAAM,kBACNC,OAAQ,kCACRC,MAAO,2BACR,GAQDC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOhB,YAAa,CACpCiB,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC,EAODlC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOb,YAAa,CACpCc,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC,EAODlC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOZ,aAAc,CACrCa,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC,EAODlC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOX,cAAe,CACtCY,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC,EAODlC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOV,cAAe,CACtCW,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC,EAODlC,EAAEZ,QAAQ,EAAEa,MAAM,WAiDjB,IAAIC,EAAS,IAAIC,OAAOT,eAAgB,CACvCU,YAAa,OACbC,iBAAkB,CAAA,EAClBC,YAAa,CAAA,EACbC,SAAU,CACTC,cAAe,CAAA,EACfC,QAAS,EACTC,UAAW,aACXC,WAAY,CAAC,OAAQ,QACtB,EACAC,UAAW,CACVC,IA3DF,SAAqBC,GACpB;iBACeA,EAAQf;;;;0BAICe,EAAQC,OAAwB;OACnDC,KAAKC,cAAcH,CAAO;;;;2DAI0BA,EAAQhB;;sCAE7BgB,EAAQjB;;;GAI7C,EA2CEqB,aAzCF,SAAgCJ,GAC/B;UACQE,KAAKC,cAAcH,CAAO;oCACAA,EAAQC,OAAwB;;;MAI/DD,EAAQhB;;0DAG6CgB,EAAQhB;YAE1D;cAEMgB,EAAQjB;kCACYiB,EAAQf;;GAGzC,EAwBEoB,eAtBF,SAAgCC,GAC/B;iBACeJ,KAAKK,SAASC,WAAWJ,gBAAgBF,KAAKK,SAASC,WAAWJ;cACrEF,KAAKpB,MAAM2B,OAAS,iBAAiBH,EAAYG,OAAW;YAC9DH,EAAYG;;GAGvB,CAgBC,EACAC,UAAW7B,QACZ,CAAC,EAGDO,EAAOuB,GAAG,kBAEV,SAA4BC,GAC3BC,QAAQC,IAAIZ,IAAI,EAChBW,QAAQC,IAAIF,EAAEG,OAAOC,IAAIC,UAAWL,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,CAAC,EAErHQ,EAAEG,OAAOC,IAAIE,UAAUC,SAAY/B,EAAOmB,SAASC,WAAWJ,aAA9B,UAAoD,GAAGhB,EAAOK,SAAS2B,UAAU,CACtH,CAP+C,CAQhD,CAAC"}
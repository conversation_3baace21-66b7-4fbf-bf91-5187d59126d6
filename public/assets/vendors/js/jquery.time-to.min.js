!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t(require("jquery")):t(jQuery)}(function(v){var t,p=86400,m=3600,T={callback:null,step:null,stepCount:1,captionSize:0,countdown:!0,countdownAlertLimit:10,displayCaptions:!1,displayDays:0,displayHours:!0,fontFamily:"Inter, sans-serif",fontSize:0,lang:"en",languages:{},seconds:0,start:!0,theme:"white",width:25,height:30,gap:11,vals:[0,0,0,0,0,0,0,0,0],limits:[9,9,9,2,9,5,9,5,9],iSec:8,iHour:4,tickTimeout:1e3,intervalId:null,tickCount:0},I={start:function(t){t&&(S.call(this,t),t=setTimeout(f.bind(this),1e3),this.data("ttStartTime",v.now()),this.data("intervalId",t))},stop:function(){var t=this.data();return t.intervalId&&(clearTimeout(t.intervalId),this.data("intervalId",null)),t},reset:function(t){var i=I.stop.call(this),i=void 0===t?i.seconds:t;this.find("div").css({backgroundPosition:"left center"}),this.find("ul").parent().removeClass("timeTo-alert"),S.call(this,i,!0)}},w={en:{days:"days",hours:"hours",min:"minutes",sec:"seconds"},ru:{days:"дней",hours:"часов",min:"минут",sec:"секунд"},ua:{days:"днiв",hours:"годин",min:"хвилин",sec:"секунд"},de:{days:"Tag",hours:"Uhr",min:"Minuten",sec:"Secunden"},fr:{days:"jours",hours:"heures",min:"minutes",sec:"secondes"},es:{days:"días",hours:"horas",min:"minutos",sec:"segundos"},sp:{days:"días",hours:"horas",min:"minutos",sec:"segundos"},it:{days:"giorni",hours:"ore",min:"minuti",sec:"secondi"},nl:{days:"dagen",hours:"uren",min:"minuten",sec:"seconden"},no:{days:"dager",hours:"timer",min:"minutter",sec:"sekunder"},pt:{days:"dias",hours:"horas",min:"minutos",sec:"segundos"},tr:{days:"gün",hours:"saat",min:"dakika",sec:"saniye"},pl:{days:"dni",hours:"godziny",min:"minuty",sec:"secundy"}};function S(t,i){var e,s,a,n,o,l,d,r,c=this.data(),u=this.find("ul"),h=!1;if(c.vals&&0!==u.length){for(t=t||c.seconds,c.intervalId&&(h=!0,clearTimeout(c.intervalId)),e=(n=Math.floor(t/p))*p,e+=(s=Math.floor((t-e)/m))*m,a=Math.floor((t-e)/60),l=c.vals.length-1,d=(o=(n<100?"0"+(n<10?"0":""):"")+n+(s<10?"0":"")+s+(a<10?"0":"")+a+((n=t-(e+=60*a))<10?"0":"")+n).length-1;0<=l;--l,--d)r=parseInt(o.substr(d,1),10),c.vals[l]=r,u.eq(l).children().html(r);(h||i)&&(c.ttStartTime=Date.now(),c.intervalId=setTimeout(f.bind(this),1e3),this.data("intervalId",c.intervalId))}}function f(t){var i,e,s,a,n,o,l=this,d=this.find("ul"),r=this.data();r.vals&&0!==d.length?(void 0===t&&(t=r.iSec),this.data("tickCount",r.tickCount+1),i=r.vals[t],e=d.eq(t),s=e.children(),a=r.countdown?-1:1,s.eq(1).html(i),i+=a,"function"==typeof r.step&&r.tickCount%r.stepCount==0&&(this.data("tickCount",0),r.step()),t===r.iSec&&(n=r.tickTimeout,o=Date.now()-r.ttStartTime,r.sec+=a,n+=Math.abs(r.seconds-r.sec)*n-o,r.intervalId=setTimeout(f.bind(this),n)),i<0||i>r.limits[t]?(i<0?(i=r.limits[t],t===r.iHour&&0<r.displayDays&&0===r.vals[t-1]&&(i=3)):i=0,0<t&&f.call(this,t-1)):!r.countdown&&t===r.iHour&&0<r.displayDays&&2===r.vals[t-1]&&3===r.vals[t]&&(i=0,f.call(this,t-1)),s.eq(0).html(i),v.support.transition?(e.addClass("transition"),e.css({top:0}),setTimeout(function(){e.removeClass("transition"),s.eq(1).html(i),e.css({top:"-"+r.height+"px"}),0<a||t!==r.iSec||(r.sec===r.countdownAlertLimit&&d.parent().addClass("timeTo-alert"),0===r.sec&&(d.parent().removeClass("timeTo-alert"),r.intervalId&&(clearTimeout(r.intervalId),l.data("intervalId",null)),"function"==typeof r.callback)&&r.callback())},410)):e.stop().animate({top:0},400,t!==r.iSec?null:function(){s.eq(1).html(i),e.css({top:"-"+r.height+"px"}),0<a||t!==r.iSec||(r.sec===r.countdownAlertLimit?d.parent().addClass("timeTo-alert"):0===r.sec&&(d.parent().removeClass("timeTo-alert"),r.intervalId&&(clearTimeout(r.intervalId),l.data("intervalId",null)),"function"==typeof r.callback)&&r.callback())}),r.vals[t]=i):(r.intervalId&&(clearTimeout(r.intervalId),this.data("intervalId",null)),r.callback&&r.callback())}return void 0===v.support.transition&&(v.support.transition=void 0!==(t=(document.body||document.documentElement).style).transition||void 0!==t.WebkitTransition||void 0!==t.MozTransition||void 0!==t.MsTransition||void 0!==t.OTransition),v.fn.timeTo=function(){for(var t,y,i,e,s,a,g={},n=Date.now(),o=0;o<arguments.length;o+=1)t=arguments[o],0===o&&"string"==typeof t?y=t:"object"==typeof t?"function"==typeof t.getTime?g.timeTo=t:g=v.extend(g,t):"function"==typeof t?g.callback=t:(t=parseInt(t,10),isNaN(t)||(g.seconds=t));if(g.timeTo)g.timeTo.getTime?i=g.timeTo.getTime():"number"==typeof g.timeTo&&(i=g.timeTo),g.seconds=n<i?Math.floor((i-n)/1e3):0;else if(g.time||!g.seconds)if("object"==typeof(i=(i=g.time)||new Date(n))&&i.getTime)g.seconds=i.getDate()*p+i.getHours()*m+60*i.getMinutes()+i.getSeconds(),g.countdown=!1;else if("string"==typeof i){for(e=i.split(":"),s=0,a=1;e.length;)s+=e.pop()*a,a*=60;g.seconds=s,g.countdown=!1}return!1!==g.countdown&&g.seconds>p&&void 0===g.displayDays?(n=Math.floor(g.seconds/p),g.displayDays=(n<10?1:n<100&&2)||3):!0===g.displayDays?g.displayDays=3:g.displayDays&&(g.displayDays=0<g.displayDays?Math.floor(g.displayDays):3),this.each(function(){var t,i,e,s,a,n,o,l,d,r,c,u,h,p,m=v(this),f=m.data();if(f.intervalId&&(clearInterval(f.intervalId),f.intervalId=null),f.vals)"reset"!==y&&v.extend(f,g);else{if(i=f.opt?f.options:g,l=Object.keys(T).reduce(function(t,i){return Array.isArray(T[i])?t[i]=T[i].slice(0):t[i]=T[i],t},{}),(f=v.extend(l,i)).options=i,f.height=Math.round(100*f.fontSize/93)||f.height,f.width=Math.round(.8*f.fontSize+.13*f.height)||f.width,f.displayHours=!(!f.displayDays&&!f.displayHours),l={fontFamily:f.fontFamily},0<f.fontSize&&(l.fontSize=f.fontSize+"px"),i=f.languages[f.lang]||w[f.lang],m.addClass("timeTo").addClass("timeTo-"+f.theme).css(l),l='<ul style="left:'+Math.round(f.height/10)+"px; top:-"+f.height+'px"><li>0</li><li>0</li></ul></div>',e='<div class="first"'+(a=f.fontSize?' style="width:'+f.width+"px; height:"+f.height+'px;"':' style=""')+">"+l,s="<div"+a+">"+l,a="<span>:</span>",l=Math.round(2*f.width+3),n=(o=f.captionSize||f.fontSize&&Math.round(.43*f.fontSize))?"font-size:"+o+"px;":"",o=o?' style="'+n+'"':"",l=(f.displayCaptions?(f.displayHours?'<figure style="max-width:'+l+'px">$1<figcaption'+o+">"+i.hours+"</figcaption></figure>"+a:"")+'<figure style="max-width:'+l+'px">$1<figcaption'+o+">"+i.min+"</figcaption></figure>"+a+'<figure style="max-width:'+l+'px">$1<figcaption'+o+">"+i.sec+"</figcaption></figure>":(f.displayHours?"$1"+a:"")+"$1"+a+"$1").replace(/\$1/g,e+s),0<f.displayDays){for(d=Math.round(.4*f.fontSize||T.gap),r=e,t=f.displayDays-1;0<t;--t)r+=1===t?s.replace('">',"margin-right:"+d+'px">'):s;1===f.displayDays&&(r=r.replace('">',"margin-right:"+d+'px">')),l=(f.displayCaptions?'<figure style="width:'+(f.width*f.displayDays+d+4)+'px">$1<figcaption style="'+n+"padding-right:"+d+'px">'+i.days+"</figcaption></figure>":"$1").replace(/\$1/,r)+l}m.html(l)}if((c=m.find("div")).length<f.vals.length){for(u=f.vals.length-c.length,h=f.vals,p=f.limits,f.vals=[],f.limits=[],t=0;t<c.length;t+=1)f.vals[t]=h[u+t],f.limits[t]=p[u+t];f.iSec=f.vals.length-1,f.iHour=f.vals.length-5}f.sec=f.seconds,m.data(f),(y&&I[y]?I[y]:f.start?I.start:S).call(m,f.seconds)})},v});
//# sourceMappingURL=jquery.time-to.min.js.map

{"version": 3, "file": "jquery.validate.min.js", "sources": ["jquery.validate.min.js"], "sourcesContent": ["/*!\r\n * jQuery Validation Plugin v1.15.0\r\n *\r\n * http://jqueryvalidation.org/\r\n *\r\n * Copyright (c) 2016 <PERSON><PERSON><PERSON>\r\n * Released under the MIT license\r\n */\r\n(function( factory ) {\r\n\tif ( typeof define === \"function\" && define.amd ) {\r\n\t\tdefine( [\"jquery\"], factory );\r\n\t} else if (typeof module === \"object\" && module.exports) {\r\n\t\tmodule.exports = factory( require( \"jquery\" ) );\r\n\t} else {\r\n\t\tfactory( jQuery );\r\n\t}\r\n}(function( $ ) {\r\n\r\n$.extend( $.fn, {\r\n\r\n\t// http://jqueryvalidation.org/validate/\r\n\tvalidate: function( options ) {\r\n\r\n\t\t// If nothing is selected, return nothing; can't chain anyway\r\n\t\tif ( !this.length ) {\r\n\t\t\tif ( options && options.debug && window.console ) {\r\n\t\t\t\tconsole.warn( \"Nothing selected, can't validate, returning nothing.\" );\r\n\t\t\t}\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Check if a validator for this form was already created\r\n\t\tvar validator = $.data( this[ 0 ], \"validator\" );\r\n\t\tif ( validator ) {\r\n\t\t\treturn validator;\r\n\t\t}\r\n\r\n\t\t// Add novalidate tag if HTML5.\r\n\t\tthis.attr( \"novalidate\", \"novalidate\" );\r\n\r\n\t\tvalidator = new $.validator( options, this[ 0 ] );\r\n\t\t$.data( this[ 0 ], \"validator\", validator );\r\n\r\n\t\tif ( validator.settings.onsubmit ) {\r\n\r\n\t\t\tthis.on( \"click.validate\", \":submit\", function( event ) {\r\n\t\t\t\tif ( validator.settings.submitHandler ) {\r\n\t\t\t\t\tvalidator.submitButton = event.target;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Allow suppressing validation by adding a cancel class to the submit button\r\n\t\t\t\tif ( $( this ).hasClass( \"cancel\" ) ) {\r\n\t\t\t\t\tvalidator.cancelSubmit = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Allow suppressing validation by adding the html5 formnovalidate attribute to the submit button\r\n\t\t\t\tif ( $( this ).attr( \"formnovalidate\" ) !== undefined ) {\r\n\t\t\t\t\tvalidator.cancelSubmit = true;\r\n\t\t\t\t}\r\n\t\t\t} );\r\n\r\n\t\t\t// Validate the form on submit\r\n\t\t\tthis.on( \"submit.validate\", function( event ) {\r\n\t\t\t\tif ( validator.settings.debug ) {\r\n\r\n\t\t\t\t\t// Prevent form submit to be able to see console output\r\n\t\t\t\t\tevent.preventDefault();\r\n\t\t\t\t}\r\n\t\t\t\tfunction handle() {\r\n\t\t\t\t\tvar hidden, result;\r\n\t\t\t\t\tif ( validator.settings.submitHandler ) {\r\n\t\t\t\t\t\tif ( validator.submitButton ) {\r\n\r\n\t\t\t\t\t\t\t// Insert a hidden input as a replacement for the missing submit button\r\n\t\t\t\t\t\t\thidden = $( \"<input type='hidden'/>\" )\r\n\t\t\t\t\t\t\t\t.attr( \"name\", validator.submitButton.name )\r\n\t\t\t\t\t\t\t\t.val( $( validator.submitButton ).val() )\r\n\t\t\t\t\t\t\t\t.appendTo( validator.currentForm );\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tresult = validator.settings.submitHandler.call( validator, validator.currentForm, event );\r\n\t\t\t\t\t\tif ( validator.submitButton ) {\r\n\r\n\t\t\t\t\t\t\t// And clean up afterwards; thanks to no-block-scope, hidden can be referenced\r\n\t\t\t\t\t\t\thidden.remove();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif ( result !== undefined ) {\r\n\t\t\t\t\t\t\treturn result;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Prevent submit for invalid forms or custom submit handlers\r\n\t\t\t\tif ( validator.cancelSubmit ) {\r\n\t\t\t\t\tvalidator.cancelSubmit = false;\r\n\t\t\t\t\treturn handle();\r\n\t\t\t\t}\r\n\t\t\t\tif ( validator.form() ) {\r\n\t\t\t\t\tif ( validator.pendingRequest ) {\r\n\t\t\t\t\t\tvalidator.formSubmitted = true;\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn handle();\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvalidator.focusInvalid();\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t} );\r\n\t\t}\r\n\r\n\t\treturn validator;\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/valid/\r\n\tvalid: function() {\r\n\t\tvar valid, validator, errorList;\r\n\r\n\t\tif ( $( this[ 0 ] ).is( \"form\" ) ) {\r\n\t\t\tvalid = this.validate().form();\r\n\t\t} else {\r\n\t\t\terrorList = [];\r\n\t\t\tvalid = true;\r\n\t\t\tvalidator = $( this[ 0 ].form ).validate();\r\n\t\t\tthis.each( function() {\r\n\t\t\t\tvalid = validator.element( this ) && valid;\r\n\t\t\t\tif ( !valid ) {\r\n\t\t\t\t\terrorList = errorList.concat( validator.errorList );\r\n\t\t\t\t}\r\n\t\t\t} );\r\n\t\t\tvalidator.errorList = errorList;\r\n\t\t}\r\n\t\treturn valid;\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/rules/\r\n\trules: function( command, argument ) {\r\n\r\n\t\t// If nothing is selected, return nothing; can't chain anyway\r\n\t\tif ( !this.length ) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tvar element = this[ 0 ],\r\n\t\t\tsettings, staticRules, existingRules, data, param, filtered;\r\n\r\n\t\tif ( command ) {\r\n\t\t\tsettings = $.data( element.form, \"validator\" ).settings;\r\n\t\t\tstaticRules = settings.rules;\r\n\t\t\texistingRules = $.validator.staticRules( element );\r\n\t\t\tswitch ( command ) {\r\n\t\t\tcase \"add\":\r\n\t\t\t\t$.extend( existingRules, $.validator.normalizeRule( argument ) );\r\n\r\n\t\t\t\t// Remove messages from rules, but allow them to be set separately\r\n\t\t\t\tdelete existingRules.messages;\r\n\t\t\t\tstaticRules[ element.name ] = existingRules;\r\n\t\t\t\tif ( argument.messages ) {\r\n\t\t\t\t\tsettings.messages[ element.name ] = $.extend( settings.messages[ element.name ], argument.messages );\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"remove\":\r\n\t\t\t\tif ( !argument ) {\r\n\t\t\t\t\tdelete staticRules[ element.name ];\r\n\t\t\t\t\treturn existingRules;\r\n\t\t\t\t}\r\n\t\t\t\tfiltered = {};\r\n\t\t\t\t$.each( argument.split( /\\s/ ), function( index, method ) {\r\n\t\t\t\t\tfiltered[ method ] = existingRules[ method ];\r\n\t\t\t\t\tdelete existingRules[ method ];\r\n\t\t\t\t\tif ( method === \"required\" ) {\r\n\t\t\t\t\t\t$( element ).removeAttr( \"aria-required\" );\r\n\t\t\t\t\t}\r\n\t\t\t\t} );\r\n\t\t\t\treturn filtered;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tdata = $.validator.normalizeRules(\r\n\t\t$.extend(\r\n\t\t\t{},\r\n\t\t\t$.validator.classRules( element ),\r\n\t\t\t$.validator.attributeRules( element ),\r\n\t\t\t$.validator.dataRules( element ),\r\n\t\t\t$.validator.staticRules( element )\r\n\t\t), element );\r\n\r\n\t\t// Make sure required is at front\r\n\t\tif ( data.required ) {\r\n\t\t\tparam = data.required;\r\n\t\t\tdelete data.required;\r\n\t\t\tdata = $.extend( { required: param }, data );\r\n\t\t\t$( element ).attr( \"aria-required\", \"true\" );\r\n\t\t}\r\n\r\n\t\t// Make sure remote is at back\r\n\t\tif ( data.remote ) {\r\n\t\t\tparam = data.remote;\r\n\t\t\tdelete data.remote;\r\n\t\t\tdata = $.extend( data, { remote: param } );\r\n\t\t}\r\n\r\n\t\treturn data;\r\n\t}\r\n} );\r\n\r\n// Custom selectors\r\n$.extend( $.expr[ \":\" ], {\r\n\r\n\t// http://jqueryvalidation.org/blank-selector/\r\n\tblank: function( a ) {\r\n\t\treturn !$.trim( \"\" + $( a ).val() );\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/filled-selector/\r\n\tfilled: function( a ) {\r\n\t\tvar val = $( a ).val();\r\n\t\treturn val !== null && !!$.trim( \"\" + val );\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/unchecked-selector/\r\n\tunchecked: function( a ) {\r\n\t\treturn !$( a ).prop( \"checked\" );\r\n\t}\r\n} );\r\n\r\n// Constructor for validator\r\n$.validator = function( options, form ) {\r\n\tthis.settings = $.extend( true, {}, $.validator.defaults, options );\r\n\tthis.currentForm = form;\r\n\tthis.init();\r\n};\r\n\r\n// http://jqueryvalidation.org/jQuery.validator.format/\r\n$.validator.format = function( source, params ) {\r\n\tif ( arguments.length === 1 ) {\r\n\t\treturn function() {\r\n\t\t\tvar args = $.makeArray( arguments );\r\n\t\t\targs.unshift( source );\r\n\t\t\treturn $.validator.format.apply( this, args );\r\n\t\t};\r\n\t}\r\n\tif ( params === undefined ) {\r\n\t\treturn source;\r\n\t}\r\n\tif ( arguments.length > 2 && params.constructor !== Array  ) {\r\n\t\tparams = $.makeArray( arguments ).slice( 1 );\r\n\t}\r\n\tif ( params.constructor !== Array ) {\r\n\t\tparams = [ params ];\r\n\t}\r\n\t$.each( params, function( i, n ) {\r\n\t\tsource = source.replace( new RegExp( \"\\\\{\" + i + \"\\\\}\", \"g\" ), function() {\r\n\t\t\treturn n;\r\n\t\t} );\r\n\t} );\r\n\treturn source;\r\n};\r\n\r\n$.extend( $.validator, {\r\n\r\n\tdefaults: {\r\n\t\tmessages: {},\r\n\t\tgroups: {},\r\n\t\trules: {},\r\n\t\terrorClass: \"error\",\r\n\t\tpendingClass: \"pending\",\r\n\t\tvalidClass: \"valid\",\r\n\t\terrorElement: \"label\",\r\n\t\tfocusCleanup: false,\r\n\t\tfocusInvalid: true,\r\n\t\terrorContainer: $( [] ),\r\n\t\terrorLabelContainer: $( [] ),\r\n\t\tonsubmit: true,\r\n\t\tignore: \":hidden\",\r\n\t\tignoreTitle: false,\r\n\t\tonfocusin: function( element ) {\r\n\t\t\tthis.lastActive = element;\r\n\r\n\t\t\t// Hide error label and remove error class on focus if enabled\r\n\t\t\tif ( this.settings.focusCleanup ) {\r\n\t\t\t\tif ( this.settings.unhighlight ) {\r\n\t\t\t\t\tthis.settings.unhighlight.call( this, element, this.settings.errorClass, this.settings.validClass );\r\n\t\t\t\t}\r\n\t\t\t\tthis.hideThese( this.errorsFor( element ) );\r\n\t\t\t}\r\n\t\t},\r\n\t\tonfocusout: function( element ) {\r\n\t\t\tif ( !this.checkable( element ) && ( element.name in this.submitted || !this.optional( element ) ) ) {\r\n\t\t\t\tthis.element( element );\r\n\t\t\t}\r\n\t\t},\r\n\t\tonkeyup: function( element, event ) {\r\n\r\n\t\t\t// Avoid revalidate the field when pressing one of the following keys\r\n\t\t\t// Shift       => 16\r\n\t\t\t// Ctrl        => 17\r\n\t\t\t// Alt         => 18\r\n\t\t\t// Caps lock   => 20\r\n\t\t\t// End         => 35\r\n\t\t\t// Home        => 36\r\n\t\t\t// Left arrow  => 37\r\n\t\t\t// Up arrow    => 38\r\n\t\t\t// Right arrow => 39\r\n\t\t\t// Down arrow  => 40\r\n\t\t\t// Insert      => 45\r\n\t\t\t// Num lock    => 144\r\n\t\t\t// AltGr key   => 225\r\n\t\t\tvar excludedKeys = [\r\n\t\t\t\t16, 17, 18, 20, 35, 36, 37,\r\n\t\t\t\t38, 39, 40, 45, 144, 225\r\n\t\t\t];\r\n\r\n\t\t\tif ( event.which === 9 && this.elementValue( element ) === \"\" || $.inArray( event.keyCode, excludedKeys ) !== -1 ) {\r\n\t\t\t\treturn;\r\n\t\t\t} else if ( element.name in this.submitted || element.name in this.invalid ) {\r\n\t\t\t\tthis.element( element );\r\n\t\t\t}\r\n\t\t},\r\n\t\tonclick: function( element ) {\r\n\r\n\t\t\t// Click on selects, radiobuttons and checkboxes\r\n\t\t\tif ( element.name in this.submitted ) {\r\n\t\t\t\tthis.element( element );\r\n\r\n\t\t\t// Or option elements, check parent select in that case\r\n\t\t\t} else if ( element.parentNode.name in this.submitted ) {\r\n\t\t\t\tthis.element( element.parentNode );\r\n\t\t\t}\r\n\t\t},\r\n\t\thighlight: function( element, errorClass, validClass ) {\r\n\t\t\tif ( element.type === \"radio\" ) {\r\n\t\t\t\tthis.findByName( element.name ).addClass( errorClass ).removeClass( validClass );\r\n\t\t\t} else {\r\n\t\t\t\t$( element ).addClass( errorClass ).removeClass( validClass );\r\n\t\t\t}\r\n\t\t},\r\n\t\tunhighlight: function( element, errorClass, validClass ) {\r\n\t\t\tif ( element.type === \"radio\" ) {\r\n\t\t\t\tthis.findByName( element.name ).removeClass( errorClass ).addClass( validClass );\r\n\t\t\t} else {\r\n\t\t\t\t$( element ).removeClass( errorClass ).addClass( validClass );\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/jQuery.validator.setDefaults/\r\n\tsetDefaults: function( settings ) {\r\n\t\t$.extend( $.validator.defaults, settings );\r\n\t},\r\n\r\n\tmessages: {\r\n\t\trequired: \"This field is required.\",\r\n\t\tremote: \"Please fix this field.\",\r\n\t\temail: \"Please enter a valid email address.\",\r\n\t\turl: \"Please enter a valid URL.\",\r\n\t\tdate: \"Please enter a valid date.\",\r\n\t\tdateISO: \"Please enter a valid date ( ISO ).\",\r\n\t\tnumber: \"Please enter a valid number.\",\r\n\t\tdigits: \"Please enter only digits.\",\r\n\t\tequalTo: \"Please enter the same value again.\",\r\n\t\tmaxlength: $.validator.format( \"Please enter no more than {0} characters.\" ),\r\n\t\tminlength: $.validator.format( \"Please enter at least {0} characters.\" ),\r\n\t\trangelength: $.validator.format( \"Please enter a value between {0} and {1} characters long.\" ),\r\n\t\trange: $.validator.format( \"Please enter a value between {0} and {1}.\" ),\r\n\t\tmax: $.validator.format( \"Please enter a value less than or equal to {0}.\" ),\r\n\t\tmin: $.validator.format( \"Please enter a value greater than or equal to {0}.\" ),\r\n\t\tstep: $.validator.format( \"Please enter a multiple of {0}.\" )\r\n\t},\r\n\r\n\tautoCreateRanges: false,\r\n\r\n\tprototype: {\r\n\r\n\t\tinit: function() {\r\n\t\t\tthis.labelContainer = $( this.settings.errorLabelContainer );\r\n\t\t\tthis.errorContext = this.labelContainer.length && this.labelContainer || $( this.currentForm );\r\n\t\t\tthis.containers = $( this.settings.errorContainer ).add( this.settings.errorLabelContainer );\r\n\t\t\tthis.submitted = {};\r\n\t\t\tthis.valueCache = {};\r\n\t\t\tthis.pendingRequest = 0;\r\n\t\t\tthis.pending = {};\r\n\t\t\tthis.invalid = {};\r\n\t\t\tthis.reset();\r\n\r\n\t\t\tvar groups = ( this.groups = {} ),\r\n\t\t\t\trules;\r\n\t\t\t$.each( this.settings.groups, function( key, value ) {\r\n\t\t\t\tif ( typeof value === \"string\" ) {\r\n\t\t\t\t\tvalue = value.split( /\\s/ );\r\n\t\t\t\t}\r\n\t\t\t\t$.each( value, function( index, name ) {\r\n\t\t\t\t\tgroups[ name ] = key;\r\n\t\t\t\t} );\r\n\t\t\t} );\r\n\t\t\trules = this.settings.rules;\r\n\t\t\t$.each( rules, function( key, value ) {\r\n\t\t\t\trules[ key ] = $.validator.normalizeRule( value );\r\n\t\t\t} );\r\n\r\n\t\t\tfunction delegate( event ) {\r\n\t\t\t\tvar validator = $.data( this.form, \"validator\" ),\r\n\t\t\t\t\teventType = \"on\" + event.type.replace( /^validate/, \"\" ),\r\n\t\t\t\t\tsettings = validator.settings;\r\n\t\t\t\tif ( settings[ eventType ] && !$( this ).is( settings.ignore ) ) {\r\n\t\t\t\t\tsettings[ eventType ].call( validator, this, event );\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t$( this.currentForm )\r\n\t\t\t\t.on( \"focusin.validate focusout.validate keyup.validate\",\r\n\t\t\t\t\t\":text, [type='password'], [type='file'], select, textarea, [type='number'], [type='search'], \" +\r\n\t\t\t\t\t\"[type='tel'], [type='url'], [type='email'], [type='datetime'], [type='date'], [type='month'], \" +\r\n\t\t\t\t\t\"[type='week'], [type='time'], [type='datetime-local'], [type='range'], [type='color'], \" +\r\n\t\t\t\t\t\"[type='radio'], [type='checkbox'], [contenteditable]\", delegate )\r\n\r\n\t\t\t\t// Support: Chrome, oldIE\r\n\t\t\t\t// \"select\" is provided as event.target when clicking a option\r\n\t\t\t\t.on( \"click.validate\", \"select, option, [type='radio'], [type='checkbox']\", delegate );\r\n\r\n\t\t\tif ( this.settings.invalidHandler ) {\r\n\t\t\t\t$( this.currentForm ).on( \"invalid-form.validate\", this.settings.invalidHandler );\r\n\t\t\t}\r\n\r\n\t\t\t// Add aria-required to any Static/Data/Class required fields before first validation\r\n\t\t\t// Screen readers require this attribute to be present before the initial submission http://www.w3.org/TR/WCAG-TECHS/ARIA2.html\r\n\t\t\t$( this.currentForm ).find( \"[required], [data-rule-required], .required\" ).attr( \"aria-required\", \"true\" );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/Validator.form/\r\n\t\tform: function() {\r\n\t\t\tthis.checkForm();\r\n\t\t\t$.extend( this.submitted, this.errorMap );\r\n\t\t\tthis.invalid = $.extend( {}, this.errorMap );\r\n\t\t\tif ( !this.valid() ) {\r\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\r\n\t\t\t}\r\n\t\t\tthis.showErrors();\r\n\t\t\treturn this.valid();\r\n\t\t},\r\n\r\n\t\tcheckForm: function() {\r\n\t\t\tthis.prepareForm();\r\n\t\t\tfor ( var i = 0, elements = ( this.currentElements = this.elements() ); elements[ i ]; i++ ) {\r\n\t\t\t\tthis.check( elements[ i ] );\r\n\t\t\t}\r\n\t\t\treturn this.valid();\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/Validator.element/\r\n\t\telement: function( element ) {\r\n\t\t\tvar cleanElement = this.clean( element ),\r\n\t\t\t\tcheckElement = this.validationTargetFor( cleanElement ),\r\n\t\t\t\tv = this,\r\n\t\t\t\tresult = true,\r\n\t\t\t\trs, group;\r\n\r\n\t\t\tif ( checkElement === undefined ) {\r\n\t\t\t\tdelete this.invalid[ cleanElement.name ];\r\n\t\t\t} else {\r\n\t\t\t\tthis.prepareElement( checkElement );\r\n\t\t\t\tthis.currentElements = $( checkElement );\r\n\r\n\t\t\t\t// If this element is grouped, then validate all group elements already\r\n\t\t\t\t// containing a value\r\n\t\t\t\tgroup = this.groups[ checkElement.name ];\r\n\t\t\t\tif ( group ) {\r\n\t\t\t\t\t$.each( this.groups, function( name, testgroup ) {\r\n\t\t\t\t\t\tif ( testgroup === group && name !== checkElement.name ) {\r\n\t\t\t\t\t\t\tcleanElement = v.validationTargetFor( v.clean( v.findByName( name ) ) );\r\n\t\t\t\t\t\t\tif ( cleanElement && cleanElement.name in v.invalid ) {\r\n\t\t\t\t\t\t\t\tv.currentElements.push( cleanElement );\r\n\t\t\t\t\t\t\t\tresult = result && v.check( cleanElement );\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} );\r\n\t\t\t\t}\r\n\r\n\t\t\t\trs = this.check( checkElement ) !== false;\r\n\t\t\t\tresult = result && rs;\r\n\t\t\t\tif ( rs ) {\r\n\t\t\t\t\tthis.invalid[ checkElement.name ] = false;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.invalid[ checkElement.name ] = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif ( !this.numberOfInvalids() ) {\r\n\r\n\t\t\t\t\t// Hide error containers on last error\r\n\t\t\t\t\tthis.toHide = this.toHide.add( this.containers );\r\n\t\t\t\t}\r\n\t\t\t\tthis.showErrors();\r\n\r\n\t\t\t\t// Add aria-invalid status for screen readers\r\n\t\t\t\t$( element ).attr( \"aria-invalid\", !rs );\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/Validator.showErrors/\r\n\t\tshowErrors: function( errors ) {\r\n\t\t\tif ( errors ) {\r\n\t\t\t\tvar validator = this;\r\n\r\n\t\t\t\t// Add items to error list and map\r\n\t\t\t\t$.extend( this.errorMap, errors );\r\n\t\t\t\tthis.errorList = $.map( this.errorMap, function( message, name ) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmessage: message,\r\n\t\t\t\t\t\telement: validator.findByName( name )[ 0 ]\r\n\t\t\t\t\t};\r\n\t\t\t\t} );\r\n\r\n\t\t\t\t// Remove items from success list\r\n\t\t\t\tthis.successList = $.grep( this.successList, function( element ) {\r\n\t\t\t\t\treturn !( element.name in errors );\r\n\t\t\t\t} );\r\n\t\t\t}\r\n\t\t\tif ( this.settings.showErrors ) {\r\n\t\t\t\tthis.settings.showErrors.call( this, this.errorMap, this.errorList );\r\n\t\t\t} else {\r\n\t\t\t\tthis.defaultShowErrors();\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/Validator.resetForm/\r\n\t\tresetForm: function() {\r\n\t\t\tif ( $.fn.resetForm ) {\r\n\t\t\t\t$( this.currentForm ).resetForm();\r\n\t\t\t}\r\n\t\t\tthis.invalid = {};\r\n\t\t\tthis.submitted = {};\r\n\t\t\tthis.prepareForm();\r\n\t\t\tthis.hideErrors();\r\n\t\t\tvar elements = this.elements()\r\n\t\t\t\t.removeData( \"previousValue\" )\r\n\t\t\t\t.removeAttr( \"aria-invalid\" );\r\n\r\n\t\t\tthis.resetElements( elements );\r\n\t\t},\r\n\r\n\t\tresetElements: function( elements ) {\r\n\t\t\tvar i;\r\n\r\n\t\t\tif ( this.settings.unhighlight ) {\r\n\t\t\t\tfor ( i = 0; elements[ i ]; i++ ) {\r\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ],\r\n\t\t\t\t\t\tthis.settings.errorClass, \"\" );\r\n\t\t\t\t\tthis.findByName( elements[ i ].name ).removeClass( this.settings.validClass );\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\telements\r\n\t\t\t\t\t.removeClass( this.settings.errorClass )\r\n\t\t\t\t\t.removeClass( this.settings.validClass );\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tnumberOfInvalids: function() {\r\n\t\t\treturn this.objectLength( this.invalid );\r\n\t\t},\r\n\r\n\t\tobjectLength: function( obj ) {\r\n\t\t\t/* jshint unused: false */\r\n\t\t\tvar count = 0,\r\n\t\t\t\ti;\r\n\t\t\tfor ( i in obj ) {\r\n\t\t\t\tif ( obj[ i ] ) {\r\n\t\t\t\t\tcount++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn count;\r\n\t\t},\r\n\r\n\t\thideErrors: function() {\r\n\t\t\tthis.hideThese( this.toHide );\r\n\t\t},\r\n\r\n\t\thideThese: function( errors ) {\r\n\t\t\terrors.not( this.containers ).text( \"\" );\r\n\t\t\tthis.addWrapper( errors ).hide();\r\n\t\t},\r\n\r\n\t\tvalid: function() {\r\n\t\t\treturn this.size() === 0;\r\n\t\t},\r\n\r\n\t\tsize: function() {\r\n\t\t\treturn this.errorList.length;\r\n\t\t},\r\n\r\n\t\tfocusInvalid: function() {\r\n\t\t\tif ( this.settings.focusInvalid ) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t$( this.findLastActive() || this.errorList.length && this.errorList[ 0 ].element || [] )\r\n\t\t\t\t\t.filter( \":visible\" )\r\n\t\t\t\t\t.focus()\r\n\r\n\t\t\t\t\t// Manually trigger focusin event; without it, focusin handler isn't called, findLastActive won't have anything to find\r\n\t\t\t\t\t.trigger( \"focusin\" );\r\n\t\t\t\t} catch ( e ) {\r\n\r\n\t\t\t\t\t// Ignore IE throwing errors when focusing hidden elements\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tfindLastActive: function() {\r\n\t\t\tvar lastActive = this.lastActive;\r\n\t\t\treturn lastActive && $.grep( this.errorList, function( n ) {\r\n\t\t\t\treturn n.element.name === lastActive.name;\r\n\t\t\t} ).length === 1 && lastActive;\r\n\t\t},\r\n\r\n\t\telements: function() {\r\n\t\t\tvar validator = this,\r\n\t\t\t\trulesCache = {};\r\n\r\n\t\t\t// Select all valid inputs inside the form (no submit or reset buttons)\r\n\t\t\treturn $( this.currentForm )\r\n\t\t\t.find( \"input, select, textarea, [contenteditable]\" )\r\n\t\t\t.not( \":submit, :reset, :image, :disabled\" )\r\n\t\t\t.not( this.settings.ignore )\r\n\t\t\t.filter( function() {\r\n\t\t\t\tvar name = this.name || $( this ).attr( \"name\" ); // For contenteditable\r\n\t\t\t\tif ( !name && validator.settings.debug && window.console ) {\r\n\t\t\t\t\tconsole.error( \"%o has no name assigned\", this );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Set form expando on contenteditable\r\n\t\t\t\tif ( this.hasAttribute( \"contenteditable\" ) ) {\r\n\t\t\t\t\tthis.form = $( this ).closest( \"form\" )[ 0 ];\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Select only the first element for each name, and only those with rules specified\r\n\t\t\t\tif ( name in rulesCache || !validator.objectLength( $( this ).rules() ) ) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\trulesCache[ name ] = true;\r\n\t\t\t\treturn true;\r\n\t\t\t} );\r\n\t\t},\r\n\r\n\t\tclean: function( selector ) {\r\n\t\t\treturn $( selector )[ 0 ];\r\n\t\t},\r\n\r\n\t\terrors: function() {\r\n\t\t\tvar errorClass = this.settings.errorClass.split( \" \" ).join( \".\" );\r\n\t\t\treturn $( this.settings.errorElement + \".\" + errorClass, this.errorContext );\r\n\t\t},\r\n\r\n\t\tresetInternals: function() {\r\n\t\t\tthis.successList = [];\r\n\t\t\tthis.errorList = [];\r\n\t\t\tthis.errorMap = {};\r\n\t\t\tthis.toShow = $( [] );\r\n\t\t\tthis.toHide = $( [] );\r\n\t\t},\r\n\r\n\t\treset: function() {\r\n\t\t\tthis.resetInternals();\r\n\t\t\tthis.currentElements = $( [] );\r\n\t\t},\r\n\r\n\t\tprepareForm: function() {\r\n\t\t\tthis.reset();\r\n\t\t\tthis.toHide = this.errors().add( this.containers );\r\n\t\t},\r\n\r\n\t\tprepareElement: function( element ) {\r\n\t\t\tthis.reset();\r\n\t\t\tthis.toHide = this.errorsFor( element );\r\n\t\t},\r\n\r\n\t\telementValue: function( element ) {\r\n\t\t\tvar $element = $( element ),\r\n\t\t\t\ttype = element.type,\r\n\t\t\t\tval, idx;\r\n\r\n\t\t\tif ( type === \"radio\" || type === \"checkbox\" ) {\r\n\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).val();\r\n\t\t\t} else if ( type === \"number\" && typeof element.validity !== \"undefined\" ) {\r\n\t\t\t\treturn element.validity.badInput ? \"NaN\" : $element.val();\r\n\t\t\t}\r\n\r\n\t\t\tif ( element.hasAttribute( \"contenteditable\" ) ) {\r\n\t\t\t\tval = $element.text();\r\n\t\t\t} else {\r\n\t\t\t\tval = $element.val();\r\n\t\t\t}\r\n\r\n\t\t\tif ( type === \"file\" ) {\r\n\r\n\t\t\t\t// Modern browser (chrome & safari)\r\n\t\t\t\tif ( val.substr( 0, 12 ) === \"C:\\\\fakepath\\\\\" ) {\r\n\t\t\t\t\treturn val.substr( 12 );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Legacy browsers\r\n\t\t\t\t// Unix-based path\r\n\t\t\t\tidx = val.lastIndexOf( \"/\" );\r\n\t\t\t\tif ( idx >= 0 ) {\r\n\t\t\t\t\treturn val.substr( idx + 1 );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Windows-based path\r\n\t\t\t\tidx = val.lastIndexOf( \"\\\\\" );\r\n\t\t\t\tif ( idx >= 0 ) {\r\n\t\t\t\t\treturn val.substr( idx + 1 );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Just the file name\r\n\t\t\t\treturn val;\r\n\t\t\t}\r\n\r\n\t\t\tif ( typeof val === \"string\" ) {\r\n\t\t\t\treturn val.replace( /\\r/g, \"\" );\r\n\t\t\t}\r\n\t\t\treturn val;\r\n\t\t},\r\n\r\n\t\tcheck: function( element ) {\r\n\t\t\telement = this.validationTargetFor( this.clean( element ) );\r\n\r\n\t\t\tvar rules = $( element ).rules(),\r\n\t\t\t\trulesCount = $.map( rules, function( n, i ) {\r\n\t\t\t\t\treturn i;\r\n\t\t\t\t} ).length,\r\n\t\t\t\tdependencyMismatch = false,\r\n\t\t\t\tval = this.elementValue( element ),\r\n\t\t\t\tresult, method, rule;\r\n\r\n\t\t\t// If a normalizer is defined for this element, then\r\n\t\t\t// call it to retreive the changed value instead\r\n\t\t\t// of using the real one.\r\n\t\t\t// Note that `this` in the normalizer is `element`.\r\n\t\t\tif ( typeof rules.normalizer === \"function\" ) {\r\n\t\t\t\tval = rules.normalizer.call( element, val );\r\n\r\n\t\t\t\tif ( typeof val !== \"string\" ) {\r\n\t\t\t\t\tthrow new TypeError( \"The normalizer should return a string value.\" );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Delete the normalizer from rules to avoid treating\r\n\t\t\t\t// it as a pre-defined method.\r\n\t\t\t\tdelete rules.normalizer;\r\n\t\t\t}\r\n\r\n\t\t\tfor ( method in rules ) {\r\n\t\t\t\trule = { method: method, parameters: rules[ method ] };\r\n\t\t\t\ttry {\r\n\t\t\t\t\tresult = $.validator.methods[ method ].call( this, val, element, rule.parameters );\r\n\r\n\t\t\t\t\t// If a method indicates that the field is optional and therefore valid,\r\n\t\t\t\t\t// don't mark it as valid when there are no other rules\r\n\t\t\t\t\tif ( result === \"dependency-mismatch\" && rulesCount === 1 ) {\r\n\t\t\t\t\t\tdependencyMismatch = true;\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdependencyMismatch = false;\r\n\r\n\t\t\t\t\tif ( result === \"pending\" ) {\r\n\t\t\t\t\t\tthis.toHide = this.toHide.not( this.errorsFor( element ) );\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif ( !result ) {\r\n\t\t\t\t\t\tthis.formatAndAdd( element, rule );\r\n\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch ( e ) {\r\n\t\t\t\t\tif ( this.settings.debug && window.console ) {\r\n\t\t\t\t\t\tconsole.log( \"Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\", e );\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif ( e instanceof TypeError ) {\r\n\t\t\t\t\t\te.message += \".  Exception occurred when checking element \" + element.id + \", check the '\" + rule.method + \"' method.\";\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tthrow e;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ( dependencyMismatch ) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif ( this.objectLength( rules ) ) {\r\n\t\t\t\tthis.successList.push( element );\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t},\r\n\r\n\t\t// Return the custom message for the given element and validation method\r\n\t\t// specified in the element's HTML5 data attribute\r\n\t\t// return the generic message if present and no method specific message is present\r\n\t\tcustomDataMessage: function( element, method ) {\r\n\t\t\treturn $( element ).data( \"msg\" + method.charAt( 0 ).toUpperCase() +\r\n\t\t\t\tmethod.substring( 1 ).toLowerCase() ) || $( element ).data( \"msg\" );\r\n\t\t},\r\n\r\n\t\t// Return the custom message for the given element name and validation method\r\n\t\tcustomMessage: function( name, method ) {\r\n\t\t\tvar m = this.settings.messages[ name ];\r\n\t\t\treturn m && ( m.constructor === String ? m : m[ method ] );\r\n\t\t},\r\n\r\n\t\t// Return the first defined argument, allowing empty strings\r\n\t\tfindDefined: function() {\r\n\t\t\tfor ( var i = 0; i < arguments.length; i++ ) {\r\n\t\t\t\tif ( arguments[ i ] !== undefined ) {\r\n\t\t\t\t\treturn arguments[ i ];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn undefined;\r\n\t\t},\r\n\r\n\t\tdefaultMessage: function( element, rule ) {\r\n\t\t\tvar message = this.findDefined(\r\n\t\t\t\t\tthis.customMessage( element.name, rule.method ),\r\n\t\t\t\t\tthis.customDataMessage( element, rule.method ),\r\n\r\n\t\t\t\t\t// 'title' is never undefined, so handle empty string as undefined\r\n\t\t\t\t\t!this.settings.ignoreTitle && element.title || undefined,\r\n\t\t\t\t\t$.validator.messages[ rule.method ],\r\n\t\t\t\t\t\"<strong>Warning: No message defined for \" + element.name + \"</strong>\"\r\n\t\t\t\t),\r\n\t\t\t\ttheregex = /\\$?\\{(\\d+)\\}/g;\r\n\t\t\tif ( typeof message === \"function\" ) {\r\n\t\t\t\tmessage = message.call( this, rule.parameters, element );\r\n\t\t\t} else if ( theregex.test( message ) ) {\r\n\t\t\t\tmessage = $.validator.format( message.replace( theregex, \"{$1}\" ), rule.parameters );\r\n\t\t\t}\r\n\r\n\t\t\treturn message;\r\n\t\t},\r\n\r\n\t\tformatAndAdd: function( element, rule ) {\r\n\t\t\tvar message = this.defaultMessage( element, rule );\r\n\r\n\t\t\tthis.errorList.push( {\r\n\t\t\t\tmessage: message,\r\n\t\t\t\telement: element,\r\n\t\t\t\tmethod: rule.method\r\n\t\t\t} );\r\n\r\n\t\t\tthis.errorMap[ element.name ] = message;\r\n\t\t\tthis.submitted[ element.name ] = message;\r\n\t\t},\r\n\r\n\t\taddWrapper: function( toToggle ) {\r\n\t\t\tif ( this.settings.wrapper ) {\r\n\t\t\t\ttoToggle = toToggle.add( toToggle.parent( this.settings.wrapper ) );\r\n\t\t\t}\r\n\t\t\treturn toToggle;\r\n\t\t},\r\n\r\n\t\tdefaultShowErrors: function() {\r\n\t\t\tvar i, elements, error;\r\n\t\t\tfor ( i = 0; this.errorList[ i ]; i++ ) {\r\n\t\t\t\terror = this.errorList[ i ];\r\n\t\t\t\tif ( this.settings.highlight ) {\r\n\t\t\t\t\tthis.settings.highlight.call( this, error.element, this.settings.errorClass, this.settings.validClass );\r\n\t\t\t\t}\r\n\t\t\t\tthis.showLabel( error.element, error.message );\r\n\t\t\t}\r\n\t\t\tif ( this.errorList.length ) {\r\n\t\t\t\tthis.toShow = this.toShow.add( this.containers );\r\n\t\t\t}\r\n\t\t\tif ( this.settings.success ) {\r\n\t\t\t\tfor ( i = 0; this.successList[ i ]; i++ ) {\r\n\t\t\t\t\tthis.showLabel( this.successList[ i ] );\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ( this.settings.unhighlight ) {\r\n\t\t\t\tfor ( i = 0, elements = this.validElements(); elements[ i ]; i++ ) {\r\n\t\t\t\t\tthis.settings.unhighlight.call( this, elements[ i ], this.settings.errorClass, this.settings.validClass );\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.toHide = this.toHide.not( this.toShow );\r\n\t\t\tthis.hideErrors();\r\n\t\t\tthis.addWrapper( this.toShow ).show();\r\n\t\t},\r\n\r\n\t\tvalidElements: function() {\r\n\t\t\treturn this.currentElements.not( this.invalidElements() );\r\n\t\t},\r\n\r\n\t\tinvalidElements: function() {\r\n\t\t\treturn $( this.errorList ).map( function() {\r\n\t\t\t\treturn this.element;\r\n\t\t\t} );\r\n\t\t},\r\n\r\n\t\tshowLabel: function( element, message ) {\r\n\t\t\tvar place, group, errorID, v,\r\n\t\t\t\terror = this.errorsFor( element ),\r\n\t\t\t\telementID = this.idOrName( element ),\r\n\t\t\t\tdescribedBy = $( element ).attr( \"aria-describedby\" );\r\n\r\n\t\t\tif ( error.length ) {\r\n\r\n\t\t\t\t// Refresh error/success class\r\n\t\t\t\terror.removeClass( this.settings.validClass ).addClass( this.settings.errorClass );\r\n\r\n\t\t\t\t// Replace message on existing label\r\n\t\t\t\terror.html( message );\r\n\t\t\t} else {\r\n\r\n\t\t\t\t// Create error element\r\n\t\t\t\terror = $( \"<\" + this.settings.errorElement + \">\" )\r\n\t\t\t\t\t.attr( \"id\", elementID + \"-error\" )\r\n\t\t\t\t\t.addClass( this.settings.errorClass )\r\n\t\t\t\t\t.html( message || \"\" );\r\n\r\n\t\t\t\t// Maintain reference to the element to be placed into the DOM\r\n\t\t\t\tplace = error;\r\n\t\t\t\tif ( this.settings.wrapper ) {\r\n\r\n\t\t\t\t\t// Make sure the element is visible, even in IE\r\n\t\t\t\t\t// actually showing the wrapped element is handled elsewhere\r\n\t\t\t\t\tplace = error.hide().show().wrap( \"<\" + this.settings.wrapper + \"/>\" ).parent();\r\n\t\t\t\t}\r\n\t\t\t\tif ( this.labelContainer.length ) {\r\n\t\t\t\t\tthis.labelContainer.append( place );\r\n\t\t\t\t} else if ( this.settings.errorPlacement ) {\r\n\t\t\t\t\tthis.settings.errorPlacement( place, $( element ) );\r\n\t\t\t\t} else {\r\n\t\t\t\t\tplace.insertAfter( element );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Link error back to the element\r\n\t\t\t\tif ( error.is( \"label\" ) ) {\r\n\r\n\t\t\t\t\t// If the error is a label, then associate using 'for'\r\n\t\t\t\t\terror.attr( \"for\", elementID );\r\n\r\n\t\t\t\t\t// If the element is not a child of an associated label, then it's necessary\r\n\t\t\t\t\t// to explicitly apply aria-describedby\r\n\t\t\t\t} else if ( error.parents( \"label[for='\" + this.escapeCssMeta( elementID ) + \"']\" ).length === 0 ) {\r\n\t\t\t\t\terrorID = error.attr( \"id\" );\r\n\r\n\t\t\t\t\t// Respect existing non-error aria-describedby\r\n\t\t\t\t\tif ( !describedBy ) {\r\n\t\t\t\t\t\tdescribedBy = errorID;\r\n\t\t\t\t\t} else if ( !describedBy.match( new RegExp( \"\\\\b\" + this.escapeCssMeta( errorID ) + \"\\\\b\" ) ) ) {\r\n\r\n\t\t\t\t\t\t// Add to end of list if not already present\r\n\t\t\t\t\t\tdescribedBy += \" \" + errorID;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t$( element ).attr( \"aria-describedby\", describedBy );\r\n\r\n\t\t\t\t\t// If this element is grouped, then assign to all elements in the same group\r\n\t\t\t\t\tgroup = this.groups[ element.name ];\r\n\t\t\t\t\tif ( group ) {\r\n\t\t\t\t\t\tv = this;\r\n\t\t\t\t\t\t$.each( v.groups, function( name, testgroup ) {\r\n\t\t\t\t\t\t\tif ( testgroup === group ) {\r\n\t\t\t\t\t\t\t\t$( \"[name='\" + v.escapeCssMeta( name ) + \"']\", v.currentForm )\r\n\t\t\t\t\t\t\t\t\t.attr( \"aria-describedby\", error.attr( \"id\" ) );\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} );\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif ( !message && this.settings.success ) {\r\n\t\t\t\terror.text( \"\" );\r\n\t\t\t\tif ( typeof this.settings.success === \"string\" ) {\r\n\t\t\t\t\terror.addClass( this.settings.success );\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.settings.success( error, element );\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.toShow = this.toShow.add( error );\r\n\t\t},\r\n\r\n\t\terrorsFor: function( element ) {\r\n\t\t\tvar name = this.escapeCssMeta( this.idOrName( element ) ),\r\n\t\t\t\tdescriber = $( element ).attr( \"aria-describedby\" ),\r\n\t\t\t\tselector = \"label[for='\" + name + \"'], label[for='\" + name + \"'] *\";\r\n\r\n\t\t\t// 'aria-describedby' should directly reference the error element\r\n\t\t\tif ( describer ) {\r\n\t\t\t\tselector = selector + \", #\" + this.escapeCssMeta( describer )\r\n\t\t\t\t\t.replace( /\\s+/g, \", #\" );\r\n\t\t\t}\r\n\r\n\t\t\treturn this\r\n\t\t\t\t.errors()\r\n\t\t\t\t.filter( selector );\r\n\t\t},\r\n\r\n\t\t// See https://api.jquery.com/category/selectors/, for CSS\r\n\t\t// meta-characters that should be escaped in order to be used with JQuery\r\n\t\t// as a literal part of a name/id or any selector.\r\n\t\tescapeCssMeta: function( string ) {\r\n\t\t\treturn string.replace( /([\\\\!\"#$%&'()*+,./:;<=>?@\\[\\]^`{|}~])/g, \"\\\\$1\" );\r\n\t\t},\r\n\r\n\t\tidOrName: function( element ) {\r\n\t\t\treturn this.groups[ element.name ] || ( this.checkable( element ) ? element.name : element.id || element.name );\r\n\t\t},\r\n\r\n\t\tvalidationTargetFor: function( element ) {\r\n\r\n\t\t\t// If radio/checkbox, validate first element in group instead\r\n\t\t\tif ( this.checkable( element ) ) {\r\n\t\t\t\telement = this.findByName( element.name );\r\n\t\t\t}\r\n\r\n\t\t\t// Always apply ignore filter\r\n\t\t\treturn $( element ).not( this.settings.ignore )[ 0 ];\r\n\t\t},\r\n\r\n\t\tcheckable: function( element ) {\r\n\t\t\treturn ( /radio|checkbox/i ).test( element.type );\r\n\t\t},\r\n\r\n\t\tfindByName: function( name ) {\r\n\t\t\treturn $( this.currentForm ).find( \"[name='\" + this.escapeCssMeta( name ) + \"']\" );\r\n\t\t},\r\n\r\n\t\tgetLength: function( value, element ) {\r\n\t\t\tswitch ( element.nodeName.toLowerCase() ) {\r\n\t\t\tcase \"select\":\r\n\t\t\t\treturn $( \"option:selected\", element ).length;\r\n\t\t\tcase \"input\":\r\n\t\t\t\tif ( this.checkable( element ) ) {\r\n\t\t\t\t\treturn this.findByName( element.name ).filter( \":checked\" ).length;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn value.length;\r\n\t\t},\r\n\r\n\t\tdepend: function( param, element ) {\r\n\t\t\treturn this.dependTypes[ typeof param ] ? this.dependTypes[ typeof param ]( param, element ) : true;\r\n\t\t},\r\n\r\n\t\tdependTypes: {\r\n\t\t\t\"boolean\": function( param ) {\r\n\t\t\t\treturn param;\r\n\t\t\t},\r\n\t\t\t\"string\": function( param, element ) {\r\n\t\t\t\treturn !!$( param, element.form ).length;\r\n\t\t\t},\r\n\t\t\t\"function\": function( param, element ) {\r\n\t\t\t\treturn param( element );\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\toptional: function( element ) {\r\n\t\t\tvar val = this.elementValue( element );\r\n\t\t\treturn !$.validator.methods.required.call( this, val, element ) && \"dependency-mismatch\";\r\n\t\t},\r\n\r\n\t\tstartRequest: function( element ) {\r\n\t\t\tif ( !this.pending[ element.name ] ) {\r\n\t\t\t\tthis.pendingRequest++;\r\n\t\t\t\t$( element ).addClass( this.settings.pendingClass );\r\n\t\t\t\tthis.pending[ element.name ] = true;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tstopRequest: function( element, valid ) {\r\n\t\t\tthis.pendingRequest--;\r\n\r\n\t\t\t// Sometimes synchronization fails, make sure pendingRequest is never < 0\r\n\t\t\tif ( this.pendingRequest < 0 ) {\r\n\t\t\t\tthis.pendingRequest = 0;\r\n\t\t\t}\r\n\t\t\tdelete this.pending[ element.name ];\r\n\t\t\t$( element ).removeClass( this.settings.pendingClass );\r\n\t\t\tif ( valid && this.pendingRequest === 0 && this.formSubmitted && this.form() ) {\r\n\t\t\t\t$( this.currentForm ).submit();\r\n\t\t\t\tthis.formSubmitted = false;\r\n\t\t\t} else if ( !valid && this.pendingRequest === 0 && this.formSubmitted ) {\r\n\t\t\t\t$( this.currentForm ).triggerHandler( \"invalid-form\", [ this ] );\r\n\t\t\t\tthis.formSubmitted = false;\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tpreviousValue: function( element, method ) {\r\n\t\t\treturn $.data( element, \"previousValue\" ) || $.data( element, \"previousValue\", {\r\n\t\t\t\told: null,\r\n\t\t\t\tvalid: true,\r\n\t\t\t\tmessage: this.defaultMessage( element, { method: method } )\r\n\t\t\t} );\r\n\t\t},\r\n\r\n\t\t// Cleans up all forms and elements, removes validator-specific events\r\n\t\tdestroy: function() {\r\n\t\t\tthis.resetForm();\r\n\r\n\t\t\t$( this.currentForm )\r\n\t\t\t\t.off( \".validate\" )\r\n\t\t\t\t.removeData( \"validator\" )\r\n\t\t\t\t.find( \".validate-equalTo-blur\" )\r\n\t\t\t\t\t.off( \".validate-equalTo\" )\r\n\t\t\t\t\t.removeClass( \"validate-equalTo-blur\" );\r\n\t\t}\r\n\r\n\t},\r\n\r\n\tclassRuleSettings: {\r\n\t\trequired: { required: true },\r\n\t\temail: { email: true },\r\n\t\turl: { url: true },\r\n\t\tdate: { date: true },\r\n\t\tdateISO: { dateISO: true },\r\n\t\tnumber: { number: true },\r\n\t\tdigits: { digits: true },\r\n\t\tcreditcard: { creditcard: true }\r\n\t},\r\n\r\n\taddClassRules: function( className, rules ) {\r\n\t\tif ( className.constructor === String ) {\r\n\t\t\tthis.classRuleSettings[ className ] = rules;\r\n\t\t} else {\r\n\t\t\t$.extend( this.classRuleSettings, className );\r\n\t\t}\r\n\t},\r\n\r\n\tclassRules: function( element ) {\r\n\t\tvar rules = {},\r\n\t\t\tclasses = $( element ).attr( \"class\" );\r\n\r\n\t\tif ( classes ) {\r\n\t\t\t$.each( classes.split( \" \" ), function() {\r\n\t\t\t\tif ( this in $.validator.classRuleSettings ) {\r\n\t\t\t\t\t$.extend( rules, $.validator.classRuleSettings[ this ] );\r\n\t\t\t\t}\r\n\t\t\t} );\r\n\t\t}\r\n\t\treturn rules;\r\n\t},\r\n\r\n\tnormalizeAttributeRule: function( rules, type, method, value ) {\r\n\r\n\t\t// Convert the value to a number for number inputs, and for text for backwards compability\r\n\t\t// allows type=\"date\" and others to be compared as strings\r\n\t\tif ( /min|max|step/.test( method ) && ( type === null || /number|range|text/.test( type ) ) ) {\r\n\t\t\tvalue = Number( value );\r\n\r\n\t\t\t// Support Opera Mini, which returns NaN for undefined minlength\r\n\t\t\tif ( isNaN( value ) ) {\r\n\t\t\t\tvalue = undefined;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif ( value || value === 0 ) {\r\n\t\t\trules[ method ] = value;\r\n\t\t} else if ( type === method && type !== \"range\" ) {\r\n\r\n\t\t\t// Exception: the jquery validate 'range' method\r\n\t\t\t// does not test for the html5 'range' type\r\n\t\t\trules[ method ] = true;\r\n\t\t}\r\n\t},\r\n\r\n\tattributeRules: function( element ) {\r\n\t\tvar rules = {},\r\n\t\t\t$element = $( element ),\r\n\t\t\ttype = element.getAttribute( \"type\" ),\r\n\t\t\tmethod, value;\r\n\r\n\t\tfor ( method in $.validator.methods ) {\r\n\r\n\t\t\t// Support for <input required> in both html5 and older browsers\r\n\t\t\tif ( method === \"required\" ) {\r\n\t\t\t\tvalue = element.getAttribute( method );\r\n\r\n\t\t\t\t// Some browsers return an empty string for the required attribute\r\n\t\t\t\t// and non-HTML5 browsers might have required=\"\" markup\r\n\t\t\t\tif ( value === \"\" ) {\r\n\t\t\t\t\tvalue = true;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Force non-HTML5 browsers to return bool\r\n\t\t\t\tvalue = !!value;\r\n\t\t\t} else {\r\n\t\t\t\tvalue = $element.attr( method );\r\n\t\t\t}\r\n\r\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\r\n\t\t}\r\n\r\n\t\t// 'maxlength' may be returned as -1, 2147483647 ( IE ) and 524288 ( safari ) for text inputs\r\n\t\tif ( rules.maxlength && /-1|2147483647|524288/.test( rules.maxlength ) ) {\r\n\t\t\tdelete rules.maxlength;\r\n\t\t}\r\n\r\n\t\treturn rules;\r\n\t},\r\n\r\n\tdataRules: function( element ) {\r\n\t\tvar rules = {},\r\n\t\t\t$element = $( element ),\r\n\t\t\ttype = element.getAttribute( \"type\" ),\r\n\t\t\tmethod, value;\r\n\r\n\t\tfor ( method in $.validator.methods ) {\r\n\t\t\tvalue = $element.data( \"rule\" + method.charAt( 0 ).toUpperCase() + method.substring( 1 ).toLowerCase() );\r\n\t\t\tthis.normalizeAttributeRule( rules, type, method, value );\r\n\t\t}\r\n\t\treturn rules;\r\n\t},\r\n\r\n\tstaticRules: function( element ) {\r\n\t\tvar rules = {},\r\n\t\t\tvalidator = $.data( element.form, \"validator\" );\r\n\r\n\t\tif ( validator.settings.rules ) {\r\n\t\t\trules = $.validator.normalizeRule( validator.settings.rules[ element.name ] ) || {};\r\n\t\t}\r\n\t\treturn rules;\r\n\t},\r\n\r\n\tnormalizeRules: function( rules, element ) {\r\n\r\n\t\t// Handle dependency check\r\n\t\t$.each( rules, function( prop, val ) {\r\n\r\n\t\t\t// Ignore rule when param is explicitly false, eg. required:false\r\n\t\t\tif ( val === false ) {\r\n\t\t\t\tdelete rules[ prop ];\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif ( val.param || val.depends ) {\r\n\t\t\t\tvar keepRule = true;\r\n\t\t\t\tswitch ( typeof val.depends ) {\r\n\t\t\t\tcase \"string\":\r\n\t\t\t\t\tkeepRule = !!$( val.depends, element.form ).length;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"function\":\r\n\t\t\t\t\tkeepRule = val.depends.call( element, element );\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tif ( keepRule ) {\r\n\t\t\t\t\trules[ prop ] = val.param !== undefined ? val.param : true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t$.data( element.form, \"validator\" ).resetElements( $( element ) );\r\n\t\t\t\t\tdelete rules[ prop ];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} );\r\n\r\n\t\t// Evaluate parameters\r\n\t\t$.each( rules, function( rule, parameter ) {\r\n\t\t\trules[ rule ] = $.isFunction( parameter ) && rule !== \"normalizer\" ? parameter( element ) : parameter;\r\n\t\t} );\r\n\r\n\t\t// Clean number parameters\r\n\t\t$.each( [ \"minlength\", \"maxlength\" ], function() {\r\n\t\t\tif ( rules[ this ] ) {\r\n\t\t\t\trules[ this ] = Number( rules[ this ] );\r\n\t\t\t}\r\n\t\t} );\r\n\t\t$.each( [ \"rangelength\", \"range\" ], function() {\r\n\t\t\tvar parts;\r\n\t\t\tif ( rules[ this ] ) {\r\n\t\t\t\tif ( $.isArray( rules[ this ] ) ) {\r\n\t\t\t\t\trules[ this ] = [ Number( rules[ this ][ 0 ] ), Number( rules[ this ][ 1 ] ) ];\r\n\t\t\t\t} else if ( typeof rules[ this ] === \"string\" ) {\r\n\t\t\t\t\tparts = rules[ this ].replace( /[\\[\\]]/g, \"\" ).split( /[\\s,]+/ );\r\n\t\t\t\t\trules[ this ] = [ Number( parts[ 0 ] ), Number( parts[ 1 ] ) ];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t} );\r\n\r\n\t\tif ( $.validator.autoCreateRanges ) {\r\n\r\n\t\t\t// Auto-create ranges\r\n\t\t\tif ( rules.min != null && rules.max != null ) {\r\n\t\t\t\trules.range = [ rules.min, rules.max ];\r\n\t\t\t\tdelete rules.min;\r\n\t\t\t\tdelete rules.max;\r\n\t\t\t}\r\n\t\t\tif ( rules.minlength != null && rules.maxlength != null ) {\r\n\t\t\t\trules.rangelength = [ rules.minlength, rules.maxlength ];\r\n\t\t\t\tdelete rules.minlength;\r\n\t\t\t\tdelete rules.maxlength;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn rules;\r\n\t},\r\n\r\n\t// Converts a simple string to a {string: true} rule, e.g., \"required\" to {required:true}\r\n\tnormalizeRule: function( data ) {\r\n\t\tif ( typeof data === \"string\" ) {\r\n\t\t\tvar transformed = {};\r\n\t\t\t$.each( data.split( /\\s/ ), function() {\r\n\t\t\t\ttransformed[ this ] = true;\r\n\t\t\t} );\r\n\t\t\tdata = transformed;\r\n\t\t}\r\n\t\treturn data;\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/jQuery.validator.addMethod/\r\n\taddMethod: function( name, method, message ) {\r\n\t\t$.validator.methods[ name ] = method;\r\n\t\t$.validator.messages[ name ] = message !== undefined ? message : $.validator.messages[ name ];\r\n\t\tif ( method.length < 3 ) {\r\n\t\t\t$.validator.addClassRules( name, $.validator.normalizeRule( name ) );\r\n\t\t}\r\n\t},\r\n\r\n\t// http://jqueryvalidation.org/jQuery.validator.methods/\r\n\tmethods: {\r\n\r\n\t\t// http://jqueryvalidation.org/required-method/\r\n\t\trequired: function( value, element, param ) {\r\n\r\n\t\t\t// Check if dependency is met\r\n\t\t\tif ( !this.depend( param, element ) ) {\r\n\t\t\t\treturn \"dependency-mismatch\";\r\n\t\t\t}\r\n\t\t\tif ( element.nodeName.toLowerCase() === \"select\" ) {\r\n\r\n\t\t\t\t// Could be an array for select-multiple or a string, both are fine this way\r\n\t\t\t\tvar val = $( element ).val();\r\n\t\t\t\treturn val && val.length > 0;\r\n\t\t\t}\r\n\t\t\tif ( this.checkable( element ) ) {\r\n\t\t\t\treturn this.getLength( value, element ) > 0;\r\n\t\t\t}\r\n\t\t\treturn value.length > 0;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/email-method/\r\n\t\temail: function( value, element ) {\r\n\r\n\t\t\t// From https://html.spec.whatwg.org/multipage/forms.html#valid-e-mail-address\r\n\t\t\t// Retrieved 2014-01-14\r\n\t\t\t// If you have a problem with this implementation, report a bug against the above spec\r\n\t\t\t// Or use custom methods to implement your own email validation\r\n\t\t\treturn this.optional( element ) || /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test( value );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/url-method/\r\n\t\turl: function( value, element ) {\r\n\r\n\t\t\t// Copyright (c) 2010-2013 Diego Perini, MIT licensed\r\n\t\t\t// https://gist.github.com/dperini/729294\r\n\t\t\t// see also https://mathiasbynens.be/demo/url-regex\r\n\t\t\t// modified to allow protocol-relative URLs\r\n\t\t\treturn this.optional( element ) || /^(?:(?:(?:https?|ftp):)?\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})).?)(?::\\d{2,5})?(?:[/?#]\\S*)?$/i.test( value );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/date-method/\r\n\t\tdate: function( value, element ) {\r\n\t\t\treturn this.optional( element ) || !/Invalid|NaN/.test( new Date( value ).toString() );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/dateISO-method/\r\n\t\tdateISO: function( value, element ) {\r\n\t\t\treturn this.optional( element ) || /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test( value );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/number-method/\r\n\t\tnumber: function( value, element ) {\r\n\t\t\treturn this.optional( element ) || /^(?:-?\\d+|-?\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$/.test( value );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/digits-method/\r\n\t\tdigits: function( value, element ) {\r\n\t\t\treturn this.optional( element ) || /^\\d+$/.test( value );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/minlength-method/\r\n\t\tminlength: function( value, element, param ) {\r\n\t\t\tvar length = $.isArray( value ) ? value.length : this.getLength( value, element );\r\n\t\t\treturn this.optional( element ) || length >= param;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/maxlength-method/\r\n\t\tmaxlength: function( value, element, param ) {\r\n\t\t\tvar length = $.isArray( value ) ? value.length : this.getLength( value, element );\r\n\t\t\treturn this.optional( element ) || length <= param;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/rangelength-method/\r\n\t\trangelength: function( value, element, param ) {\r\n\t\t\tvar length = $.isArray( value ) ? value.length : this.getLength( value, element );\r\n\t\t\treturn this.optional( element ) || ( length >= param[ 0 ] && length <= param[ 1 ] );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/min-method/\r\n\t\tmin: function( value, element, param ) {\r\n\t\t\treturn this.optional( element ) || value >= param;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/max-method/\r\n\t\tmax: function( value, element, param ) {\r\n\t\t\treturn this.optional( element ) || value <= param;\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/range-method/\r\n\t\trange: function( value, element, param ) {\r\n\t\t\treturn this.optional( element ) || ( value >= param[ 0 ] && value <= param[ 1 ] );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/step-method/\r\n\t\tstep: function( value, element, param ) {\r\n\t\t\tvar type = $( element ).attr( \"type\" ),\r\n\t\t\t\terrorMessage = \"Step attribute on input type \" + type + \" is not supported.\",\r\n\t\t\t\tsupportedTypes = [ \"text\", \"number\", \"range\" ],\r\n\t\t\t\tre = new RegExp( \"\\\\b\" + type + \"\\\\b\" ),\r\n\t\t\t\tnotSupported = type && !re.test( supportedTypes.join() );\r\n\r\n\t\t\t// Works only for text, number and range input types\r\n\t\t\t// TODO find a way to support input types date, datetime, datetime-local, month, time and week\r\n\t\t\tif ( notSupported ) {\r\n\t\t\t\tthrow new Error( errorMessage );\r\n\t\t\t}\r\n\t\t\treturn this.optional( element ) || ( value % param === 0 );\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/equalTo-method/\r\n\t\tequalTo: function( value, element, param ) {\r\n\r\n\t\t\t// Bind to the blur event of the target in order to revalidate whenever the target field is updated\r\n\t\t\tvar target = $( param );\r\n\t\t\tif ( this.settings.onfocusout && target.not( \".validate-equalTo-blur\" ).length ) {\r\n\t\t\t\ttarget.addClass( \"validate-equalTo-blur\" ).on( \"blur.validate-equalTo\", function() {\r\n\t\t\t\t\t$( element ).valid();\r\n\t\t\t\t} );\r\n\t\t\t}\r\n\t\t\treturn value === target.val();\r\n\t\t},\r\n\r\n\t\t// http://jqueryvalidation.org/remote-method/\r\n\t\tremote: function( value, element, param, method ) {\r\n\t\t\tif ( this.optional( element ) ) {\r\n\t\t\t\treturn \"dependency-mismatch\";\r\n\t\t\t}\r\n\r\n\t\t\tmethod = typeof method === \"string\" && method || \"remote\";\r\n\r\n\t\t\tvar previous = this.previousValue( element, method ),\r\n\t\t\t\tvalidator, data, optionDataString;\r\n\r\n\t\t\tif ( !this.settings.messages[ element.name ] ) {\r\n\t\t\t\tthis.settings.messages[ element.name ] = {};\r\n\t\t\t}\r\n\t\t\tprevious.originalMessage = previous.originalMessage || this.settings.messages[ element.name ][ method ];\r\n\t\t\tthis.settings.messages[ element.name ][ method ] = previous.message;\r\n\r\n\t\t\tparam = typeof param === \"string\" && { url: param } || param;\r\n\t\t\toptionDataString = $.param( $.extend( { data: value }, param.data ) );\r\n\t\t\tif ( previous.old === optionDataString ) {\r\n\t\t\t\treturn previous.valid;\r\n\t\t\t}\r\n\r\n\t\t\tprevious.old = optionDataString;\r\n\t\t\tvalidator = this;\r\n\t\t\tthis.startRequest( element );\r\n\t\t\tdata = {};\r\n\t\t\tdata[ element.name ] = value;\r\n\t\t\t$.ajax( $.extend( true, {\r\n\t\t\t\tmode: \"abort\",\r\n\t\t\t\tport: \"validate\" + element.name,\r\n\t\t\t\tdataType: \"json\",\r\n\t\t\t\tdata: data,\r\n\t\t\t\tcontext: validator.currentForm,\r\n\t\t\t\tsuccess: function( response ) {\r\n\t\t\t\t\tvar valid = response === true || response === \"true\",\r\n\t\t\t\t\t\terrors, message, submitted;\r\n\r\n\t\t\t\t\tvalidator.settings.messages[ element.name ][ method ] = previous.originalMessage;\r\n\t\t\t\t\tif ( valid ) {\r\n\t\t\t\t\t\tsubmitted = validator.formSubmitted;\r\n\t\t\t\t\t\tvalidator.resetInternals();\r\n\t\t\t\t\t\tvalidator.toHide = validator.errorsFor( element );\r\n\t\t\t\t\t\tvalidator.formSubmitted = submitted;\r\n\t\t\t\t\t\tvalidator.successList.push( element );\r\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = false;\r\n\t\t\t\t\t\tvalidator.showErrors();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\terrors = {};\r\n\t\t\t\t\t\tmessage = response || validator.defaultMessage( element, { method: method, parameters: value } );\r\n\t\t\t\t\t\terrors[ element.name ] = previous.message = message;\r\n\t\t\t\t\t\tvalidator.invalid[ element.name ] = true;\r\n\t\t\t\t\t\tvalidator.showErrors( errors );\r\n\t\t\t\t\t}\r\n\t\t\t\t\tprevious.valid = valid;\r\n\t\t\t\t\tvalidator.stopRequest( element, valid );\r\n\t\t\t\t}\r\n\t\t\t}, param ) );\r\n\t\t\treturn \"pending\";\r\n\t\t}\r\n\t}\r\n\r\n} );\r\n\r\n// Ajax mode: abort\r\n// usage: $.ajax({ mode: \"abort\"[, port: \"uniqueport\"]});\r\n// if mode:\"abort\" is used, the previous request on that port (port can be undefined) is aborted via XMLHttpRequest.abort()\r\n\r\nvar pendingRequests = {},\r\n\tajax;\r\n\r\n// Use a prefilter if available (1.5+)\r\nif ( $.ajaxPrefilter ) {\r\n\t$.ajaxPrefilter( function( settings, _, xhr ) {\r\n\t\tvar port = settings.port;\r\n\t\tif ( settings.mode === \"abort\" ) {\r\n\t\t\tif ( pendingRequests[ port ] ) {\r\n\t\t\t\tpendingRequests[ port ].abort();\r\n\t\t\t}\r\n\t\t\tpendingRequests[ port ] = xhr;\r\n\t\t}\r\n\t} );\r\n} else {\r\n\r\n\t// Proxy ajax\r\n\tajax = $.ajax;\r\n\t$.ajax = function( settings ) {\r\n\t\tvar mode = ( \"mode\" in settings ? settings : $.ajaxSettings ).mode,\r\n\t\t\tport = ( \"port\" in settings ? settings : $.ajaxSettings ).port;\r\n\t\tif ( mode === \"abort\" ) {\r\n\t\t\tif ( pendingRequests[ port ] ) {\r\n\t\t\t\tpendingRequests[ port ].abort();\r\n\t\t\t}\r\n\t\t\tpendingRequests[ port ] = ajax.apply( this, arguments );\r\n\t\t\treturn pendingRequests[ port ];\r\n\t\t}\r\n\t\treturn ajax.apply( this, arguments );\r\n\t};\r\n}\r\n\r\n}));"], "names": ["factory", "define", "amd", "module", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "extend", "fn", "validate", "options", "validator", "this", "length", "data", "attr", "settings", "onsubmit", "on", "event", "<PERSON><PERSON><PERSON><PERSON>", "submitButton", "target", "hasClass", "cancelSubmit", "undefined", "handle", "hidden", "result", "name", "val", "appendTo", "currentForm", "call", "remove", "debug", "preventDefault", "form", "pendingRequest", "formSubmitted", "focusInvalid", "window", "console", "warn", "valid", "errorList", "is", "each", "element", "concat", "rules", "command", "argument", "staticRules", "existingRules", "param", "filtered", "normalizeRule", "messages", "split", "index", "method", "removeAttr", "normalizeRules", "classRules", "attributeRules", "dataRules", "required", "remote", "expr", "blank", "a", "trim", "filled", "unchecked", "prop", "defaults", "init", "format", "source", "params", "arguments", "args", "makeArray", "unshift", "apply", "constructor", "Array", "slice", "i", "n", "replace", "RegExp", "groups", "errorClass", "pendingClass", "validClass", "errorElement", "focusCleanup", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignore", "ignoreTitle", "onfocusin", "lastActive", "unhighlight", "hideThese", "errorsFor", "onfocusout", "checkable", "submitted", "optional", "onkeyup", "which", "elementValue", "inArray", "keyCode", "invalid", "onclick", "parentNode", "highlight", "type", "findByName", "addClass", "removeClass", "setDefaults", "email", "url", "date", "dateISO", "number", "digits", "equalTo", "maxlength", "minlength", "rangelength", "range", "max", "min", "step", "autoCreateRanges", "prototype", "labelContainer", "errorContext", "containers", "add", "valueCache", "pending", "reset", "delegate", "eventType", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "find", "checkForm", "errorMap", "<PERSON><PERSON><PERSON><PERSON>", "showErrors", "prepareForm", "elements", "currentElements", "check", "rs", "group", "cleanElement", "clean", "checkElement", "validationTargetFor", "v", "prepareElement", "testgroup", "push", "numberOfInvalids", "toHide", "errors", "map", "message", "successList", "grep", "defaultShowErrors", "resetForm", "hideErrors", "removeData", "resetElements", "objectLength", "obj", "count", "not", "text", "addWrapper", "hide", "size", "findLastActive", "filter", "focus", "trigger", "e", "rulesCache", "error", "hasAttribute", "closest", "selector", "join", "resetInternals", "toShow", "$element", "validity", "badInput", "substr", "idx", "lastIndexOf", "rule", "rulesCount", "dependencyMismatch", "normalizer", "TypeError", "parameters", "methods", "formatAndAdd", "log", "id", "customDataMessage", "char<PERSON>t", "toUpperCase", "substring", "toLowerCase", "customMessage", "m", "String", "findDefined", "defaultMessage", "title", "theregex", "test", "toToggle", "wrapper", "parent", "showLabel", "success", "validElements", "show", "invalidElements", "errorID", "elementID", "idOrName", "describedBy", "html", "place", "wrap", "append", "errorPlacement", "insertAfter", "parents", "escapeCssMeta", "match", "describer", "string", "<PERSON><PERSON><PERSON><PERSON>", "nodeName", "depend", "dependTypes", "boolean", "function", "startRequest", "stopRequest", "submit", "previousValue", "old", "destroy", "off", "classRuleSettings", "creditcard", "addClassRules", "className", "classes", "normalizeAttributeRule", "Number", "isNaN", "getAttribute", "depends", "keepRule", "parameter", "isFunction", "parts", "isArray", "transformed", "addMethod", "Date", "toString", "errorMessage", "re", "Error", "optionDataString", "previous", "originalMessage", "ajax", "mode", "port", "dataType", "context", "response", "pendingRequests", "ajaxPrefilter", "_", "xhr", "abort", "ajaxSettings"], "mappings": "AAQA,CAAC,SAAUA,GACa,YAAlB,OAAOC,QAAyBA,OAAOC,IAC3CD,OAAQ,CAAC,UAAWD,CAAQ,EACA,UAAlB,OAAOG,QAAuBA,OAAOC,QAC/CD,OAAOC,QAAUJ,EAASK,QAAS,QAAS,CAAE,EAE9CL,EAASM,MAAO,CAElB,EAAE,SAAUC,GAEZA,EAAEC,OAAQD,EAAEE,GAAI,CAGfC,SAAU,SAAUC,GAGnB,IAQIC,EARJ,GAAMC,KAAKC,OAuFX,OA/EIF,EAAYL,EAAEQ,KAAMF,KAAM,GAAK,WAAY,KAM/CA,KAAKG,KAAM,aAAc,YAAa,EAEtCJ,EAAY,IAAIL,EAAEK,UAAWD,EAASE,KAAM,EAAI,EAChDN,EAAEQ,KAAMF,KAAM,GAAK,YAAaD,CAAU,EAErCA,EAAUK,SAASC,WAEvBL,KAAKM,GAAI,iBAAkB,UAAW,SAAUC,GAC1CR,EAAUK,SAASI,gBACvBT,EAAUU,aAAeF,EAAMG,QAI3BhB,EAAGM,IAAK,EAAEW,SAAU,QAAS,IACjCZ,EAAUa,aAAe,CAAA,GAIkBC,KAAAA,IAAvCnB,EAAGM,IAAK,EAAEG,KAAM,gBAAiB,IACrCJ,EAAUa,aAAe,CAAA,EAE3B,CAAE,EAGFZ,KAAKM,GAAI,kBAAmB,SAAUC,GAMrC,SAASO,IACR,IAAIC,EAAQC,EACZ,MAAKjB,CAAAA,EAAUK,SAASI,gBAClBT,EAAUU,eAGdM,EAASrB,EAAG,wBAAyB,EACnCS,KAAM,OAAQJ,EAAUU,aAAaQ,IAAK,EAC1CC,IAAKxB,EAAGK,EAAUU,YAAa,EAAES,IAAI,CAAE,EACvCC,SAAUpB,EAAUqB,WAAY,GAEnCJ,EAASjB,EAAUK,SAASI,cAAca,KAAMtB,EAAWA,EAAUqB,YAAab,CAAM,EACnFR,EAAUU,cAGdM,EAAOO,OAAO,EAECT,KAAAA,IAAXG,GACGA,EAKV,CAGA,OA/BKjB,EAAUK,SAASmB,OAGvBhB,EAAMiB,eAAe,EA4BjBzB,EAAUa,cACdb,EAAUa,aAAe,CAAA,EAClBE,EAAO,GAEVf,EAAU0B,KAAK,EACd1B,EAAU2B,eAEP,EADP3B,EAAU4B,cAAgB,CAAA,GAGpBb,EAAO,GAEdf,EAAU6B,aAAa,EAChB,CAAA,EAET,CAAE,IAGI7B,EAtFDD,GAAWA,EAAQyB,OAASM,OAAOC,SACvCA,QAAQC,KAAM,sDAAuD,CAsFxE,EAGAC,MAAO,WACN,IAAIA,EAAOjC,EAAWkC,EAgBtB,OAdKvC,EAAGM,KAAM,EAAI,EAAEkC,GAAI,MAAO,EAC9BF,EAAQhC,KAAKH,SAAS,EAAE4B,KAAK,GAE7BQ,EAAY,GACZD,EAAQ,CAAA,EACRjC,EAAYL,EAAGM,KAAM,GAAIyB,IAAK,EAAE5B,SAAS,EACzCG,KAAKmC,KAAM,YACVH,EAAQjC,EAAUqC,QAASpC,IAAK,GAAKgC,KAEpCC,EAAYA,EAAUI,OAAQtC,EAAUkC,SAAU,EAEpD,CAAE,EACFlC,EAAUkC,UAAYA,GAEhBD,CACR,EAGAM,MAAO,SAAUC,EAASC,GAGzB,GAAMxC,KAAKC,OAAX,CAIA,IACCG,EAAUqC,EAAaC,EAAqBC,EAAOC,EADhDR,EAAUpC,KAAM,GAGpB,GAAKuC,EAIJ,OAFAE,GADArC,EAAWV,EAAEQ,KAAMkC,EAAQX,KAAM,WAAY,EAAErB,UACxBkC,MACvBI,EAAgBhD,EAAEK,UAAU0C,YAAaL,CAAQ,EACxCG,GACT,IAAK,MACJ7C,EAAEC,OAAQ+C,EAAehD,EAAEK,UAAU8C,cAAeL,CAAS,CAAE,EAG/D,OAAOE,EAAcI,SACrBL,EAAaL,EAAQnB,MAASyB,EACzBF,EAASM,WACb1C,EAAS0C,SAAUV,EAAQnB,MAASvB,EAAEC,OAAQS,EAAS0C,SAAUV,EAAQnB,MAAQuB,EAASM,QAAS,GAEpG,MACD,IAAK,SACJ,OAAMN,GAINI,EAAW,GACXlD,EAAEyC,KAAMK,EAASO,MAAO,IAAK,EAAG,SAAUC,EAAOC,GAChDL,EAAUK,GAAWP,EAAeO,GACpC,OAAOP,EAAeO,GACN,aAAXA,GACJvD,EAAG0C,CAAQ,EAAEc,WAAY,eAAgB,CAE3C,CAAE,EACKN,IAXN,OAAOH,EAAaL,EAAQnB,MACrByB,EAWT,CA2BD,OAxBAxC,EAAOR,EAAEK,UAAUoD,eACnBzD,EAAEC,OACD,GACAD,EAAEK,UAAUqD,WAAYhB,CAAQ,EAChC1C,EAAEK,UAAUsD,eAAgBjB,CAAQ,EACpC1C,EAAEK,UAAUuD,UAAWlB,CAAQ,EAC/B1C,EAAEK,UAAU0C,YAAaL,CAAQ,CAClC,EAAGA,CAAQ,GAGDmB,WACTZ,EAAQzC,EAAKqD,SACb,OAAOrD,EAAKqD,SACZrD,EAAOR,EAAEC,OAAQ,CAAE4D,SAAUZ,CAAM,EAAGzC,CAAK,EAC3CR,EAAG0C,CAAQ,EAAEjC,KAAM,gBAAiB,MAAO,GAIvCD,EAAKsD,SACTb,EAAQzC,EAAKsD,OACb,OAAOtD,EAAKsD,OACZtD,EAAOR,EAAEC,OAAQO,EAAM,CAAEsD,OAAQb,CAAM,CAAE,GAGnCzC,CA7DP,CA8DD,CACD,CAAE,EAGFR,EAAEC,OAAQD,EAAE+D,KAAM,KAAO,CAGxBC,MAAO,SAAUC,GAChB,MAAO,CAACjE,EAAEkE,KAAM,GAAKlE,EAAGiE,CAAE,EAAEzC,IAAI,CAAE,CACnC,EAGA2C,OAAQ,SAAUF,GACbzC,EAAMxB,EAAGiE,CAAE,EAAEzC,IAAI,EACrB,OAAe,OAARA,GAAgB,CAAC,CAACxB,EAAEkE,KAAM,GAAK1C,CAAI,CAC3C,EAGA4C,UAAW,SAAUH,GACpB,MAAO,CAACjE,EAAGiE,CAAE,EAAEI,KAAM,SAAU,CAChC,CACD,CAAE,EAGFrE,EAAEK,UAAY,SAAUD,EAAS2B,GAChCzB,KAAKI,SAAWV,EAAEC,OAAQ,CAAA,EAAM,GAAID,EAAEK,UAAUiE,SAAUlE,CAAQ,EAClEE,KAAKoB,YAAcK,EACnBzB,KAAKiE,KAAK,CACX,EAGAvE,EAAEK,UAAUmE,OAAS,SAAUC,EAAQC,GACtC,OAA0B,IAArBC,UAAUpE,OACP,WACN,IAAIqE,EAAO5E,EAAE6E,UAAWF,SAAU,EAElC,OADAC,EAAKE,QAASL,CAAO,EACdzE,EAAEK,UAAUmE,OAAOO,MAAOzE,KAAMsE,CAAK,CAC7C,GAEezD,KAAAA,IAAXuD,KAIJA,EADuB,EAAnBC,UAAUpE,QAAcmE,EAAOM,cAAgBC,MAC1CjF,EAAE6E,UAAWF,SAAU,EAAEO,MAAO,CAAE,EAEvCR,GAAOM,cAAgBC,QAC3BP,EAAS,CAAEA,IAEZ1E,EAAEyC,KAAMiC,EAAQ,SAAUS,EAAGC,GAC5BX,EAASA,EAAOY,QAAS,IAAIC,OAAQ,MAAQH,EAAI,MAAO,GAAI,EAAG,WAC9D,OAAOC,CACR,CAAE,CACH,CAAE,GACKX,EACR,EAEAzE,EAAEC,OAAQD,EAAEK,UAAW,CAEtBiE,SAAU,CACTlB,SAAU,GACVmC,OAAQ,GACR3C,MAAO,GACP4C,WAAY,QACZC,aAAc,UACdC,WAAY,QACZC,aAAc,QACdC,aAAc,CAAA,EACd1D,aAAc,CAAA,EACd2D,eAAgB7F,EAAG,EAAG,EACtB8F,oBAAqB9F,EAAG,EAAG,EAC3BW,SAAU,CAAA,EACVoF,OAAQ,UACRC,YAAa,CAAA,EACbC,UAAW,SAAUvD,GACpBpC,KAAK4F,WAAaxD,EAGbpC,KAAKI,SAASkF,eACbtF,KAAKI,SAASyF,aAClB7F,KAAKI,SAASyF,YAAYxE,KAAMrB,KAAMoC,EAASpC,KAAKI,SAAS8E,WAAYlF,KAAKI,SAASgF,UAAW,EAEnGpF,KAAK8F,UAAW9F,KAAK+F,UAAW3D,CAAQ,CAAE,EAE5C,EACA4D,WAAY,SAAU5D,GACfpC,KAAKiG,UAAW7D,CAAQ,GAAOA,EAAAA,EAAQnB,QAAQjB,KAAKkG,YAAclG,KAAKmG,SAAU/D,CAAQ,GAC9FpC,KAAKoC,QAASA,CAAQ,CAExB,EACAgE,QAAS,SAAUhE,EAAS7B,GAqBN,IAAhBA,EAAM8F,OAAgD,KAAjCrG,KAAKsG,aAAclE,CAAQ,GAAyD,CAAC,IAA9C1C,EAAE6G,QAAShG,EAAMiG,QAL/D,CAClB,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GACxB,GAAI,GAAI,GAAI,GAAI,IAAK,IAGkF,IAE5FpE,EAAQnB,QAAQjB,KAAKkG,WAAa9D,EAAQnB,QAAQjB,KAAKyG,UAClEzG,KAAKoC,QAASA,CAAQ,CAExB,EACAsE,QAAS,SAAUtE,GAGbA,EAAQnB,QAAQjB,KAAKkG,UACzBlG,KAAKoC,QAASA,CAAQ,EAGXA,EAAQuE,WAAW1F,QAAQjB,KAAKkG,WAC3ClG,KAAKoC,QAASA,EAAQuE,UAAW,CAEnC,EACAC,UAAW,SAAUxE,EAAS8C,EAAYE,IACnB,UAAjBhD,EAAQyE,KACZ7G,KAAK8G,WAAY1E,EAAQnB,IAAK,EAE9BvB,EAAG0C,CAAQ,GAFqB2E,SAAU7B,CAAW,EAAE8B,YAAa5B,CAAW,CAIjF,EACAS,YAAa,SAAUzD,EAAS8C,EAAYE,IACrB,UAAjBhD,EAAQyE,KACZ7G,KAAK8G,WAAY1E,EAAQnB,IAAK,EAE9BvB,EAAG0C,CAAQ,GAFqB4E,YAAa9B,CAAW,EAAE6B,SAAU3B,CAAW,CAIjF,CACD,EAGA6B,YAAa,SAAU7G,GACtBV,EAAEC,OAAQD,EAAEK,UAAUiE,SAAU5D,CAAS,CAC1C,EAEA0C,SAAU,CACTS,SAAU,0BACVC,OAAQ,yBACR0D,MAAO,sCACPC,IAAK,4BACLC,KAAM,6BACNC,QAAS,qCACTC,OAAQ,+BACRC,OAAQ,4BACRC,QAAS,qCACTC,UAAW/H,EAAEK,UAAUmE,OAAQ,2CAA4C,EAC3EwD,UAAWhI,EAAEK,UAAUmE,OAAQ,uCAAwC,EACvEyD,YAAajI,EAAEK,UAAUmE,OAAQ,2DAA4D,EAC7F0D,MAAOlI,EAAEK,UAAUmE,OAAQ,2CAA4C,EACvE2D,IAAKnI,EAAEK,UAAUmE,OAAQ,iDAAkD,EAC3E4D,IAAKpI,EAAEK,UAAUmE,OAAQ,oDAAqD,EAC9E6D,KAAMrI,EAAEK,UAAUmE,OAAQ,iCAAkC,CAC7D,EAEA8D,iBAAkB,CAAA,EAElBC,UAAW,CAEVhE,KAAM,WACLjE,KAAKkI,eAAiBxI,EAAGM,KAAKI,SAASoF,mBAAoB,EAC3DxF,KAAKmI,aAAenI,KAAKkI,eAAejI,QAAUD,KAAKkI,gBAAkBxI,EAAGM,KAAKoB,WAAY,EAC7FpB,KAAKoI,WAAa1I,EAAGM,KAAKI,SAASmF,cAAe,EAAE8C,IAAKrI,KAAKI,SAASoF,mBAAoB,EAC3FxF,KAAKkG,UAAY,GACjBlG,KAAKsI,WAAa,GAClBtI,KAAK0B,eAAiB,EACtB1B,KAAKuI,QAAU,GACfvI,KAAKyG,QAAU,GACfzG,KAAKwI,MAAM,EAEX,IACClG,EADG2C,EAAWjF,KAAKiF,OAAS,GAe7B,SAASwD,EAAUlI,GAClB,IAAIR,EAAYL,EAAEQ,KAAMF,KAAKyB,KAAM,WAAY,EAC9CiH,EAAY,KAAOnI,EAAMsG,KAAK9B,QAAS,YAAa,EAAG,EACvD3E,EAAWL,EAAUK,SACjBA,EAAUsI,IAAe,CAAChJ,EAAGM,IAAK,EAAEkC,GAAI9B,EAASqF,MAAO,GAC5DrF,EAAUsI,GAAYrH,KAAMtB,EAAWC,KAAMO,CAAM,CAErD,CApBAb,EAAEyC,KAAMnC,KAAKI,SAAS6E,OAAQ,SAAU0D,EAAKC,GACtB,UAAjB,OAAOA,IACXA,EAAQA,EAAM7F,MAAO,IAAK,GAE3BrD,EAAEyC,KAAMyG,EAAO,SAAU5F,EAAO/B,GAC/BgE,EAAQhE,GAAS0H,CAClB,CAAE,CACH,CAAE,EACFrG,EAAQtC,KAAKI,SAASkC,MACtB5C,EAAEyC,KAAMG,EAAO,SAAUqG,EAAKC,GAC7BtG,EAAOqG,GAAQjJ,EAAEK,UAAU8C,cAAe+F,CAAM,CACjD,CAAE,EAWFlJ,EAAGM,KAAKoB,WAAY,EAClBd,GAAI,oDACJ,yUAGwDmI,CAAS,EAIjEnI,GAAI,iBAAkB,oDAAqDmI,CAAS,EAEjFzI,KAAKI,SAASyI,gBAClBnJ,EAAGM,KAAKoB,WAAY,EAAEd,GAAI,wBAAyBN,KAAKI,SAASyI,cAAe,EAKjFnJ,EAAGM,KAAKoB,WAAY,EAAE0H,KAAM,6CAA8C,EAAE3I,KAAM,gBAAiB,MAAO,CAC3G,EAGAsB,KAAM,WAQL,OAPAzB,KAAK+I,UAAU,EACfrJ,EAAEC,OAAQK,KAAKkG,UAAWlG,KAAKgJ,QAAS,EACxChJ,KAAKyG,QAAU/G,EAAEC,OAAQ,GAAIK,KAAKgJ,QAAS,EACrChJ,KAAKgC,MAAM,GAChBtC,EAAGM,KAAKoB,WAAY,EAAE6H,eAAgB,eAAgB,CAAEjJ,KAAO,EAEhEA,KAAKkJ,WAAW,EACTlJ,KAAKgC,MAAM,CACnB,EAEA+G,UAAW,WACV/I,KAAKmJ,YAAY,EACjB,IAAM,IAAItE,EAAI,EAAGuE,EAAapJ,KAAKqJ,gBAAkBrJ,KAAKoJ,SAAS,EAAKA,EAAUvE,GAAKA,CAAC,GACvF7E,KAAKsJ,MAAOF,EAAUvE,EAAI,EAE3B,OAAO7E,KAAKgC,MAAM,CACnB,EAGAI,QAAS,SAAUA,GAClB,IAICmH,EAAIC,EAJDC,EAAezJ,KAAK0J,MAAOtH,CAAQ,EACtCuH,EAAe3J,KAAK4J,oBAAqBH,CAAa,EACtDI,EAAI7J,KACJgB,EAAS,CAAA,EA2CV,OAxCsBH,KAAAA,IAAjB8I,EACJ,OAAO3J,KAAKyG,QAASgD,EAAaxI,OAElCjB,KAAK8J,eAAgBH,CAAa,EAClC3J,KAAKqJ,gBAAkB3J,EAAGiK,CAAa,GAIvCH,EAAQxJ,KAAKiF,OAAQ0E,EAAa1I,QAEjCvB,EAAEyC,KAAMnC,KAAKiF,OAAQ,SAAUhE,EAAM8I,GAC/BA,IAAcP,GAASvI,IAAS0I,EAAa1I,OACjDwI,EAAeI,EAAED,oBAAqBC,EAAEH,MAAOG,EAAE/C,WAAY7F,CAAK,CAAE,CAAE,IACjDwI,EAAaxI,QAAQ4I,EAAEpD,UAC3CoD,EAAER,gBAAgBW,KAAMP,CAAa,EACrCzI,EAASA,GAAU6I,EAAEP,MAAOG,CAAa,EAG5C,CAAE,EAGHF,EAAoC,CAAA,IAA/BvJ,KAAKsJ,MAAOK,CAAa,EAC9B3I,EAASA,GAAUuI,EAElBvJ,KAAKyG,QAASkD,EAAa1I,MADvBsI,CAAAA,EAMCvJ,KAAKiK,iBAAiB,IAG3BjK,KAAKkK,OAASlK,KAAKkK,OAAO7B,IAAKrI,KAAKoI,UAAW,GAEhDpI,KAAKkJ,WAAW,EAGhBxJ,EAAG0C,CAAQ,EAAEjC,KAAM,eAAgB,CAACoJ,CAAG,GAGjCvI,CACR,EAGAkI,WAAY,SAAUiB,GACrB,IACKpK,EADAoK,IAIJzK,EAAEC,QAHEI,EAAYC,MAGDgJ,SAAUmB,CAAO,EAChCnK,KAAKiC,UAAYvC,EAAE0K,IAAKpK,KAAKgJ,SAAU,SAAUqB,EAASpJ,GACzD,MAAO,CACNoJ,QAASA,EACTjI,QAASrC,EAAU+G,WAAY7F,CAAK,EAAG,EACxC,CACD,CAAE,EAGFjB,KAAKsK,YAAc5K,EAAE6K,KAAMvK,KAAKsK,YAAa,SAAUlI,GACtD,MAAO,EAAGA,EAAQnB,QAAQkJ,EAC3B,CAAE,GAEEnK,KAAKI,SAAS8I,WAClBlJ,KAAKI,SAAS8I,WAAW7H,KAAMrB,KAAMA,KAAKgJ,SAAUhJ,KAAKiC,SAAU,EAEnEjC,KAAKwK,kBAAkB,CAEzB,EAGAC,UAAW,WACL/K,EAAEE,GAAG6K,WACT/K,EAAGM,KAAKoB,WAAY,EAAEqJ,UAAU,EAEjCzK,KAAKyG,QAAU,GACfzG,KAAKkG,UAAY,GACjBlG,KAAKmJ,YAAY,EACjBnJ,KAAK0K,WAAW,EAChB,IAAItB,EAAWpJ,KAAKoJ,SAAS,EAC3BuB,WAAY,eAAgB,EAC5BzH,WAAY,cAAe,EAE7BlD,KAAK4K,cAAexB,CAAS,CAC9B,EAEAwB,cAAe,SAAUxB,GACxB,IAAIvE,EAEJ,GAAK7E,KAAKI,SAASyF,YAClB,IAAMhB,EAAI,EAAGuE,EAAUvE,GAAKA,CAAC,GAC5B7E,KAAKI,SAASyF,YAAYxE,KAAMrB,KAAMoJ,EAAUvE,GAC/C7E,KAAKI,SAAS8E,WAAY,EAAG,EAC9BlF,KAAK8G,WAAYsC,EAAUvE,GAAI5D,IAAK,EAAE+F,YAAahH,KAAKI,SAASgF,UAAW,OAG7EgE,EACEpC,YAAahH,KAAKI,SAAS8E,UAAW,EACtC8B,YAAahH,KAAKI,SAASgF,UAAW,CAE1C,EAEA6E,iBAAkB,WACjB,OAAOjK,KAAK6K,aAAc7K,KAAKyG,OAAQ,CACxC,EAEAoE,aAAc,SAAUC,GAEvB,IACCjG,EADGkG,EAAQ,EAEZ,IAAMlG,KAAKiG,EACLA,EAAKjG,IACTkG,CAAK,GAGP,OAAOA,CACR,EAEAL,WAAY,WACX1K,KAAK8F,UAAW9F,KAAKkK,MAAO,CAC7B,EAEApE,UAAW,SAAUqE,GACpBA,EAAOa,IAAKhL,KAAKoI,UAAW,EAAE6C,KAAM,EAAG,EACvCjL,KAAKkL,WAAYf,CAAO,EAAEgB,KAAK,CAChC,EAEAnJ,MAAO,WACN,OAAuB,IAAhBhC,KAAKoL,KAAK,CAClB,EAEAA,KAAM,WACL,OAAOpL,KAAKiC,UAAUhC,MACvB,EAEA2B,aAAc,WACb,GAAK5B,KAAKI,SAASwB,aAClB,IACClC,EAAGM,KAAKqL,eAAe,GAAKrL,KAAKiC,UAAUhC,QAAUD,KAAKiC,UAAW,GAAIG,SAAW,EAAG,EACtFkJ,OAAQ,UAAW,EACnBC,MAAM,EAGNC,QAAS,SAAU,CAIrB,CAHE,MAAQC,IAKZ,EAEAJ,eAAgB,WACf,IAAIzF,EAAa5F,KAAK4F,WACtB,OAAOA,GAEQ,IAFMlG,EAAE6K,KAAMvK,KAAKiC,UAAW,SAAU6C,GACtD,OAAOA,EAAE1C,QAAQnB,OAAS2E,EAAW3E,IACtC,CAAE,EAAEhB,QAAgB2F,CACrB,EAEAwD,SAAU,WACT,IAAIrJ,EAAYC,KACf0L,EAAa,GAGd,OAAOhM,EAAGM,KAAKoB,WAAY,EAC1B0H,KAAM,4CAA6C,EACnDkC,IAAK,oCAAqC,EAC1CA,IAAKhL,KAAKI,SAASqF,MAAO,EAC1B6F,OAAQ,WACR,IAAIrK,EAAOjB,KAAKiB,MAAQvB,EAAGM,IAAK,EAAEG,KAAM,MAAO,EAW/C,MAVK,CAACc,GAAQlB,EAAUK,SAASmB,OAASM,OAAOC,SAChDA,QAAQ6J,MAAO,0BAA2B3L,IAAK,EAI3CA,KAAK4L,aAAc,iBAAkB,IACzC5L,KAAKyB,KAAO/B,EAAGM,IAAK,EAAE6L,QAAS,MAAO,EAAG,IAIrC5K,EAAAA,KAAQyK,GAAe3L,CAAAA,EAAU8K,aAAcnL,EAAGM,IAAK,EAAEsC,MAAM,CAAE,KAItEoJ,EAAYzK,GAAS,CAAA,EAEtB,CAAE,CACH,EAEAyI,MAAO,SAAUoC,GAChB,OAAOpM,EAAGoM,CAAS,EAAG,EACvB,EAEA3B,OAAQ,WACP,IAAIjF,EAAalF,KAAKI,SAAS8E,WAAWnC,MAAO,GAAI,EAAEgJ,KAAM,GAAI,EACjE,OAAOrM,EAAGM,KAAKI,SAASiF,aAAe,IAAMH,EAAYlF,KAAKmI,YAAa,CAC5E,EAEA6D,eAAgB,WACfhM,KAAKsK,YAAc,GACnBtK,KAAKiC,UAAY,GACjBjC,KAAKgJ,SAAW,GAChBhJ,KAAKiM,OAASvM,EAAG,EAAG,EACpBM,KAAKkK,OAASxK,EAAG,EAAG,CACrB,EAEA8I,MAAO,WACNxI,KAAKgM,eAAe,EACpBhM,KAAKqJ,gBAAkB3J,EAAG,EAAG,CAC9B,EAEAyJ,YAAa,WACZnJ,KAAKwI,MAAM,EACXxI,KAAKkK,OAASlK,KAAKmK,OAAO,EAAE9B,IAAKrI,KAAKoI,UAAW,CAClD,EAEA0B,eAAgB,SAAU1H,GACzBpC,KAAKwI,MAAM,EACXxI,KAAKkK,OAASlK,KAAK+F,UAAW3D,CAAQ,CACvC,EAEAkE,aAAc,SAAUlE,GACvB,IAAI8J,EAAWxM,EAAG0C,CAAQ,EACzByE,EAAOzE,EAAQyE,KAGhB,MAAc,UAATA,GAA6B,aAATA,EACjB7G,KAAK8G,WAAY1E,EAAQnB,IAAK,EAAEqK,OAAQ,UAAW,EAAEpK,IAAI,EAC5C,WAAT2F,GAAiD,KAAA,IAArBzE,EAAQ+J,SACxC/J,EAAQ+J,SAASC,SAAW,MAAQF,EAAShL,IAAI,GAIxDA,EADIkB,EAAQwJ,aAAc,iBAAkB,EACtCM,EAASjB,KAAK,EAEdiB,EAAShL,IAAI,EAGN,SAAT2F,EAGyB,mBAAxB3F,EAAImL,OAAQ,EAAG,EAAG,EACfnL,EAAImL,OAAQ,EAAG,EAMX,IADZC,EAAMpL,EAAIqL,YAAa,GAAI,IAOf,IADZD,EAAMpL,EAAIqL,YAAa,IAAK,GAEpBrL,EAAImL,OAAQC,EAAM,CAAE,EAIrBpL,EAGY,UAAf,OAAOA,EACJA,EAAI6D,QAAS,MAAO,EAAG,EAExB7D,EACR,EAEAoI,MAAO,SAAUlH,GAChBA,EAAUpC,KAAK4J,oBAAqB5J,KAAK0J,MAAOtH,CAAQ,CAAE,EAE1D,IAMCpB,EAAQiC,EAAQuJ,EANblK,EAAQ5C,EAAG0C,CAAQ,EAAEE,MAAM,EAC9BmK,EAAa/M,EAAE0K,IAAK9H,EAAO,SAAUwC,EAAGD,GACvC,OAAOA,CACR,CAAE,EAAE5E,OACJyM,EAAqB,CAAA,EACrBxL,EAAMlB,KAAKsG,aAAclE,CAAQ,EAOlC,GAAiC,YAA5B,OAAOE,EAAMqK,WAA4B,CAG7C,GAAoB,UAAf,OAFLzL,EAAMoB,EAAMqK,WAAWtL,KAAMe,EAASlB,CAAI,GAGzC,MAAM,IAAI0L,UAAW,8CAA+C,EAKrE,OAAOtK,EAAMqK,UACd,CAEA,IAAM1J,KAAUX,EAAQ,CACvBkK,EAAO,CAAEvJ,OAAQA,EAAQ4J,WAAYvK,EAAOW,EAAS,EACrD,IAKC,GAAgB,yBAJhBjC,EAAStB,EAAEK,UAAU+M,QAAS7J,GAAS5B,KAAMrB,KAAMkB,EAAKkB,EAASoK,EAAKK,UAAW,IAIzB,IAAfJ,EACxCC,EAAqB,CAAA,MADtB,CAMA,GAFAA,EAAqB,CAAA,EAEL,YAAX1L,EAEJ,OADAhB,KAAAA,KAAKkK,OAASlK,KAAKkK,OAAOc,IAAKhL,KAAK+F,UAAW3D,CAAQ,CAAE,GAI1D,GAAK,CAACpB,EAEL,OADAhB,KAAK+M,aAAc3K,EAASoK,CAAK,EAC1B,CAAA,CAVR,CAqBD,CATE,MAAQf,GAQT,MAPKzL,KAAKI,SAASmB,OAASM,OAAOC,SAClCA,QAAQkL,IAAK,4CAA8C5K,EAAQ6K,GAAK,gBAAkBT,EAAKvJ,OAAS,YAAawI,CAAE,EAEnHA,aAAamB,YACjBnB,EAAEpB,SAAW,+CAAiDjI,EAAQ6K,GAAK,gBAAkBT,EAAKvJ,OAAS,aAGtGwI,CACP,CACD,CACA,GAAKiB,CAAAA,EAML,OAHK1M,KAAK6K,aAAcvI,CAAM,GAC7BtC,KAAKsK,YAAYN,KAAM5H,CAAQ,EAEzB,CAAA,CACR,EAKA8K,kBAAmB,SAAU9K,EAASa,GACrC,OAAOvD,EAAG0C,CAAQ,EAAElC,KAAM,MAAQ+C,EAAOkK,OAAQ,CAAE,EAAEC,YAAY,EAChEnK,EAAOoK,UAAW,CAAE,EAAEC,YAAY,CAAE,GAAK5N,EAAG0C,CAAQ,EAAElC,KAAM,KAAM,CACpE,EAGAqN,cAAe,SAAUtM,EAAMgC,GAC1BuK,EAAIxN,KAAKI,SAAS0C,SAAU7B,GAChC,OAAOuM,IAAOA,EAAE9I,cAAgB+I,OAASD,EAAIA,EAAGvK,GACjD,EAGAyK,YAAa,WACZ,IAAM,IAAI7I,EAAI,EAAGA,EAAIR,UAAUpE,OAAQ4E,CAAC,GACvC,GAAwBhE,KAAAA,IAAnBwD,UAAWQ,GACf,OAAOR,UAAWQ,EAIrB,EAEA8I,eAAgB,SAAUvL,EAASoK,GAClC,IAAInC,EAAUrK,KAAK0N,YACjB1N,KAAKuN,cAAenL,EAAQnB,KAAMuL,EAAKvJ,MAAO,EAC9CjD,KAAKkN,kBAAmB9K,EAASoK,EAAKvJ,MAAO,EAG7C,CAACjD,KAAKI,SAASsF,aAAetD,EAAQwL,OAAS/M,KAAAA,EAC/CnB,EAAEK,UAAU+C,SAAU0J,EAAKvJ,QAC3B,2CAA6Cb,EAAQnB,KAAO,WAC7D,EACA4M,EAAW,gBAOZ,MANwB,YAAnB,OAAOxD,EACXA,EAAUA,EAAQhJ,KAAMrB,KAAMwM,EAAKK,WAAYzK,CAAQ,EAC5CyL,EAASC,KAAMzD,CAAQ,IAClCA,EAAU3K,EAAEK,UAAUmE,OAAQmG,EAAQtF,QAAS8I,EAAU,MAAO,EAAGrB,EAAKK,UAAW,GAG7ExC,CACR,EAEA0C,aAAc,SAAU3K,EAASoK,GAChC,IAAInC,EAAUrK,KAAK2N,eAAgBvL,EAASoK,CAAK,EAEjDxM,KAAKiC,UAAU+H,KAAM,CACpBK,QAASA,EACTjI,QAASA,EACTa,OAAQuJ,EAAKvJ,MACd,CAAE,EAEFjD,KAAKgJ,SAAU5G,EAAQnB,MAASoJ,EAChCrK,KAAKkG,UAAW9D,EAAQnB,MAASoJ,CAClC,EAEAa,WAAY,SAAU6C,GAIrB,OAFCA,EADI/N,KAAKI,SAAS4N,QACPD,EAAS1F,IAAK0F,EAASE,OAAQjO,KAAKI,SAAS4N,OAAQ,CAAE,EAE5DD,CACR,EAEAvD,kBAAmB,WAElB,IADA,IAAOpB,EAAUuC,EACX9G,EAAI,EAAG7E,KAAKiC,UAAW4C,GAAKA,CAAC,GAClC8G,EAAQ3L,KAAKiC,UAAW4C,GACnB7E,KAAKI,SAASwG,WAClB5G,KAAKI,SAASwG,UAAUvF,KAAMrB,KAAM2L,EAAMvJ,QAASpC,KAAKI,SAAS8E,WAAYlF,KAAKI,SAASgF,UAAW,EAEvGpF,KAAKkO,UAAWvC,EAAMvJ,QAASuJ,EAAMtB,OAAQ,EAK9C,GAHKrK,KAAKiC,UAAUhC,SACnBD,KAAKiM,OAASjM,KAAKiM,OAAO5D,IAAKrI,KAAKoI,UAAW,GAE3CpI,KAAKI,SAAS+N,QAClB,IAAMtJ,EAAI,EAAG7E,KAAKsK,YAAazF,GAAKA,CAAC,GACpC7E,KAAKkO,UAAWlO,KAAKsK,YAAazF,EAAI,EAGxC,GAAK7E,KAAKI,SAASyF,YAClB,IAAMhB,EAAI,EAAGuE,EAAWpJ,KAAKoO,cAAc,EAAGhF,EAAUvE,GAAKA,CAAC,GAC7D7E,KAAKI,SAASyF,YAAYxE,KAAMrB,KAAMoJ,EAAUvE,GAAK7E,KAAKI,SAAS8E,WAAYlF,KAAKI,SAASgF,UAAW,EAG1GpF,KAAKkK,OAASlK,KAAKkK,OAAOc,IAAKhL,KAAKiM,MAAO,EAC3CjM,KAAK0K,WAAW,EAChB1K,KAAKkL,WAAYlL,KAAKiM,MAAO,EAAEoC,KAAK,CACrC,EAEAD,cAAe,WACd,OAAOpO,KAAKqJ,gBAAgB2B,IAAKhL,KAAKsO,gBAAgB,CAAE,CACzD,EAEAA,gBAAiB,WAChB,OAAO5O,EAAGM,KAAKiC,SAAU,EAAEmI,IAAK,WAC/B,OAAOpK,KAAKoC,OACb,CAAE,CACH,EAEA8L,UAAW,SAAU9L,EAASiI,GAC7B,IAAWb,EAAO+E,EAAS1E,EAC1B8B,EAAQ3L,KAAK+F,UAAW3D,CAAQ,EAChCoM,EAAYxO,KAAKyO,SAAUrM,CAAQ,EACnCsM,EAAchP,EAAG0C,CAAQ,EAAEjC,KAAM,kBAAmB,EAEhDwL,EAAM1L,QAGV0L,EAAM3E,YAAahH,KAAKI,SAASgF,UAAW,EAAE2B,SAAU/G,KAAKI,SAAS8E,UAAW,EAGjFyG,EAAMgD,KAAMtE,CAAQ,IAUpBuE,EANAjD,EAAQjM,EAAG,IAAMM,KAAKI,SAASiF,aAAe,GAAI,EAChDlF,KAAM,KAAMqO,EAAY,QAAS,EACjCzH,SAAU/G,KAAKI,SAAS8E,UAAW,EACnCyJ,KAAMtE,GAAW,EAAG,EAIjBrK,KAAKI,SAAS4N,UAIlBY,EAAQjD,EAAMR,KAAK,EAAEkD,KAAK,EAAEQ,KAAM,IAAM7O,KAAKI,SAAS4N,QAAU,IAAK,EAAEC,OAAO,GAE1EjO,KAAKkI,eAAejI,OACxBD,KAAKkI,eAAe4G,OAAQF,CAAM,EACvB5O,KAAKI,SAAS2O,eACzB/O,KAAKI,SAAS2O,eAAgBH,EAAOlP,EAAG0C,CAAQ,CAAE,EAElDwM,EAAMI,YAAa5M,CAAQ,EAIvBuJ,EAAMzJ,GAAI,OAAQ,EAGtByJ,EAAMxL,KAAM,MAAOqO,CAAU,EAIiE,IAAnF7C,EAAMsD,QAAS,cAAgBjP,KAAKkP,cAAeV,CAAU,EAAI,IAAK,EAAEvO,SACnFsO,EAAU5C,EAAMxL,KAAM,IAAK,EAGrBuO,EAEOA,EAAYS,MAAO,IAAInK,OAAQ,MAAQhF,KAAKkP,cAAeX,CAAQ,EAAI,KAAM,CAAE,IAG3FG,GAAe,IAAMH,GAJrBG,EAAcH,EAMf7O,EAAG0C,CAAQ,EAAEjC,KAAM,mBAAoBuO,CAAY,EAGnDlF,EAAQxJ,KAAKiF,OAAQ7C,EAAQnB,QAG5BvB,EAAEyC,MADF0H,EAAI7J,MACMiF,OAAQ,SAAUhE,EAAM8I,GAC5BA,IAAcP,GAClB9J,EAAG,UAAYmK,EAAEqF,cAAejO,CAAK,EAAI,KAAM4I,EAAEzI,WAAY,EAC3DjB,KAAM,mBAAoBwL,EAAMxL,KAAM,IAAK,CAAE,CAEjD,CAAE,GAIA,CAACkK,GAAWrK,KAAKI,SAAS+N,UAC9BxC,EAAMV,KAAM,EAAG,EACuB,UAAjC,OAAOjL,KAAKI,SAAS+N,QACzBxC,EAAM5E,SAAU/G,KAAKI,SAAS+N,OAAQ,EAEtCnO,KAAKI,SAAS+N,QAASxC,EAAOvJ,CAAQ,GAGxCpC,KAAKiM,OAASjM,KAAKiM,OAAO5D,IAAKsD,CAAM,CACtC,EAEA5F,UAAW,SAAU3D,GACpB,IAAInB,EAAOjB,KAAKkP,cAAelP,KAAKyO,SAAUrM,CAAQ,CAAE,EACvDgN,EAAY1P,EAAG0C,CAAQ,EAAEjC,KAAM,kBAAmB,EAClD2L,EAAW,cAAgB7K,EAAO,kBAAoBA,EAAO,OAQ9D,OALKmO,IACJtD,EAAWA,EAAW,MAAQ9L,KAAKkP,cAAeE,CAAU,EAC1DrK,QAAS,OAAQ,KAAM,GAGnB/E,KACLmK,OAAO,EACPmB,OAAQQ,CAAS,CACpB,EAKAoD,cAAe,SAAUG,GACxB,OAAOA,EAAOtK,QAAS,yCAA0C,MAAO,CACzE,EAEA0J,SAAU,SAAUrM,GACnB,OAAOpC,KAAKiF,OAAQ7C,EAAQnB,OAAYjB,CAAAA,KAAKiG,UAAW7D,CAAQ,GAAmBA,EAAQ6K,IAAM7K,EAAQnB,IAC1G,EAEA2I,oBAAqB,SAAUxH,GAQ9B,OALKpC,KAAKiG,UAAW7D,CAAQ,IAC5BA,EAAUpC,KAAK8G,WAAY1E,EAAQnB,IAAK,GAIlCvB,EAAG0C,CAAQ,EAAE4I,IAAKhL,KAAKI,SAASqF,MAAO,EAAG,EAClD,EAEAQ,UAAW,SAAU7D,GACpB,MAAO,kBAAsB0L,KAAM1L,EAAQyE,IAAK,CACjD,EAEAC,WAAY,SAAU7F,GACrB,OAAOvB,EAAGM,KAAKoB,WAAY,EAAE0H,KAAM,UAAY9I,KAAKkP,cAAejO,CAAK,EAAI,IAAK,CAClF,EAEAqO,UAAW,SAAU1G,EAAOxG,GAC3B,OAASA,EAAQmN,SAASjC,YAAY,GACtC,IAAK,SACJ,OAAO5N,EAAG,kBAAmB0C,CAAQ,EAAEnC,OACxC,IAAK,QACJ,GAAKD,KAAKiG,UAAW7D,CAAQ,EAC5B,OAAOpC,KAAK8G,WAAY1E,EAAQnB,IAAK,EAAEqK,OAAQ,UAAW,EAAErL,MAE9D,CACA,OAAO2I,EAAM3I,MACd,EAEAuP,OAAQ,SAAU7M,EAAOP,GACxB,MAAOpC,CAAAA,KAAKyP,YAAa,OAAO9M,IAAU3C,KAAKyP,YAAa,OAAO9M,GAASA,EAAOP,CAAQ,CAC5F,EAEAqN,YAAa,CACZC,QAAW,SAAU/M,GACpB,OAAOA,CACR,EACA0M,OAAU,SAAU1M,EAAOP,GAC1B,MAAO,CAAC,CAAC1C,EAAGiD,EAAOP,EAAQX,IAAK,EAAExB,MACnC,EACA0P,SAAY,SAAUhN,EAAOP,GAC5B,OAAOO,EAAOP,CAAQ,CACvB,CACD,EAEA+D,SAAU,SAAU/D,GACnB,IAAIlB,EAAMlB,KAAKsG,aAAclE,CAAQ,EACrC,MAAO,CAAC1C,EAAEK,UAAU+M,QAAQvJ,SAASlC,KAAMrB,KAAMkB,EAAKkB,CAAQ,GAAK,qBACpE,EAEAwN,aAAc,SAAUxN,GACjBpC,KAAKuI,QAASnG,EAAQnB,QAC3BjB,KAAK0B,cAAc,GACnBhC,EAAG0C,CAAQ,EAAE2E,SAAU/G,KAAKI,SAAS+E,YAAa,EAClDnF,KAAKuI,QAASnG,EAAQnB,MAAS,CAAA,EAEjC,EAEA4O,YAAa,SAAUzN,EAASJ,GAC/BhC,KAAK0B,cAAc,GAGd1B,KAAK0B,eAAiB,IAC1B1B,KAAK0B,eAAiB,GAEvB,OAAO1B,KAAKuI,QAASnG,EAAQnB,MAC7BvB,EAAG0C,CAAQ,EAAE4E,YAAahH,KAAKI,SAAS+E,YAAa,EAChDnD,GAAiC,IAAxBhC,KAAK0B,gBAAwB1B,KAAK2B,eAAiB3B,KAAKyB,KAAK,GAC1E/B,EAAGM,KAAKoB,WAAY,EAAE0O,OAAO,EAC7B9P,KAAK2B,cAAgB,CAAA,GACV,CAACK,GAAiC,IAAxBhC,KAAK0B,gBAAwB1B,KAAK2B,gBACvDjC,EAAGM,KAAKoB,WAAY,EAAE6H,eAAgB,eAAgB,CAAEjJ,KAAO,EAC/DA,KAAK2B,cAAgB,CAAA,EAEvB,EAEAoO,cAAe,SAAU3N,EAASa,GACjC,OAAOvD,EAAEQ,KAAMkC,EAAS,eAAgB,GAAK1C,EAAEQ,KAAMkC,EAAS,gBAAiB,CAC9E4N,IAAK,KACLhO,MAAO,CAAA,EACPqI,QAASrK,KAAK2N,eAAgBvL,EAAS,CAAEa,OAAQA,CAAO,CAAE,CAC3D,CAAE,CACH,EAGAgN,QAAS,WACRjQ,KAAKyK,UAAU,EAEf/K,EAAGM,KAAKoB,WAAY,EAClB8O,IAAK,WAAY,EACjBvF,WAAY,WAAY,EACxB7B,KAAM,wBAAyB,EAC9BoH,IAAK,mBAAoB,EACzBlJ,YAAa,uBAAwB,CACzC,CAED,EAEAmJ,kBAAmB,CAClB5M,SAAU,CAAEA,SAAU,CAAA,CAAK,EAC3B2D,MAAO,CAAEA,MAAO,CAAA,CAAK,EACrBC,IAAK,CAAEA,IAAK,CAAA,CAAK,EACjBC,KAAM,CAAEA,KAAM,CAAA,CAAK,EACnBC,QAAS,CAAEA,QAAS,CAAA,CAAK,EACzBC,OAAQ,CAAEA,OAAQ,CAAA,CAAK,EACvBC,OAAQ,CAAEA,OAAQ,CAAA,CAAK,EACvB6I,WAAY,CAAEA,WAAY,CAAA,CAAK,CAChC,EAEAC,cAAe,SAAUC,EAAWhO,GAC9BgO,EAAU5L,cAAgB+I,OAC9BzN,KAAKmQ,kBAAmBG,GAAchO,EAEtC5C,EAAEC,OAAQK,KAAKmQ,kBAAmBG,CAAU,CAE9C,EAEAlN,WAAY,SAAUhB,GACrB,IAAIE,EAAQ,GACXiO,EAAU7Q,EAAG0C,CAAQ,EAAEjC,KAAM,OAAQ,EAStC,OAPKoQ,GACJ7Q,EAAEyC,KAAMoO,EAAQxN,MAAO,GAAI,EAAG,WACxB/C,QAAQN,EAAEK,UAAUoQ,mBACxBzQ,EAAEC,OAAQ2C,EAAO5C,EAAEK,UAAUoQ,kBAAmBnQ,KAAO,CAEzD,CAAE,EAEIsC,CACR,EAEAkO,uBAAwB,SAAUlO,EAAOuE,EAAM5D,EAAQ2F,IASpDA,EALG,eAAekF,KAAM7K,CAAO,IAAgB,OAAT4D,GAAiB,oBAAoBiH,KAAMjH,CAAK,KACvF+B,EAAQ6H,OAAQ7H,CAAM,EAGjB8H,MAAO9H,CAAM,GACT/H,KAAAA,EAIL+H,IAAmB,IAAVA,EACbtG,EAAOW,GAAW2F,EACP/B,IAAS5D,GAAmB,UAAT4D,IAI9BvE,EAAOW,GAAW,CAAA,EAEpB,EAEAI,eAAgB,SAAUjB,GACzB,IAGCa,EAAQ2F,EAHLtG,EAAQ,GACX4J,EAAWxM,EAAG0C,CAAQ,EACtByE,EAAOzE,EAAQuO,aAAc,MAAO,EAGrC,IAAM1N,KAAUvD,EAAEK,UAAU+M,QAa1BlE,EAVe,aAAX3F,EAUI,CAAC,EAJR2F,EADc,MAJfA,EAAQxG,EAAQuO,aAAc1N,CAAO,GAK5B,CAAA,EAIC2F,GAEFsD,EAAS/L,KAAM8C,CAAO,EAG/BjD,KAAKwQ,uBAAwBlO,EAAOuE,EAAM5D,EAAQ2F,CAAM,EAQzD,OAJKtG,EAAMmF,WAAa,uBAAuBqG,KAAMxL,EAAMmF,SAAU,GACpE,OAAOnF,EAAMmF,UAGPnF,CACR,EAEAgB,UAAW,SAAUlB,GACpB,IAGCa,EAAQ2F,EAHLtG,EAAQ,GACX4J,EAAWxM,EAAG0C,CAAQ,EACtByE,EAAOzE,EAAQuO,aAAc,MAAO,EAGrC,IAAM1N,KAAUvD,EAAEK,UAAU+M,QAC3BlE,EAAQsD,EAAShM,KAAM,OAAS+C,EAAOkK,OAAQ,CAAE,EAAEC,YAAY,EAAInK,EAAOoK,UAAW,CAAE,EAAEC,YAAY,CAAE,EACvGtN,KAAKwQ,uBAAwBlO,EAAOuE,EAAM5D,EAAQ2F,CAAM,EAEzD,OAAOtG,CACR,EAEAG,YAAa,SAAUL,GACtB,IAAIE,EAAQ,GACXvC,EAAYL,EAAEQ,KAAMkC,EAAQX,KAAM,WAAY,EAK/C,OAFCa,EADIvC,EAAUK,SAASkC,MACf5C,EAAEK,UAAU8C,cAAe9C,EAAUK,SAASkC,MAAOF,EAAQnB,KAAO,GAAK,GAE3EqB,CACR,EAEAa,eAAgB,SAAUb,EAAOF,GAmEhC,OAhEA1C,EAAEyC,KAAMG,EAAO,SAAUyB,EAAM7C,GAG9B,GAAa,CAAA,IAARA,EACJ,OAAOoB,EAAOyB,QAGf,GAAK7C,EAAIyB,OAASzB,EAAI0P,QAAU,CAC/B,IAAIC,EAAW,CAAA,EACf,OAAS,OAAO3P,EAAI0P,SACpB,IAAK,SACJC,EAAW,CAAC,CAACnR,EAAGwB,EAAI0P,QAASxO,EAAQX,IAAK,EAAExB,OAC5C,MACD,IAAK,WACJ4Q,EAAW3P,EAAI0P,QAAQvP,KAAMe,EAASA,CAAQ,CAE/C,CACKyO,EACJvO,EAAOyB,GAAuBlD,KAAAA,IAAdK,EAAIyB,OAAsBzB,EAAIyB,OAE9CjD,EAAEQ,KAAMkC,EAAQX,KAAM,WAAY,EAAEmJ,cAAelL,EAAG0C,CAAQ,CAAE,EAChE,OAAOE,EAAOyB,GAEhB,CACD,CAAE,EAGFrE,EAAEyC,KAAMG,EAAO,SAAUkK,EAAMsE,GAC9BxO,EAAOkK,GAAS9M,EAAEqR,WAAYD,CAAU,GAAc,eAATtE,EAAwBsE,EAAW1O,CAAQ,EAAI0O,CAC7F,CAAE,EAGFpR,EAAEyC,KAAM,CAAE,YAAa,aAAe,WAChCG,EAAOtC,QACXsC,EAAOtC,MAASyQ,OAAQnO,EAAOtC,KAAO,EAExC,CAAE,EACFN,EAAEyC,KAAM,CAAE,cAAe,SAAW,WACnC,IAAI6O,EACC1O,EAAOtC,QACNN,EAAEuR,QAAS3O,EAAOtC,KAAO,EAC7BsC,EAAOtC,MAAS,CAAEyQ,OAAQnO,EAAOtC,MAAQ,EAAI,EAAGyQ,OAAQnO,EAAOtC,MAAQ,EAAI,GACvC,UAAzB,OAAOsC,EAAOtC,QACzBgR,EAAQ1O,EAAOtC,MAAO+E,QAAS,UAAW,EAAG,EAAEhC,MAAO,QAAS,EAC/DT,EAAOtC,MAAS,CAAEyQ,OAAQO,EAAO,EAAI,EAAGP,OAAQO,EAAO,EAAI,IAG9D,CAAE,EAEGtR,EAAEK,UAAUiI,mBAGE,MAAb1F,EAAMwF,KAA4B,MAAbxF,EAAMuF,MAC/BvF,EAAMsF,MAAQ,CAAEtF,EAAMwF,IAAKxF,EAAMuF,KACjC,OAAOvF,EAAMwF,IACb,OAAOxF,EAAMuF,KAEU,MAAnBvF,EAAMoF,YAAwC,MAAnBpF,EAAMmF,YACrCnF,EAAMqF,YAAc,CAAErF,EAAMoF,UAAWpF,EAAMmF,WAC7C,OAAOnF,EAAMoF,UACb,OAAOpF,EAAMmF,WAIRnF,CACR,EAGAO,cAAe,SAAU3C,GACxB,IACKgR,EAML,MAPqB,UAAhB,OAAOhR,IACPgR,EAAc,GAClBxR,EAAEyC,KAAMjC,EAAK6C,MAAO,IAAK,EAAG,WAC3BmO,EAAalR,MAAS,CAAA,CACvB,CAAE,EACFE,EAAOgR,GAEDhR,CACR,EAGAiR,UAAW,SAAUlQ,EAAMgC,EAAQoH,GAClC3K,EAAEK,UAAU+M,QAAS7L,GAASgC,EAC9BvD,EAAEK,UAAU+C,SAAU7B,GAAqBJ,KAAAA,IAAZwJ,EAAwBA,EAAU3K,EAAEK,UAAU+C,SAAU7B,GAClFgC,EAAOhD,OAAS,GACpBP,EAAEK,UAAUsQ,cAAepP,EAAMvB,EAAEK,UAAU8C,cAAe5B,CAAK,CAAE,CAErE,EAGA6L,QAAS,CAGRvJ,SAAU,SAAUqF,EAAOxG,EAASO,GAGnC,OAAM3C,KAAKwP,OAAQ7M,EAAOP,CAAQ,EAGM,WAAnCA,EAAQmN,SAASjC,YAAY,GAG7BpM,EAAMxB,EAAG0C,CAAQ,EAAElB,IAAI,IACA,EAAbA,EAAIjB,OAEdD,KAAKiG,UAAW7D,CAAQ,EACc,EAAnCpC,KAAKsP,UAAW1G,EAAOxG,CAAQ,EAEjB,EAAfwG,EAAM3I,OAXL,qBAYT,EAGAiH,MAAO,SAAU0B,EAAOxG,GAMvB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,wIAAwI0L,KAAMlF,CAAM,CACxL,EAGAzB,IAAK,SAAUyB,EAAOxG,GAMrB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,2cAA2c0L,KAAMlF,CAAM,CAC3f,EAGAxB,KAAM,SAAUwB,EAAOxG,GACtB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,CAAC,cAAc0L,KAAM,IAAIsD,KAAMxI,CAAM,EAAEyI,SAAS,CAAE,CACtF,EAGAhK,QAAS,SAAUuB,EAAOxG,GACzB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,+DAA+D0L,KAAMlF,CAAM,CAC/G,EAGAtB,OAAQ,SAAUsB,EAAOxG,GACxB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,8CAA8C0L,KAAMlF,CAAM,CAC9F,EAGArB,OAAQ,SAAUqB,EAAOxG,GACxB,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAK,QAAQ0L,KAAMlF,CAAM,CACxD,EAGAlB,UAAW,SAAUkB,EAAOxG,EAASO,GAChC1C,EAASP,EAAEuR,QAASrI,CAAM,EAAIA,EAAM3I,OAASD,KAAKsP,UAAW1G,EAAOxG,CAAQ,EAChF,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAeO,GAAV1C,CACpC,EAGAwH,UAAW,SAAUmB,EAAOxG,EAASO,GAChC1C,EAASP,EAAEuR,QAASrI,CAAM,EAAIA,EAAM3I,OAASD,KAAKsP,UAAW1G,EAAOxG,CAAQ,EAChF,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAKnC,GAAU0C,CAC9C,EAGAgF,YAAa,SAAUiB,EAAOxG,EAASO,GAClC1C,EAASP,EAAEuR,QAASrI,CAAM,EAAIA,EAAM3I,OAASD,KAAKsP,UAAW1G,EAAOxG,CAAQ,EAChF,OAAOpC,KAAKmG,SAAU/D,CAAQ,GAAOnC,GAAU0C,EAAO,IAAO1C,GAAU0C,EAAO,EAC/E,EAGAmF,IAAK,SAAUc,EAAOxG,EAASO,GAC9B,OAAO3C,KAAKmG,SAAU/D,CAAQ,GAAcO,GAATiG,CACpC,EAGAf,IAAK,SAAUe,EAAOxG,EAASO,GAC9B,OAAO3C,KAAKmG,SAAU/D,CAAQ,GAAKwG,GAASjG,CAC7C,EAGAiF,MAAO,SAAUgB,EAAOxG,EAASO,GAChC,OAAO3C,KAAKmG,SAAU/D,CAAQ,GAAOwG,GAASjG,EAAO,IAAOiG,GAASjG,EAAO,EAC7E,EAGAoF,KAAM,SAAUa,EAAOxG,EAASO,GAC/B,IAAIkE,EAAOnH,EAAG0C,CAAQ,EAAEjC,KAAM,MAAO,EACpCmR,EAAe,gCAAkCzK,EAAO,qBAExD0K,EAAK,IAAIvM,OAAQ,MAAQ6B,EAAO,KAAM,EAKvC,GAJgBA,GAAQ,CAAC0K,EAAGzD,KAFV,CAAE,OAAQ,SAAU,SAEW/B,KAAK,CAAE,EAKvD,MAAM,IAAIyF,MAAOF,CAAa,EAE/B,OAAOtR,KAAKmG,SAAU/D,CAAQ,GAAOwG,EAAQjG,GAAU,CACxD,EAGA6E,QAAS,SAAUoB,EAAOxG,EAASO,GAG9BjC,EAAShB,EAAGiD,CAAM,EAMtB,OALK3C,KAAKI,SAAS4F,YAActF,EAAOsK,IAAK,wBAAyB,EAAE/K,QACvES,EAAOqG,SAAU,uBAAwB,EAAEzG,GAAI,wBAAyB,WACvEZ,EAAG0C,CAAQ,EAAEJ,MAAM,CACpB,CAAE,EAEI4G,IAAUlI,EAAOQ,IAAI,CAC7B,EAGAsC,OAAQ,SAAUoF,EAAOxG,EAASO,EAAOM,GACxC,GAAKjD,KAAKmG,SAAU/D,CAAQ,EAC3B,MAAO,sBAGRa,EAA2B,UAAlB,OAAOA,GAAuBA,GAAU,SAEjD,IACClD,EAAiB0R,EADdC,EAAW1R,KAAK+P,cAAe3N,EAASa,CAAO,EAWnD,OARMjD,KAAKI,SAAS0C,SAAUV,EAAQnB,QACrCjB,KAAKI,SAAS0C,SAAUV,EAAQnB,MAAS,IAE1CyQ,EAASC,gBAAkBD,EAASC,iBAAmB3R,KAAKI,SAAS0C,SAAUV,EAAQnB,MAAQgC,GAC/FjD,KAAKI,SAAS0C,SAAUV,EAAQnB,MAAQgC,GAAWyO,EAASrH,QAG5DoH,EAAmB/R,EAAEiD,MAAOjD,EAAEC,OAAQ,CAAEO,KAAM0I,CAAM,GADpDjG,EAAyB,UAAjB,OAAOA,EAAsB,CAAEwE,IAAKxE,CAAM,EAAKA,GACMzC,IAAK,CAAE,EAC/DwR,EAAS1B,MAAQyB,EACdC,EAAS1P,OAGjB0P,EAAS1B,IAAMyB,GACf1R,EAAYC,MACP4P,aAAcxN,CAAQ,GAC3BlC,EAAO,IACDkC,EAAQnB,MAAS2H,EACvBlJ,EAAEkS,KAAMlS,EAAEC,OAAQ,CAAA,EAAM,CACvBkS,KAAM,QACNC,KAAM,WAAa1P,EAAQnB,KAC3B8Q,SAAU,OACV7R,KAAMA,EACN8R,QAASjS,EAAUqB,YACnB+M,QAAS,SAAU8D,GAClB,IACkB/L,EADdlE,EAAqB,CAAA,IAAbiQ,GAAkC,SAAbA,EAGjClS,EAAUK,SAAS0C,SAAUV,EAAQnB,MAAQgC,GAAWyO,EAASC,gBAC5D3P,GACJkE,EAAYnG,EAAU4B,cACtB5B,EAAUiM,eAAe,EACzBjM,EAAUmK,OAASnK,EAAUgG,UAAW3D,CAAQ,EAChDrC,EAAU4B,cAAgBuE,EAC1BnG,EAAUuK,YAAYN,KAAM5H,CAAQ,EACpCrC,EAAU0G,QAASrE,EAAQnB,MAAS,CAAA,EACpClB,EAAUmJ,WAAW,IAErBiB,EAAS,GACTE,EAAU4H,GAAYlS,EAAU4N,eAAgBvL,EAAS,CAAEa,OAAQA,EAAQ4J,WAAYjE,CAAM,CAAE,EAC/FuB,EAAQ/H,EAAQnB,MAASyQ,EAASrH,QAAUA,EAC5CtK,EAAU0G,QAASrE,EAAQnB,MAAS,CAAA,EACpClB,EAAUmJ,WAAYiB,CAAO,GAE9BuH,EAAS1P,MAAQA,EACjBjC,EAAU8P,YAAazN,EAASJ,CAAM,CACvC,CACD,EAAGW,CAAM,CAAE,EACJ,UACR,CACD,CAED,CAAE,EAMF,IACCiP,EADGM,EAAkB,GAIjBxS,EAAEyS,cACNzS,EAAEyS,cAAe,SAAU/R,EAAUgS,EAAGC,GACvC,IAAIP,EAAO1R,EAAS0R,KACG,UAAlB1R,EAASyR,OACRK,EAAiBJ,IACrBI,EAAiBJ,GAAOQ,MAAM,EAE/BJ,EAAiBJ,GAASO,EAE5B,CAAE,GAIFT,EAAOlS,EAAEkS,KACTlS,EAAEkS,KAAO,SAAUxR,GAClB,IAAIyR,GAAS,SAAUzR,EAAWA,EAAWV,EAAE6S,cAAeV,KAC7DC,GAAS,SAAU1R,EAAWA,EAAWV,EAAE6S,cAAeT,KAC3D,MAAc,UAATD,GACCK,EAAiBJ,IACrBI,EAAiBJ,GAAOQ,MAAM,EAE/BJ,EAAiBJ,GAASF,EAAKnN,MAAOzE,KAAMqE,SAAU,EAC/C6N,EAAiBJ,IAElBF,EAAKnN,MAAOzE,KAAMqE,SAAU,CACpC,EAGD,CAAE"}
!function(){"use strict";function y(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function d(e){return e[e.length-1]}function b(t,...e){return e.forEach(e=>{t.includes(e)||t.push(e)}),t}function o(e,t){return e?e.split(t):[]}function c(e,t,i){return(void 0===t||t<=e)&&(void 0===i||e<=i)}function W(e,t,i){return e<t?t:i<e?i:e}function r(e,t,s={},a=0,i=""){i+=`<${Object.keys(s).reduce((e,t)=>{let i=s[t];return`${e} ${t}="${i="function"==typeof i?i(a):i}"`},e)}></${e}>`;var n=a+1;return n<t?r(e,t,s,n,i):i}function e(e){return e.replace(/>\s+/g,">").replace(/\s+</,"<")}function a(e){return new Date(e).setHours(0,0,0,0)}function h(){return(new Date).setHours(0,0,0,0)}function k(...e){switch(e.length){case 0:return h();case 1:return a(e[0])}var t=new Date(0);return t.setFullYear(...e),t.setHours(0,0,0,0)}function u(e,t){e=new Date(e);return e.setDate(e.getDate()+t)}function f(e,t){e=new Date(e),t=e.getMonth()+t;let i=t%12;i<0&&(i+=12);t=e.setMonth(t);return e.getMonth()!==i?e.setDate(0):t}function p(e,t){var e=new Date(e),i=e.getMonth(),t=e.setFullYear(e.getFullYear()+t);return 1===i&&2===e.getMonth()?e.setDate(0):t}function H(e,t){return(e-t+7)%7}function n(e,t,i=0){var s=new Date(e).getDay();return u(e,H(t,i)-H(s,i))}function g(e,t){e=new Date(e).getFullYear();return Math.floor(e/t)*t}function x(e,t,i){return 1!==t&&2!==t?e:(e=new Date(e),1===t?i?e.setMonth(e.getMonth()+1,0):e.setDate(1):i?e.setFullYear(e.getFullYear()+1,0,0):e.setMonth(0,1),e.setHours(0,0,0,0))}const M=/dd?|DD?|mm?|MM?|yy?(?:yy)?/,j=/[\s!-/:-@[-`{-~年月日]+/;let i={};const T={y:(e,t)=>new Date(e).setFullYear(parseInt(t,10)),m(t,e,i){const s=new Date(t);let a=parseInt(e,10)-1;if(isNaN(a)){if(!e)return NaN;const t=e.toLowerCase(),s=e=>e.toLowerCase().startsWith(t);if((a=(a=i.monthsShort.findIndex(s))<0?i.months.findIndex(s):a)<0)return NaN}return s.setMonth(a),s.getMonth()!==function e(t){return-1<t?t%12:e(t+12)}(a)?s.setDate(0):s.getTime()},d:(e,t)=>new Date(e).setDate(parseInt(t,10))},_={d:e=>e.getDate(),dd:e=>t(e.getDate(),2),D:(e,t)=>t.daysShort[e.getDay()],DD:(e,t)=>t.days[e.getDay()],m:e=>e.getMonth()+1,mm:e=>t(e.getMonth()+1,2),M:(e,t)=>t.monthsShort[e.getMonth()],MM:(e,t)=>t.months[e.getMonth()],y:e=>e.getFullYear(),yy:e=>t(e.getFullYear(),2).slice(-2),yyyy:e=>t(e.getFullYear(),4)};function t(e,t){return e.toString().padStart(t,"0")}function K(e){if("string"!=typeof e)throw new Error("Invalid date format.");if(e in i)return i[e];const n=e.split(M),a=e.match(new RegExp(M,"g"));if(0===n.length||!a)throw new Error("Invalid date format.");const t=a.map(e=>_[e]),r=Object.keys(T).reduce((e,t)=>(a.find(e=>"D"!==e[0]&&e[0].toLowerCase()===t)&&e.push(t),e),[]);return i[e]={parser(e,i){const s=e.split(j).reduce((e,t,i)=>{return 0<t.length&&a[i]&&("M"===(i=a[i][0])?e.m=t:"D"!==i&&(e[i]=t)),e},{});return r.reduce((e,t)=>{t=T[t](e,s[t],i);return isNaN(t)?e:t},h())},formatter:(s,a)=>t.reduce((e,t,i)=>e+(""+n[i]+t(s,a)),"")+d(n)}}function S(e,t,i){if(e instanceof Date||"number"==typeof e){const t=a(e);return isNaN(t)?void 0:t}var s;if(e)return"today"===e?h():t&&t.toValue?(s=t.toValue(e,t,i),isNaN(s)?void 0:a(s)):K(t).parser(e,i)}function s(e,t,i){return isNaN(e)||!e&&0!==e?"":(e="number"==typeof e?new Date(e):e,t.toDisplay?t.toDisplay(e,t,i):K(t).formatter(e,i))}const R=document.createRange();function O(e){return R.createContextualFragment(e)}function I(e){return e.parentElement||(e.parentNode instanceof ShadowRoot?e.parentNode.host:void 0)}function l(e){return e.getRootNode().activeElement===e}function m(e){"none"!==e.style.display&&(e.style.display&&(e.dataset.styleDisplay=e.style.display),e.style.display="none")}function w(e){"none"===e.style.display&&(e.dataset.styleDisplay?(e.style.display=e.dataset.styleDisplay,delete e.dataset.styleDisplay):e.style.display="")}function v(e){e.firstChild&&(e.removeChild(e.firstChild),v(e))}const D=new WeakMap,{addEventListener:P,removeEventListener:q}=EventTarget.prototype;function C(e,t){let i=D.get(e);i||(i=[],D.set(e,i)),t.forEach(e=>{P.call(...e),i.push(e)})}function $(e){var t=D.get(e);t&&(t.forEach(e=>{q.call(...e)}),D.delete(e))}if(!Event.prototype.composedPath){const y=(e,t=[])=>{let i;return t.push(e),e.parentNode?i=e.parentNode:e.host?i=e.host:e.defaultView&&(i=e.defaultView),i?y(i,t):t};Event.prototype.composedPath=function(){return y(this.target)}}function J(e,t){var i="function"==typeof t?t:e=>e instanceof Element&&e.matches(t);return function e(t,i,s){var[t,...a]=t;return i(t)?t:t!==s&&"HTML"!==t.tagName&&0!==a.length?e(a,i,s):void 0}(e.composedPath(),i,e.currentTarget)}const E={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",titleFormat:"MM y"}},F={autohide:!1,beforeShowDay:null,beforeShowDecade:null,beforeShowMonth:null,beforeShowYear:null,calendarWeeks:!1,clearBtn:!1,dateDelimiter:",",datesDisabled:[],daysOfWeekDisabled:[],daysOfWeekHighlighted:[],defaultViewDate:void 0,disableTouchKeyboard:!1,format:"mm/dd/yyyy",language:"en",maxDate:null,maxNumberOfDates:1,maxView:3,minDate:null,nextArrow:"»",orientation:"auto",pickLevel:0,prevArrow:"«",showDaysOfWeek:!0,showOnClick:!0,showOnFocus:!0,startView:0,title:"",todayBtn:!1,todayBtnMode:0,todayHighlight:!1,updateOnBlur:!0,weekStart:0},{language:V,format:U,weekStart:z}=F;function X(e,t){return e.length<6&&0<=t&&t<7?b(e,t):e}function G(e){return(e+6)%7}function Q(e,t,i,s){e=S(e,t,i);return void 0!==e?e:s}function Z(e,t,i=3){e=parseInt(e,10);return 0<=e&&e<=i?e:t}function ee(t,e){const i=Object.assign({},t),s={},a=e.constructor.locales,n=e.rangeSideIndex;let{format:r,language:d,locale:o,maxDate:l,maxView:c,minDate:h,pickLevel:u,startView:f,weekStart:p}=e.config||{};if(i.language){let e;if(i.language!==d&&(a[i.language]?e=i.language:void 0===a[e=i.language.split("-")[0]]&&(e=!1)),delete i.language,e){d=s.language=e;const t=o||a[V];o=Object.assign({format:U,weekStart:z},a[V]),d!==V&&Object.assign(o,a[d]),s.locale=o,r===t.format&&(r=s.format=o.format),p===t.weekStart&&(p=s.weekStart=o.weekStart,s.weekEnd=G(o.weekStart))}}if(i.format){const y="function"==typeof i.format.toDisplay,t="function"==typeof i.format.toValue,b=M.test(i.format);(y&&t||b)&&(r=s.format=i.format),delete i.format}let g=u,m=(void 0!==i.pickLevel&&(g=Z(i.pickLevel,2),delete i.pickLevel),g!==u&&(g>u&&(void 0===i.minDate&&(i.minDate=h),void 0===i.maxDate)&&(i.maxDate=l),i.datesDisabled||(i.datesDisabled=[]),u=s.pickLevel=g),h),w=l;if(void 0!==i.minDate){const y=k(0,0,1);(m=null===i.minDate?y:Q(i.minDate,r,o,m))!==y&&(m=x(m,u,!1)),delete i.minDate}if(void 0!==i.maxDate&&(void 0!==(w=null===i.maxDate?void 0:Q(i.maxDate,r,o,w))&&(w=x(w,u,!0)),delete i.maxDate),w<m?(h=s.minDate=w,l=s.maxDate=m):(h!==m&&(h=s.minDate=m),l!==w&&(l=s.maxDate=w)),i.datesDisabled&&(s.datesDisabled=i.datesDisabled.reduce((e,t)=>{t=S(t,r,o);return void 0!==t?b(e,x(t,u,n)):e},[]),delete i.datesDisabled),void 0!==i.defaultViewDate){const y=S(i.defaultViewDate,r,o);void 0!==y&&(s.defaultViewDate=y),delete i.defaultViewDate}if(void 0!==i.weekStart){const y=Number(i.weekStart)%7;isNaN(y)||(p=s.weekStart=y,s.weekEnd=G(y)),delete i.weekStart}if(i.daysOfWeekDisabled&&(s.daysOfWeekDisabled=i.daysOfWeekDisabled.reduce(X,[]),delete i.daysOfWeekDisabled),i.daysOfWeekHighlighted&&(s.daysOfWeekHighlighted=i.daysOfWeekHighlighted.reduce(X,[]),delete i.daysOfWeekHighlighted),void 0!==i.maxNumberOfDates){const y=parseInt(i.maxNumberOfDates,10);0<=y&&(s.maxNumberOfDates=y,s.multidate=1!==y),delete i.maxNumberOfDates}i.dateDelimiter&&(s.dateDelimiter=String(i.dateDelimiter),delete i.dateDelimiter);let v=c,D=(void 0!==i.maxView&&(v=Z(i.maxView,c),delete i.maxView),(v=u>v?u:v)!==c&&(c=s.maxView=v),f);if(void 0!==i.startView&&(D=Z(i.startView,D),delete i.startView),D<u?D=u:D>c&&(D=c),D!==f&&(s.startView=D),i.prevArrow){const y=O(i.prevArrow);0<y.childNodes.length&&(s.prevArrow=y.childNodes),delete i.prevArrow}if(i.nextArrow){const y=O(i.nextArrow);0<y.childNodes.length&&(s.nextArrow=y.childNodes),delete i.nextArrow}if(void 0!==i.disableTouchKeyboard&&(s.disableTouchKeyboard="ontouchstart"in document&&!!i.disableTouchKeyboard,delete i.disableTouchKeyboard),i.orientation){const y=i.orientation.toLowerCase().split(/\s+/g);s.orientation={x:y.find(e=>"left"===e||"right"===e)||"auto",y:y.find(e=>"top"===e||"bottom"===e)||"auto"},delete i.orientation}if(void 0!==i.todayBtnMode){switch(i.todayBtnMode){case 0:case 1:s.todayBtnMode=i.todayBtnMode}delete i.todayBtnMode}return Object.keys(i).forEach(e=>{void 0!==i[e]&&y(F,e)&&(s[e]=i[e])}),s}const te=e('<div class="datepicker">\n  <div class="datepicker-picker">\n    <div class="datepicker-header">\n      <div class="datepicker-title"></div>\n      <div class="datepicker-controls">\n        <button type="button" class="%buttonClass% prev-btn"></button>\n        <button type="button" class="%buttonClass% view-switch"></button>\n        <button type="button" class="%buttonClass% next-btn"></button>\n      </div>\n    </div>\n    <div class="datepicker-main"></div>\n    <div class="datepicker-footer">\n      <div class="datepicker-controls">\n        <button type="button" class="%buttonClass% today-btn"></button>\n        <button type="button" class="%buttonClass% clear-btn"></button>\n      </div>\n    </div>\n  </div>\n</div>'),ie=e(`<div class="days">
  <div class="days-of-week">${r("span",7,{class:"dow"})}</div>
  <div class="datepicker-grid">${r("span",42)}</div>
</div>`),se=e(`<div class="calendar-weeks">
  <div class="days-of-week"><span class="dow"></span></div>
  <div class="weeks">${r("span",6,{class:"week"})}</div>
</div>`);class ae{constructor(e,t){Object.assign(this,t,{picker:e,element:O('<div class="datepicker-view"></div>').firstChild,selected:[]}),this.init(this.picker.datepicker.config)}init(e){void 0!==e.pickLevel&&(this.isMinView=this.id===e.pickLevel),this.setOptions(e),this.updateFocus(),this.updateSelection()}performBeforeHook(e,t,i){let s=this.beforeShow(new Date(i));switch(typeof s){case"boolean":s={enabled:s};break;case"string":s={classes:s}}if(s){if(!1===s.enabled&&(e.classList.add("disabled"),b(this.disabled,t)),s.classes){const i=s.classes.split(/\s+/);e.classList.add(...i),i.includes("disabled")&&b(this.disabled,t)}s.content&&(a=e,i=s.content,v(a),i instanceof DocumentFragment?a.appendChild(i):"string"==typeof i?a.appendChild(O(i)):"function"==typeof i.forEach&&i.forEach(e=>{a.appendChild(e)}))}var a}}class ne extends ae{constructor(e){super(e,{id:0,name:"days",cellClass:"day"})}init(e,t=!0){if(t){const e=O(ie).firstChild;this.dow=e.firstChild,this.grid=e.lastChild,this.element.appendChild(e)}super.init(e)}setOptions(e){let t;if(y(e,"minDate")&&(this.minDate=e.minDate),y(e,"maxDate")&&(this.maxDate=e.maxDate),e.datesDisabled&&(this.datesDisabled=e.datesDisabled),e.daysOfWeekDisabled&&(this.daysOfWeekDisabled=e.daysOfWeekDisabled,t=!0),e.daysOfWeekHighlighted&&(this.daysOfWeekHighlighted=e.daysOfWeekHighlighted),void 0!==e.todayHighlight&&(this.todayHighlight=e.todayHighlight),void 0!==e.weekStart&&(this.weekStart=e.weekStart,this.weekEnd=e.weekEnd,t=!0),e.locale){const y=this.locale=e.locale;this.dayNames=y.daysMin,this.switchLabelFormat=y.titleFormat,t=!0}if(void 0!==e.beforeShowDay&&(this.beforeShow="function"==typeof e.beforeShowDay?e.beforeShowDay:void 0),void 0!==e.calendarWeeks)if(e.calendarWeeks&&!this.calendarWeeks){const y=O(se).firstChild;this.calendarWeeks={element:y,dow:y.firstChild,weeks:y.lastChild},this.element.insertBefore(y,this.element.firstChild)}else this.calendarWeeks&&!e.calendarWeeks&&(this.element.removeChild(this.calendarWeeks.element),this.calendarWeeks=null);void 0!==e.showDaysOfWeek&&(e.showDaysOfWeek?(w(this.dow),this.calendarWeeks&&w(this.calendarWeeks.dow)):(m(this.dow),this.calendarWeeks&&m(this.calendarWeeks.dow))),t&&Array.from(this.dow.children).forEach((e,t)=>{t=(this.weekStart+t)%7;e.textContent=this.dayNames[t],e.className=this.daysOfWeekDisabled.includes(t)?"dow disabled":"dow"})}updateFocus(){var e=new Date(this.picker.viewDate),t=e.getFullYear(),e=e.getMonth(),i=k(t,e,1),s=n(i,this.weekStart,this.weekStart);this.first=i,this.last=k(t,e+1,0),this.start=s,this.focused=this.picker.viewDate}updateSelection(){var{dates:e,rangepicker:t}=this.picker.datepicker;this.selected=e,t&&(this.range=t.dates)}render(){this.today=this.todayHighlight?h():void 0,this.disabled=[...this.datesDisabled];const i=s(this.focused,this.switchLabelFormat,this.locale);if(this.picker.setViewSwitchLabel(i),this.picker.setPrevBtnDisabled(this.first<=this.minDate),this.picker.setNextBtnDisabled(this.last>=this.maxDate),this.calendarWeeks){const i=n(this.first,1,1);Array.from(this.calendarWeeks.weeks.children).forEach((e,t)=>{e.textContent=(e=n(u(i,7*t),4,1),t=n(new Date(e).setMonth(0,4),4,1),Math.round((e-t)/6048e5)+1)})}Array.from(this.grid.children).forEach((e,t)=>{var i=e.classList,s=u(this.start,t),t=new Date(s),a=t.getDay();if(e.className="datepicker-cell "+this.cellClass,e.dataset.date=s,e.textContent=t.getDate(),s<this.first?i.add("prev"):s>this.last&&i.add("next"),this.today===s&&i.add("today"),(s<this.minDate||s>this.maxDate||this.disabled.includes(s))&&i.add("disabled"),this.daysOfWeekDisabled.includes(a)&&(i.add("disabled"),b(this.disabled,s)),this.daysOfWeekHighlighted.includes(a)&&i.add("highlighted"),this.range){const[e,t]=this.range;s>e&&s<t&&i.add("range"),s===e&&i.add("range-start"),s===t&&i.add("range-end")}this.selected.includes(s)&&i.add("selected"),s===this.focused&&i.add("focused"),this.beforeShow&&this.performBeforeHook(e,s,s)})}refresh(){const[i,s]=this.range||[];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach(e=>{e.classList.remove("range","range-start","range-end","selected","focused")}),Array.from(this.grid.children).forEach(e=>{var t=Number(e.dataset.date),e=e.classList;t>i&&t<s&&e.add("range"),t===i&&e.add("range-start"),t===s&&e.add("range-end"),this.selected.includes(t)&&e.add("selected"),t===this.focused&&e.add("focused")})}refreshFocus(){var e=Math.round((this.focused-this.start)/864e5);this.grid.querySelectorAll(".focused").forEach(e=>{e.classList.remove("focused")}),this.grid.children[e].classList.add("focused")}}function re(e,t){var i,s,a;if(e&&e[0]&&e[1])return[[e,i],[s,a]]=e,t<e||s<t?void 0:[e===t?i:-1,s===t?a:12]}class de extends ae{constructor(e){super(e,{id:1,name:"months",cellClass:"month"})}init(e,t=!0){t&&(this.grid=this.element,this.element.classList.add("months","datepicker-grid"),this.grid.appendChild(O(r("span",12,{"data-month":e=>e})))),super.init(e)}setOptions(e){if(e.locale&&(this.monthNames=e.locale.monthsShort),y(e,"minDate"))if(void 0===e.minDate)this.minYear=this.minMonth=this.minDate=void 0;else{const y=new Date(e.minDate);this.minYear=y.getFullYear(),this.minMonth=y.getMonth(),this.minDate=y.setDate(1)}if(y(e,"maxDate"))if(void 0===e.maxDate)this.maxYear=this.maxMonth=this.maxDate=void 0;else{const y=new Date(e.maxDate);this.maxYear=y.getFullYear(),this.maxMonth=y.getMonth(),this.maxDate=k(this.maxYear,this.maxMonth+1,0)}this.isMinView?e.datesDisabled&&(this.datesDisabled=e.datesDisabled):this.datesDisabled=[],void 0!==e.beforeShowMonth&&(this.beforeShow="function"==typeof e.beforeShowMonth?e.beforeShowMonth:void 0)}updateFocus(){var e=new Date(this.picker.viewDate);this.year=e.getFullYear(),this.focused=e.getMonth()}updateSelection(){var{dates:e,rangepicker:t}=this.picker.datepicker;this.selected=e.reduce((e,t)=>{var t=new Date(t),i=t.getFullYear(),t=t.getMonth();return void 0===e[i]?e[i]=[t]:b(e[i],t),e},{}),t&&t.dates&&(this.range=t.dates.map(e=>{e=new Date(e);return isNaN(e)?void 0:[e.getFullYear(),e.getMonth()]}))}render(){this.disabled=this.datesDisabled.reduce((e,t)=>{t=new Date(t);return this.year===t.getFullYear()&&e.push(t.getMonth()),e},[]),this.picker.setViewSwitchLabel(this.year),this.picker.setPrevBtnDisabled(this.year<=this.minYear),this.picker.setNextBtnDisabled(this.year>=this.maxYear);const a=this.selected[this.year]||[],n=this.year<this.minYear||this.year>this.maxYear,r=this.year===this.minYear,d=this.year===this.maxYear,o=re(this.range,this.year);Array.from(this.grid.children).forEach((e,t)=>{var i=e.classList,s=k(this.year,t,1);if(e.className="datepicker-cell "+this.cellClass,this.isMinView&&(e.dataset.date=s),e.textContent=this.monthNames[t],(n||r&&t<this.minMonth||d&&t>this.maxMonth||this.disabled.includes(t))&&i.add("disabled"),o){const[a,n]=o;t>a&&t<n&&i.add("range"),t===a&&i.add("range-start"),t===n&&i.add("range-end")}a.includes(t)&&i.add("selected"),t===this.focused&&i.add("focused"),this.beforeShow&&this.performBeforeHook(e,t,s)})}refresh(){const i=this.selected[this.year]||[],[s,a]=re(this.range,this.year)||[];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach(e=>{e.classList.remove("range","range-start","range-end","selected","focused")}),Array.from(this.grid.children).forEach((e,t)=>{e=e.classList;t>s&&t<a&&e.add("range"),t===s&&e.add("range-start"),t===a&&e.add("range-end"),i.includes(t)&&e.add("selected"),t===this.focused&&e.add("focused")})}refreshFocus(){this.grid.querySelectorAll(".focused").forEach(e=>{e.classList.remove("focused")}),this.grid.children[this.focused].classList.add("focused")}}class oe extends ae{constructor(e,t){super(e,t)}init(e,t=!0){t&&(this.navStep=10*this.step,this.beforeShowOption="beforeShow"+[...this.cellClass].reduce((e,t,i)=>e+(i?t:t.toUpperCase()),""),this.grid=this.element,this.element.classList.add(this.name,"datepicker-grid"),this.grid.appendChild(O(r("span",12)))),super.init(e)}setOptions(e){if(y(e,"minDate")&&(void 0===e.minDate?this.minYear=this.minDate=void 0:(this.minYear=g(e.minDate,this.step),this.minDate=k(this.minYear,0,1))),y(e,"maxDate")&&(void 0===e.maxDate?this.maxYear=this.maxDate=void 0:(this.maxYear=g(e.maxDate,this.step),this.maxDate=k(this.maxYear,11,31))),this.isMinView?e.datesDisabled&&(this.datesDisabled=e.datesDisabled):this.datesDisabled=[],void 0!==e[this.beforeShowOption]){const y=e[this.beforeShowOption];this.beforeShow="function"==typeof y?y:void 0}}updateFocus(){var e=new Date(this.picker.viewDate),t=g(e,this.navStep),i=t+9*this.step;this.first=t,this.last=i,this.start=t-this.step,this.focused=g(e,this.step)}updateSelection(){var{dates:e,rangepicker:t}=this.picker.datepicker;this.selected=e.reduce((e,t)=>b(e,g(t,this.step)),[]),t&&t.dates&&(this.range=t.dates.map(e=>{if(void 0!==e)return g(e,this.step)}))}render(){this.disabled=this.datesDisabled.map(e=>new Date(e).getFullYear()),this.picker.setViewSwitchLabel(this.first+"-"+this.last),this.picker.setPrevBtnDisabled(this.first<=this.minYear),this.picker.setNextBtnDisabled(this.last>=this.maxYear),Array.from(this.grid.children).forEach((e,t)=>{var i=e.classList,s=this.start+t*this.step,a=k(s,0,1);if(e.className="datepicker-cell "+this.cellClass,this.isMinView&&(e.dataset.date=a),e.textContent=e.dataset.year=s,0===t?i.add("prev"):11===t&&i.add("next"),(s<this.minYear||s>this.maxYear||this.disabled.includes(s))&&i.add("disabled"),this.range){const[e,t]=this.range;s>e&&s<t&&i.add("range"),s===e&&i.add("range-start"),s===t&&i.add("range-end")}this.selected.includes(s)&&i.add("selected"),s===this.focused&&i.add("focused"),this.beforeShow&&this.performBeforeHook(e,s,a)})}refresh(){const[i,s]=this.range||[];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach(e=>{e.classList.remove("range","range-start","range-end","selected","focused")}),Array.from(this.grid.children).forEach(e=>{var t=Number(e.textContent),e=e.classList;t>i&&t<s&&e.add("range"),t===i&&e.add("range-start"),t===s&&e.add("range-end"),this.selected.includes(t)&&e.add("selected"),t===this.focused&&e.add("focused")})}refreshFocus(){var e=Math.round((this.focused-this.start)/this.step);this.grid.querySelectorAll(".focused").forEach(e=>{e.classList.remove("focused")}),this.grid.children[e].classList.add("focused")}}function N(e,t){var i={date:e.getDate(),viewDate:new Date(e.picker.viewDate),viewId:e.picker.currentView.id,datepicker:e};e.element.dispatchEvent(new CustomEvent(t,{detail:i}))}function L(e,t){var{minDate:i,maxDate:s}=e.config,{currentView:a,viewDate:n}=e.picker;let r;switch(a.id){case 0:r=f(n,t);break;case 1:r=p(n,t);break;default:r=p(n,t*a.navStep)}r=W(r,i,s),e.picker.changeFocus(r).render()}function le(e){var t=e.picker.currentView.id;t!==e.config.maxView&&e.picker.changeView(t+1).render()}function ce(e){e.config.updateOnBlur?e.update({revert:!0}):e.refresh("input"),e.hide()}function he(e,t){var i,s,t=J(t,".datepicker-cell");t&&!t.classList.contains("disabled")&&({id:s,isMinView:i}=e.picker.currentView,i?e.setDate(Number(t.dataset.date)):(i=e,e=Number(1===s?t.dataset.month:t.dataset.year),i=i.picker,s=new Date(i.viewDate),t=i.currentView.id,e=1===t?f(s,e-s.getMonth()):p(s,e-s.getFullYear()),i.changeFocus(e).changeView(t-1).render()))}function ue(e){e.preventDefault()}const fe=["left","top","right","bottom"].reduce((e,t)=>(e[t]="datepicker-orient-"+t,e),{}),pe=e=>e&&e+"px";function ge(e,t){if(void 0!==t.title&&(t.title?(e.controls.title.textContent=t.title,w):(e.controls.title.textContent="",m))(e.controls.title),t.prevArrow){const y=e.controls.prevBtn;v(y),t.prevArrow.forEach(e=>{y.appendChild(e.cloneNode(!0))})}if(t.nextArrow){const y=e.controls.nextBtn;v(y),t.nextArrow.forEach(e=>{y.appendChild(e.cloneNode(!0))})}if(t.locale&&(e.controls.todayBtn.textContent=t.locale.today,e.controls.clearBtn.textContent=t.locale.clear),void 0!==t.todayBtn&&(t.todayBtn?w:m)(e.controls.todayBtn),y(t,"minDate")||y(t,"maxDate")){const{minDate:y,maxDate:t}=e.datepicker.config;e.controls.todayBtn.disabled=!c(h(),y,t)}void 0!==t.clearBtn&&(t.clearBtn?w:m)(e.controls.clearBtn)}function me(e){var{dates:e,config:t}=e;return W(0<e.length?d(e):t.defaultViewDate,t.minDate,t.maxDate)}function we(e,t){var i=new Date(e.viewDate),s=new Date(t),{id:a,year:n,first:r,last:d}=e.currentView,o=s.getFullYear();switch(e.viewDate=t,o!==i.getFullYear()&&N(e.datepicker,"changeYear"),s.getMonth()!==i.getMonth()&&N(e.datepicker,"changeMonth"),a){case 0:return t<r||d<t;case 1:return o!==n;default:return o<r||d<o}}function ve(e){return window.getComputedStyle(e).direction}class De{constructor(e){var t=(this.datepicker=e)["config"],i=te.replace(/%buttonClass%/g,t.buttonClass),i=this.element=O(i).firstChild,[s,a,n]=i.firstChild.children,r=s.firstElementChild,[s,d,o]=s.lastElementChild.children,[n,l]=n.firstChild.children,r={title:r,prevBtn:s,viewSwitch:d,nextBtn:o,todayBtn:n,clearBtn:l},s=(this.main=a,this.controls=r,e.inline?"inline":"dropdown");i.classList.add("datepicker-"+s),ge(this,t),this.viewDate=me(e),C(e,[[i,"mousedown",ue],[a,"click",he.bind(null,e)],[r.viewSwitch,"click",function(e){le(e)}.bind(null,e)],[r.prevBtn,"click",function(e){L(e,-1)}.bind(null,e)],[r.nextBtn,"click",function(e){L(e,1)}.bind(null,e)],[r.todayBtn,"click",function(e){var t=e.picker,i=h();if(1===e.config.todayBtnMode){if(e.config.autohide)return void e.setDate(i);e.setDate(i,{render:!1}),t.update()}t.viewDate!==i&&t.changeFocus(i),t.changeView(0).render()}.bind(null,e)],[r.clearBtn,"click",function(e){e.setDate({clear:!0})}.bind(null,e)]]),this.views=[new ne(this),new de(this),new oe(this,{id:2,name:"years",cellClass:"year",step:1}),new oe(this,{id:3,name:"decades",cellClass:"decade",step:10})],this.currentView=this.views[t.startView],this.currentView.render(),this.main.appendChild(this.currentView.element),t.container?t.container.appendChild(this.element):e.inputField.after(this.element)}setOptions(t){ge(this,t),this.views.forEach(e=>{e.init(t,!1)}),this.currentView.render()}detach(){this.element.remove()}show(){var e,t,i;this.active||({datepicker:e,element:t}=this,e.inline?t.classList.add("active"):((i=ve(e.inputField))!==ve(I(t))?t.dir=i:t.dir&&t.removeAttribute("dir"),t.style.visiblity="hidden",t.classList.add("active"),this.place(),t.style.visiblity="",e.config.disableTouchKeyboard&&e.inputField.blur()),this.active=!0,N(e,"show"))}hide(){this.active&&(this.datepicker.exitEditMode(),this.element.classList.remove("active"),this.active=!1,N(this.datepicker,"hide"))}place(){const{classList:e,offsetParent:t,style:i}=this.element,{config:s,inputField:a}=this.datepicker,{width:n,height:r}=this.element.getBoundingClientRect(),{left:d,top:o,right:l,bottom:c,width:h,height:u}=a.getBoundingClientRect();let{x:f,y:p}=s.orientation,g=d,m=o;if(t!==document.body&&t){const e=t.getBoundingClientRect();g-=e.left-t.scrollLeft,m-=e.top-t.scrollTop}else g+=window.scrollX,m+=window.scrollY;var w=function e(t){t=I(t);if(t!==document.body&&t)return"visible"!==window.getComputedStyle(t).overflow?t:e(t)}(a);let v=0,D=0,{clientWidth:y,clientHeight:b}=document.documentElement;if(w){const e=w.getBoundingClientRect();0<e.top&&(D=e.top),0<e.left&&(v=e.left),e.right<y&&(y=e.right),e.bottom<b&&(b=e.bottom)}let k=0;"auto"===f&&(d<v?(f="left",k=v-d):d+n>y?(f="right",y<l&&(k=y-l)):f="rtl"!==ve(a)||l-n<v?"left":"right"),"right"===f&&(g+=h-n),g+=k,"top"===(p="auto"===p?o-r>D&&c+r>b?"top":"bottom":p)?m-=r:m+=u,e.remove(...Object.values(fe)),e.add(fe[f],fe[p]),i.left=pe(g),i.top=pe(m)}setViewSwitchLabel(e){this.controls.viewSwitch.textContent=e}setPrevBtnDisabled(e){this.controls.prevBtn.disabled=e}setNextBtnDisabled(e){this.controls.nextBtn.disabled=e}changeView(e){var t=this.currentView,e=this.views[e];return e.id!==t.id&&(this.currentView=e,this._renderMethod="render",N(this.datepicker,"changeView"),this.main.replaceChild(e.element,t.element)),this}changeFocus(e){return this._renderMethod=we(this,e)?"render":"refreshFocus",this.views.forEach(e=>{e.updateFocus()}),this}update(){var e=me(this.datepicker);return this._renderMethod=we(this,e)?"render":"refresh",this.views.forEach(e=>{e.updateFocus(),e.updateSelection()}),this}render(e=!0){e=e&&this._renderMethod||"render";delete this._renderMethod,this.currentView[e]()}}function B(e,t,i,s){const a=e.picker,n=a.currentView,r=n.step||1;let d,o,l=a.viewDate;switch(n.id){case 0:l=s?u(l,7*i):(t.ctrlKey||t.metaKey?p:u)(l,i),d=u,o=e=>n.disabled.includes(e);break;case 1:l=f(l,s?4*i:i),d=f,o=e=>{var e=new Date(e),{year:t,disabled:i}=n;return e.getFullYear()===t&&i.includes(e.getMonth())};break;default:l=p(l,i*(s?4:1)*r),d=p,o=e=>n.disabled.includes(g(e,r))}void 0!==(l=function e(t,i,s,a,n,r){if(c(t,n,r))return a(t)?e(i(t,s),i,s,a,n,r):t}(l,d,i<0?-r:r,o,n.minDate,n.maxDate))&&a.changeFocus(l).render()}function ye(e,t){return e.map(e=>s(e,t.format,t.locale)).join(t.dateDelimiter)}function be(e,t,i=!1){const{config:s,dates:a,rangeSideIndex:n}=e;if(0===t.length)return i?[]:void 0;let r=t.reduce((e,t)=>{t=S(t,s.format,s.locale);return void 0===t||!c(t=x(t,s.pickLevel,n),s.minDate,s.maxDate)||e.includes(t)||s.datesDisabled.includes(t)||!(0<s.pickLevel)&&s.daysOfWeekDisabled.includes(new Date(t).getDay())||e.push(t),e},[]);return 0!==r.length?(s.multidate&&!i&&(r=r.reduce((e,t)=>(a.includes(t)||e.push(t),e),a.filter(e=>!r.includes(e)))),s.maxNumberOfDates&&r.length>s.maxNumberOfDates?r.slice(-1*s.maxNumberOfDates):r):void 0}function A(e,t=3,i=!0){var{config:s,picker:a,inputField:n}=e;if(2&t){const e=a.active?s.pickLevel:s.startView;a.update().changeView(e).render(i)}1&t&&n&&(n.value=ye(e.dates,s))}function ke(e,t,i){let{clear:s,render:a,autohide:n,revert:r}=i;(a=void 0===a?!0:a)?void 0===n&&(n=e.config.autohide):n=!1;i=be(e,t,s);(i||r)&&(i&&i.toString()!==e.dates.toString()?(e.dates=i,A(e,a?3:1),N(e,"changeDate")):A(e,1),n)&&e.hide()}class xe{constructor(e,t={},i){(e.datepicker=this).element=e;var s=this.config=Object.assign({buttonClass:t.buttonClass&&String(t.buttonClass)||"button",container:null,defaultViewDate:h(),maxDate:void 0,minDate:void 0},ee(F,this)),a=this.inline="INPUT"!==e.tagName;let n,r;if(a?s.container=e:(t.container&&(s.container=t.container instanceof HTMLElement?t.container:document.querySelector(t.container)),(n=this.inputField=e).classList.add("datepicker-input")),i){const e=i.inputs.indexOf(n),t=i.datepickers;if(e<0||1<e||!Array.isArray(t))throw Error("Invalid rangepicker object.");t[e]=this,Object.defineProperty(this,"rangepicker",{get:()=>i}),Object.defineProperty(this,"rangeSideIndex",{get:()=>e})}this._options=t,Object.assign(s,ee(t,this)),a?(r=o(e.dataset.date,s.dateDelimiter),delete e.dataset.date):r=o(n.value,s.dateDelimiter),this.dates=[];t=be(this,r),t&&0<t.length&&(this.dates=t),n&&(n.value=ye(this.dates,s)),t=this.picker=new De(this);if(a)this.show();else{const e=function(e,t){const{element:i,picker:s}=e;if(s.active||l(i)){const a=s.element;J(t,e=>e===i||e===a)||ce(e)}}.bind(null,this);C(this,[[n,"keydown",function(e,t){var i=t.key;if("Tab"===i)ce(e);else{var s=e.picker,{id:a,isMinView:n}=s.currentView;if(s.active){if(e.editMode)return void("Enter"===i?e.exitEditMode({update:!0,autohide:e.config.autohide}):"Escape"===i&&s.hide());if("ArrowLeft"===i)if(t.ctrlKey||t.metaKey)L(e,-1);else{if(t.shiftKey)return void e.enterEditMode();B(e,t,-1,!1)}else if("ArrowRight"===i)if(t.ctrlKey||t.metaKey)L(e,1);else{if(t.shiftKey)return void e.enterEditMode();B(e,t,1,!1)}else if("ArrowUp"===i)if(t.ctrlKey||t.metaKey)le(e);else{if(t.shiftKey)return void e.enterEditMode();B(e,t,-1,!0)}else if("ArrowDown"===i){if(t.shiftKey&&!t.ctrlKey&&!t.metaKey)return void e.enterEditMode();B(e,t,1,!0)}else{if("Enter"!==i)return void("Escape"===i?s.hide():"Backspace"!==i&&"Delete"!==i&&(1!==i.length||t.ctrlKey||t.metaKey)||e.enterEditMode());if(n)return void e.setDate(s.viewDate);s.changeView(a-1).render()}}else{if("ArrowDown"!==i)return void("Enter"===i?e.update():"Escape"===i&&s.show());s.show()}t.preventDefault()}}.bind(null,this)],[n,"focus",function(e){e.config.showOnFocus&&!e._showing&&e.show()}.bind(null,this)],[n,"mousedown",function(e,t){const i=t.target;(e.picker.active||e.config.showOnClick)&&(i._active=l(i),i._clicking=setTimeout(()=>{delete i._active,delete i._clicking},2e3))}.bind(null,this)],[n,"click",function(e,t){(t=t.target)._clicking&&(clearTimeout(t._clicking),delete t._clicking,t._active&&e.enterEditMode(),delete t._active,e.config.showOnClick)&&e.show()}.bind(null,this)],[n,"paste",function(e,t){t.clipboardData.types.includes("text/plain")&&e.enterEditMode()}.bind(null,this)],[document,"mousedown",e],[document,"touchstart",e],[window,"resize",t.place.bind(t)]])}}static formatDate(e,t,i){return s(e,t,i&&E[i]||E.en)}static parseDate(e,t,i){return S(e,t,i&&E[i]||E.en)}static get locales(){return E}get active(){return!(!this.picker||!this.picker.active)}get pickerElement(){return this.picker?this.picker.element:void 0}setOptions(e){var t=this.picker,i=ee(e,this);Object.assign(this._options,e),Object.assign(this.config,i),t.setOptions(i),A(this,3)}show(){if(this.inputField){if(this.inputField.disabled)return;l(this.inputField)||this.config.disableTouchKeyboard||(this._showing=!0,this.inputField.focus(),delete this._showing)}this.picker.show()}hide(){this.inline||(this.picker.hide(),this.picker.update().changeView(this.config.startView).render())}destroy(){return this.hide(),$(this),this.picker.detach(),this.inline||this.inputField.classList.remove("datepicker-input"),delete this.element.datepicker,this}getDate(t){var e=t?e=>s(e,t,this.config.locale):e=>new Date(e);return this.config.multidate?this.dates.map(e):0<this.dates.length?e(this.dates[0]):void 0}setDate(...e){var t=[...e],i={},e=d(e);"object"!=typeof e||Array.isArray(e)||e instanceof Date||!e||Object.assign(i,t.pop()),ke(this,Array.isArray(t[0])?t[0]:t,i)}update(e){this.inline||(e=Object.assign(e||{},{clear:!0,render:!0}),ke(this,o(this.inputField.value,this.config.dateDelimiter),e))}refresh(e,t=!1){e&&"string"!=typeof e&&(t=e,e=void 0),A(this,"picker"===e?2:"input"===e?1:3,!t)}enterEditMode(){this.inline||!this.picker.active||this.editMode||(this.editMode=!0,this.inputField.classList.add("in-edit"))}exitEditMode(e){!this.inline&&this.editMode&&(e=Object.assign({update:!1},e),delete this.editMode,this.inputField.classList.remove("in-edit"),e.update)&&this.update(e)}}function Me(e){e=Object.assign({},e);return delete e.inputs,delete e.allowOneSidedRange,delete e.maxNumberOfDates,e}function Se(e,t,i,s){C(e,[[i,"changeDate",t]]),new xe(i,s,e)}function Y(e,t){var i,s,a,n,r;e._updating||(e._updating=!0,void 0!==(t=t.target).datepicker&&(s={render:!1},n=(i=e.datepickers)[t=e.inputs.indexOf(t)].dates[0],r=i[a=0===t?1:0].dates[0],void 0!==n&&void 0!==r?0===t&&r<n?(i[0].setDate(r,s),i[1].setDate(n,s)):1===t&&n<r&&(i[0].setDate(n,s),i[1].setDate(r,s)):e.allowOneSidedRange||void 0===n&&void 0===r||(s.clear=!0,i[a].setDate(i[t].dates,s)),i[0].picker.update().render(),i[1].picker.update().render(),delete e._updating))}window.Datepicker=xe,window.DateRangePicker=class{constructor(e,t={}){var i=Array.isArray(t.inputs)?t.inputs:Array.from(e.querySelectorAll("input"));if(!(i.length<2)){(e.rangepicker=this).element=e,this.inputs=i.slice(0,2),this.allowOneSidedRange=!!t.allowOneSidedRange;const s=Y.bind(null,this),a=Me(t),n=[];Object.defineProperty(this,"datepickers",{get:()=>n}),Se(this,s,this.inputs[0],a),Se(this,s,this.inputs[1],a),Object.freeze(n),0<n[0].dates.length?Y(this,{target:this.inputs[0]}):0<n[1].dates.length&&Y(this,{target:this.inputs[1]})}}get dates(){return 2===this.datepickers.length?[this.datepickers[0].dates[0],this.datepickers[1].dates[0]]:void 0}setOptions(e){this.allowOneSidedRange=!!e.allowOneSidedRange;e=Me(e);this.datepickers[0].setOptions(e),this.datepickers[1].setOptions(e)}destroy(){this.datepickers[0].destroy(),this.datepickers[1].destroy(),$(this),delete this.element.rangepicker}getDates(t){const i=t?e=>s(e,t,this.datepickers[0].config.locale):e=>new Date(e);return this.dates.map(e=>void 0===e?e:i(e))}setDates(e,t){var[i,s]=this.datepickers,a=this.dates;this._updating=!0,i.setDate(e),s.setDate(t),delete this._updating,s.dates[0]!==a[1]?Y(this,{target:this.inputs[1]}):i.dates[0]!==a[0]&&Y(this,{target:this.inputs[0]})}}}();
//# sourceMappingURL=datepicker.min.js.map

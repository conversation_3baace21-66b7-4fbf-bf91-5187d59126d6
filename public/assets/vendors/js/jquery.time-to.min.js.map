{"version": 3, "file": "jquery.time-to.min.js", "sources": ["jquery.time-to.min.js"], "sourcesContent": ["/**\n * Time-To jQuery plug-in\n * Show countdown timer or realtime clock\n *\n * <AUTHOR> <<EMAIL>>\n * @version 1.3.0\n * @license MIT http://opensource.org/licenses/MIT\n * @date 2019-05-14\n * @host https://github.com/Lexxus/jq-timeTo\n */\n\n(function (factory) {\n    if (typeof define === 'function' && define.amd) {\n        // AMD (RequireJS)\n        define(['jquery'], factory);\n    } else if (typeof exports === 'object') {\n        // CommonJS (Node)\n        /* eslint-disable import/no-unresolved */\n        module.exports = factory(require('jquery'));\n    } else {\n        // globals\n        factory(jQuery);\n    }\n}(function ($) {\n    var SECONDS_PER_DAY = 86400;\n    var SECONDS_PER_HOUR = 3600;\n\n    /* eslint-disable no-multi-spaces */\n    var defaults = {\n        callback: null,          // callback function for exec when timer out\n        step: null,              // callback function to exec every {stepCount} ticks\n        stepCount: 1,            // number of ticks to increment before executing stepCount\n        captionSize: 0,          // font-size by pixels for captions, if 0 then calculate automaticaly\n        countdown: true,         // is countdown or real clock\n        countdownAlertLimit: 10, // limit in seconds when display red background\n        displayCaptions: false,  // display captions under digit groups\n        displayDays: 0,          // display day timer, count of days digits\n        displayHours: true,      // display hours\n        fontFamily: 'Inter, sans-serif',\n        fontSize: 0,             // font-size of a digit by pixels (0 - use CSS instead)\n        lang: 'en',              // language of caption\n        languages: {},           // extra or overridden languages\n        seconds: 0,              // timer's countdown value in seconds\n        start: true,             // true to start timer immediately\n        theme: 'white',          // 'white' or 'black' theme fo timer's view\n        width: 25,               // width of a digit area\n        height: 30,              // height of a digit area\n        gap: 11,                 // gap size between numbers\n        vals: [0, 0, 0, 0, 0, 0, 0, 0, 0],  // private, current value of each digit\n        limits: [9, 9, 9, 2, 9, 5, 9, 5, 9], // private, max value of each digit\n        iSec: 8,                 // private, index of second digit\n        iHour: 4,                // private, index of hour digit\n        tickTimeout: 1000,       // timeout betweet each timer tick in miliseconds\n        intervalId: null,        // private\n        tickCount: 0             // private\n    };\n\n    var methods = {\n        start: function (sec) {\n            var intervalId;\n\n            if (sec) {\n                init.call(this, sec);\n                intervalId = setTimeout(tick.bind(this), 1000);\n\n                // save start time\n                this.data('ttStartTime', $.now());\n                this.data('intervalId', intervalId);\n            }\n        },\n\n        stop: function () {\n            var data = this.data();\n\n            if (data.intervalId) {\n                clearTimeout(data.intervalId);\n                this.data('intervalId', null);\n            }\n            return data;\n        },\n\n        reset: function (sec) {\n            var data = methods.stop.call(this);\n            var secs = typeof sec === 'undefined' ? data.seconds : sec;\n\n            this.find('div').css({ backgroundPosition: 'left center' });\n            this.find('ul').parent().removeClass('timeTo-alert');\n\n            init.call(this, secs, true);\n        }\n    };\n\n    var dictionary = {\n        en: { days: 'days',   hours: 'hours',  min: 'minutes',  sec: 'seconds' },\n        ru: { days: 'дней',   hours: 'часов',  min: 'минут',    sec: 'секунд' },\n        ua: { days: 'днiв',   hours: 'годин',  min: 'хвилин',   sec: 'секунд' },\n        de: { days: 'Tag',    hours: 'Uhr',    min: 'Minuten',  sec: 'Secunden' },\n        fr: { days: 'jours',  hours: 'heures', min: 'minutes',  sec: 'secondes' },\n        es: { days: 'días',   hours: 'horas',  min: 'minutos',  sec: 'segundos' },\n        sp: { days: 'días',   hours: 'horas',  min: 'minutos',  sec: 'segundos' },\n        it: { days: 'giorni', hours: 'ore',    min: 'minuti',   sec: 'secondi' },\n        nl: { days: 'dagen',  hours: 'uren',   min: 'minuten',  sec: 'seconden' },\n        no: { days: 'dager',  hours: 'timer',  min: 'minutter', sec: 'sekunder' },\n        pt: { days: 'dias',   hours: 'horas',  min: 'minutos',  sec: 'segundos' },\n        tr: { days: 'gün',    hours: 'saat',   min: 'dakika',   sec: 'saniye' },\n        pl: { days: 'dni', hours: 'godziny', min: 'minuty', sec: 'secundy' }\n    };\n\n    /* eslint-enable no-multi-spaces */\n\n    if (typeof $.support.transition === 'undefined') {\n        $.support.transition = (function () {\n            var thisBody = document.body || document.documentElement;\n            var thisStyle = thisBody.style;\n            var support = thisStyle.transition !== undefined\n                || thisStyle.WebkitTransition !== undefined\n                || thisStyle.MozTransition !== undefined\n                || thisStyle.MsTransition !== undefined\n                || thisStyle.OTransition !== undefined;\n\n            return support;\n        }());\n    }\n\n    $.fn.timeTo = function () {\n        var options = {};\n        var now = Date.now();\n        var j, arg, num, method, time, days, tt, sec, m, t;\n\n        for (j = 0; j < arguments.length; j += 1) {\n            arg = arguments[j];\n            if (j === 0 && typeof arg === 'string') {\n                method = arg;\n            } else if (typeof arg === 'object') {\n                if (typeof arg.getTime === 'function') {\n                    // arg is a Date object\n                    options.timeTo = arg;\n                } else {\n                    // arg is an options object\n                    options = $.extend(options, arg);\n                }\n            } else if (typeof arg === 'function') {\n                // arg is callback\n                options.callback = arg;\n            } else {\n                num = parseInt(arg, 10);\n                // arg is seconds of timeout\n                if (!isNaN(num)) {\n                    options.seconds = num;\n                }\n            }\n        }\n\n        // set time to countdown to\n        if (options.timeTo) {\n            if (options.timeTo.getTime) {\n                // set time as date object\n                time = options.timeTo.getTime();\n            } else if (typeof options.timeTo === 'number') {\n                // set time as integer in millisec\n                time = options.timeTo;\n            }\n            if (time > now) {\n                options.seconds = Math.floor((time - now) / 1000);\n            } else {\n                options.seconds = 0;\n            }\n        } else if (options.time || !options.seconds) {\n            time = options.time;\n\n            if (!time) {\n                time = new Date(now);\n            }\n\n            if (typeof time === 'object' && time.getTime) {\n                options.seconds = (time.getDate() * SECONDS_PER_DAY) + (time.getHours() * SECONDS_PER_HOUR) +\n                  (time.getMinutes() * 60) + time.getSeconds();\n                options.countdown = false;\n            } else if (typeof time === 'string') {\n                tt = time.split(':');\n                sec = 0;\n                m = 1;\n\n                while (tt.length) {\n                    t = tt.pop();\n                    sec += t * m;\n                    m *= 60;\n                }\n                options.seconds = sec;\n                options.countdown = false;\n            }\n        }\n\n        if (options.countdown !== false\n                && options.seconds > SECONDS_PER_DAY\n                && typeof options.displayDays === 'undefined') {\n            days = Math.floor(options.seconds / SECONDS_PER_DAY);\n            options.displayDays = (days < 10 && 1) || (days < 100 && 2) || 3;\n        } else if (options.displayDays === true) {\n            options.displayDays = 3;\n        } else if (options.displayDays) {\n            options.displayDays = options.displayDays > 0 ? Math.floor(options.displayDays) : 3;\n        }\n\n        return this.each(function () {\n            var $this = $(this);\n            var data = $this.data();\n            var defs, opt, i, css, language, left, ulhtml, style, dhtml1, dhtml2, dot2, maxWidth,\n                captionSize, fsStyleVal, fsStyle, thtml, marginRight, dhtml, $digits, dif, vals, limits;\n\n            if (data.intervalId) {\n                clearInterval(data.intervalId);\n                data.intervalId = null;\n            }\n\n            if (!data.vals) {\n                // new clock\n                if (data.opt) {\n                    opt = data.options;\n                } else {\n                    opt = options;\n                }\n\n                // clone the defaults object\n                defs = Object.keys(defaults).reduce(function (obj, key) {\n                    if (Array.isArray(defaults[key])) {\n                        obj[key] = defaults[key].slice(0);\n                    } else {\n                        obj[key] = defaults[key];\n                    }\n                    return obj;\n                }, {});\n\n                data = $.extend(defs, opt);\n                data.options = opt;\n\n                data.height = Math.round((data.fontSize * 100) / 93) || data.height;\n                data.width = Math.round((data.fontSize * 0.8) + (data.height * 0.13)) || data.width;\n                data.displayHours = !!(data.displayDays || data.displayHours);\n\n                css = {\n                    fontFamily: data.fontFamily\n                };\n                if (data.fontSize > 0) {\n                    css.fontSize = data.fontSize + 'px';\n                }\n                language = data.languages[data.lang] || dictionary[data.lang];\n\n                $this\n                    .addClass('timeTo')\n                    .addClass('timeTo-' + data.theme)\n                    .css(css);\n\n                left = Math.round(data.height / 10);\n                ulhtml = '<ul style=\"left:' + left + 'px; top:-' + data.height + 'px\"><li>0</li><li>0</li></ul></div>';\n                style = data.fontSize\n                    ? ' style=\"width:' + data.width + 'px; height:' + data.height + 'px;\"'\n                    : ' style=\"\"';\n                dhtml1 = '<div class=\"first\"' + style + '>' + ulhtml;\n                dhtml2 = '<div' + style + '>' + ulhtml;\n                dot2 = '<span>:</span>';\n                maxWidth = Math.round((data.width * 2) + 3);\n                captionSize = data.captionSize || (data.fontSize && Math.round(data.fontSize * 0.43));\n                fsStyleVal = captionSize ? 'font-size:' + captionSize + 'px;' : '';\n                fsStyle = captionSize ? ' style=\"' + fsStyleVal + '\"' : '';\n\n                thtml = (data.displayCaptions\n                    ? (data.displayHours\n                        ? '<figure style=\"max-width:' + maxWidth + 'px\">$1<figcaption' + fsStyle + '>'\n                            + language.hours + '</figcaption></figure>' + dot2\n                        : '')\n                        + '<figure style=\"max-width:' + maxWidth + 'px\">$1<figcaption' + fsStyle + '>' + language.min\n                        + '</figcaption></figure>' + dot2\n                        + '<figure style=\"max-width:' + maxWidth + 'px\">$1<figcaption' + fsStyle + '>' + language.sec\n                        + '</figcaption></figure>'\n                    : (data.displayHours ? '$1' + dot2 : '') + '$1' + dot2 + '$1'\n                ).replace(/\\$1/g, dhtml1 + dhtml2);\n\n                if (data.displayDays > 0) {\n                    marginRight = Math.round(data.fontSize * 0.4 || defaults.gap);\n                    dhtml = dhtml1;\n\n                    for (i = data.displayDays - 1; i > 0; i -= 1) {\n                        dhtml += i === 1\n                            ? dhtml2.replace('\">', 'margin-right:' + marginRight + 'px\">')\n                            : dhtml2;\n                    }\n\n                    if (data.displayDays === 1) {\n                        dhtml = dhtml.replace('\">', 'margin-right:' + marginRight + 'px\">');\n                    }\n\n                    thtml = (data.displayCaptions\n                        ? '<figure style=\"width:' + ((data.width * data.displayDays) + marginRight + 4) + 'px\">$1'\n                            + '<figcaption style=\"' + fsStyleVal + 'padding-right:' + marginRight + 'px\">'\n                            + language.days + '</figcaption></figure>'\n                        : '$1')\n                        .replace(/\\$1/, dhtml) + thtml;\n                }\n                $this.html(thtml);\n            } else if (method !== 'reset') {\n                // exists clock\n                $.extend(data, options);\n            }\n\n            $digits = $this.find('div');\n\n            if ($digits.length < data.vals.length) {\n                dif = data.vals.length - $digits.length;\n                vals = data.vals;\n                limits = data.limits;\n\n                data.vals = [];\n                data.limits = [];\n                for (i = 0; i < $digits.length; i += 1) {\n                    data.vals[i] = vals[dif + i];\n                    data.limits[i] = limits[dif + i];\n                }\n                data.iSec = data.vals.length - 1;\n                data.iHour = data.vals.length - 5;\n            }\n            data.sec = data.seconds;\n            $this.data(data);\n\n            if (method && methods[method]) {\n                methods[method].call($this, data.seconds);\n            } else if (data.start) {\n                methods.start.call($this, data.seconds);\n            } else {\n                init.call($this, data.seconds);\n            }\n        });\n    };\n\n\n    function init(sec, force) {\n        var data = this.data();\n        var $digits = this.find('ul');\n        var isInterval = false;\n        var days, rest, hours, minutes, secs, str, i, j, val;\n\n        if (!data.vals || $digits.length === 0) {\n            return;\n        }\n\n        if (!sec) {\n            sec = data.seconds;\n        }\n\n        if (data.intervalId) {\n            isInterval = true;\n            clearTimeout(data.intervalId);\n        }\n\n\n        days = Math.floor(sec / SECONDS_PER_DAY);\n        rest = days * SECONDS_PER_DAY;\n        hours = Math.floor((sec - rest) / SECONDS_PER_HOUR);\n\n        rest += hours * SECONDS_PER_HOUR;\n\n        minutes = Math.floor((sec - rest) / 60);\n\n        rest += minutes * 60;\n\n        secs = sec - rest;\n        str = (days < 100 ? '0' + (days < 10 ? '0' : '') : '')\n            + days + (hours < 10 ? '0' : '') + hours + (minutes < 10 ? '0' : '')\n            + minutes + (secs < 10 ? '0' : '') + secs;\n\n\n        for (i = data.vals.length - 1, j = str.length - 1; i >= 0; i -= 1, j -= 1) {\n            val = parseInt(str.substr(j, 1), 10);\n            data.vals[i] = val;\n            $digits.eq(i).children().html(val);\n        }\n        if (isInterval || force) {\n            data.ttStartTime = Date.now();\n            data.intervalId = setTimeout(tick.bind(this), 1000);\n            this.data('intervalId', data.intervalId);\n        }\n    }\n\n    /**\n     * Switch specified digit by digit index\n     * @param {number} - digit index\n     */\n    function tick(digit) {\n        var me = this;\n        var $digits = this.find('ul');\n        var data = this.data();\n        var n, $ul, $li, step, tickTimeout, timeDiff;\n\n        if (!data.vals || $digits.length === 0) {\n            if (data.intervalId) {\n                clearTimeout(data.intervalId);\n                this.data('intervalId', null);\n            }\n            if (data.callback) {\n                data.callback();\n            }\n            return;\n        }\n        if (typeof digit === 'undefined') {\n            digit = data.iSec;\n        }\n\n\n        this.data('tickCount', data.tickCount + 1);\n\n        n = data.vals[digit];\n        $ul = $digits.eq(digit);\n        $li = $ul.children();\n        step = data.countdown ? -1 : 1;\n\n        $li.eq(1).html(n);\n        n += step;\n\n\n        if (typeof data.step === 'function' && data.tickCount % data.stepCount === 0) { // simplified if-block\n            this.data('tickCount', 0); // prevent number overload\n            data.step();\n        }\n\n        if (digit === data.iSec) {\n            tickTimeout = data.tickTimeout;\n            timeDiff = Date.now() - data.ttStartTime;\n\n            data.sec += step;\n\n            tickTimeout += (Math.abs(data.seconds - data.sec) * tickTimeout) - timeDiff;\n\n            data.intervalId = setTimeout(tick.bind(this), tickTimeout);\n        }\n\n        if (n < 0 || n > data.limits[digit]) {\n            if (n < 0) {\n                n = data.limits[digit];\n                // fix for hours when day changing\n                if (digit === data.iHour\n                    && data.displayDays > 0\n                    && data.vals[digit - 1] === 0) {\n                    n = 3;\n                }\n            } else {\n                n = 0;\n            }\n\n            if (digit > 0) {\n                tick.call(this, digit - 1);\n            }\n        } else if (!data.countdown // fix for hours when day changing in clock mode\n            && digit === data.iHour\n            && data.displayDays > 0\n            && data.vals[digit - 1] === 2 && data.vals[digit] === 3) {\n            n = 0;\n            tick.call(this, digit - 1);\n        }\n        $li.eq(0).html(n);\n\n        if ($.support.transition) {\n            $ul.addClass('transition');\n            $ul.css({ top: 0 });\n\n            setTimeout(function () {\n                $ul.removeClass('transition');\n                $li.eq(1).html(n);\n                $ul.css({ top: '-' + data.height + 'px' });\n\n                if (step > 0 || digit !== data.iSec) {\n                    return;\n                }\n\n                if (data.sec === data.countdownAlertLimit) {\n                    $digits.parent().addClass('timeTo-alert');\n                }\n\n                if (data.sec === 0) {\n                    $digits.parent().removeClass('timeTo-alert');\n\n                    if (data.intervalId) {\n                        clearTimeout(data.intervalId);\n                        me.data('intervalId', null);\n                    }\n\n                    if (typeof data.callback === 'function') {\n                        data.callback();\n                    }\n                }\n            }, 410);\n        } else {\n            $ul.stop().animate({ top: 0 }, 400, digit !== data.iSec ? null : function () {\n                $li.eq(1).html(n);\n                $ul.css({ top: '-' + data.height + 'px' });\n\n                if (step > 0 || digit !== data.iSec) {\n                    return;\n                }\n\n                if (data.sec === data.countdownAlertLimit) {\n                    $digits.parent().addClass('timeTo-alert');\n                } else if (data.sec === 0) {\n                    $digits.parent().removeClass('timeTo-alert');\n\n                    if (data.intervalId) {\n                        clearTimeout(data.intervalId);\n                        me.data('intervalId', null);\n                    }\n\n                    if (typeof data.callback === 'function') {\n                        data.callback();\n                    }\n                }\n            });\n        }\n        data.vals[digit] = n;\n    }\n\n    return $;\n}));\n"], "names": ["factory", "define", "amd", "exports", "module", "require", "j<PERSON><PERSON><PERSON>", "$", "thisStyle", "SECONDS_PER_DAY", "SECONDS_PER_HOUR", "defaults", "callback", "step", "stepCount", "captionSize", "countdown", "countdownAlertLimit", "displayCaptions", "displayDays", "displayHours", "fontFamily", "fontSize", "lang", "languages", "seconds", "start", "theme", "width", "height", "gap", "vals", "limits", "iSec", "iHour", "tickTimeout", "intervalId", "tickCount", "methods", "sec", "init", "call", "this", "setTimeout", "tick", "bind", "data", "now", "stop", "clearTimeout", "reset", "secs", "find", "css", "backgroundPosition", "parent", "removeClass", "dictionary", "en", "days", "hours", "min", "ru", "ua", "de", "fr", "es", "sp", "it", "nl", "no", "pt", "tr", "pl", "force", "rest", "minutes", "str", "i", "j", "val", "$digits", "isInterval", "length", "Math", "floor", "parseInt", "substr", "eq", "children", "html", "ttStartTime", "Date", "digit", "n", "$ul", "$li", "timeDiff", "me", "abs", "support", "transition", "addClass", "top", "animate", "undefined", "document", "body", "documentElement", "style", "WebkitTransition", "MozTransition", "MsTransition", "OTransition", "fn", "timeTo", "num", "method", "time", "tt", "m", "options", "arguments", "arg", "getTime", "extend", "isNaN", "getDate", "getHours", "getMinutes", "getSeconds", "split", "pop", "each", "language", "dhtml1", "dhtml2", "dot2", "fsStyleVal", "fsStyle", "thtml", "marginRight", "dhtml", "dif", "$this", "clearInterval", "opt", "defs", "Object", "keys", "reduce", "obj", "key", "Array", "isArray", "slice", "round", "ulhtml", "max<PERSON><PERSON><PERSON>", "replace"], "mappings": "AAWA,CAAC,SAAUA,GACe,YAAlB,OAAOC,QAAyBA,OAAOC,IAEvCD,OAAO,CAAC,UAAWD,CAAO,EACA,UAAnB,OAAOG,QAGdC,OAAOD,QAAUH,EAAQK,QAAQ,QAAQ,CAAC,EAG1CL,EAAQM,MAAM,CAEtB,EAAE,SAAUC,GACR,IAyFYC,EAzFRC,EAAkB,MAClBC,EAAmB,KAGnBC,EAAW,CACXC,SAAU,KACVC,KAAM,KACNC,UAAW,EACXC,YAAa,EACbC,UAAW,CAAA,EACXC,oBAAqB,GACrBC,gBAAiB,CAAA,EACjBC,YAAa,EACbC,aAAc,CAAA,EACdC,WAAY,oBACZC,SAAU,EACVC,KAAM,KACNC,UAAW,GACXC,QAAS,EACTC,MAAO,CAAA,EACPC,MAAO,QACPC,MAAO,GACPC,OAAQ,GACRC,IAAK,GACLC,KAAM,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAC/BC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GACjCC,KAAM,EACNC,MAAO,EACPC,YAAa,IACbC,WAAY,KACZC,UAAW,CACf,EAEIC,EAAU,CACVZ,MAAO,SAAUa,GAGTA,IACAC,EAAKC,KAAKC,KAAMH,CAAG,EACnBH,EAAaO,WAAWC,EAAKC,KAAKH,IAAI,EAAG,GAAI,EAG7CA,KAAKI,KAAK,cAAevC,EAAEwC,IAAI,CAAC,EAChCL,KAAKI,KAAK,aAAcV,CAAU,EAE1C,EAEAY,KAAM,WACF,IAAIF,EAAOJ,KAAKI,KAAK,EAMrB,OAJIA,EAAKV,aACLa,aAAaH,EAAKV,UAAU,EAC5BM,KAAKI,KAAK,aAAc,IAAI,GAEzBA,CACX,EAEAI,MAAO,SAAUX,GACb,IAAIO,EAAOR,EAAQU,KAAKP,KAAKC,IAAI,EAC7BS,EAAsB,KAAA,IAARZ,EAAsBO,EAAKrB,QAAUc,EAEvDG,KAAKU,KAAK,KAAK,EAAEC,IAAI,CAAEC,mBAAoB,aAAc,CAAC,EAC1DZ,KAAKU,KAAK,IAAI,EAAEG,OAAO,EAAEC,YAAY,cAAc,EAEnDhB,EAAKC,KAAKC,KAAMS,EAAM,CAAA,CAAI,CAC9B,CACJ,EAEIM,EAAa,CACbC,GAAI,CAAEC,KAAM,OAAUC,MAAO,QAAUC,IAAK,UAAYtB,IAAK,SAAU,EACvEuB,GAAI,CAAEH,KAAM,OAAUC,MAAO,QAAUC,IAAK,QAAYtB,IAAK,QAAS,EACtEwB,GAAI,CAAEJ,KAAM,OAAUC,MAAO,QAAUC,IAAK,SAAYtB,IAAK,QAAS,EACtEyB,GAAI,CAAEL,KAAM,MAAUC,MAAO,MAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxE0B,GAAI,CAAEN,KAAM,QAAUC,MAAO,SAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxE2B,GAAI,CAAEP,KAAM,OAAUC,MAAO,QAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxE4B,GAAI,CAAER,KAAM,OAAUC,MAAO,QAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxE6B,GAAI,CAAET,KAAM,SAAUC,MAAO,MAAUC,IAAK,SAAYtB,IAAK,SAAU,EACvE8B,GAAI,CAAEV,KAAM,QAAUC,MAAO,OAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxE+B,GAAI,CAAEX,KAAM,QAAUC,MAAO,QAAUC,IAAK,WAAYtB,IAAK,UAAW,EACxEgC,GAAI,CAAEZ,KAAM,OAAUC,MAAO,QAAUC,IAAK,UAAYtB,IAAK,UAAW,EACxEiC,GAAI,CAAEb,KAAM,MAAUC,MAAO,OAAUC,IAAK,SAAYtB,IAAK,QAAS,EACtEkC,GAAI,CAAEd,KAAM,MAAOC,MAAO,UAAWC,IAAK,SAAUtB,IAAK,SAAU,CACvE,EAqOA,SAASC,EAAKD,EAAKmC,GACf,IAGUC,EAAMf,EAAOgB,EAASzB,EAAM0B,EAAKC,EAAGC,EAAGC,EAH7ClC,EAAOJ,KAAKI,KAAK,EACjBmC,EAAUvC,KAAKU,KAAK,IAAI,EACxB8B,EAAa,CAAA,EAGjB,GAAKpC,EAAKf,MAA2B,IAAnBkD,EAAQE,OAA1B,CA8BA,IA1BK5C,EAAAA,GACKO,EAAKrB,QAGXqB,EAAKV,aACL8C,EAAa,CAAA,EACbjC,aAAaH,EAAKV,UAAU,GAKhCuC,GADAhB,EAAOyB,KAAKC,MAAM9C,EAAM9B,CAAe,GACzBA,EAGdkE,IAFAf,EAAQwB,KAAKC,OAAO9C,EAAMoC,GAAQjE,CAAgB,GAElCA,EAEhBkE,EAAUQ,KAAKC,OAAO9C,EAAMoC,GAAQ,EAAE,EAUjCG,EAAIhC,EAAKf,KAAKoD,OAAS,EAAGJ,GAL/BF,GAAOlB,EAAO,IAAM,KAAOA,EAAO,GAAK,IAAM,IAAM,IAC7CA,GAAQC,EAAQ,GAAK,IAAM,IAAMA,GAASgB,EAAU,GAAK,IAAM,IAC/DA,IAHNzB,EAAOZ,GAFPoC,GAAkB,GAAVC,IAKgB,GAAK,IAAM,IAAMzB,GAGFgC,OAAS,EAAQ,GAALL,EAAQA,EAAAA,EAAQC,EAAAA,EAC/DC,EAAMM,SAAST,EAAIU,OAAOR,EAAG,CAAC,EAAG,EAAE,EACnCjC,EAAKf,KAAK+C,GAAKE,EACfC,EAAQO,GAAGV,CAAC,EAAEW,SAAS,EAAEC,KAAKV,CAAG,GAEjCE,GAAcR,KACd5B,EAAK6C,YAAcC,KAAK7C,IAAI,EAC5BD,EAAKV,WAAaO,WAAWC,EAAKC,KAAKH,IAAI,EAAG,GAAI,EAClDA,KAAKI,KAAK,aAAcA,EAAKV,UAAU,EApC3C,CAsCJ,CAMA,SAASQ,EAAKiD,GACV,IAGIC,EAAGC,EAAKC,EAAKnF,EAAMsB,EAAa8D,EAHhCC,EAAKxD,KACLuC,EAAUvC,KAAKU,KAAK,IAAI,EACxBN,EAAOJ,KAAKI,KAAK,EAGhBA,EAAKf,MAA2B,IAAnBkD,EAAQE,QAUL,KAAA,IAAVU,IACPA,EAAQ/C,EAAKb,MAIjBS,KAAKI,KAAK,YAAaA,EAAKT,UAAY,CAAC,EAEzCyD,EAAIhD,EAAKf,KAAK8D,GACdE,EAAMd,EAAQO,GAAGK,CAAK,EACtBG,EAAMD,EAAIN,SAAS,EACnB5E,EAAOiC,EAAK9B,UAAY,CAAC,EAAI,EAE7BgF,EAAIR,GAAG,CAAC,EAAEE,KAAKI,CAAC,EAChBA,GAAKjF,EAGoB,YAArB,OAAOiC,EAAKjC,MAAuBiC,EAAKT,UAAYS,EAAKhC,WAAc,IACvE4B,KAAKI,KAAK,YAAa,CAAC,EACxBA,EAAKjC,KAAK,GAGVgF,IAAU/C,EAAKb,OACfE,EAAcW,EAAKX,YACnB8D,EAAWL,KAAK7C,IAAI,EAAID,EAAK6C,YAE7B7C,EAAKP,KAAO1B,EAEZsB,GAAgBiD,KAAKe,IAAIrD,EAAKrB,QAAUqB,EAAKP,GAAG,EAAIJ,EAAe8D,EAEnEnD,EAAKV,WAAaO,WAAWC,EAAKC,KAAKH,IAAI,EAAGP,CAAW,GAGzD2D,EAAI,GAAKA,EAAIhD,EAAKd,OAAO6D,IACrBC,EAAI,GACJA,EAAIhD,EAAKd,OAAO6D,GAEZA,IAAU/C,EAAKZ,OACO,EAAnBY,EAAK3B,aACoB,IAAzB2B,EAAKf,KAAK8D,EAAQ,KACrBC,EAAI,IAGRA,EAAI,EAGI,EAARD,GACAjD,EAAKH,KAAKC,KAAMmD,EAAQ,CAAC,GAEtB,CAAC/C,EAAK9B,WACV6E,IAAU/C,EAAKZ,OACI,EAAnBY,EAAK3B,aACoB,IAAzB2B,EAAKf,KAAK8D,EAAQ,IAAiC,IAArB/C,EAAKf,KAAK8D,KAC3CC,EAAI,EACJlD,EAAKH,KAAKC,KAAMmD,EAAQ,CAAC,GAE7BG,EAAIR,GAAG,CAAC,EAAEE,KAAKI,CAAC,EAEZvF,EAAE6F,QAAQC,YACVN,EAAIO,SAAS,YAAY,EACzBP,EAAI1C,IAAI,CAAEkD,IAAK,CAAE,CAAC,EAElB5D,WAAW,WACPoD,EAAIvC,YAAY,YAAY,EAC5BwC,EAAIR,GAAG,CAAC,EAAEE,KAAKI,CAAC,EAChBC,EAAI1C,IAAI,CAAEkD,IAAK,IAAMzD,EAAKjB,OAAS,IAAK,CAAC,EAE9B,EAAPhB,GAAYgF,IAAU/C,EAAKb,OAI3Ba,EAAKP,MAAQO,EAAK7B,qBAClBgE,EAAQ1B,OAAO,EAAE+C,SAAS,cAAc,EAG3B,IAAbxD,EAAKP,MACL0C,EAAQ1B,OAAO,EAAEC,YAAY,cAAc,EAEvCV,EAAKV,aACLa,aAAaH,EAAKV,UAAU,EAC5B8D,EAAGpD,KAAK,aAAc,IAAI,GAGD,YAAzB,OAAOA,EAAKlC,WACZkC,EAAKlC,SAAS,EAG1B,EAAG,GAAG,GAENmF,EAAI/C,KAAK,EAAEwD,QAAQ,CAAED,IAAK,CAAE,EAAG,IAAKV,IAAU/C,EAAKb,KAAO,KAAO,WAC7D+D,EAAIR,GAAG,CAAC,EAAEE,KAAKI,CAAC,EAChBC,EAAI1C,IAAI,CAAEkD,IAAK,IAAMzD,EAAKjB,OAAS,IAAK,CAAC,EAE9B,EAAPhB,GAAYgF,IAAU/C,EAAKb,OAI3Ba,EAAKP,MAAQO,EAAK7B,oBAClBgE,EAAQ1B,OAAO,EAAE+C,SAAS,cAAc,EACpB,IAAbxD,EAAKP,MACZ0C,EAAQ1B,OAAO,EAAEC,YAAY,cAAc,EAEvCV,EAAKV,aACLa,aAAaH,EAAKV,UAAU,EAC5B8D,EAAGpD,KAAK,aAAc,IAAI,GAGD,YAAzB,OAAOA,EAAKlC,WACZkC,EAAKlC,SAAS,EAG1B,CAAC,EAELkC,EAAKf,KAAK8D,GAASC,IAzHXhD,EAAKV,aACLa,aAAaH,EAAKV,UAAU,EAC5BM,KAAKI,KAAK,aAAc,IAAI,GAE5BA,EAAKlC,UACLkC,EAAKlC,SAAS,EAqH1B,CAEA,OAxZoC,KAAA,IAAzBL,EAAE6F,QAAQC,aACjB9F,EAAE6F,QAAQC,WAGiCI,KAAAA,KADnCjG,GADWkG,SAASC,MAAQD,SAASE,iBAChBC,OACDR,YACcI,KAAAA,IAA/BjG,EAAUsG,kBACkBL,KAAAA,IAA5BjG,EAAUuG,eACiBN,KAAAA,IAA3BjG,EAAUwG,cACgBP,KAAAA,IAA1BjG,EAAUyG,aAMzB1G,EAAE2G,GAAGC,OAAS,WAKV,IAJA,IAEYC,EAAKC,EAAQC,EAAYC,EAAIhF,EAAKiF,EAF1CC,EAAU,GACV1E,EAAM6C,KAAK7C,IAAI,EAGdgC,EAAI,EAAGA,EAAI2C,UAAUvC,OAAQJ,GAAK,EACnC4C,EAAMD,UAAU3C,GACN,IAANA,GAA0B,UAAf,OAAO4C,EAClBN,EAASM,EACa,UAAf,OAAOA,EACa,YAAvB,OAAOA,EAAIC,QAEXH,EAAQN,OAASQ,EAGjBF,EAAUlH,EAAEsH,OAAOJ,EAASE,CAAG,EAEb,YAAf,OAAOA,EAEdF,EAAQ7G,SAAW+G,GAEnBP,EAAM9B,SAASqC,EAAK,EAAE,EAEjBG,MAAMV,CAAG,IACVK,EAAQhG,QAAU2F,IAM9B,GAAIK,EAAQN,OACJM,EAAQN,OAAOS,QAEfN,EAAOG,EAAQN,OAAOS,QAAQ,EACG,UAA1B,OAAOH,EAAQN,SAEtBG,EAAOG,EAAQN,QAGfM,EAAQhG,QADDsB,EAAPuE,EACkBlC,KAAKC,OAAOiC,EAAOvE,GAAO,GAAI,EAE9B,OAEnB,GAAI0E,EAAQH,MAAQ,CAACG,EAAQhG,QAOhC,GAAoB,UAAhB,OAJC6F,GAFLA,EAAOG,EAAQH,OAGJ,IAAI1B,KAAK7C,CAAG,IAGSuE,EAAKM,QACjCH,EAAQhG,QAAW6F,EAAKS,QAAQ,EAAItH,EAAoB6G,EAAKU,SAAS,EAAItH,EACnD,GAApB4G,EAAKW,WAAW,EAAUX,EAAKY,WAAW,EAC7CT,EAAQzG,UAAY,CAAA,OACjB,GAAoB,UAAhB,OAAOsG,EAAmB,CAKjC,IAJAC,EAAKD,EAAKa,MAAM,GAAG,EACnB5F,EAAM,EACNiF,EAAI,EAEGD,EAAGpC,QAEN5C,GADIgF,EAAGa,IAAI,EACAZ,EACXA,GAAK,GAETC,EAAQhG,QAAUc,EAClBkF,EAAQzG,UAAY,CAAA,CACxB,CAcJ,MAX0B,CAAA,IAAtByG,EAAQzG,WACDyG,EAAQhG,QAAUhB,GACa,KAAA,IAAxBgH,EAAQtG,aACtBwC,EAAOyB,KAAKC,MAAMoC,EAAQhG,QAAUhB,CAAe,EACnDgH,EAAQtG,aAAewC,EAAO,GAAM,EAAOA,EAAO,KAAO,IAAM,GAChC,CAAA,IAAxB8D,EAAQtG,YACfsG,EAAQtG,YAAc,EACfsG,EAAQtG,cACfsG,EAAQtG,YAAoC,EAAtBsG,EAAQtG,YAAkBiE,KAAKC,MAAMoC,EAAQtG,WAAW,EAAI,GAG/EuB,KAAK2F,KAAK,WACb,IAEevD,EAAQwD,EAA+BC,EAAQC,EAAQC,EACrDC,EAAYC,EAASC,EAAOC,EAAaC,EAAO7D,EAAS8D,EAAKhH,EAAMC,EAHjFgH,EAAQzI,EAAEmC,IAAI,EACdI,EAAOkG,EAAMlG,KAAK,EAStB,GALIA,EAAKV,aACL6G,cAAcnG,EAAKV,UAAU,EAC7BU,EAAKV,WAAa,MAGjBU,EAAKf,KAqFY,UAAXsF,GAEP9G,EAAEsH,OAAO/E,EAAM2E,CAAO,MAvFV,CA+DZ,GA5DIyB,EADApG,EAAKoG,IACCpG,EAAK2E,QAELA,EAIV0B,EAAOC,OAAOC,KAAK1I,CAAQ,EAAE2I,OAAO,SAAUC,EAAKC,GAM/C,OALIC,MAAMC,QAAQ/I,EAAS6I,EAAI,EAC3BD,EAAIC,GAAO7I,EAAS6I,GAAKG,MAAM,CAAC,EAEhCJ,EAAIC,GAAO7I,EAAS6I,GAEjBD,CACX,EAAG,EAAE,GAELzG,EAAOvC,EAAEsH,OAAOsB,EAAMD,CAAG,GACpBzB,QAAUyB,EAEfpG,EAAKjB,OAASuD,KAAKwE,MAAuB,IAAhB9G,EAAKxB,SAAkB,EAAE,GAAKwB,EAAKjB,OAC7DiB,EAAKlB,MAAQwD,KAAKwE,MAAuB,GAAhB9G,EAAKxB,SAAiC,IAAdwB,EAAKjB,MAAc,GAAKiB,EAAKlB,MAC9EkB,EAAK1B,aAAe,EAAG0B,CAAAA,EAAK3B,aAAe2B,CAAAA,EAAK1B,cAEhDiC,EAAM,CACFhC,WAAYyB,EAAKzB,UACrB,EACoB,EAAhByB,EAAKxB,WACL+B,EAAI/B,SAAWwB,EAAKxB,SAAW,MAEnCgH,EAAWxF,EAAKtB,UAAUsB,EAAKvB,OAASkC,EAAWX,EAAKvB,MAExDyH,EACK1C,SAAS,QAAQ,EACjBA,SAAS,UAAYxD,EAAKnB,KAAK,EAC/B0B,IAAIA,CAAG,EAGZwG,EAAS,mBADFzE,KAAKwE,MAAM9G,EAAKjB,OAAS,EAAE,EACG,YAAciB,EAAKjB,OAAS,sCAIjE0G,EAAS,sBAHT1B,EAAQ/D,EAAKxB,SACP,iBAAmBwB,EAAKlB,MAAQ,cAAgBkB,EAAKjB,OAAS,OAC9D,aACkC,IAAMgI,EAC9CrB,EAAS,OAAS3B,EAAQ,IAAMgD,EAChCpB,EAAO,iBACPqB,EAAW1E,KAAKwE,MAAoB,EAAb9G,EAAKlB,MAAa,CAAC,EAE1C8G,GADA3H,EAAc+B,EAAK/B,aAAgB+B,EAAKxB,UAAY8D,KAAKwE,MAAsB,IAAhB9G,EAAKxB,QAAe,GACxD,aAAeP,EAAc,MAAQ,GAChE4H,EAAU5H,EAAc,WAAa2H,EAAa,IAAM,GAExDE,GAAS9F,EAAK5B,iBACP4B,EAAK1B,aACF,4BAA8B0I,EAAW,oBAAsBnB,EAAU,IACrEL,EAAS1E,MAAQ,yBAA2B6E,EAChD,IACA,4BAA8BqB,EAAW,oBAAsBnB,EAAU,IAAML,EAASzE,IACxF,yBAA2B4E,EAC3B,4BAA8BqB,EAAW,oBAAsBnB,EAAU,IAAML,EAAS/F,IACxF,0BACHO,EAAK1B,aAAe,KAAOqH,EAAO,IAAM,KAAOA,EAAO,MAC3DsB,QAAQ,OAAQxB,EAASC,CAAM,EAEV,EAAnB1F,EAAK3B,YAAiB,CAItB,IAHA0H,EAAczD,KAAKwE,MAAsB,GAAhB9G,EAAKxB,UAAkBX,EAASmB,GAAG,EAC5DgH,EAAQP,EAEHzD,EAAIhC,EAAK3B,YAAc,EAAO,EAAJ2D,EAAOA,EAAAA,EAClCgE,GAAe,IAANhE,EACH0D,EAAOuB,QAAQ,KAAM,gBAAkBlB,EAAc,MAAM,EAC3DL,EAGe,IAArB1F,EAAK3B,cACL2H,EAAQA,EAAMiB,QAAQ,KAAM,gBAAkBlB,EAAc,MAAM,GAGtED,GAAS9F,EAAK5B,gBACR,yBAA4B4B,EAAKlB,MAAQkB,EAAK3B,YAAe0H,EAAc,GACvE,4BAAwBH,EAAa,iBAAmBG,EAAc,OACtEP,EAAS3E,KAAO,yBACpB,MACDoG,QAAQ,MAAOjB,CAAK,EAAIF,CACjC,CACAI,EAAMtD,KAAKkD,CAAK,CACpB,CAOA,IAFA3D,EAAU+D,EAAM5F,KAAK,KAAK,GAEd+B,OAASrC,EAAKf,KAAKoD,OAAQ,CAOnC,IANA4D,EAAMjG,EAAKf,KAAKoD,OAASF,EAAQE,OACjCpD,EAAOe,EAAKf,KACZC,EAASc,EAAKd,OAEdc,EAAKf,KAAO,GACZe,EAAKd,OAAS,GACT8C,EAAI,EAAGA,EAAIG,EAAQE,OAAQL,GAAK,EACjChC,EAAKf,KAAK+C,GAAK/C,EAAKgH,EAAMjE,GAC1BhC,EAAKd,OAAO8C,GAAK9C,EAAO+G,EAAMjE,GAElChC,EAAKb,KAAOa,EAAKf,KAAKoD,OAAS,EAC/BrC,EAAKZ,MAAQY,EAAKf,KAAKoD,OAAS,CACpC,CACArC,EAAKP,IAAMO,EAAKrB,QAChBuH,EAAMlG,KAAKA,CAAI,GAEXuE,GAAU/E,EAAQ+E,GAClB/E,EAAQ+E,GACDvE,EAAKpB,MACZY,EAAQZ,MAERc,GAJgBC,KAAKuG,EAAOlG,EAAKrB,OAAO,CAMhD,CAAC,CACL,EA0LOlB,CACX,CAAE"}
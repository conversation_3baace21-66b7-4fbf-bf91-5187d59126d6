{"version": 3, "file": "jquery.steps.min.js", "sources": ["jquery.steps.min.js"], "sourcesContent": ["/*! \n * jQuery Steps v1.1.0 - 09/04/2014\n * Copyright (c) 2014 <PERSON> (http://www.jquery-steps.com)\n * Licensed under MIT http://www.opensource.org/licenses/MIT\n */\n;(function ($, undefined)\n{\n$.fn.extend({\n    _aria: function (name, value)\n    {\n        return this.attr(\"aria-\" + name, value);\n    },\n\n    _removeAria: function (name)\n    {\n        return this.removeAttr(\"aria-\" + name);\n    },\n\n    _enableAria: function (enable)\n    {\n        return (enable == null || enable) ? \n            this.removeClass(\"disabled\")._aria(\"disabled\", \"false\") : \n            this.addClass(\"disabled\")._aria(\"disabled\", \"true\");\n    },\n\n    _showAria: function (show)\n    {\n        return (show == null || show) ? \n            this.show()._aria(\"hidden\", \"false\") : \n            this.hide()._aria(\"hidden\", \"true\");\n    },\n\n    _selectAria: function (select)\n    {\n        return (select == null || select) ? \n            this.addClass(\"current\")._aria(\"selected\", \"true\") : \n            this.removeClass(\"current\")._aria(\"selected\", \"false\");\n    },\n\n    _id: function (id)\n    {\n        return (id) ? this.attr(\"id\", id) : this.attr(\"id\");\n    }\n});\n\nif (!String.prototype.format)\n{\n    String.prototype.format = function()\n    {\n        var args = (arguments.length === 1 && $.isArray(arguments[0])) ? arguments[0] : arguments;\n        var formattedString = this;\n        for (var i = 0; i < args.length; i++)\n        {\n            var pattern = new RegExp(\"\\\\{\" + i + \"\\\\}\", \"gm\");\n            formattedString = formattedString.replace(pattern, args[i]);\n        }\n        return formattedString;\n    };\n}\n\n/**\n * A global unique id count.\n *\n * @static\n * @private\n * @property _uniqueId\n * @type Integer\n **/\nvar _uniqueId = 0;\n\n/**\n * The plugin prefix for cookies.\n *\n * @final\n * @private\n * @property _cookiePrefix\n * @type String\n **/\nvar _cookiePrefix = \"jQu3ry_5teps_St@te_\";\n\n/**\n * Suffix for the unique tab id.\n *\n * @final\n * @private\n * @property _tabSuffix\n * @type String\n * @since 0.9.7\n **/\nvar _tabSuffix = \"-t-\";\n\n/**\n * Suffix for the unique tabpanel id.\n *\n * @final\n * @private\n * @property _tabpanelSuffix\n * @type String\n * @since 0.9.7\n **/\nvar _tabpanelSuffix = \"-p-\";\n\n/**\n * Suffix for the unique title id.\n *\n * @final\n * @private\n * @property _titleSuffix\n * @type String\n * @since 0.9.7\n **/\nvar _titleSuffix = \"-h-\";\n\n/**\n * An error message for an \"index out of range\" error.\n *\n * @final\n * @private\n * @property _indexOutOfRangeErrorMessage\n * @type String\n **/\nvar _indexOutOfRangeErrorMessage = \"Index out of range.\";\n\n/**\n * An error message for an \"missing corresponding element\" error.\n *\n * @final\n * @private\n * @property _missingCorrespondingElementErrorMessage\n * @type String\n **/\nvar _missingCorrespondingElementErrorMessage = \"One or more corresponding step {0} are missing.\";\n\n/**\n * Adds a step to the cache.\n *\n * @static\n * @private\n * @method addStepToCache\n * @param wizard {Object} A jQuery wizard object\n * @param step {Object} The step object to add\n **/\nfunction addStepToCache(wizard, step)\n{\n    getSteps(wizard).push(step);\n}\n\nfunction analyzeData(wizard, options, state)\n{\n    var stepTitles = wizard.children(options.headerTag),\n        stepContents = wizard.children(options.bodyTag);\n\n    // Validate content\n    if (stepTitles.length > stepContents.length)\n    {\n        throwError(_missingCorrespondingElementErrorMessage, \"contents\");\n    }\n    else if (stepTitles.length < stepContents.length)\n    {\n        throwError(_missingCorrespondingElementErrorMessage, \"titles\");\n    }\n        \n    var startIndex = options.startIndex;\n\n    state.stepCount = stepTitles.length;\n\n    // Tries to load the saved state (step position)\n    if (options.saveState && $.cookie)\n    {\n        var savedState = $.cookie(_cookiePrefix + getUniqueId(wizard));\n        // Sets the saved position to the start index if not undefined or out of range \n        var savedIndex = parseInt(savedState, 0);\n        if (!isNaN(savedIndex) && savedIndex < state.stepCount)\n        {\n            startIndex = savedIndex;\n        }\n    }\n\n    state.currentIndex = startIndex;\n\n    stepTitles.each(function (index)\n    {\n        var item = $(this), // item == header\n            content = stepContents.eq(index),\n            modeData = content.data(\"mode\"),\n            mode = (modeData == null) ? contentMode.html : getValidEnumValue(contentMode,\n                (/^\\s*$/.test(modeData) || isNaN(modeData)) ? modeData : parseInt(modeData, 0)),\n            contentUrl = (mode === contentMode.html || content.data(\"url\") === undefined) ?\n                \"\" : content.data(\"url\"),\n            contentLoaded = (mode !== contentMode.html && content.data(\"loaded\") === \"1\"),\n            step = $.extend({}, stepModel, {\n                title: item.html(),\n                content: (mode === contentMode.html) ? content.html() : \"\",\n                contentUrl: contentUrl,\n                contentMode: mode,\n                contentLoaded: contentLoaded\n            });\n\n        addStepToCache(wizard, step);\n    });\n}\n\n/**\n * Triggers the onCanceled event.\n *\n * @static\n * @private\n * @method cancel\n * @param wizard {Object} The jQuery wizard object\n **/\nfunction cancel(wizard)\n{\n    wizard.triggerHandler(\"canceled\");\n}\n\nfunction decreaseCurrentIndexBy(state, decreaseBy)\n{\n    return state.currentIndex - decreaseBy;\n}\n\n/**\n * Removes the control functionality completely and transforms the current state to the initial HTML structure.\n *\n * @static\n * @private\n * @method destroy\n * @param wizard {Object} A jQuery wizard object\n **/\nfunction destroy(wizard, options)\n{\n    var eventNamespace = getEventNamespace(wizard);\n\n    // Remove virtual data objects from the wizard\n    wizard.unbind(eventNamespace).removeData(\"uid\").removeData(\"options\")\n        .removeData(\"state\").removeData(\"steps\").removeData(\"eventNamespace\")\n        .find(\".actions a\").unbind(eventNamespace);\n\n    // Remove attributes and CSS classes from the wizard\n    wizard.removeClass(options.clearFixCssClass + \" vertical\");\n\n    var contents = wizard.find(\".content > *\");\n\n    // Remove virtual data objects from panels and their titles\n    contents.removeData(\"loaded\").removeData(\"mode\").removeData(\"url\");\n\n    // Remove attributes, CSS classes and reset inline styles on all panels and their titles\n    contents.removeAttr(\"id\").removeAttr(\"role\").removeAttr(\"tabindex\")\n        .removeAttr(\"class\").removeAttr(\"style\")._removeAria(\"labelledby\")\n        ._removeAria(\"hidden\");\n\n    // Empty panels if the mode is set to 'async' or 'iframe'\n    wizard.find(\".content > [data-mode='async'],.content > [data-mode='iframe']\").empty();\n\n    var wizardSubstitute = $(\"<{0} class=\\\"{1}\\\"></{0}>\".format(wizard.get(0).tagName, wizard.attr(\"class\")));\n\n    var wizardId = wizard._id();\n    if (wizardId != null && wizardId !== \"\")\n    {\n        wizardSubstitute._id(wizardId);\n    }\n\n    wizardSubstitute.html(wizard.find(\".content\").html());\n    wizard.after(wizardSubstitute);\n    wizard.remove();\n\n    return wizardSubstitute;\n}\n\n/**\n * Triggers the onFinishing and onFinished event.\n *\n * @static\n * @private\n * @method finishStep\n * @param wizard {Object} The jQuery wizard object\n * @param state {Object} The state container of the current wizard\n **/\nfunction finishStep(wizard, state)\n{\n    var currentStep = wizard.find(\".steps li\").eq(state.currentIndex);\n\n    if (wizard.triggerHandler(\"finishing\", [state.currentIndex]))\n    {\n        currentStep.addClass(\"done\").removeClass(\"error\");\n        wizard.triggerHandler(\"finished\", [state.currentIndex]);\n    }\n    else\n    {\n        currentStep.addClass(\"error\");\n    }\n}\n\n/**\n * Gets or creates if not exist an unique event namespace for the given wizard instance.\n *\n * @static\n * @private\n * @method getEventNamespace\n * @param wizard {Object} A jQuery wizard object\n * @return {String} Returns the unique event namespace for the given wizard\n */\nfunction getEventNamespace(wizard)\n{\n    var eventNamespace = wizard.data(\"eventNamespace\");\n\n    if (eventNamespace == null)\n    {\n        eventNamespace = \".\" + getUniqueId(wizard);\n        wizard.data(\"eventNamespace\", eventNamespace);\n    }\n\n    return eventNamespace;\n}\n\nfunction getStepAnchor(wizard, index)\n{\n    var uniqueId = getUniqueId(wizard);\n\n    return wizard.find(\"#\" + uniqueId + _tabSuffix + index);\n}\n\nfunction getStepPanel(wizard, index)\n{\n    var uniqueId = getUniqueId(wizard);\n\n    return wizard.find(\"#\" + uniqueId + _tabpanelSuffix + index);\n}\n\nfunction getStepTitle(wizard, index)\n{\n    var uniqueId = getUniqueId(wizard);\n\n    return wizard.find(\"#\" + uniqueId + _titleSuffix + index);\n}\n\nfunction getOptions(wizard)\n{\n    return wizard.data(\"options\");\n}\n\nfunction getState(wizard)\n{\n    return wizard.data(\"state\");\n}\n\nfunction getSteps(wizard)\n{\n    return wizard.data(\"steps\");\n}\n\n/**\n * Gets a specific step object by index.\n *\n * @static\n * @private\n * @method getStep\n * @param index {Integer} An integer that belongs to the position of a step\n * @return {Object} A specific step object\n **/\nfunction getStep(wizard, index)\n{\n    var steps = getSteps(wizard);\n\n    if (index < 0 || index >= steps.length)\n    {\n        throwError(_indexOutOfRangeErrorMessage);\n    }\n\n    return steps[index];\n}\n\n/**\n * Gets or creates if not exist an unique id from the given wizard instance.\n *\n * @static\n * @private\n * @method getUniqueId\n * @param wizard {Object} A jQuery wizard object\n * @return {String} Returns the unique id for the given wizard\n */\nfunction getUniqueId(wizard)\n{\n    var uniqueId = wizard.data(\"uid\");\n\n    if (uniqueId == null)\n    {\n        uniqueId = wizard._id();\n        if (uniqueId == null)\n        {\n            uniqueId = \"steps-uid-\".concat(_uniqueId);\n            wizard._id(uniqueId);\n        }\n\n        _uniqueId++;\n        wizard.data(\"uid\", uniqueId);\n    }\n\n    return uniqueId;\n}\n\n/**\n * Gets a valid enum value by checking a specific enum key or value.\n * \n * @static\n * @private\n * @method getValidEnumValue\n * @param enumType {Object} Type of enum\n * @param keyOrValue {Object} Key as `String` or value as `Integer` to check for\n */\nfunction getValidEnumValue(enumType, keyOrValue)\n{\n    validateArgument(\"enumType\", enumType);\n    validateArgument(\"keyOrValue\", keyOrValue);\n\n    // Is key\n    if (typeof keyOrValue === \"string\")\n    {\n        var value = enumType[keyOrValue];\n        if (value === undefined)\n        {\n            throwError(\"The enum key '{0}' does not exist.\", keyOrValue);\n        }\n\n        return value;\n    }\n    // Is value\n    else if (typeof keyOrValue === \"number\")\n    {\n        for (var key in enumType)\n        {\n            if (enumType[key] === keyOrValue)\n            {\n                return keyOrValue;\n            }\n        }\n\n        throwError(\"Invalid enum value '{0}'.\", keyOrValue);\n    }\n    // Type is not supported\n    else\n    {\n        throwError(\"Invalid key or value type.\");\n    }\n}\n\n/**\n * Routes to the next step.\n *\n * @static\n * @private\n * @method goToNextStep\n * @param wizard {Object} The jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @return {Boolean} Indicates whether the action executed\n **/\nfunction goToNextStep(wizard, options, state)\n{\n    return paginationClick(wizard, options, state, increaseCurrentIndexBy(state, 1));\n}\n\n/**\n * Routes to the previous step.\n *\n * @static\n * @private\n * @method goToPreviousStep\n * @param wizard {Object} The jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @return {Boolean} Indicates whether the action executed\n **/\nfunction goToPreviousStep(wizard, options, state)\n{\n    return paginationClick(wizard, options, state, decreaseCurrentIndexBy(state, 1));\n}\n\n/**\n * Routes to a specific step by a given index.\n *\n * @static\n * @private\n * @method goToStep\n * @param wizard {Object} The jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param index {Integer} The position (zero-based) to route to\n * @return {Boolean} Indicates whether the action succeeded or failed\n **/\nfunction goToStep(wizard, options, state, index)\n{\n    if (index < 0 || index >= state.stepCount)\n    {\n        throwError(_indexOutOfRangeErrorMessage);\n    }\n\n    if (options.forceMoveForward && index < state.currentIndex)\n    {\n        return;\n    }\n\n    var oldIndex = state.currentIndex;\n    if (wizard.triggerHandler(\"stepChanging\", [state.currentIndex, index]))\n    {\n        // Save new state\n        state.currentIndex = index;\n        saveCurrentStateToCookie(wizard, options, state);\n\n        // Change visualisation\n        refreshStepNavigation(wizard, options, state, oldIndex);\n        refreshPagination(wizard, options, state);\n        loadAsyncContent(wizard, options, state);\n        startTransitionEffect(wizard, options, state, index, oldIndex, function()\n        {\n            wizard.triggerHandler(\"stepChanged\", [index, oldIndex]);\n        });\n    }\n    else\n    {\n        wizard.find(\".steps li\").eq(oldIndex).addClass(\"error\");\n    }\n\n    return true;\n}\n\nfunction increaseCurrentIndexBy(state, increaseBy)\n{\n    return state.currentIndex + increaseBy;\n}\n\n/**\n * Initializes the component.\n *\n * @static\n * @private\n * @method initialize\n * @param options {Object} The component settings\n **/\nfunction initialize(options)\n{\n    /*jshint -W040 */\n    var opts = $.extend(true, {}, defaults, options);\n\n    return this.each(function ()\n    {\n        var wizard = $(this);\n        var state = {\n            currentIndex: opts.startIndex,\n            currentStep: null,\n            stepCount: 0,\n            transitionElement: null\n        };\n\n        // Create data container\n        wizard.data(\"options\", opts);\n        wizard.data(\"state\", state);\n        wizard.data(\"steps\", []);\n\n        analyzeData(wizard, opts, state);\n        render(wizard, opts, state);\n        registerEvents(wizard, opts);\n\n        // Trigger focus\n        if (opts.autoFocus && _uniqueId === 0)\n        {\n            getStepAnchor(wizard, opts.startIndex).focus();\n        }\n\n        wizard.triggerHandler(\"init\", [opts.startIndex]);\n    });\n}\n\n/**\n * Inserts a new step to a specific position.\n *\n * @static\n * @private\n * @method insertStep\n * @param wizard {Object} The jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param index {Integer} The position (zero-based) to add\n * @param step {Object} The step object to add\n * @example\n *     $(\"#wizard\").steps().insert(0, {\n *         title: \"Title\",\n *         content: \"\", // optional\n *         contentMode: \"async\", // optional\n *         contentUrl: \"/Content/Step/1\" // optional\n *     });\n * @chainable\n **/\nfunction insertStep(wizard, options, state, index, step)\n{\n    if (index < 0 || index > state.stepCount)\n    {\n        throwError(_indexOutOfRangeErrorMessage);\n    }\n\n    // TODO: Validate step object\n\n    // Change data\n    step = $.extend({}, stepModel, step);\n    insertStepToCache(wizard, index, step);\n    if (state.currentIndex !== state.stepCount && state.currentIndex >= index)\n    {\n        state.currentIndex++;\n        saveCurrentStateToCookie(wizard, options, state);\n    }\n    state.stepCount++;\n\n    var contentContainer = wizard.find(\".content\"),\n        header = $(\"<{0}>{1}</{0}>\".format(options.headerTag, step.title)),\n        body = $(\"<{0}></{0}>\".format(options.bodyTag));\n\n    if (step.contentMode == null || step.contentMode === contentMode.html)\n    {\n        body.html(step.content);\n    }\n\n    if (index === 0)\n    {\n        contentContainer.prepend(body).prepend(header);\n    }\n    else\n    {\n        getStepPanel(wizard, (index - 1)).after(body).after(header);\n    }\n\n    renderBody(wizard, state, body, index);\n    renderTitle(wizard, options, state, header, index);\n    refreshSteps(wizard, options, state, index);\n    if (index === state.currentIndex)\n    {\n        refreshStepNavigation(wizard, options, state);\n    }\n    refreshPagination(wizard, options, state);\n\n    return wizard;\n}\n\n/**\n * Inserts a step object to the cache at a specific position.\n *\n * @static\n * @private\n * @method insertStepToCache\n * @param wizard {Object} A jQuery wizard object\n * @param index {Integer} The position (zero-based) to add\n * @param step {Object} The step object to add\n **/\nfunction insertStepToCache(wizard, index, step)\n{\n    getSteps(wizard).splice(index, 0, step);\n}\n\n/**\n * Handles the keyup DOM event for pagination.\n *\n * @static\n * @private\n * @event keyup\n * @param event {Object} An event object\n */\nfunction keyUpHandler(event)\n{\n    var wizard = $(this),\n        options = getOptions(wizard),\n        state = getState(wizard);\n\n    if (options.suppressPaginationOnFocus && wizard.find(\":focus\").is(\":input\"))\n    {\n        event.preventDefault();\n        return false;\n    }\n\n    var keyCodes = { left: 37, right: 39 };\n    if (event.keyCode === keyCodes.left)\n    {\n        event.preventDefault();\n        goToPreviousStep(wizard, options, state);\n    }\n    else if (event.keyCode === keyCodes.right)\n    {\n        event.preventDefault();\n        goToNextStep(wizard, options, state);\n    }\n}\n\n/**\n * Loads and includes async content.\n *\n * @static\n * @private\n * @method loadAsyncContent\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n */\nfunction loadAsyncContent(wizard, options, state)\n{\n    if (state.stepCount > 0)\n    {\n        var currentIndex = state.currentIndex,\n            currentStep = getStep(wizard, currentIndex);\n\n        if (!options.enableContentCache || !currentStep.contentLoaded)\n        {\n            switch (getValidEnumValue(contentMode, currentStep.contentMode))\n            {\n                case contentMode.iframe:\n                    wizard.find(\".content > .body\").eq(state.currentIndex).empty()\n                        .html(\"<iframe src=\\\"\" + currentStep.contentUrl + \"\\\" frameborder=\\\"0\\\" scrolling=\\\"no\\\" />\")\n                        .data(\"loaded\", \"1\");\n                    break;\n\n                case contentMode.async:\n                    var currentStepContent = getStepPanel(wizard, currentIndex)._aria(\"busy\", \"true\")\n                        .empty().append(renderTemplate(options.loadingTemplate, { text: options.labels.loading }));\n\n                    $.ajax({ url: currentStep.contentUrl, cache: false }).done(function (data)\n                    {\n                        currentStepContent.empty().html(data)._aria(\"busy\", \"false\").data(\"loaded\", \"1\");\n                        wizard.triggerHandler(\"contentLoaded\", [currentIndex]);\n                    });\n                    break;\n            }\n        }\n    }\n}\n\n/**\n * Fires the action next or previous click event.\n *\n * @static\n * @private\n * @method paginationClick\n * @param wizard {Object} The jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param index {Integer} The position (zero-based) to route to\n * @return {Boolean} Indicates whether the event fired successfully or not\n **/\nfunction paginationClick(wizard, options, state, index)\n{\n    var oldIndex = state.currentIndex;\n\n    if (index >= 0 && index < state.stepCount && !(options.forceMoveForward && index < state.currentIndex))\n    {\n        var anchor = getStepAnchor(wizard, index),\n            parent = anchor.parent(),\n            isDisabled = parent.hasClass(\"disabled\");\n\n        // Enable the step to make the anchor clickable!\n        parent._enableAria();\n        anchor.click();\n\n        // An error occured\n        if (oldIndex === state.currentIndex && isDisabled)\n        {\n            // Disable the step again if current index has not changed; prevents click action.\n            parent._enableAria(false);\n            return false;\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n/**\n * Fires when a pagination click happens.\n *\n * @static\n * @private\n * @event click\n * @param event {Object} An event object\n */\nfunction paginationClickHandler(event)\n{\n    event.preventDefault();\n\n    var anchor = $(this),\n        wizard = anchor.parent().parent().parent().parent(),\n        options = getOptions(wizard),\n        state = getState(wizard),\n        href = anchor.attr(\"href\");\n\n    switch (href.substring(href.lastIndexOf(\"#\") + 1))\n    {\n        case \"cancel\":\n            cancel(wizard);\n            break;\n\n        case \"finish\":\n            finishStep(wizard, state);\n            break;\n\n        case \"next\":\n            goToNextStep(wizard, options, state);\n            break;\n\n        case \"previous\":\n            goToPreviousStep(wizard, options, state);\n            break;\n    }\n}\n\n/**\n * Refreshs the visualization state for the entire pagination.\n *\n * @static\n * @private\n * @method refreshPagination\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n */\nfunction refreshPagination(wizard, options, state)\n{\n    if (options.enablePagination)\n    {\n        var finish = wizard.find(\".actions a[href$='#finish']\").parent(),\n            next = wizard.find(\".actions a[href$='#next']\").parent();\n\n        if (!options.forceMoveForward)\n        {\n            var previous = wizard.find(\".actions a[href$='#previous']\").parent();\n            previous._enableAria(state.currentIndex > 0);\n        }\n\n        if (options.enableFinishButton && options.showFinishButtonAlways)\n        {\n            finish._enableAria(state.stepCount > 0);\n            next._enableAria(state.stepCount > 1 && state.stepCount > (state.currentIndex + 1));\n        }\n        else\n        {\n            finish._showAria(options.enableFinishButton && state.stepCount === (state.currentIndex + 1));\n            next._showAria(state.stepCount === 0 || state.stepCount > (state.currentIndex + 1)).\n                _enableAria(state.stepCount > (state.currentIndex + 1) || !options.enableFinishButton);\n        }\n    }\n}\n\n/**\n * Refreshs the visualization state for the step navigation (tabs).\n *\n * @static\n * @private\n * @method refreshStepNavigation\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param [oldIndex] {Integer} The index of the prior step\n */\nfunction refreshStepNavigation(wizard, options, state, oldIndex)\n{\n    var currentOrNewStepAnchor = getStepAnchor(wizard, state.currentIndex),\n        currentInfo = $(\"<span class=\\\"current-info audible\\\">\" + options.labels.current + \" </span>\"),\n        stepTitles = wizard.find(\".content > .title\");\n\n    if (oldIndex != null)\n    {\n        var oldStepAnchor = getStepAnchor(wizard, oldIndex);\n        oldStepAnchor.parent().addClass(\"done\").removeClass(\"error\")._selectAria(false);\n        stepTitles.eq(oldIndex).removeClass(\"current\").next(\".body\").removeClass(\"current\");\n        currentInfo = oldStepAnchor.find(\".current-info\");\n        currentOrNewStepAnchor.focus();\n    }\n\n    currentOrNewStepAnchor.prepend(currentInfo).parent()._selectAria().removeClass(\"done\")._enableAria();\n    stepTitles.eq(state.currentIndex).addClass(\"current\").next(\".body\").addClass(\"current\");\n}\n\n/**\n * Refreshes step buttons and their related titles beyond a certain position.\n *\n * @static\n * @private\n * @method refreshSteps\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param index {Integer} The start point for refreshing ids\n */\nfunction refreshSteps(wizard, options, state, index)\n{\n    var uniqueId = getUniqueId(wizard);\n\n    for (var i = index; i < state.stepCount; i++)\n    {\n        var uniqueStepId = uniqueId + _tabSuffix + i,\n            uniqueBodyId = uniqueId + _tabpanelSuffix + i,\n            uniqueHeaderId = uniqueId + _titleSuffix + i,\n            title = wizard.find(\".title\").eq(i)._id(uniqueHeaderId);\n\n        wizard.find(\".steps a\").eq(i)._id(uniqueStepId)\n            ._aria(\"controls\", uniqueBodyId).attr(\"href\", \"#\" + uniqueHeaderId)\n            .html(renderTemplate(options.titleTemplate, { index: i + 1, title: title.html() }));\n        wizard.find(\".body\").eq(i)._id(uniqueBodyId)\n            ._aria(\"labelledby\", uniqueHeaderId);\n    }\n}\n\nfunction registerEvents(wizard, options)\n{\n    var eventNamespace = getEventNamespace(wizard);\n\n    wizard.bind(\"canceled\" + eventNamespace, options.onCanceled);\n    wizard.bind(\"contentLoaded\" + eventNamespace, options.onContentLoaded);\n    wizard.bind(\"finishing\" + eventNamespace, options.onFinishing);\n    wizard.bind(\"finished\" + eventNamespace, options.onFinished);\n    wizard.bind(\"init\" + eventNamespace, options.onInit);\n    wizard.bind(\"stepChanging\" + eventNamespace, options.onStepChanging);\n    wizard.bind(\"stepChanged\" + eventNamespace, options.onStepChanged);\n\n    if (options.enableKeyNavigation)\n    {\n        wizard.bind(\"keyup\" + eventNamespace, keyUpHandler);\n    }\n\n    wizard.find(\".actions a\").bind(\"click\" + eventNamespace, paginationClickHandler);\n}\n\n/**\n * Removes a specific step by an given index.\n *\n * @static\n * @private\n * @method removeStep\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param index {Integer} The position (zero-based) of the step to remove\n * @return Indecates whether the item is removed.\n **/\nfunction removeStep(wizard, options, state, index)\n{\n    // Index out of range and try deleting current item will return false.\n    if (index < 0 || index >= state.stepCount || state.currentIndex === index)\n    {\n        return false;\n    }\n\n    // Change data\n    removeStepFromCache(wizard, index);\n    if (state.currentIndex > index)\n    {\n        state.currentIndex--;\n        saveCurrentStateToCookie(wizard, options, state);\n    }\n    state.stepCount--;\n\n    getStepTitle(wizard, index).remove();\n    getStepPanel(wizard, index).remove();\n    getStepAnchor(wizard, index).parent().remove();\n\n    // Set the \"first\" class to the new first step button \n    if (index === 0)\n    {\n        wizard.find(\".steps li\").first().addClass(\"first\");\n    }\n\n    // Set the \"last\" class to the new last step button \n    if (index === state.stepCount)\n    {\n        wizard.find(\".steps li\").eq(index).addClass(\"last\");\n    }\n\n    refreshSteps(wizard, options, state, index);\n    refreshPagination(wizard, options, state);\n\n    return true;\n}\n\nfunction removeStepFromCache(wizard, index)\n{\n    getSteps(wizard).splice(index, 1);\n}\n\n/**\n * Transforms the base html structure to a more sensible html structure.\n *\n * @static\n * @private\n * @method render\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n **/\nfunction render(wizard, options, state)\n{\n    // Create a content wrapper and copy HTML from the intial wizard structure\n    var wrapperTemplate = \"<{0} class=\\\"{1}\\\">{2}</{0}>\",\n        orientation = getValidEnumValue(stepsOrientation, options.stepsOrientation),\n        verticalCssClass = (orientation === stepsOrientation.vertical) ? \" vertical\" : \"\",\n        contentWrapper = $(wrapperTemplate.format(options.contentContainerTag, \"content \" + options.clearFixCssClass, wizard.html())),\n        stepsWrapper = $(wrapperTemplate.format(options.stepsContainerTag, \"steps \" + options.clearFixCssClass, \"<ul role=\\\"tablist\\\"></ul>\")),\n        stepTitles = contentWrapper.children(options.headerTag),\n        stepContents = contentWrapper.children(options.bodyTag);\n\n    // Transform the wizard wrapper and remove the inner HTML\n    wizard.attr(\"role\", \"application\").empty().append(stepsWrapper).append(contentWrapper)\n        .addClass(options.cssClass + \" \" + options.clearFixCssClass + verticalCssClass);\n\n    // Add WIA-ARIA support\n    stepContents.each(function (index)\n    {\n        renderBody(wizard, state, $(this), index);\n    });\n\n    stepTitles.each(function (index)\n    {\n        renderTitle(wizard, options, state, $(this), index);\n    });\n\n    refreshStepNavigation(wizard, options, state);\n    renderPagination(wizard, options, state);\n}\n\n/**\n * Transforms the body to a proper tabpanel.\n *\n * @static\n * @private\n * @method renderBody\n * @param wizard {Object} A jQuery wizard object\n * @param body {Object} A jQuery body object\n * @param index {Integer} The position of the body\n */\nfunction renderBody(wizard, state, body, index)\n{\n    var uniqueId = getUniqueId(wizard),\n        uniqueBodyId = uniqueId + _tabpanelSuffix + index,\n        uniqueHeaderId = uniqueId + _titleSuffix + index;\n\n    body._id(uniqueBodyId).attr(\"role\", \"tabpanel\")._aria(\"labelledby\", uniqueHeaderId)\n        .addClass(\"body\")._showAria(state.currentIndex === index);\n}\n\n/**\n * Renders a pagination if enabled.\n *\n * @static\n * @private\n * @method renderPagination\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n */\nfunction renderPagination(wizard, options, state)\n{\n    if (options.enablePagination)\n    {\n        var pagination = \"<{0} class=\\\"actions {1}\\\"><ul role=\\\"menu\\\" aria-label=\\\"{2}\\\">{3}</ul></{0}>\",\n            buttonTemplate = \"<li><a href=\\\"#{0}\\\" role=\\\"menuitem\\\">{1}</a></li>\",\n            buttons = \"\";\n\n        if (!options.forceMoveForward)\n        {\n            buttons += buttonTemplate.format(\"previous\", options.labels.previous);\n        }\n\n        buttons += buttonTemplate.format(\"next\", options.labels.next);\n\n        if (options.enableFinishButton)\n        {\n            buttons += buttonTemplate.format(\"finish\", options.labels.finish);\n        }\n\n        if (options.enableCancelButton)\n        {\n            buttons += buttonTemplate.format(\"cancel\", options.labels.cancel);\n        }\n\n        wizard.append(pagination.format(options.actionContainerTag, options.clearFixCssClass,\n            options.labels.pagination, buttons));\n\n        refreshPagination(wizard, options, state);\n        loadAsyncContent(wizard, options, state);\n    }\n}\n\n/**\n * Renders a template and replaces all placeholder.\n *\n * @static\n * @private\n * @method renderTemplate\n * @param template {String} A template\n * @param substitutes {Object} A list of substitute\n * @return {String} The rendered template\n */\nfunction renderTemplate(template, substitutes)\n{\n    var matches = template.match(/#([a-z]*)#/gi);\n\n    for (var i = 0; i < matches.length; i++)\n    {\n        var match = matches[i], \n            key = match.substring(1, match.length - 1);\n\n        if (substitutes[key] === undefined)\n        {\n            throwError(\"The key '{0}' does not exist in the substitute collection!\", key);\n        }\n\n        template = template.replace(match, substitutes[key]);\n    }\n\n    return template;\n}\n\n/**\n * Transforms the title to a step item button.\n *\n * @static\n * @private\n * @method renderTitle\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n * @param header {Object} A jQuery header object\n * @param index {Integer} The position of the header\n */\nfunction renderTitle(wizard, options, state, header, index)\n{\n    var uniqueId = getUniqueId(wizard),\n        uniqueStepId = uniqueId + _tabSuffix + index,\n        uniqueBodyId = uniqueId + _tabpanelSuffix + index,\n        uniqueHeaderId = uniqueId + _titleSuffix + index,\n        stepCollection = wizard.find(\".steps > ul\"),\n        title = renderTemplate(options.titleTemplate, {\n            index: index + 1,\n            title: header.html()\n        }),\n        stepItem = $(\"<li role=\\\"tab\\\"><a id=\\\"\" + uniqueStepId + \"\\\" href=\\\"#\" + uniqueHeaderId + \n            \"\\\" aria-controls=\\\"\" + uniqueBodyId + \"\\\">\" + title + \"</a></li>\");\n        \n    stepItem._enableAria(options.enableAllSteps || state.currentIndex > index);\n\n    if (state.currentIndex > index)\n    {\n        stepItem.addClass(\"done\");\n    }\n\n    header._id(uniqueHeaderId).attr(\"tabindex\", \"-1\").addClass(\"title\");\n\n    if (index === 0)\n    {\n        stepCollection.prepend(stepItem);\n    }\n    else\n    {\n        stepCollection.find(\"li\").eq(index - 1).after(stepItem);\n    }\n\n    // Set the \"first\" class to the new first step button\n    if (index === 0)\n    {\n        stepCollection.find(\"li\").removeClass(\"first\").eq(index).addClass(\"first\");\n    }\n\n    // Set the \"last\" class to the new last step button\n    if (index === (state.stepCount - 1))\n    {\n        stepCollection.find(\"li\").removeClass(\"last\").eq(index).addClass(\"last\");\n    }\n\n    // Register click event\n    stepItem.children(\"a\").bind(\"click\" + getEventNamespace(wizard), stepClickHandler);\n}\n\n/**\n * Saves the current state to a cookie.\n *\n * @static\n * @private\n * @method saveCurrentStateToCookie\n * @param wizard {Object} A jQuery wizard object\n * @param options {Object} Settings of the current wizard\n * @param state {Object} The state container of the current wizard\n */\nfunction saveCurrentStateToCookie(wizard, options, state)\n{\n    if (options.saveState && $.cookie)\n    {\n        $.cookie(_cookiePrefix + getUniqueId(wizard), state.currentIndex);\n    }\n}\n\nfunction startTransitionEffect(wizard, options, state, index, oldIndex, doneCallback)\n{\n    var stepContents = wizard.find(\".content > .body\"),\n        effect = getValidEnumValue(transitionEffect, options.transitionEffect),\n        effectSpeed = options.transitionEffectSpeed,\n        newStep = stepContents.eq(index),\n        currentStep = stepContents.eq(oldIndex);\n\n    switch (effect)\n    {\n        case transitionEffect.fade:\n        case transitionEffect.slide:\n            var hide = (effect === transitionEffect.fade) ? \"fadeOut\" : \"slideUp\",\n                show = (effect === transitionEffect.fade) ? \"fadeIn\" : \"slideDown\";\n\n            state.transitionElement = newStep;\n            currentStep[hide](effectSpeed, function ()\n            {\n                var wizard = $(this)._showAria(false).parent().parent(),\n                    state = getState(wizard);\n\n                if (state.transitionElement)\n                {\n                    state.transitionElement[show](effectSpeed, function ()\n                    {\n                        $(this)._showAria();\n                    }).promise().done(doneCallback);\n                    state.transitionElement = null;\n                }\n            });\n            break;\n\n        case transitionEffect.slideLeft:\n            var outerWidth = currentStep.outerWidth(true),\n                posFadeOut = (index > oldIndex) ? -(outerWidth) : outerWidth,\n                posFadeIn = (index > oldIndex) ? outerWidth : -(outerWidth);\n\n            $.when(currentStep.animate({ left: posFadeOut }, effectSpeed, \n                    function () { $(this)._showAria(false); }),\n                newStep.css(\"left\", posFadeIn + \"px\")._showAria()\n                    .animate({ left: 0 }, effectSpeed)).done(doneCallback);\n            break;\n\n        default:\n            $.when(currentStep._showAria(false), newStep._showAria())\n                .done(doneCallback);\n            break;\n    }\n}\n\n/**\n * Fires when a step click happens.\n *\n * @static\n * @private\n * @event click\n * @param event {Object} An event object\n */\nfunction stepClickHandler(event)\n{\n    event.preventDefault();\n\n    var anchor = $(this),\n        wizard = anchor.parent().parent().parent().parent(),\n        options = getOptions(wizard),\n        state = getState(wizard),\n        oldIndex = state.currentIndex;\n\n    if (anchor.parent().is(\":not(.disabled):not(.current)\"))\n    {\n        var href = anchor.attr(\"href\"),\n            position = parseInt(href.substring(href.lastIndexOf(\"-\") + 1), 0);\n\n        goToStep(wizard, options, state, position);\n    }\n\n    // If nothing has changed\n    if (oldIndex === state.currentIndex)\n    {\n        getStepAnchor(wizard, oldIndex).focus();\n        return false;\n    }\n}\n\nfunction throwError(message)\n{\n    if (arguments.length > 1)\n    {\n        message = message.format(Array.prototype.slice.call(arguments, 1));\n    }\n\n    throw new Error(message);\n}\n\n/**\n * Checks an argument for null or undefined and throws an error if one check applies.\n *\n * @static\n * @private\n * @method validateArgument\n * @param argumentName {String} The name of the given argument\n * @param argumentValue {Object} The argument itself\n */\nfunction validateArgument(argumentName, argumentValue)\n{\n    if (argumentValue == null)\n    {\n        throwError(\"The argument '{0}' is null or undefined.\", argumentName);\n    }\n}\n\n/**\n * Represents a jQuery wizard plugin.\n *\n * @class steps\n * @constructor\n * @param [method={}] The name of the method as `String` or an JSON object for initialization\n * @param [params=]* {Array} Additional arguments for a method call\n * @chainable\n **/\n$.fn.steps = function (method)\n{\n    if ($.fn.steps[method])\n    {\n        return $.fn.steps[method].apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n    else if (typeof method === \"object\" || !method)\n    {\n        return initialize.apply(this, arguments);\n    }\n    else\n    {\n        $.error(\"Method \" + method + \" does not exist on jQuery.steps\");\n    }\n};\n\n/**\n * Adds a new step.\n *\n * @method add\n * @param step {Object} The step object to add\n * @chainable\n **/\n$.fn.steps.add = function (step)\n{\n    var state = getState(this);\n    return insertStep(this, getOptions(this), state, state.stepCount, step);\n};\n\n/**\n * Removes the control functionality completely and transforms the current state to the initial HTML structure.\n *\n * @method destroy\n * @chainable\n **/\n$.fn.steps.destroy = function ()\n{\n    return destroy(this, getOptions(this));\n};\n\n/**\n * Triggers the onFinishing and onFinished event.\n *\n * @method finish\n **/\n$.fn.steps.finish = function ()\n{\n    finishStep(this, getState(this));\n};\n\n/**\n * Gets the current step index.\n *\n * @method getCurrentIndex\n * @return {Integer} The actual step index (zero-based)\n * @for steps\n **/\n$.fn.steps.getCurrentIndex = function ()\n{\n    return getState(this).currentIndex;\n};\n\n/**\n * Gets the current step object.\n *\n * @method getCurrentStep\n * @return {Object} The actual step object\n **/\n$.fn.steps.getCurrentStep = function ()\n{\n    return getStep(this, getState(this).currentIndex);\n};\n\n/**\n * Gets a specific step object by index.\n *\n * @method getStep\n * @param index {Integer} An integer that belongs to the position of a step\n * @return {Object} A specific step object\n **/\n$.fn.steps.getStep = function (index)\n{\n    return getStep(this, index);\n};\n\n/**\n * Inserts a new step to a specific position.\n *\n * @method insert\n * @param index {Integer} The position (zero-based) to add\n * @param step {Object} The step object to add\n * @example\n *     $(\"#wizard\").steps().insert(0, {\n *         title: \"Title\",\n *         content: \"\", // optional\n *         contentMode: \"async\", // optional\n *         contentUrl: \"/Content/Step/1\" // optional\n *     });\n * @chainable\n **/\n$.fn.steps.insert = function (index, step)\n{\n    return insertStep(this, getOptions(this), getState(this), index, step);\n};\n\n/**\n * Routes to the next step.\n *\n * @method next\n * @return {Boolean} Indicates whether the action executed\n **/\n$.fn.steps.next = function ()\n{\n    return goToNextStep(this, getOptions(this), getState(this));\n};\n\n/**\n * Routes to the previous step.\n *\n * @method previous\n * @return {Boolean} Indicates whether the action executed\n **/\n$.fn.steps.previous = function ()\n{\n    return goToPreviousStep(this, getOptions(this), getState(this));\n};\n\n/**\n * Removes a specific step by an given index.\n *\n * @method remove\n * @param index {Integer} The position (zero-based) of the step to remove\n * @return Indecates whether the item is removed.\n **/\n$.fn.steps.remove = function (index)\n{\n    return removeStep(this, getOptions(this), getState(this), index);\n};\n\n/**\n * Sets a specific step object by index.\n *\n * @method setStep\n * @param index {Integer} An integer that belongs to the position of a step\n * @param step {Object} The step object to change\n **/\n$.fn.steps.setStep = function (index, step)\n{\n    throw new Error(\"Not yet implemented!\");\n};\n\n/**\n * Skips an certain amount of steps.\n *\n * @method skip\n * @param count {Integer} The amount of steps that should be skipped\n * @return {Boolean} Indicates whether the action executed\n **/\n$.fn.steps.skip = function (count)\n{\n    throw new Error(\"Not yet implemented!\");\n};\n\n/**\n * An enum represents the different content types of a step and their loading mechanisms.\n *\n * @class contentMode\n * @for steps\n **/\nvar contentMode = $.fn.steps.contentMode = {\n    /**\n     * HTML embedded content\n     *\n     * @readOnly\n     * @property html\n     * @type Integer\n     * @for contentMode\n     **/\n    html: 0,\n\n    /**\n     * IFrame embedded content\n     *\n     * @readOnly\n     * @property iframe\n     * @type Integer\n     * @for contentMode\n     **/\n    iframe: 1,\n\n    /**\n     * Async embedded content\n     *\n     * @readOnly\n     * @property async\n     * @type Integer\n     * @for contentMode\n     **/\n    async: 2\n};\n\n/**\n * An enum represents the orientation of the steps navigation.\n *\n * @class stepsOrientation\n * @for steps\n **/\nvar stepsOrientation = $.fn.steps.stepsOrientation = {\n    /**\n     * Horizontal orientation\n     *\n     * @readOnly\n     * @property horizontal\n     * @type Integer\n     * @for stepsOrientation\n     **/\n    horizontal: 0,\n\n    /**\n     * Vertical orientation\n     *\n     * @readOnly\n     * @property vertical\n     * @type Integer\n     * @for stepsOrientation\n     **/\n    vertical: 1\n};\n\n/**\n * An enum that represents the various transition animations.\n *\n * @class transitionEffect\n * @for steps\n **/\nvar transitionEffect = $.fn.steps.transitionEffect = {\n    /**\n     * No transition animation\n     *\n     * @readOnly\n     * @property none\n     * @type Integer\n     * @for transitionEffect\n     **/\n    none: 0,\n\n    /**\n     * Fade in transition\n     *\n     * @readOnly\n     * @property fade\n     * @type Integer\n     * @for transitionEffect\n     **/\n    fade: 1,\n\n    /**\n     * Slide up transition\n     *\n     * @readOnly\n     * @property slide\n     * @type Integer\n     * @for transitionEffect\n     **/\n    slide: 2,\n\n    /**\n     * Slide left transition\n     *\n     * @readOnly\n     * @property slideLeft\n     * @type Integer\n     * @for transitionEffect\n     **/\n    slideLeft: 3\n};\n\nvar stepModel = $.fn.steps.stepModel = {\n    title: \"\",\n    content: \"\",\n    contentUrl: \"\",\n    contentMode: contentMode.html,\n    contentLoaded: false\n};\n\n/**\n * An object that represents the default settings.\n * There are two possibities to override the sub-properties.\n * Either by doing it generally (global) or on initialization.\n *\n * @static\n * @class defaults\n * @for steps\n * @example\n *   // Global approach\n *   $.steps.defaults.headerTag = \"h3\";\n * @example\n *   // Initialization approach\n *   $(\"#wizard\").steps({ headerTag: \"h3\" });\n **/\nvar defaults = $.fn.steps.defaults = {\n    /**\n     * The header tag is used to find the step button text within the declared wizard area.\n     *\n     * @property headerTag\n     * @type String\n     * @default \"h1\"\n     * @for defaults\n     **/\n    headerTag: \"h1\",\n\n    /**\n     * The body tag is used to find the step content within the declared wizard area.\n     *\n     * @property bodyTag\n     * @type String\n     * @default \"div\"\n     * @for defaults\n     **/\n    bodyTag: \"div\",\n\n    /**\n     * The content container tag which will be used to wrap all step contents.\n     *\n     * @property contentContainerTag\n     * @type String\n     * @default \"div\"\n     * @for defaults\n     **/\n    contentContainerTag: \"div\",\n\n    /**\n     * The action container tag which will be used to wrap the pagination navigation.\n     *\n     * @property actionContainerTag\n     * @type String\n     * @default \"div\"\n     * @for defaults\n     **/\n    actionContainerTag: \"div\",\n\n    /**\n     * The steps container tag which will be used to wrap the steps navigation.\n     *\n     * @property stepsContainerTag\n     * @type String\n     * @default \"div\"\n     * @for defaults\n     **/\n    stepsContainerTag: \"div\",\n\n    /**\n     * The css class which will be added to the outer component wrapper.\n     *\n     * @property cssClass\n     * @type String\n     * @default \"wizard\"\n     * @for defaults\n     * @example\n     *     <div class=\"wizard\">\n     *         ...\n     *     </div>\n     **/\n    cssClass: \"wizard\",\n\n    /**\n     * The css class which will be used for floating scenarios.\n     *\n     * @property clearFixCssClass\n     * @type String\n     * @default \"clearfix\"\n     * @for defaults\n     **/\n    clearFixCssClass: \"clearfix\",\n\n    /**\n     * Determines whether the steps are vertically or horizontally oriented.\n     *\n     * @property stepsOrientation\n     * @type stepsOrientation\n     * @default horizontal\n     * @for defaults\n     * @since 1.0.0\n     **/\n    stepsOrientation: stepsOrientation.horizontal,\n\n    /*\n     * Tempplates\n     */\n\n    /**\n     * The title template which will be used to create a step button.\n     *\n     * @property titleTemplate\n     * @type String\n     * @default \"<span class=\\\"number\\\">#index#.</span> #title#\"\n     * @for defaults\n     **/\n    titleTemplate: \"<span class=\\\"number\\\">#index#.</span> #title#\",\n\n    /**\n     * The loading template which will be used to create the loading animation.\n     *\n     * @property loadingTemplate\n     * @type String\n     * @default \"<span class=\\\"spinner\\\"></span> #text#\"\n     * @for defaults\n     **/\n    loadingTemplate: \"<span class=\\\"spinner\\\"></span> #text#\",\n\n    /*\n     * Behaviour\n     */\n\n    /**\n     * Sets the focus to the first wizard instance in order to enable the key navigation from the begining if `true`. \n     *\n     * @property autoFocus\n     * @type Boolean\n     * @default false\n     * @for defaults\n     * @since 0.9.4\n     **/\n    autoFocus: false,\n\n    /**\n     * Enables all steps from the begining if `true` (all steps are clickable).\n     *\n     * @property enableAllSteps\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    enableAllSteps: false,\n\n    /**\n     * Enables keyboard navigation if `true` (arrow left and arrow right).\n     *\n     * @property enableKeyNavigation\n     * @type Boolean\n     * @default true\n     * @for defaults\n     **/\n    enableKeyNavigation: true,\n\n    /**\n     * Enables pagination if `true`.\n     *\n     * @property enablePagination\n     * @type Boolean\n     * @default true\n     * @for defaults\n     **/\n    enablePagination: true,\n\n    /**\n     * Suppresses pagination if a form field is focused.\n     *\n     * @property suppressPaginationOnFocus\n     * @type Boolean\n     * @default true\n     * @for defaults\n     **/\n    suppressPaginationOnFocus: true,\n\n    /**\n     * Enables cache for async loaded or iframe embedded content.\n     *\n     * @property enableContentCache\n     * @type Boolean\n     * @default true\n     * @for defaults\n     **/\n    enableContentCache: true,\n\n    /**\n     * Shows the cancel button if enabled.\n     *\n     * @property enableCancelButton\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    enableCancelButton: false,\n\n    /**\n     * Shows the finish button if enabled.\n     *\n     * @property enableFinishButton\n     * @type Boolean\n     * @default true\n     * @for defaults\n     **/\n    enableFinishButton: true,\n\n    /**\n     * Not yet implemented.\n     *\n     * @property preloadContent\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    preloadContent: false,\n\n    /**\n     * Shows the finish button always (on each step; right beside the next button) if `true`. \n     * Otherwise the next button will be replaced by the finish button if the last step becomes active.\n     *\n     * @property showFinishButtonAlways\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    showFinishButtonAlways: false,\n\n    /**\n     * Prevents jumping to a previous step.\n     *\n     * @property forceMoveForward\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    forceMoveForward: false,\n\n    /**\n     * Saves the current state (step position) to a cookie.\n     * By coming next time the last active step becomes activated.\n     *\n     * @property saveState\n     * @type Boolean\n     * @default false\n     * @for defaults\n     **/\n    saveState: false,\n\n    /**\n     * The position to start on (zero-based).\n     *\n     * @property startIndex\n     * @type Integer\n     * @default 0\n     * @for defaults\n     **/\n    startIndex: 0,\n\n    /*\n     * Animation Effect Configuration\n     */\n\n    /**\n     * The animation effect which will be used for step transitions.\n     *\n     * @property transitionEffect\n     * @type transitionEffect\n     * @default none\n     * @for defaults\n     **/\n    transitionEffect: transitionEffect.none,\n\n    /**\n     * Animation speed for step transitions (in milliseconds).\n     *\n     * @property transitionEffectSpeed\n     * @type Integer\n     * @default 200\n     * @for defaults\n     **/\n    transitionEffectSpeed: 200,\n\n    /*\n     * Events\n     */\n\n    /**\n     * Fires before the step changes and can be used to prevent step changing by returning `false`. \n     * Very useful for form validation. \n     *\n     * @property onStepChanging\n     * @type Event\n     * @default function (event, currentIndex, newIndex) { return true; }\n     * @for defaults\n     **/\n    onStepChanging: function (event, currentIndex, newIndex) { return true; },\n\n    /**\n     * Fires after the step has change. \n     *\n     * @property onStepChanged\n     * @type Event\n     * @default function (event, currentIndex, priorIndex) { }\n     * @for defaults\n     **/\n    onStepChanged: function (event, currentIndex, priorIndex) { },\n\n    /**\n     * Fires after cancelation. \n     *\n     * @property onCanceled\n     * @type Event\n     * @default function (event) { }\n     * @for defaults\n     **/\n    onCanceled: function (event) { },\n\n    /**\n     * Fires before finishing and can be used to prevent completion by returning `false`. \n     * Very useful for form validation. \n     *\n     * @property onFinishing\n     * @type Event\n     * @default function (event, currentIndex) { return true; }\n     * @for defaults\n     **/\n    onFinishing: function (event, currentIndex) { return true; },\n\n    /**\n     * Fires after completion. \n     *\n     * @property onFinished\n     * @type Event\n     * @default function (event, currentIndex) { }\n     * @for defaults\n     **/\n    onFinished: function (event, currentIndex) { },\n\n    /**\n     * Fires after async content is loaded. \n     *\n     * @property onContentLoaded\n     * @type Event\n     * @default function (event, index) { }\n     * @for defaults\n     **/\n    onContentLoaded: function (event, currentIndex) { },\n\n    /**\n     * Fires when the wizard is initialized. \n     *\n     * @property onInit\n     * @type Event\n     * @default function (event) { }\n     * @for defaults\n     **/\n    onInit: function (event, currentIndex) { },\n\n    /**\n     * Contains all labels. \n     *\n     * @property labels\n     * @type Object\n     * @for defaults\n     **/\n    labels: {\n        /**\n         * Label for the cancel button.\n         *\n         * @property cancel\n         * @type String\n         * @default \"Cancel\"\n         * @for defaults\n         **/\n        cancel: \"Cancel\",\n\n        /**\n         * This label is important for accessability reasons.\n         * Indicates which step is activated.\n         *\n         * @property current\n         * @type String\n         * @default \"current step:\"\n         * @for defaults\n         **/\n        current: \"current step:\",\n\n        /**\n         * This label is important for accessability reasons and describes the kind of navigation.\n         *\n         * @property pagination\n         * @type String\n         * @default \"Pagination\"\n         * @for defaults\n         * @since 0.9.7\n         **/\n        pagination: \"Pagination\",\n\n        /**\n         * Label for the finish button.\n         *\n         * @property finish\n         * @type String\n         * @default \"Finish\"\n         * @for defaults\n         **/\n        finish: \"Finish\",\n\n        /**\n         * Label for the next button.\n         *\n         * @property next\n         * @type String\n         * @default \"Next\"\n         * @for defaults\n         **/\n        next: \"Next\",\n\n        /**\n         * Label for the previous button.\n         *\n         * @property previous\n         * @type String\n         * @default \"Previous\"\n         * @for defaults\n         **/\n        previous: \"Previous\",\n\n        /**\n         * Label for the loading animation.\n         *\n         * @property loading\n         * @type String\n         * @default \"Loading ...\"\n         * @for defaults\n         **/\n        loading: \"Loading ...\"\n    }\n};\n})(jQuery);"], "names": ["$", "undefined", "fn", "extend", "_aria", "name", "value", "this", "attr", "_remove<PERSON><PERSON>", "removeAttr", "_enableAria", "enable", "removeClass", "addClass", "_showAria", "show", "hide", "_selectAria", "select", "_id", "id", "String", "prototype", "format", "args", "arguments", "length", "isArray", "formattedString", "i", "pattern", "RegExp", "replace", "_uniqueId", "_cookiePrefix", "_tabSuffix", "_tabpanelSuffix", "_titleSuffix", "_indexOutOfRangeErrorMessage", "_missingCorrespondingElementErrorMessage", "analyzeData", "wizard", "options", "state", "<PERSON><PERSON><PERSON><PERSON>", "children", "headerTag", "stepContents", "bodyTag", "startIndex", "throwError", "stepCount", "saveState", "cookie", "savedState", "getUniqueId", "savedIndex", "parseInt", "isNaN", "currentIndex", "each", "index", "item", "content", "eq", "modeData", "data", "mode", "contentMode", "html", "getValidEnumValue", "test", "contentUrl", "contentLoaded", "step", "step<PERSON><PERSON>l", "title", "getSteps", "push", "finishStep", "currentStep", "find", "<PERSON><PERSON><PERSON><PERSON>", "getEventNamespace", "eventNamespace", "getStepAnchor", "uniqueId", "getStepPanel", "getOptions", "getState", "getStep", "steps", "concat", "enumType", "keyOr<PERSON><PERSON>ue", "validateArgument", "key", "goToNextStep", "paginationClick", "increaseBy", "goToPreviousStep", "decreaseBy", "goToStep", "forceMoveForward", "oldIndex", "saveCurrentStateToCookie", "refreshStepNavigation", "refreshPagination", "loadAsyncContent", "startTransitionEffect", "doneCallback", "effect", "transitionEffect", "effectSpeed", "transitionEffectSpeed", "newStep", "fade", "slide", "transitionElement", "parent", "promise", "done", "slideLeft", "outerWidth", "posFadeOut", "posFadeIn", "when", "animate", "left", "css", "initialize", "opts", "defaults", "wrapperTemplate", "verticalCssClass", "stepsOrientation", "vertical", "contentWrapper", "contentContainerTag", "clearFixCssClass", "steps<PERSON><PERSON><PERSON>", "stepsContainerTag", "empty", "append", "cssClass", "renderBody", "renderTitle", "enablePagination", "buttonTemplate", "buttons", "labels", "previous", "next", "enableFinishButton", "finish", "enableCancelButton", "cancel", "actionContainerTag", "pagination", "bind", "onCanceled", "onContentLoaded", "onFinishing", "onFinished", "onInit", "onStepChanging", "onStepChanged", "enableKeyNavigation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paginationClickHandler", "autoFocus", "focus", "insertStep", "splice", "contentContainer", "header", "body", "prepend", "after", "refreshSteps", "event", "suppressPaginationOnFocus", "is", "preventDefault", "keyCodes", "keyCode", "enableContentCache", "iframe", "async", "currentStepContent", "renderTemplate", "loadingTemplate", "text", "loading", "ajax", "url", "cache", "isDisabled", "anchor", "hasClass", "click", "href", "substring", "lastIndexOf", "showFinishButtonAlways", "currentOrNewStepAnchor", "currentInfo", "current", "oldStepAnchor", "uniqueStepId", "uniqueBodyId", "uniqueHeaderId", "titleTemplate", "removeStep", "remove", "first", "template", "substitutes", "matches", "match", "stepCollection", "stepItem", "enableAllSteps", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "Array", "slice", "call", "Error", "argumentName", "argumentValue", "method", "apply", "error", "add", "destroy", "unbind", "removeData", "contents", "wizardSubstitute", "get", "tagName", "wizardId", "getCurrentIndex", "getCurrentStep", "insert", "setStep", "skip", "count", "horizontal", "none", "preloadContent", "newIndex", "priorIndex", "j<PERSON><PERSON><PERSON>"], "mappings": "AAKC,CAAA,SAAWA,EAAGC,GAEfD,EAAEE,GAAGC,OAAO,CACRC,MAAO,SAAUC,EAAMC,GAEnB,OAAOC,KAAKC,KAAK,QAAUH,EAAMC,CAAK,CAC1C,EAEAG,YAAa,SAAUJ,GAEnB,OAAOE,KAAKG,WAAW,QAAUL,CAAI,CACzC,EAEAM,YAAa,SAAUC,GAEnB,OAAkB,MAAVA,GAAkBA,EACtBL,KAAKM,YAAY,UAAU,EAAET,MAAM,WAAY,OAAO,EACtDG,KAAKO,SAAS,UAAU,EAAEV,MAAM,WAAY,MAAM,CAC1D,EAEAW,UAAW,SAAUC,GAEjB,OAAgB,MAARA,GAAgBA,EACpBT,KAAKS,KAAK,EAAEZ,MAAM,SAAU,OAAO,EACnCG,KAAKU,KAAK,EAAEb,MAAM,SAAU,MAAM,CAC1C,EAEAc,YAAa,SAAUC,GAEnB,OAAkB,MAAVA,GAAkBA,EACtBZ,KAAKO,SAAS,SAAS,EAAEV,MAAM,WAAY,MAAM,EACjDG,KAAKM,YAAY,SAAS,EAAET,MAAM,WAAY,OAAO,CAC7D,EAEAgB,IAAK,SAAUC,GAEX,OAAO,EAAOd,KAAKC,KAAK,KAAMa,CAAE,EAAId,KAAKC,KAAK,IAAI,CACtD,CACJ,CAAC,EAEIc,OAAOC,UAAUC,SAElBF,OAAOC,UAAUC,OAAS,WAItB,IAFA,IAAIC,EAA6B,IAArBC,UAAUC,QAAgB3B,EAAE4B,QAAQF,UAAU,EAAE,EAAKA,UAAU,GAAKA,UAC5EG,EAAkBtB,KACbuB,EAAI,EAAGA,EAAIL,EAAKE,OAAQG,CAAC,GAE9B,IAAIC,EAAU,IAAIC,OAAO,MAAQF,EAAI,MAAO,IAAI,EAChDD,EAAkBA,EAAgBI,QAAQF,EAASN,EAAKK,EAAE,EAE9D,OAAOD,CACX,GAWJ,IAAIK,EAAY,EAUZC,EAAgB,sBAWhBC,EAAa,MAWbC,EAAkB,MAWlBC,EAAe,MAUfC,EAA+B,sBAU/BC,EAA2C,kDAgB/C,SAASC,EAAYC,EAAQC,EAASC,GAElC,IAAIC,EAAaH,EAAOI,SAASH,EAAQI,SAAS,EAC9CC,EAAeN,EAAOI,SAASH,EAAQM,OAAO,EAY9CC,GATAL,EAAWlB,OAASqB,EAAarB,OAEjCwB,EAAWX,EAA0C,UAAU,EAE1DK,EAAWlB,OAASqB,EAAarB,QAEtCwB,EAAWX,EAA0C,QAAQ,EAGhDG,EAAQO,YAEzBN,EAAMQ,UAAYP,EAAWlB,OAGzBgB,EAAQU,WAAarD,EAAEsD,SAEnBC,EAAavD,EAAEsD,OAAOnB,EAAgBqB,EAAYd,CAAM,CAAC,EAEzDe,EAAaC,SAASH,EAAY,CAAC,EACnC,CAACI,MAAMF,CAAU,IAAKA,EAAab,EAAMQ,YAEzCF,EAAaO,GAIrBb,EAAMgB,aAAeV,EAErBL,EAAWgB,KAAK,SAAUC,GAEtB,IAAIC,EAAO/D,EAAEO,IAAI,EACbyD,EAAUhB,EAAaiB,GAAGH,CAAK,EAC/BI,EAAWF,EAAQG,KAAK,MAAM,EAC9BC,EAAoB,MAAZF,EAAoBG,EAAYC,KAAOC,EAAkBF,EAC5D,QAAQG,KAAKN,CAAQ,GAAKP,MAAMO,CAAQ,EAAKA,EAAWR,SAASQ,EAAU,CAAC,CAAC,EAClFO,EAAcL,IAASC,EAAYC,MAAQN,EAAQG,KAAK,KAAK,IAAMlE,EAC/D,GAAK+D,EAAQG,KAAK,KAAK,EAC3BO,EAAiBN,IAASC,EAAYC,MAAmC,MAA3BN,EAAQG,KAAK,QAAQ,EACnEQ,EAAO3E,EAAEG,OAAO,GAAIyE,EAAW,CAC3BC,MAAOd,EAAKO,KAAK,EACjBN,QAAUI,IAASC,EAAYC,KAAQN,EAAQM,KAAK,EAAI,GACxDG,WAAYA,EACZJ,YAAaD,EACbM,cAAeA,CACnB,CAAC,EAtDmBC,EAwDDA,EAtD3BG,EAsDmBpC,CAtDJ,EAAEqC,KAAKJ,CAAI,CAuD1B,CAAC,CACL,CA6EA,SAASK,EAAWtC,EAAQE,GAExB,IAAIqC,EAAcvC,EAAOwC,KAAK,WAAW,EAAEjB,GAAGrB,EAAMgB,YAAY,EAE5DlB,EAAOyC,eAAe,YAAa,CAACvC,EAAMgB,aAAa,GAEvDqB,EAAYnE,SAAS,MAAM,EAAED,YAAY,OAAO,EAChD6B,EAAOyC,eAAe,WAAY,CAACvC,EAAMgB,aAAa,GAItDqB,EAAYnE,SAAS,OAAO,CAEpC,CAWA,SAASsE,EAAkB1C,GAEvB,IAAI2C,EAAiB3C,EAAOyB,KAAK,gBAAgB,EAQjD,OANsB,MAAlBkB,IAEAA,EAAiB,IAAM7B,EAAYd,CAAM,EACzCA,EAAOyB,KAAK,iBAAkBkB,CAAc,GAGzCA,CACX,CAEA,SAASC,EAAc5C,EAAQoB,GAE3B,IAAIyB,EAAW/B,EAAYd,CAAM,EAEjC,OAAOA,EAAOwC,KAAK,IAAMK,EAAWnD,EAAa0B,CAAK,CAC1D,CAEA,SAAS0B,EAAa9C,EAAQoB,GAE1B,IAAIyB,EAAW/B,EAAYd,CAAM,EAEjC,OAAOA,EAAOwC,KAAK,IAAMK,EAAWlD,EAAkByB,CAAK,CAC/D,CASA,SAAS2B,EAAW/C,GAEhB,OAAOA,EAAOyB,KAAK,SAAS,CAChC,CAEA,SAASuB,EAAShD,GAEd,OAAOA,EAAOyB,KAAK,OAAO,CAC9B,CAEA,SAASW,EAASpC,GAEd,OAAOA,EAAOyB,KAAK,OAAO,CAC9B,CAWA,SAASwB,EAAQjD,EAAQoB,GAEjB8B,EAAQd,EAASpC,CAAM,EAO3B,OALIoB,EAAQ,GAAKA,GAAS8B,EAAMjE,SAE5BwB,EAAWZ,CAA4B,EAGpCqD,EAAM9B,EACjB,CAWA,SAASN,EAAYd,GAEjB,IAAI6C,EAAW7C,EAAOyB,KAAK,KAAK,EAehC,OAbgB,MAAZoB,IAGgB,OADhBA,EAAW7C,EAAOtB,IAAI,KAGlBmE,EAAW,aAAaM,OAAO3D,CAAS,EACxCQ,EAAOtB,IAAImE,CAAQ,GAGvBrD,CAAS,GACTQ,EAAOyB,KAAK,MAAOoB,CAAQ,GAGxBA,CACX,CAWA,SAAShB,EAAkBuB,EAAUC,GAMjC,IAEQzF,EAFR,GAJA0F,EAAiB,WAAYF,CAAQ,EACrCE,EAAiB,aAAcD,CAAU,EAGf,UAAtB,OAAOA,EAQP,OANIzF,EAAQwF,EAASC,MACP9F,GAEVkD,EAAW,qCAAsC4C,CAAU,EAGxDzF,EAGN,GAA0B,UAAtB,OAAOyF,EAChB,CACI,IAAK,IAAIE,KAAOH,EAEZ,GAAIA,EAASG,KAASF,EAElB,OAAOA,EAIf5C,EAAW,4BAA6B4C,CAAU,CACtD,MAII5C,EAAW,4BAA4B,CAE/C,CAaA,SAAS+C,EAAaxD,EAAQC,EAASC,GAEnC,OAAOuD,EAAgBzD,EAAQC,EAASC,GAmELwD,EAnE0C,EAAPxD,EAqEzDgB,aAAewC,EArEmD,CACnF,CAaA,SAASC,EAAiB3D,EAAQC,EAASC,GAEvC,OAAOuD,EAAgBzD,EAAQC,EAASC,GAnQL0D,EAmQ0C,EAAP1D,EAjQzDgB,aAAe0C,EAiQmD,CACnF,CAcA,SAASC,EAAS7D,EAAQC,EAASC,EAAOkB,GAOtC,IALIA,EAAQ,GAAKA,GAASlB,EAAMQ,YAE5BD,EAAWZ,CAA4B,EAGvCI,EAAAA,EAAQ6D,kBAAoB1C,EAAQlB,EAAMgB,cAA9C,CAKA,IAAI6C,EAAW7D,EAAMgB,aACrB,GAAIlB,EAAOyC,eAAe,eAAgB,CAACvC,EAAMgB,aAAcE,EAAM,EACrE,CAEIlB,EAAMgB,aAAeE,EACrB4C,EAAyBhE,EAAQC,EAASC,CAAK,EAG/C+D,EAAsBjE,EAAQC,EAASC,EAAO6D,CAAQ,EACtDG,EAAkBlE,EAAQC,EAASC,CAAK,EACxCiE,EAAiBnE,EAAQC,EAASC,CAAK,EACvCkE,IAyqBwClE,EAzqBDA,EAyqBQkB,EAzqBDA,EAyqBQ2C,EAzqBDA,EAyqBWM,EAzqBD,WAE3DrE,EAAOyC,eAAe,cAAe,CAACrB,EAAO2C,EAAS,CAC1D,EAwqBAzD,GAAeN,EA3qBOA,GA2qBAwC,KAAK,kBAAkB,EAC7C8B,EAASzC,EAAkB0C,EAAkBtE,EAAQsE,gBAAgB,EACrEC,EAAcvE,EAAQwE,sBACtBC,EAAUpE,EAAaiB,GAAGH,CAAK,EAC/BmB,EAAcjC,EAAaiB,GAAGwC,CAAQ,EAE1C,OAAQO,GAEJ,KAAKC,EAAiBI,KACtB,KAAKJ,EAAiBK,MAClB,IAAIrG,EAAQ+F,IAAWC,EAAiBI,KAAQ,UAAY,UACxDrG,EAAQgG,IAAWC,EAAiBI,KAAQ,SAAW,YAE3DzE,EAAM2E,kBAAoBH,EAC1BnC,EAAYhE,GAAMiG,EAAa,WAE3B,IACItE,EAAQ8C,EADC1F,EAAEO,IAAI,EAAEQ,UAAU,CAAA,CAAK,EAAEyG,OAAO,EAAEA,OAAO,CAC3B,EAEvB5E,EAAM2E,oBAEN3E,EAAM2E,kBAAkBvG,GAAMkG,EAAa,WAEvClH,EAAEO,IAAI,EAAEQ,UAAU,CACtB,CAAC,EAAE0G,QAAQ,EAAEC,KAAKX,CAAY,EAC9BnE,EAAM2E,kBAAoB,KAElC,CAAC,EACD,MAEJ,KAAKN,EAAiBU,UAClB,IAAIC,EAAa3C,EAAY2C,WAAW,CAAA,CAAI,EACxCC,EAAsBpB,EAAR3C,EAAoB,CAAC,EAAe8D,EAClDE,EAAqBrB,EAAR3C,EAAoB8D,EAAa,CAAC,EAEnD5H,EAAE+H,KAAK9C,EAAY+C,QAAQ,CAAEC,KAAMJ,CAAW,EAAGX,EACzC,WAAclH,EAAEO,IAAI,EAAEQ,UAAU,CAAA,CAAK,CAAG,CAAC,EAC7CqG,EAAQc,IAAI,OAAQJ,EAAY,IAAI,EAAE/G,UAAU,EAC3CiH,QAAQ,CAAEC,KAAM,CAAE,EAAGf,CAAW,CAAC,EAAEQ,KAAKX,CAAY,EAC7D,MAEJ,QACI/G,EAAE+H,KAAK9C,EAAYlE,UAAU,CAAA,CAAK,EAAGqG,EAAQrG,UAAU,CAAC,EACnD2G,KAAKX,CAAY,CAE9B,CAptBA,MAGIrE,EAAOwC,KAAK,WAAW,EAAEjB,GAAGwC,CAAQ,EAAE3F,SAAS,OAAO,CApB1D,CAwBJ,CAeA,SAASqH,EAAWxF,GAGhB,IAAIyF,EAAOpI,EAAEG,OAAO,CAAA,EAAM,GAAIkI,EAAU1F,CAAO,EAE/C,OAAOpC,KAAKsD,KAAK,WAEb,IA+bQnB,EAAQC,EAASC,EA/brBF,EAAS1C,EAAEO,IAAI,EACfqC,EAAQ,CACRgB,aAAcwE,EAAKlF,WACnB+B,YAAa,KACb7B,UAAW,EACXmE,kBAAmB,IACvB,EAqfkB7E,GAlflBA,EAAOyB,KAAK,UAAWiE,CAAI,EAC3B1F,EAAOyB,KAAK,QAASvB,CAAK,EAC1BF,EAAOyB,KAAK,QAAS,EAAE,EAEvB1B,EAAYC,EAAQ0F,EAAMxF,CAAK,EAkbvBF,EAjbDA,EAibkBE,EAjbJA,EAobrB0F,EAAkB,6BAElBC,EADchE,EAAkBiE,GAJhB7F,EAjbDyF,GAqb2CI,gBAAgB,IACtCA,EAAiBC,SAAY,YAAc,GAC/EC,EAAiB1I,EAAEsI,EAAgB9G,OAAOmB,EAAQgG,oBAAqB,WAAahG,EAAQiG,iBAAkBlG,EAAO4B,KAAK,CAAC,CAAC,EAC5HuE,EAAe7I,EAAEsI,EAAgB9G,OAAOmB,EAAQmG,kBAAmB,SAAWnG,EAAQiG,iBAAkB,0BAA4B,CAAC,EACrI/F,EAAa6F,EAAe5F,SAASH,EAAQI,SAAS,EACtDC,EAAe0F,EAAe5F,SAASH,EAAQM,OAAO,EAG1DP,EAAOlC,KAAK,OAAQ,aAAa,EAAEuI,MAAM,EAAEC,OAAOH,CAAY,EAAEG,OAAON,CAAc,EAChF5H,SAAS6B,EAAQsG,SAAW,IAAMtG,EAAQiG,iBAAmBL,CAAgB,EAGlFvF,EAAaa,KAAK,SAAUC,GAExBoF,EAAWxG,EAAQE,EAAO5C,EAAEO,IAAI,EAAGuD,CAAK,CAC5C,CAAC,EAEDjB,EAAWgB,KAAK,SAAUC,GAEtBqF,EAAYzG,EAAQC,EAASC,EAAO5C,EAAEO,IAAI,EAAGuD,CAAK,CACtD,CAAC,EAED6C,EAAsBjE,EAAQC,EAASC,CAAK,EAC3BF,GAiCaC,EAjCLA,EAiCcC,EAjCLA,EAjHdF,GAoJhBC,EAAQyG,mBAGJC,EAAiB,kDACjBC,EAAU,GAET3G,EAAQ6D,mBAET8C,GAAWD,EAAe7H,OAAO,WAAYmB,EAAQ4G,OAAOC,QAAQ,GAGxEF,GAAWD,EAAe7H,OAAO,OAAQmB,EAAQ4G,OAAOE,IAAI,EAExD9G,EAAQ+G,qBAERJ,GAAWD,EAAe7H,OAAO,SAAUmB,EAAQ4G,OAAOI,MAAM,GAGhEhH,EAAQiH,qBAERN,GAAWD,EAAe7H,OAAO,SAAUmB,EAAQ4G,OAAOM,MAAM,GAGpEnH,EAAOsG,OArBU,2EAqBQxH,OAAOmB,EAAQmH,mBAAoBnH,EAAQiG,iBAChEjG,EAAQ4G,OAAOQ,WAAYT,CAAO,CAAC,EAEvC1C,EAAkBlE,EAAQC,EAASC,CAAK,EACxCiE,EAAiBnE,EAAQC,EAASC,CAAK,GAzgBxBF,GA0VSC,EA1VDyF,EA4VvB/C,EAAiBD,EAAkB1C,CAAM,EAE7CA,EAAOsH,KAAK,WAAa3E,EAAgB1C,EAAQsH,UAAU,EAC3DvH,EAAOsH,KAAK,gBAAkB3E,EAAgB1C,EAAQuH,eAAe,EACrExH,EAAOsH,KAAK,YAAc3E,EAAgB1C,EAAQwH,WAAW,EAC7DzH,EAAOsH,KAAK,WAAa3E,EAAgB1C,EAAQyH,UAAU,EAC3D1H,EAAOsH,KAAK,OAAS3E,EAAgB1C,EAAQ0H,MAAM,EACnD3H,EAAOsH,KAAK,eAAiB3E,EAAgB1C,EAAQ2H,cAAc,EACnE5H,EAAOsH,KAAK,cAAgB3E,EAAgB1C,EAAQ4H,aAAa,EAE7D5H,EAAQ6H,qBAER9H,EAAOsH,KAAK,QAAU3E,EAAgBoF,CAAY,EAGtD/H,EAAOwC,KAAK,YAAY,EAAE8E,KAAK,QAAU3E,EAAgBqF,CAAsB,EAxWvEtC,EAAKuC,WAA2B,IAAdzI,GAElBoD,EAAc5C,EAAQ0F,EAAKlF,UAAU,EAAE0H,MAAM,EAGjDlI,EAAOyC,eAAe,OAAQ,CAACiD,EAAKlF,WAAW,CACnD,CAAC,CACL,CAsBA,SAAS2H,EAAWnI,EAAQC,EAASC,EAAOkB,EAAOa,IAE3Cb,EAAQ,GAAKA,EAAQlB,EAAMQ,YAE3BD,EAAWZ,CAA4B,EAM3CoC,EAAO3E,EAAEG,OAAO,GAAIyE,EAAWD,CAAI,EAiDJb,EAhDLA,EAgDYa,EAhDLA,EAkDjCG,EAlDkBpC,CAkDH,EAAEoI,OAAOhH,EAAO,EAAGa,CAAI,EAjDlC/B,EAAMgB,eAAiBhB,EAAMQ,WAAaR,EAAMgB,cAAgBE,IAEhElB,EAAMgB,YAAY,GAClB8C,EAAyBhE,EAAQC,EAASC,CAAK,GAEnDA,EAAMQ,SAAS,GA0CnB,IAxCQ2H,EAAmBrI,EAAOwC,KAAK,UAAU,EACzC8F,EAAShL,EAAE,iBAAiBwB,OAAOmB,EAAQI,UAAW4B,EAAKE,KAAK,CAAC,EACjEoG,EAAOjL,EAAE,cAAcwB,OAAOmB,EAAQM,OAAO,CAAC,EAyBlD,OAvBwB,MAApB0B,EAAKN,aAAuBM,EAAKN,cAAgBA,EAAYC,MAE7D2G,EAAK3G,KAAKK,EAAKX,OAAO,EAGZ,IAAVF,EAEAiH,EAAiBG,QAAQD,CAAI,EAAEC,QAAQF,CAAM,EAI7CxF,EAAa9C,EAASoB,EAAQ,CAAE,EAAEqH,MAAMF,CAAI,EAAEE,MAAMH,CAAM,EAG9D9B,EAAWxG,EAAQE,EAAOqI,EAAMnH,CAAK,EACrCqF,EAAYzG,EAAQC,EAASC,EAAOoI,EAAQlH,CAAK,EACjDsH,EAAa1I,EAAQC,EAASC,EAAOkB,CAAK,EACtCA,IAAUlB,EAAMgB,cAEhB+C,EAAsBjE,EAAQC,EAASC,CAAK,EAEhDgE,EAAkBlE,EAAQC,EAASC,CAAK,EAEjCF,CACX,CAyBA,SAAS+H,EAAaY,GAElB,IAAI3I,EAAS1C,EAAEO,IAAI,EACfoC,EAAU8C,EAAW/C,CAAM,EAC3BE,EAAQ8C,EAAShD,CAAM,EAE3B,GAAIC,EAAQ2I,2BAA6B5I,EAAOwC,KAAK,QAAQ,EAAEqG,GAAG,QAAQ,EAGtE,OADAF,EAAMG,eAAe,EACd,CAAA,EAGX,IAAIC,EAAmB,GAAnBA,EAA8B,GAC9BJ,EAAMK,UAAYD,GAElBJ,EAAMG,eAAe,EACrBnF,EAAiB3D,EAAQC,EAASC,CAAK,GAElCyI,EAAMK,UAAYD,IAEvBJ,EAAMG,eAAe,EACrBtF,EAAaxD,EAAQC,EAASC,CAAK,EAE3C,CAYA,SAASiE,EAAiBnE,EAAQC,EAASC,GAEvC,GAAsB,EAAlBA,EAAMQ,UACV,CACI,IAAIQ,EAAehB,EAAMgB,aACrBqB,EAAcU,EAAQjD,EAAQkB,CAAY,EAE9C,GAAI,CAACjB,EAAQgJ,oBAAsB,CAAC1G,EAAYP,cAE5C,OAAQH,EAAkBF,EAAaY,EAAYZ,WAAW,GAE1D,KAAKA,EAAYuH,OACblJ,EAAOwC,KAAK,kBAAkB,EAAEjB,GAAGrB,EAAMgB,YAAY,EAAEmF,MAAM,EACxDzE,KAAK,gBAAmBW,EAAYR,WAAa,qCAA0C,EAC3FN,KAAK,SAAU,GAAG,EACvB,MAEJ,KAAKE,EAAYwH,MACb,IAAIC,EAAqBtG,EAAa9C,EAAQkB,CAAY,EAAExD,MAAM,OAAQ,MAAM,EAC3E2I,MAAM,EAAEC,OAAO+C,EAAepJ,EAAQqJ,gBAAiB,CAAEC,KAAMtJ,EAAQ4G,OAAO2C,OAAQ,CAAC,CAAC,EAE7FlM,EAAEmM,KAAK,CAAEC,IAAKnH,EAAYR,WAAY4H,MAAO,CAAA,CAAM,CAAC,EAAE3E,KAAK,SAAUvD,GAEjE2H,EAAmB/C,MAAM,EAAEzE,KAAKH,CAAI,EAAE/D,MAAM,OAAQ,OAAO,EAAE+D,KAAK,SAAU,GAAG,EAC/EzB,EAAOyC,eAAe,gBAAiB,CAACvB,EAAa,CACzD,CAAC,CAET,CAER,CACJ,CAcA,SAASuC,EAAgBzD,EAAQC,EAASC,EAAOkB,GAE7C,IAAI2C,EAAW7D,EAAMgB,aAErB,OAAa,GAATE,GAAcA,EAAQlB,EAAMQ,WAAhC,EAA+CT,EAAQ6D,kBAAoB1C,EAAQlB,EAAMgB,eAIjF0I,GADA9E,GADA+E,EAASjH,EAAc5C,EAAQoB,CAAK,GACpB0D,OAAO,GACHgF,SAAS,UAAU,EAG3ChF,EAAO7G,YAAY,EACnB4L,EAAOE,MAAM,EAGThG,IAAa7D,EAAMgB,cAAgB0I,IAGnC9E,EAAO7G,YAAY,CAAA,CAAK,EACjB,IAOnB,CAUA,SAAS+J,EAAuBW,GAE5BA,EAAMG,eAAe,EAErB,IAAIe,EAASvM,EAAEO,IAAI,EACfmC,EAAS6J,EAAO/E,OAAO,EAAEA,OAAO,EAAEA,OAAO,EAAEA,OAAO,EAClD7E,EAAU8C,EAAW/C,CAAM,EAC3BE,EAAQ8C,EAAShD,CAAM,EACvBgK,EAAOH,EAAO/L,KAAK,MAAM,EAE7B,OAAQkM,EAAKC,UAAUD,EAAKE,YAAY,GAAG,EAAI,CAAC,GAE5C,IAAK,SACMlK,EApkBRyC,eAAe,UAAU,EAqkBxB,MAEJ,IAAK,SACDH,EAAWtC,EAAQE,CAAK,EACxB,MAEJ,IAAK,OACDsD,EAAaxD,EAAQC,EAASC,CAAK,EACnC,MAEJ,IAAK,WACDyD,EAAiB3D,EAAQC,EAASC,CAAK,CAE/C,CACJ,CAYA,SAASgE,EAAkBlE,EAAQC,EAASC,GAExC,IAEQ+G,EACAF,EAHJ9G,EAAQyG,mBAEJO,EAASjH,EAAOwC,KAAK,6BAA6B,EAAEsC,OAAO,EAC3DiC,EAAO/G,EAAOwC,KAAK,2BAA2B,EAAEsC,OAAO,EAEtD7E,EAAQ6D,kBAEM9D,EAAOwC,KAAK,+BAA+B,EAAEsC,OAAO,EAC1D7G,YAAiC,EAArBiC,EAAMgB,YAAgB,EAG3CjB,EAAQ+G,oBAAsB/G,EAAQkK,wBAEtClD,EAAOhJ,YAA8B,EAAlBiC,EAAMQ,SAAa,EACtCqG,EAAK9I,YAA8B,EAAlBiC,EAAMQ,WAAiBR,EAAMQ,UAAaR,EAAMgB,aAAe,CAAE,IAIlF+F,EAAO5I,UAAU4B,EAAQ+G,oBAAsB9G,EAAMQ,YAAeR,EAAMgB,aAAe,CAAE,EAC3F6F,EAAK1I,UAA8B,IAApB6B,EAAMQ,WAAmBR,EAAMQ,UAAaR,EAAMgB,aAAe,CAAE,EAC9EjD,YAAYiC,EAAMQ,UAAaR,EAAMgB,aAAe,GAAM,CAACjB,EAAQ+G,kBAAkB,GAGrG,CAaA,SAAS/C,EAAsBjE,EAAQC,EAASC,EAAO6D,GAEnD,IAAIqG,EAAyBxH,EAAc5C,EAAQE,EAAMgB,YAAY,EACjEmJ,EAAc/M,EAAE,sCAA0C2C,EAAQ4G,OAAOyD,QAAU,UAAU,EAC7FnK,EAAaH,EAAOwC,KAAK,mBAAmB,EAEhC,MAAZuB,KAEIwG,EAAgB3H,EAAc5C,EAAQ+D,CAAQ,GACpCe,OAAO,EAAE1G,SAAS,MAAM,EAAED,YAAY,OAAO,EAAEK,YAAY,CAAA,CAAK,EAC9E2B,EAAWoB,GAAGwC,CAAQ,EAAE5F,YAAY,SAAS,EAAE4I,KAAK,OAAO,EAAE5I,YAAY,SAAS,EAClFkM,EAAcE,EAAc/H,KAAK,eAAe,EAChD4H,EAAuBlC,MAAM,GAGjCkC,EAAuB5B,QAAQ6B,CAAW,EAAEvF,OAAO,EAAEtG,YAAY,EAAEL,YAAY,MAAM,EAAEF,YAAY,EACnGkC,EAAWoB,GAAGrB,EAAMgB,YAAY,EAAE9C,SAAS,SAAS,EAAE2I,KAAK,OAAO,EAAE3I,SAAS,SAAS,CAC1F,CAaA,SAASsK,EAAa1I,EAAQC,EAASC,EAAOkB,GAI1C,IAFA,IAAIyB,EAAW/B,EAAYd,CAAM,EAExBZ,EAAIgC,EAAOhC,EAAIc,EAAMQ,UAAWtB,CAAC,GAC1C,CACI,IAAIoL,EAAe3H,EAAWnD,EAAaN,EACvCqL,EAAe5H,EAAWlD,EAAkBP,EAC5CsL,EAAiB7H,EAAWjD,EAAeR,EAC3C+C,EAAQnC,EAAOwC,KAAK,QAAQ,EAAEjB,GAAGnC,CAAC,EAAEV,IAAIgM,CAAc,EAE1D1K,EAAOwC,KAAK,UAAU,EAAEjB,GAAGnC,CAAC,EAAEV,IAAI8L,CAAY,EACzC9M,MAAM,WAAY+M,CAAY,EAAE3M,KAAK,OAAQ,IAAM4M,CAAc,EACjE9I,KAAKyH,EAAepJ,EAAQ0K,cAAe,CAAEvJ,MAAOhC,EAAI,EAAG+C,MAAOA,EAAMP,KAAK,CAAE,CAAC,CAAC,EACtF5B,EAAOwC,KAAK,OAAO,EAAEjB,GAAGnC,CAAC,EAAEV,IAAI+L,CAAY,EACtC/M,MAAM,aAAcgN,CAAc,CAC3C,CACJ,CAkCA,SAASE,EAAW5K,EAAQC,EAASC,EAAOkB,GAGxC,IArmBkBpB,EAAQoB,EAEtByB,EAmmBJ,MAAA,EAAIzB,EAAQ,GAAKA,GAASlB,EAAMQ,WAAaR,EAAMgB,eAAiBE,IAoCnCA,EA9BLA,EAgC5BgB,EAhCoBpC,CAgCL,EAAEoI,OAAOhH,EAAO,CAAC,EA/B5BlB,EAAMgB,aAAeE,IAErBlB,EAAMgB,YAAY,GAClB8C,EAAyBhE,EAAQC,EAASC,CAAK,GAEnDA,EAAMQ,SAAS,GAjnBWU,EAmnBLA,EAjnBjByB,EAAW/B,EAFGd,EAmnBLA,CAjnBoB,EAE1BA,EAAOwC,KAAK,IAAMK,EAAWjD,EAAewB,CAAK,EA+mB5ByJ,OAAO,EACnC/H,EAAa9C,EAAQoB,CAAK,EAAEyJ,OAAO,EACnCjI,EAAc5C,EAAQoB,CAAK,EAAE0D,OAAO,EAAE+F,OAAO,EAG/B,IAAVzJ,GAEApB,EAAOwC,KAAK,WAAW,EAAEsI,MAAM,EAAE1M,SAAS,OAAO,EAIjDgD,IAAUlB,EAAMQ,WAEhBV,EAAOwC,KAAK,WAAW,EAAEjB,GAAGH,CAAK,EAAEhD,SAAS,MAAM,EAGtDsK,EAAa1I,EAAQC,EAASC,EAAOkB,CAAK,EAC1C8C,EAAkBlE,EAAQC,EAASC,CAAK,EAEjC,GACX,CAyDA,SAASsG,EAAWxG,EAAQE,EAAOqI,EAAMnH,GAErC,IAAIyB,EAAW/B,EAAYd,CAAM,EAE7B0K,EAAiB7H,EAAWjD,EAAewB,EAE/CmH,EAAK7J,IAHcmE,EAAWlD,EAAkByB,CAG3B,EAAEtD,KAAK,OAAQ,UAAU,EAAEJ,MAAM,aAAcgN,CAAc,EAC7EtM,SAAS,MAAM,EAAEC,UAAU6B,EAAMgB,eAAiBE,CAAK,CAChE,CAuDA,SAASiI,EAAe0B,EAAUC,GAI9B,IAFA,IAAIC,EAAUF,EAASG,MAAM,cAAc,EAElC9L,EAAI,EAAGA,EAAI6L,EAAQhM,OAAQG,CAAC,GACrC,CACI,IAAI8L,EAAQD,EAAQ7L,GAChBmE,EAAM2H,EAAMjB,UAAU,EAAGiB,EAAMjM,OAAS,CAAC,EAEzC+L,EAAYzH,KAAShG,GAErBkD,EAAW,6DAA8D8C,CAAG,EAGhFwH,EAAWA,EAASxL,QAAQ2L,EAAOF,EAAYzH,EAAI,CACvD,CAEA,OAAOwH,CACX,CAcA,SAAStE,EAAYzG,EAAQC,EAASC,EAAOoI,EAAQlH,GAEjD,IAAIyB,EAAW/B,EAAYd,CAAM,EAC7BwK,EAAe3H,EAAWnD,EAAa0B,EACvCqJ,EAAe5H,EAAWlD,EAAkByB,EAC5CsJ,EAAiB7H,EAAWjD,EAAewB,EAC3C+J,EAAiBnL,EAAOwC,KAAK,aAAa,EAC1CL,EAAQkH,EAAepJ,EAAQ0K,cAAe,CAC1CvJ,MAAOA,EAAQ,EACfe,MAAOmG,EAAO1G,KAAK,CACvB,CAAC,EACDwJ,EAAW9N,EAAE,yBAA8BkN,EAAe,YAAgBE,EACtE,oBAAwBD,EAAe,KAAQtI,EAAQ,WAAW,EAE1EiJ,EAASnN,YAAYgC,EAAQoL,gBAAkBnL,EAAMgB,aAAeE,CAAK,EAErElB,EAAMgB,aAAeE,GAErBgK,EAAShN,SAAS,MAAM,EAG5BkK,EAAO5J,IAAIgM,CAAc,EAAE5M,KAAK,WAAY,IAAI,EAAEM,SAAS,OAAO,EAEpD,IAAVgD,EAEA+J,EAAe3C,QAAQ4C,CAAQ,EAI/BD,EAAe3I,KAAK,IAAI,EAAEjB,GAAGH,EAAQ,CAAC,EAAEqH,MAAM2C,CAAQ,EAI5C,IAAVhK,GAEA+J,EAAe3I,KAAK,IAAI,EAAErE,YAAY,OAAO,EAAEoD,GAAGH,CAAK,EAAEhD,SAAS,OAAO,EAIzEgD,IAAWlB,EAAMQ,UAAY,GAE7ByK,EAAe3I,KAAK,IAAI,EAAErE,YAAY,MAAM,EAAEoD,GAAGH,CAAK,EAAEhD,SAAS,MAAM,EAI3EgN,EAAShL,SAAS,GAAG,EAAEkH,KAAK,QAAU5E,EAAkB1C,CAAM,EAAGsL,CAAgB,CACrF,CAYA,SAAStH,EAAyBhE,EAAQC,EAASC,GAE3CD,EAAQU,WAAarD,EAAEsD,QAEvBtD,EAAEsD,OAAOnB,EAAgBqB,EAAYd,CAAM,EAAGE,EAAMgB,YAAY,CAExE,CA4DA,SAASoK,EAAiB3C,GAEtBA,EAAMG,eAAe,EAErB,IAAIe,EAASvM,EAAEO,IAAI,EACfmC,EAAS6J,EAAO/E,OAAO,EAAEA,OAAO,EAAEA,OAAO,EAAEA,OAAO,EAClD7E,EAAU8C,EAAW/C,CAAM,EAC3BE,EAAQ8C,EAAShD,CAAM,EACvB+D,EAAW7D,EAAMgB,aAWrB,GATI2I,EAAO/E,OAAO,EAAE+D,GAAG,+BAA+B,IAE9CmB,EAAOH,EAAO/L,KAAK,MAAM,EAG7B+F,EAAS7D,EAAQC,EAASC,EAFXc,SAASgJ,EAAKC,UAAUD,EAAKE,YAAY,GAAG,EAAI,CAAC,EAAG,CAAC,CAE3B,GAIzCnG,IAAa7D,EAAMgB,aAGnB,OADA0B,EAAc5C,EAAQ+D,CAAQ,EAAEmE,MAAM,EAC/B,CAAA,CAEf,CAEA,SAASzH,EAAW8K,GAOhB,MALuB,EAAnBvM,UAAUC,SAEVsM,EAAUA,EAAQzM,OAAO0M,MAAM3M,UAAU4M,MAAMC,KAAK1M,UAAW,CAAC,CAAC,GAG/D,IAAI2M,MAAMJ,CAAO,CAC3B,CAWA,SAASjI,EAAiBsI,EAAcC,GAEf,MAAjBA,GAEApL,EAAW,2CAA4CmL,CAAY,CAE3E,CAWAtO,EAAEE,GAAG0F,MAAQ,SAAU4I,GAEnB,OAAIxO,EAAEE,GAAG0F,MAAM4I,GAEJxO,EAAEE,GAAG0F,MAAM4I,GAAQC,MAAMlO,KAAM2N,MAAM3M,UAAU4M,MAAMC,KAAK1M,UAAW,CAAC,CAAC,EAEvD,UAAlB,OAAO8M,GAAwBA,EAMpCxO,KAAAA,EAAE0O,MAAM,UAAYF,EAAS,iCAAiC,EAJvDrG,EAAWsG,MAAMlO,KAAMmB,SAAS,CAM/C,EASA1B,EAAEE,GAAG0F,MAAM+I,IAAM,SAAUhK,GAEvB,IAAI/B,EAAQ8C,EAASnF,IAAI,EACzB,OAAOsK,EAAWtK,KAAMkF,EAAWlF,IAAI,EAAGqC,EAAOA,EAAMQ,UAAWuB,CAAI,CAC1E,EAQA3E,EAAEE,GAAG0F,MAAMgJ,QAAU,WAEVA,IAlmCMlM,EAkmCEnC,KAlmCMoC,EAkmCA8C,EAAWlF,IAAI,EAhmChC8E,EAAiBD,EAAkB1C,CAAM,EAmC7C,OAhCAA,EAAOmM,OAAOxJ,CAAc,EAAEyJ,WAAW,KAAK,EAAEA,WAAW,SAAS,EAC/DA,WAAW,OAAO,EAAEA,WAAW,OAAO,EAAEA,WAAW,gBAAgB,EACnE5J,KAAK,YAAY,EAAE2J,OAAOxJ,CAAc,EAG7C3C,EAAO7B,YAAY8B,EAAQiG,iBAAmB,WAAW,GAKzDmG,EAHerM,EAAOwC,KAAK,cAAc,GAGhC4J,WAAW,QAAQ,EAAEA,WAAW,MAAM,EAAEA,WAAW,KAAK,EAGjEC,EAASrO,WAAW,IAAI,EAAEA,WAAW,MAAM,EAAEA,WAAW,UAAU,EAC7DA,WAAW,OAAO,EAAEA,WAAW,OAAO,EAAED,YAAY,YAAY,EAChEA,YAAY,QAAQ,EAGzBiC,EAAOwC,KAAK,gEAAgE,EAAE6D,MAAM,EAEhFiG,EAAmBhP,EAAE,0BAA4BwB,OAAOkB,EAAOuM,IAAI,CAAC,EAAEC,QAASxM,EAAOlC,KAAK,OAAO,CAAC,CAAC,EAGxF,OAAZ2O,EADWzM,EAAOtB,IAAI,IACW,KAAb+N,GAEpBH,EAAiB5N,IAAI+N,CAAQ,EAGjCH,EAAiB1K,KAAK5B,EAAOwC,KAAK,UAAU,EAAEZ,KAAK,CAAC,EACpD5B,EAAOyI,MAAM6D,CAAgB,EAC7BtM,EAAO6K,OAAO,EAEPyB,CA8jCX,EAOAhP,EAAEE,GAAG0F,MAAM+D,OAAS,WAEhB3E,EAAWzE,KAAMmF,EAASnF,IAAI,CAAC,CACnC,EASAP,EAAEE,GAAG0F,MAAMwJ,gBAAkB,WAEzB,OAAO1J,EAASnF,IAAI,EAAEqD,YAC1B,EAQA5D,EAAEE,GAAG0F,MAAMyJ,eAAiB,WAExB,OAAO1J,EAAQpF,KAAMmF,EAASnF,IAAI,EAAEqD,YAAY,CACpD,EASA5D,EAAEE,GAAG0F,MAAMD,QAAU,SAAU7B,GAE3B,OAAO6B,EAAQpF,KAAMuD,CAAK,CAC9B,EAiBA9D,EAAEE,GAAG0F,MAAM0J,OAAS,SAAUxL,EAAOa,GAEjC,OAAOkG,EAAWtK,KAAMkF,EAAWlF,IAAI,EAAGmF,EAASnF,IAAI,EAAGuD,EAAOa,CAAI,CACzE,EAQA3E,EAAEE,GAAG0F,MAAM6D,KAAO,WAEd,OAAOvD,EAAa3F,KAAMkF,EAAWlF,IAAI,EAAGmF,EAASnF,IAAI,CAAC,CAC9D,EAQAP,EAAEE,GAAG0F,MAAM4D,SAAW,WAElB,OAAOnD,EAAiB9F,KAAMkF,EAAWlF,IAAI,EAAGmF,EAASnF,IAAI,CAAC,CAClE,EASAP,EAAEE,GAAG0F,MAAM2H,OAAS,SAAUzJ,GAE1B,OAAOwJ,EAAW/M,KAAMkF,EAAWlF,IAAI,EAAGmF,EAASnF,IAAI,EAAGuD,CAAK,CACnE,EASA9D,EAAEE,GAAG0F,MAAM2J,QAAU,SAAUzL,EAAOa,GAElC,MAAM,IAAI0J,MAAM,sBAAsB,CAC1C,EASArO,EAAEE,GAAG0F,MAAM4J,KAAO,SAAUC,GAExB,MAAM,IAAIpB,MAAM,sBAAsB,CAC1C,EAQA,IAAIhK,EAAcrE,EAAEE,GAAG0F,MAAMvB,YAAc,CASvCC,KAAM,EAUNsH,OAAQ,EAURC,MAAO,CACX,EAQIrD,EAAmBxI,EAAEE,GAAG0F,MAAM4C,iBAAmB,CASjDkH,WAAY,EAUZjH,SAAU,CACd,EAQIxB,EAAmBjH,EAAEE,GAAG0F,MAAMqB,iBAAmB,CASjD0I,KAAM,EAUNtI,KAAM,EAUNC,MAAO,EAUPK,UAAW,CACf,EAEI/C,EAAY5E,EAAEE,GAAG0F,MAAMhB,UAAY,CACnCC,MAAO,GACPb,QAAS,GACTS,WAAY,GACZJ,YAAaA,EAAYC,KACzBI,cAAe,CAAA,CACnB,EAiBI2D,EAAWrI,EAAEE,GAAG0F,MAAMyC,SAAW,CASjCtF,UAAW,KAUXE,QAAS,MAUT0F,oBAAqB,MAUrBmB,mBAAoB,MAUpBhB,kBAAmB,MAcnBG,SAAU,SAUVL,iBAAkB,WAWlBJ,iBAAkBA,EAAiBkH,WAcnCrC,cAAe,+CAUfrB,gBAAiB,uCAejBrB,UAAW,CAAA,EAUXoD,eAAgB,CAAA,EAUhBvD,oBAAqB,CAAA,EAUrBpB,iBAAkB,CAAA,EAUlBkC,0BAA2B,CAAA,EAU3BK,mBAAoB,CAAA,EAUpB/B,mBAAoB,CAAA,EAUpBF,mBAAoB,CAAA,EAUpBkG,eAAgB,CAAA,EAWhB/C,uBAAwB,CAAA,EAUxBrG,iBAAkB,CAAA,EAWlBnD,UAAW,CAAA,EAUXH,WAAY,EAcZ+D,iBAAkBA,EAAiB0I,KAUnCxI,sBAAuB,IAevBmD,eAAgB,SAAUe,EAAOzH,EAAciM,GAAY,MAAO,CAAA,CAAM,EAUxEtF,cAAe,SAAUc,EAAOzH,EAAckM,KAU9C7F,WAAY,SAAUoB,KAWtBlB,YAAa,SAAUkB,EAAOzH,GAAgB,MAAO,CAAA,CAAM,EAU3DwG,WAAY,SAAUiB,EAAOzH,KAU7BsG,gBAAiB,SAAUmB,EAAOzH,KAUlCyG,OAAQ,SAAUgB,EAAOzH,KASzB2F,OAAQ,CASJM,OAAQ,SAWRmD,QAAS,gBAWTjD,WAAY,aAUZJ,OAAQ,SAURF,KAAM,OAUND,SAAU,WAUV0C,QAAS,aACb,CACJ,CACC,EAAE6D,MAAM"}
{"version": 3, "file": "chance.min.js", "sources": ["chance.min.js"], "sourcesContent": ["//  Chance.js 1.0.13\r\n//  http://chancejs.com\r\n//  (c) 2013 <PERSON>\r\n//  Chance may be freely distributed or modified under the MIT license.\r\n\r\n(function () {\r\n\t// Constants\r\n\tvar MAX_INT = 9007199254740992;\r\n\tvar MIN_INT = -MAX_INT;\r\n\tvar NUMBERS = \"**********\";\r\n\tvar CHARS_LOWER = \"abcdefghijklmnopqrstuvwxyz\";\r\n\tvar CHARS_UPPER = CHARS_LOWER.toUpperCase();\r\n\tvar HEX_POOL = NUMBERS + \"abcdef\";\r\n\r\n\t// Cached array helpers\r\n\tvar slice = Array.prototype.slice;\r\n\r\n\t// Constructor\r\n\tfunction Chance(seed) {\r\n\t\tif (!(this instanceof Chance)) {\r\n\t\t\tif (!seed) {\r\n\t\t\t\tseed = null;\r\n\t\t\t} // handle other non-truthy seeds, as described in issue #322\r\n\t\t\treturn seed === null ? new Chance() : new Chance(seed);\r\n\t\t}\r\n\r\n\t\t// if user has provided a function, use that as the generator\r\n\t\tif (typeof seed === \"function\") {\r\n\t\t\tthis.random = seed;\r\n\t\t\treturn this;\r\n\t\t}\r\n\r\n\t\tif (arguments.length) {\r\n\t\t\t// set a starting value of zero so we can add to it\r\n\t\t\tthis.seed = 0;\r\n\t\t}\r\n\r\n\t\t// otherwise, leave this.seed blank so that MT will receive a blank\r\n\r\n\t\tfor (var i = 0; i < arguments.length; i++) {\r\n\t\t\tvar seedling = 0;\r\n\t\t\tif (Object.prototype.toString.call(arguments[i]) === \"[object String]\") {\r\n\t\t\t\tfor (var j = 0; j < arguments[i].length; j++) {\r\n\t\t\t\t\t// create a numeric hash for each argument, add to seedling\r\n\t\t\t\t\tvar hash = 0;\r\n\t\t\t\t\tfor (var k = 0; k < arguments[i].length; k++) {\r\n\t\t\t\t\t\thash = arguments[i].charCodeAt(k) + (hash << 6) + (hash << 16) - hash;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tseedling += hash;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tseedling = arguments[i];\r\n\t\t\t}\r\n\t\t\tthis.seed += (arguments.length - i) * seedling;\r\n\t\t}\r\n\r\n\t\t// If no generator function was provided, use our MT\r\n\t\tthis.mt = this.mersenne_twister(this.seed);\r\n\t\tthis.bimd5 = this.blueimp_md5();\r\n\t\tthis.random = function () {\r\n\t\t\treturn this.mt.random(this.seed);\r\n\t\t};\r\n\r\n\t\treturn this;\r\n\t}\r\n\r\n\tChance.prototype.VERSION = \"1.0.13\";\r\n\r\n\t// Random helper functions\r\n\tfunction initOptions(options, defaults) {\r\n\t\toptions = options || {};\r\n\r\n\t\tif (defaults) {\r\n\t\t\tfor (var i in defaults) {\r\n\t\t\t\tif (typeof options[i] === \"undefined\") {\r\n\t\t\t\t\toptions[i] = defaults[i];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn options;\r\n\t}\r\n\r\n\tfunction testRange(test, errorMessage) {\r\n\t\tif (test) {\r\n\t\t\tthrow new RangeError(errorMessage);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Encode the input string with Base64.\r\n\t */\r\n\tvar base64 = function () {\r\n\t\tthrow new Error(\"No Base64 encoder available.\");\r\n\t};\r\n\r\n\t// Select proper Base64 encoder.\r\n\t(function determineBase64Encoder() {\r\n\t\tif (typeof btoa === \"function\") {\r\n\t\t\tbase64 = btoa;\r\n\t\t} else if (typeof Buffer === \"function\") {\r\n\t\t\tbase64 = function (input) {\r\n\t\t\t\treturn new Buffer(input).toString(\"base64\");\r\n\t\t\t};\r\n\t\t}\r\n\t})();\r\n\r\n\t// -- Basics --\r\n\r\n\t/**\r\n\t *  Return a random bool, either true or false\r\n\t *\r\n\t *  @param {Object} [options={ likelihood: 50 }] alter the likelihood of\r\n\t *    receiving a true or false value back.\r\n\t *  @throws {RangeError} if the likelihood is out of bounds\r\n\t *  @returns {Bool} either true or false\r\n\t */\r\n\tChance.prototype.bool = function (options) {\r\n\t\t// likelihood of success (true)\r\n\t\toptions = initOptions(options, { likelihood: 50 });\r\n\r\n\t\t// Note, we could get some minor perf optimizations by checking range\r\n\t\t// prior to initializing defaults, but that makes code a bit messier\r\n\t\t// and the check more complicated as we have to check existence of\r\n\t\t// the object then existence of the key before checking constraints.\r\n\t\t// Since the options initialization should be minor computationally,\r\n\t\t// decision made for code cleanliness intentionally. This is mentioned\r\n\t\t// here as it's the first occurrence, will not be mentioned again.\r\n\t\ttestRange(options.likelihood < 0 || options.likelihood > 100, \"Chance: Likelihood accepts values from 0 to 100.\");\r\n\r\n\t\treturn this.random() * 100 < options.likelihood;\r\n\t};\r\n\r\n\tChance.prototype.animal = function (options) {\r\n\t\t//returns a random animal\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tif (typeof options.type !== \"undefined\") {\r\n\t\t\t//if user does not put in a valid animal type, user will get an error\r\n\t\t\ttestRange(!this.get(\"animals\")[options.type.toLowerCase()], \"Please pick from desert, ocean, grassland, forest, zoo, pets, farm.\");\r\n\t\t\t//if user does put in valid animal type, will return a random animal of that type\r\n\t\t\treturn this.pick(this.get(\"animals\")[options.type.toLowerCase()]);\r\n\t\t}\r\n\t\t//if user does not put in any animal type, will return a random animal regardless\r\n\t\tanimalTypeArray = [\"desert\", \"forest\", \"ocean\", \"zoo\", \"farm\", \"pet\", \"grassland\"];\r\n\t\treturn this.pick(this.get(\"animals\")[this.pick(animalTypeArray)]);\r\n\t};\r\n\r\n\t/**\r\n\t *  Return a random character.\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a character pool, only alpha,\r\n\t *    only symbols, and casing (lower or upper)\r\n\t *  @returns {String} a single random character\r\n\t *  @throws {RangeError} Can only specify alpha or symbols, not both\r\n\t */\r\n\tChance.prototype.character = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\ttestRange(options.alpha && options.symbols, \"Chance: Cannot specify both alpha and symbols.\");\r\n\r\n\t\tvar symbols = \"!@#$%^&*()[]\",\r\n\t\t\tletters,\r\n\t\t\tpool;\r\n\r\n\t\tif (options.casing === \"lower\") {\r\n\t\t\tletters = CHARS_LOWER;\r\n\t\t} else if (options.casing === \"upper\") {\r\n\t\t\tletters = CHARS_UPPER;\r\n\t\t} else {\r\n\t\t\tletters = CHARS_LOWER + CHARS_UPPER;\r\n\t\t}\r\n\r\n\t\tif (options.pool) {\r\n\t\t\tpool = options.pool;\r\n\t\t} else if (options.alpha) {\r\n\t\t\tpool = letters;\r\n\t\t} else if (options.symbols) {\r\n\t\t\tpool = symbols;\r\n\t\t} else {\r\n\t\t\tpool = letters + NUMBERS + symbols;\r\n\t\t}\r\n\r\n\t\treturn pool.charAt(this.natural({ max: pool.length - 1 }));\r\n\t};\r\n\r\n\t// Note, wanted to use \"float\" or \"double\" but those are both JS reserved words.\r\n\r\n\t// Note, fixed means N OR LESS digits after the decimal. This because\r\n\t// It could be 14.9000 but in JavaScript, when this is cast as a number,\r\n\t// the trailing zeroes are dropped. Left to the consumer if trailing zeroes are\r\n\t// needed\r\n\t/**\r\n\t *  Return a random floating point number\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a fixed precision, min, max\r\n\t *  @returns {Number} a single floating point number\r\n\t *  @throws {RangeError} Can only specify fixed or precision, not both. Also\r\n\t *    min cannot be greater than max\r\n\t */\r\n\tChance.prototype.floating = function (options) {\r\n\t\toptions = initOptions(options, { fixed: 4 });\r\n\t\ttestRange(options.fixed && options.precision, \"Chance: Cannot specify both fixed and precision.\");\r\n\r\n\t\tvar num;\r\n\t\tvar fixed = Math.pow(10, options.fixed);\r\n\r\n\t\tvar max = MAX_INT / fixed;\r\n\t\tvar min = -max;\r\n\r\n\t\ttestRange(options.min && options.fixed && options.min < min, \"Chance: Min specified is out of range with fixed. Min should be, at least, \" + min);\r\n\t\ttestRange(options.max && options.fixed && options.max > max, \"Chance: Max specified is out of range with fixed. Max should be, at most, \" + max);\r\n\r\n\t\toptions = initOptions(options, { min: min, max: max });\r\n\r\n\t\t// Todo - Make this work!\r\n\t\t// options.precision = (typeof options.precision !== \"undefined\") ? options.precision : false;\r\n\r\n\t\tnum = this.integer({ min: options.min * fixed, max: options.max * fixed });\r\n\t\tvar num_fixed = (num / fixed).toFixed(options.fixed);\r\n\r\n\t\treturn parseFloat(num_fixed);\r\n\t};\r\n\r\n\t/**\r\n\t *  Return a random integer\r\n\t *\r\n\t *  NOTE the max and min are INCLUDED in the range. So:\r\n\t *  chance.integer({min: 1, max: 3});\r\n\t *  would return either 1, 2, or 3.\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a min and/or max\r\n\t *  @returns {Number} a single random integer number\r\n\t *  @throws {RangeError} min cannot be greater than max\r\n\t */\r\n\tChance.prototype.integer = function (options) {\r\n\t\t// 9007199254740992 (2^53) is the max integer number in JavaScript\r\n\t\t// See: http://vq.io/132sa2j\r\n\t\toptions = initOptions(options, { min: MIN_INT, max: MAX_INT });\r\n\t\ttestRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\r\n\r\n\t\treturn Math.floor(this.random() * (options.max - options.min + 1) + options.min);\r\n\t};\r\n\r\n\t/**\r\n\t *  Return a random natural\r\n\t *\r\n\t *  NOTE the max and min are INCLUDED in the range. So:\r\n\t *  chance.natural({min: 1, max: 3});\r\n\t *  would return either 1, 2, or 3.\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a min and/or maxm or a numerals count.\r\n\t *  @returns {Number} a single random integer number\r\n\t *  @throws {RangeError} min cannot be greater than max\r\n\t */\r\n\tChance.prototype.natural = function (options) {\r\n\t\toptions = initOptions(options, { min: 0, max: MAX_INT });\r\n\t\tif (typeof options.numerals === \"number\") {\r\n\t\t\ttestRange(options.numerals < 1, \"Chance: Numerals cannot be less than one.\");\r\n\t\t\toptions.min = Math.pow(10, options.numerals - 1);\r\n\t\t\toptions.max = Math.pow(10, options.numerals) - 1;\r\n\t\t}\r\n\t\ttestRange(options.min < 0, \"Chance: Min cannot be less than zero.\");\r\n\t\treturn this.integer(options);\r\n\t};\r\n\r\n\t/**\r\n\t *  Return a random hex number as string\r\n\t *\r\n\t *  NOTE the max and min are INCLUDED in the range. So:\r\n\t *  chance.hex({min: '9', max: 'B'});\r\n\t *  would return either '9', 'A' or 'B'.\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a min and/or max and/or casing\r\n\t *  @returns {String} a single random string hex number\r\n\t *  @throws {RangeError} min cannot be greater than max\r\n\t */\r\n\tChance.prototype.hex = function (options) {\r\n\t\toptions = initOptions(options, { min: 0, max: MAX_INT, casing: \"lower\" });\r\n\t\ttestRange(options.min < 0, \"Chance: Min cannot be less than zero.\");\r\n\t\tvar integer = this.natural({ min: options.min, max: options.max });\r\n\t\tif (options.casing === \"upper\") {\r\n\t\t\treturn integer.toString(16).toUpperCase();\r\n\t\t}\r\n\t\treturn integer.toString(16);\r\n\t};\r\n\r\n\tChance.prototype.letter = function (options) {\r\n\t\toptions = initOptions(options, { casing: \"lower\" });\r\n\t\tvar pool = \"abcdefghijklmnopqrstuvwxyz\";\r\n\t\tvar letter = this.character({ pool: pool });\r\n\t\tif (options.casing === \"upper\") {\r\n\t\t\tletter = letter.toUpperCase();\r\n\t\t}\r\n\t\treturn letter;\r\n\t};\r\n\r\n\t/**\r\n\t *  Return a random string\r\n\t *\r\n\t *  @param {Object} [options={}] can specify a length\r\n\t *  @returns {String} a string of random length\r\n\t *  @throws {RangeError} length cannot be less than zero\r\n\t */\r\n\tChance.prototype.string = function (options) {\r\n\t\toptions = initOptions(options, { length: this.natural({ min: 5, max: 20 }) });\r\n\t\ttestRange(options.length < 0, \"Chance: Length cannot be less than zero.\");\r\n\t\tvar length = options.length,\r\n\t\t\ttext = this.n(this.character, length, options);\r\n\r\n\t\treturn text.join(\"\");\r\n\t};\r\n\r\n\t// -- End Basics --\r\n\r\n\t// -- Helpers --\r\n\r\n\tChance.prototype.capitalize = function (word) {\r\n\t\treturn word.charAt(0).toUpperCase() + word.substr(1);\r\n\t};\r\n\r\n\tChance.prototype.mixin = function (obj) {\r\n\t\tfor (var func_name in obj) {\r\n\t\t\tChance.prototype[func_name] = obj[func_name];\r\n\t\t}\r\n\t\treturn this;\r\n\t};\r\n\r\n\t/**\r\n\t *  Given a function that generates something random and a number of items to generate,\r\n\t *    return an array of items where none repeat.\r\n\t *\r\n\t *  @param {Function} fn the function that generates something random\r\n\t *  @param {Number} num number of terms to generate\r\n\t *  @param {Object} options any options to pass on to the generator function\r\n\t *  @returns {Array} an array of length `num` with every item generated by `fn` and unique\r\n\t *\r\n\t *  There can be more parameters after these. All additional parameters are provided to the given function\r\n\t */\r\n\tChance.prototype.unique = function (fn, num, options) {\r\n\t\ttestRange(typeof fn !== \"function\", \"Chance: The first argument must be a function.\");\r\n\r\n\t\tvar comparator = function (arr, val) {\r\n\t\t\treturn arr.indexOf(val) !== -1;\r\n\t\t};\r\n\r\n\t\tif (options) {\r\n\t\t\tcomparator = options.comparator || comparator;\r\n\t\t}\r\n\r\n\t\tvar arr = [],\r\n\t\t\tcount = 0,\r\n\t\t\tresult,\r\n\t\t\tMAX_DUPLICATES = num * 50,\r\n\t\t\tparams = slice.call(arguments, 2);\r\n\r\n\t\twhile (arr.length < num) {\r\n\t\t\tvar clonedParams = JSON.parse(JSON.stringify(params));\r\n\t\t\tresult = fn.apply(this, clonedParams);\r\n\t\t\tif (!comparator(arr, result)) {\r\n\t\t\t\tarr.push(result);\r\n\t\t\t\t// reset count when unique found\r\n\t\t\t\tcount = 0;\r\n\t\t\t}\r\n\r\n\t\t\tif (++count > MAX_DUPLICATES) {\r\n\t\t\t\tthrow new RangeError(\"Chance: num is likely too large for sample set\");\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn arr;\r\n\t};\r\n\r\n\t/**\r\n\t *  Gives an array of n random terms\r\n\t *\r\n\t *  @param {Function} fn the function that generates something random\r\n\t *  @param {Number} n number of terms to generate\r\n\t *  @returns {Array} an array of length `n` with items generated by `fn`\r\n\t *\r\n\t *  There can be more parameters after these. All additional parameters are provided to the given function\r\n\t */\r\n\tChance.prototype.n = function (fn, n) {\r\n\t\ttestRange(typeof fn !== \"function\", \"Chance: The first argument must be a function.\");\r\n\r\n\t\tif (typeof n === \"undefined\") {\r\n\t\t\tn = 1;\r\n\t\t}\r\n\t\tvar i = n,\r\n\t\t\tarr = [],\r\n\t\t\tparams = slice.call(arguments, 2);\r\n\r\n\t\t// Providing a negative count should result in a noop.\r\n\t\ti = Math.max(0, i);\r\n\r\n\t\tfor (null; i--; null) {\r\n\t\t\tarr.push(fn.apply(this, params));\r\n\t\t}\r\n\r\n\t\treturn arr;\r\n\t};\r\n\r\n\t// H/T to SO for this one: http://vq.io/OtUrZ5\r\n\tChance.prototype.pad = function (number, width, pad) {\r\n\t\t// Default pad to 0 if none provided\r\n\t\tpad = pad || \"0\";\r\n\t\t// Convert number to a string\r\n\t\tnumber = number + \"\";\r\n\t\treturn number.length >= width ? number : new Array(width - number.length + 1).join(pad) + number;\r\n\t};\r\n\r\n\t// DEPRECATED on 2015-10-01\r\n\tChance.prototype.pick = function (arr, count) {\r\n\t\tif (arr.length === 0) {\r\n\t\t\tthrow new RangeError(\"Chance: Cannot pick() from an empty array\");\r\n\t\t}\r\n\t\tif (!count || count === 1) {\r\n\t\t\treturn arr[this.natural({ max: arr.length - 1 })];\r\n\t\t} else {\r\n\t\t\treturn this.shuffle(arr).slice(0, count);\r\n\t\t}\r\n\t};\r\n\r\n\t// Given an array, returns a single random element\r\n\tChance.prototype.pickone = function (arr) {\r\n\t\tif (arr.length === 0) {\r\n\t\t\tthrow new RangeError(\"Chance: Cannot pickone() from an empty array\");\r\n\t\t}\r\n\t\treturn arr[this.natural({ max: arr.length - 1 })];\r\n\t};\r\n\r\n\t// Given an array, returns a random set with 'count' elements\r\n\tChance.prototype.pickset = function (arr, count) {\r\n\t\tif (count === 0) {\r\n\t\t\treturn [];\r\n\t\t}\r\n\t\tif (arr.length === 0) {\r\n\t\t\tthrow new RangeError(\"Chance: Cannot pickset() from an empty array\");\r\n\t\t}\r\n\t\tif (count < 0) {\r\n\t\t\tthrow new RangeError(\"Chance: Count must be a positive number\");\r\n\t\t}\r\n\t\tif (!count || count === 1) {\r\n\t\t\treturn [this.pickone(arr)];\r\n\t\t} else {\r\n\t\t\treturn this.shuffle(arr).slice(0, count);\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.shuffle = function (arr) {\r\n\t\tvar old_array = arr.slice(0),\r\n\t\t\tnew_array = [],\r\n\t\t\tj = 0,\r\n\t\t\tlength = Number(old_array.length);\r\n\r\n\t\tfor (var i = 0; i < length; i++) {\r\n\t\t\t// Pick a random index from the array\r\n\t\t\tj = this.natural({ max: old_array.length - 1 });\r\n\t\t\t// Add it to the new array\r\n\t\t\tnew_array[i] = old_array[j];\r\n\t\t\t// Remove that element from the original array\r\n\t\t\told_array.splice(j, 1);\r\n\t\t}\r\n\r\n\t\treturn new_array;\r\n\t};\r\n\r\n\t// Returns a single item from an array with relative weighting of odds\r\n\tChance.prototype.weighted = function (arr, weights, trim) {\r\n\t\tif (arr.length !== weights.length) {\r\n\t\t\tthrow new RangeError(\"Chance: Length of array and weights must match\");\r\n\t\t}\r\n\r\n\t\t// scan weights array and sum valid entries\r\n\t\tvar sum = 0;\r\n\t\tvar val;\r\n\t\tfor (var weightIndex = 0; weightIndex < weights.length; ++weightIndex) {\r\n\t\t\tval = weights[weightIndex];\r\n\t\t\tif (isNaN(val)) {\r\n\t\t\t\tthrow new RangeError(\"Chance: All weights must be numbers\");\r\n\t\t\t}\r\n\r\n\t\t\tif (val > 0) {\r\n\t\t\t\tsum += val;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (sum === 0) {\r\n\t\t\tthrow new RangeError(\"Chance: No valid entries in array weights\");\r\n\t\t}\r\n\r\n\t\t// select a value within range\r\n\t\tvar selected = this.random() * sum;\r\n\r\n\t\t// find array entry corresponding to selected value\r\n\t\tvar total = 0;\r\n\t\tvar lastGoodIdx = -1;\r\n\t\tvar chosenIdx;\r\n\t\tfor (weightIndex = 0; weightIndex < weights.length; ++weightIndex) {\r\n\t\t\tval = weights[weightIndex];\r\n\t\t\ttotal += val;\r\n\t\t\tif (val > 0) {\r\n\t\t\t\tif (selected <= total) {\r\n\t\t\t\t\tchosenIdx = weightIndex;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t\tlastGoodIdx = weightIndex;\r\n\t\t\t}\r\n\r\n\t\t\t// handle any possible rounding error comparison to ensure something is picked\r\n\t\t\tif (weightIndex === weights.length - 1) {\r\n\t\t\t\tchosenIdx = lastGoodIdx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tvar chosen = arr[chosenIdx];\r\n\t\ttrim = typeof trim === \"undefined\" ? false : trim;\r\n\t\tif (trim) {\r\n\t\t\tarr.splice(chosenIdx, 1);\r\n\t\t\tweights.splice(chosenIdx, 1);\r\n\t\t}\r\n\r\n\t\treturn chosen;\r\n\t};\r\n\r\n\t// -- End Helpers --\r\n\r\n\t// -- Text --\r\n\r\n\tChance.prototype.paragraph = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tvar sentences = options.sentences || this.natural({ min: 3, max: 7 }),\r\n\t\t\tsentence_array = this.n(this.sentence, sentences);\r\n\r\n\t\treturn sentence_array.join(\" \");\r\n\t};\r\n\r\n\t// Could get smarter about this than generating random words and\r\n\t// chaining them together. Such as: http://vq.io/1a5ceOh\r\n\tChance.prototype.sentence = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tvar words = options.words || this.natural({ min: 12, max: 18 }),\r\n\t\t\tpunctuation = options.punctuation,\r\n\t\t\ttext,\r\n\t\t\tword_array = this.n(this.word, words);\r\n\r\n\t\ttext = word_array.join(\" \");\r\n\r\n\t\t// Capitalize first letter of sentence\r\n\t\ttext = this.capitalize(text);\r\n\r\n\t\t// Make sure punctuation has a usable value\r\n\t\tif (punctuation !== false && !/^[\\.\\?;!:]$/.test(punctuation)) {\r\n\t\t\tpunctuation = \".\";\r\n\t\t}\r\n\r\n\t\t// Add punctuation mark\r\n\t\tif (punctuation) {\r\n\t\t\ttext += punctuation;\r\n\t\t}\r\n\r\n\t\treturn text;\r\n\t};\r\n\r\n\tChance.prototype.syllable = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tvar length = options.length || this.natural({ min: 2, max: 3 }),\r\n\t\t\tconsonants = \"bcdfghjklmnprstvwz\", // consonants except hard to speak ones\r\n\t\t\tvowels = \"aeiou\", // vowels\r\n\t\t\tall = consonants + vowels, // all\r\n\t\t\ttext = \"\",\r\n\t\t\tchr;\r\n\r\n\t\t// I'm sure there's a more elegant way to do this, but this works\r\n\t\t// decently well.\r\n\t\tfor (var i = 0; i < length; i++) {\r\n\t\t\tif (i === 0) {\r\n\t\t\t\t// First character can be anything\r\n\t\t\t\tchr = this.character({ pool: all });\r\n\t\t\t} else if (consonants.indexOf(chr) === -1) {\r\n\t\t\t\t// Last character was a vowel, now we want a consonant\r\n\t\t\t\tchr = this.character({ pool: consonants });\r\n\t\t\t} else {\r\n\t\t\t\t// Last character was a consonant, now we want a vowel\r\n\t\t\t\tchr = this.character({ pool: vowels });\r\n\t\t\t}\r\n\r\n\t\t\ttext += chr;\r\n\t\t}\r\n\r\n\t\tif (options.capitalize) {\r\n\t\t\ttext = this.capitalize(text);\r\n\t\t}\r\n\r\n\t\treturn text;\r\n\t};\r\n\r\n\tChance.prototype.word = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\ttestRange(options.syllables && options.length, \"Chance: Cannot specify both syllables AND length.\");\r\n\r\n\t\tvar syllables = options.syllables || this.natural({ min: 1, max: 3 }),\r\n\t\t\ttext = \"\";\r\n\r\n\t\tif (options.length) {\r\n\t\t\t// Either bound word by length\r\n\t\t\tdo {\r\n\t\t\t\ttext += this.syllable();\r\n\t\t\t} while (text.length < options.length);\r\n\t\t\ttext = text.substring(0, options.length);\r\n\t\t} else {\r\n\t\t\t// Or by number of syllables\r\n\t\t\tfor (var i = 0; i < syllables; i++) {\r\n\t\t\t\ttext += this.syllable();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (options.capitalize) {\r\n\t\t\ttext = this.capitalize(text);\r\n\t\t}\r\n\r\n\t\treturn text;\r\n\t};\r\n\r\n\t// -- End Text --\r\n\r\n\t// -- Person --\r\n\r\n\tChance.prototype.age = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tvar ageRange;\r\n\r\n\t\tswitch (options.type) {\r\n\t\t\tcase \"child\":\r\n\t\t\t\tageRange = { min: 0, max: 12 };\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"teen\":\r\n\t\t\t\tageRange = { min: 13, max: 19 };\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"adult\":\r\n\t\t\t\tageRange = { min: 18, max: 65 };\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"senior\":\r\n\t\t\t\tageRange = { min: 65, max: 100 };\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"all\":\r\n\t\t\t\tageRange = { min: 0, max: 100 };\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tageRange = { min: 18, max: 65 };\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\treturn this.natural(ageRange);\r\n\t};\r\n\r\n\tChance.prototype.birthday = function (options) {\r\n\t\tvar age = this.age(options);\r\n\t\tvar currentYear = new Date().getFullYear();\r\n\r\n\t\tif (options && options.type) {\r\n\t\t\tvar min = new Date();\r\n\t\t\tvar max = new Date();\r\n\t\t\tmin.setFullYear(currentYear - age - 1);\r\n\t\t\tmax.setFullYear(currentYear - age);\r\n\r\n\t\t\toptions = initOptions(options, {\r\n\t\t\t\tmin: min,\r\n\t\t\t\tmax: max,\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\toptions = initOptions(options, {\r\n\t\t\t\tyear: currentYear - age,\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn this.date(options);\r\n\t};\r\n\r\n\t// CPF; ID to identify taxpayers in Brazil\r\n\tChance.prototype.cpf = function (options) {\r\n\t\toptions = initOptions(options, {\r\n\t\t\tformatted: true,\r\n\t\t});\r\n\r\n\t\tvar n = this.n(this.natural, 9, { max: 9 });\r\n\t\tvar d1 = n[8] * 2 + n[7] * 3 + n[6] * 4 + n[5] * 5 + n[4] * 6 + n[3] * 7 + n[2] * 8 + n[1] * 9 + n[0] * 10;\r\n\t\td1 = 11 - (d1 % 11);\r\n\t\tif (d1 >= 10) {\r\n\t\t\td1 = 0;\r\n\t\t}\r\n\t\tvar d2 = d1 * 2 + n[8] * 3 + n[7] * 4 + n[6] * 5 + n[5] * 6 + n[4] * 7 + n[3] * 8 + n[2] * 9 + n[1] * 10 + n[0] * 11;\r\n\t\td2 = 11 - (d2 % 11);\r\n\t\tif (d2 >= 10) {\r\n\t\t\td2 = 0;\r\n\t\t}\r\n\t\tvar cpf = \"\" + n[0] + n[1] + n[2] + \".\" + n[3] + n[4] + n[5] + \".\" + n[6] + n[7] + n[8] + \"-\" + d1 + d2;\r\n\t\treturn options.formatted ? cpf : cpf.replace(/\\D/g, \"\");\r\n\t};\r\n\r\n\t// CNPJ: ID to identify companies in Brazil\r\n\tChance.prototype.cnpj = function (options) {\r\n\t\toptions = initOptions(options, {\r\n\t\t\tformatted: true,\r\n\t\t});\r\n\r\n\t\tvar n = this.n(this.natural, 12, { max: 12 });\r\n\t\tvar d1 = n[11] * 2 + n[10] * 3 + n[9] * 4 + n[8] * 5 + n[7] * 6 + n[6] * 7 + n[5] * 8 + n[4] * 9 + n[3] * 2 + n[2] * 3 + n[1] * 4 + n[0] * 5;\r\n\t\td1 = 11 - (d1 % 11);\r\n\t\tif (d1 < 2) {\r\n\t\t\td1 = 0;\r\n\t\t}\r\n\t\tvar d2 = d1 * 2 + n[11] * 3 + n[10] * 4 + n[9] * 5 + n[8] * 6 + n[7] * 7 + n[6] * 8 + n[5] * 9 + n[4] * 2 + n[3] * 3 + n[2] * 4 + n[1] * 5 + n[0] * 6;\r\n\t\td2 = 11 - (d2 % 11);\r\n\t\tif (d2 < 2) {\r\n\t\t\td2 = 0;\r\n\t\t}\r\n\t\tvar cnpj = \"\" + n[0] + n[1] + \".\" + n[2] + n[3] + n[4] + \".\" + n[5] + n[6] + n[7] + \"/\" + n[8] + n[9] + n[10] + n[11] + \"-\" + d1 + d2;\r\n\t\treturn options.formatted ? cnpj : cnpj.replace(/\\D/g, \"\");\r\n\t};\r\n\r\n\tChance.prototype.first = function (options) {\r\n\t\toptions = initOptions(options, { gender: this.gender(), nationality: \"en\" });\r\n\t\treturn this.pick(this.get(\"firstNames\")[options.gender.toLowerCase()][options.nationality.toLowerCase()]);\r\n\t};\r\n\r\n\tChance.prototype.profession = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tif (options.rank) {\r\n\t\t\treturn this.pick([\"Apprentice \", \"Junior \", \"Senior \", \"Lead \"]) + this.pick(this.get(\"profession\"));\r\n\t\t} else {\r\n\t\t\treturn this.pick(this.get(\"profession\"));\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.company = function () {\r\n\t\treturn this.pick(this.get(\"company\"));\r\n\t};\r\n\r\n\tChance.prototype.gender = function (options) {\r\n\t\toptions = initOptions(options, { extraGenders: [] });\r\n\t\treturn this.pick([\"Male\", \"Female\"].concat(options.extraGenders));\r\n\t};\r\n\r\n\tChance.prototype.last = function (options) {\r\n\t\toptions = initOptions(options, { nationality: \"en\" });\r\n\t\treturn this.pick(this.get(\"lastNames\")[options.nationality.toLowerCase()]);\r\n\t};\r\n\r\n\tChance.prototype.israelId = function () {\r\n\t\tvar x = this.string({ pool: \"**********\", length: 8 });\r\n\t\tvar y = 0;\r\n\t\tfor (var i = 0; i < x.length; i++) {\r\n\t\t\tvar thisDigit = x[i] * (i / 2 === parseInt(i / 2) ? 1 : 2);\r\n\t\t\tthisDigit = this.pad(thisDigit, 2).toString();\r\n\t\t\tthisDigit = parseInt(thisDigit[0]) + parseInt(thisDigit[1]);\r\n\t\t\ty = y + thisDigit;\r\n\t\t}\r\n\t\tx = x + (10 - parseInt(y.toString().slice(-1))).toString().slice(-1);\r\n\t\treturn x;\r\n\t};\r\n\r\n\tChance.prototype.mrz = function (options) {\r\n\t\tvar checkDigit = function (input) {\r\n\t\t\tvar alpha = \"<ABCDEFGHIJKLMNOPQRSTUVWXYXZ\".split(\"\"),\r\n\t\t\t\tmultipliers = [7, 3, 1],\r\n\t\t\t\trunningTotal = 0;\r\n\r\n\t\t\tif (typeof input !== \"string\") {\r\n\t\t\t\tinput = input.toString();\r\n\t\t\t}\r\n\r\n\t\t\tinput.split(\"\").forEach(function (character, idx) {\r\n\t\t\t\tvar pos = alpha.indexOf(character);\r\n\r\n\t\t\t\tif (pos !== -1) {\r\n\t\t\t\t\tcharacter = pos === 0 ? 0 : pos + 9;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tcharacter = parseInt(character, 10);\r\n\t\t\t\t}\r\n\t\t\t\tcharacter *= multipliers[idx % multipliers.length];\r\n\t\t\t\trunningTotal += character;\r\n\t\t\t});\r\n\t\t\treturn runningTotal % 10;\r\n\t\t};\r\n\t\tvar generate = function (opts) {\r\n\t\t\tvar pad = function (length) {\r\n\t\t\t\treturn new Array(length + 1).join(\"<\");\r\n\t\t\t};\r\n\t\t\tvar number = [\"P<\", opts.issuer, opts.last.toUpperCase(), \"<<\", opts.first.toUpperCase(), pad(39 - (opts.last.length + opts.first.length + 2)), opts.passportNumber, checkDigit(opts.passportNumber), opts.nationality, opts.dob, checkDigit(opts.dob), opts.gender, opts.expiry, checkDigit(opts.expiry), pad(14), checkDigit(pad(14))].join(\"\");\r\n\r\n\t\t\treturn number + checkDigit(number.substr(44, 10) + number.substr(57, 7) + number.substr(65, 7));\r\n\t\t};\r\n\r\n\t\tvar that = this;\r\n\r\n\t\toptions = initOptions(options, {\r\n\t\t\tfirst: this.first(),\r\n\t\t\tlast: this.last(),\r\n\t\t\tpassportNumber: this.integer({ min: *********, max: ********* }),\r\n\t\t\tdob: (function () {\r\n\t\t\t\tvar date = that.birthday({ type: \"adult\" });\r\n\t\t\t\treturn [date.getFullYear().toString().substr(2), that.pad(date.getMonth() + 1, 2), that.pad(date.getDate(), 2)].join(\"\");\r\n\t\t\t})(),\r\n\t\t\texpiry: (function () {\r\n\t\t\t\tvar date = new Date();\r\n\t\t\t\treturn [(date.getFullYear() + 5).toString().substr(2), that.pad(date.getMonth() + 1, 2), that.pad(date.getDate(), 2)].join(\"\");\r\n\t\t\t})(),\r\n\t\t\tgender: this.gender() === \"Female\" ? \"F\" : \"M\",\r\n\t\t\tissuer: \"GBR\",\r\n\t\t\tnationality: \"GBR\",\r\n\t\t});\r\n\t\treturn generate(options);\r\n\t};\r\n\r\n\tChance.prototype.name = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tvar first = this.first(options),\r\n\t\t\tlast = this.last(options),\r\n\t\t\tname;\r\n\r\n\t\tif (options.middle) {\r\n\t\t\tname = first + \" \" + this.first(options) + \" \" + last;\r\n\t\t} else if (options.middle_initial) {\r\n\t\t\tname = first + \" \" + this.character({ alpha: true, casing: \"upper\" }) + \". \" + last;\r\n\t\t} else {\r\n\t\t\tname = first + \" \" + last;\r\n\t\t}\r\n\r\n\t\tif (options.prefix) {\r\n\t\t\tname = this.prefix(options) + \" \" + name;\r\n\t\t}\r\n\r\n\t\tif (options.suffix) {\r\n\t\t\tname = name + \" \" + this.suffix(options);\r\n\t\t}\r\n\r\n\t\treturn name;\r\n\t};\r\n\r\n\t// Return the list of available name prefixes based on supplied gender.\r\n\t// @todo introduce internationalization\r\n\tChance.prototype.name_prefixes = function (gender) {\r\n\t\tgender = gender || \"all\";\r\n\t\tgender = gender.toLowerCase();\r\n\r\n\t\tvar prefixes = [{ name: \"Doctor\", abbreviation: \"Dr.\" }];\r\n\r\n\t\tif (gender === \"male\" || gender === \"all\") {\r\n\t\t\tprefixes.push({ name: \"Mister\", abbreviation: \"Mr.\" });\r\n\t\t}\r\n\r\n\t\tif (gender === \"female\" || gender === \"all\") {\r\n\t\t\tprefixes.push({ name: \"Miss\", abbreviation: \"Miss\" });\r\n\t\t\tprefixes.push({ name: \"Misses\", abbreviation: \"Mrs.\" });\r\n\t\t}\r\n\r\n\t\treturn prefixes;\r\n\t};\r\n\r\n\t// Alias for name_prefix\r\n\tChance.prototype.prefix = function (options) {\r\n\t\treturn this.name_prefix(options);\r\n\t};\r\n\r\n\tChance.prototype.name_prefix = function (options) {\r\n\t\toptions = initOptions(options, { gender: \"all\" });\r\n\t\treturn options.full ? this.pick(this.name_prefixes(options.gender)).name : this.pick(this.name_prefixes(options.gender)).abbreviation;\r\n\t};\r\n\t//Hungarian ID number\r\n\tChance.prototype.HIDN = function () {\r\n\t\t//Hungarian ID nuber structure: XXXXXXYY (X=number,Y=Capital Latin letter)\r\n\t\tvar idn_pool = \"**********\";\r\n\t\tvar idn_chrs = \"ABCDEFGHIJKLMNOPQRSTUVWXYXZ\";\r\n\t\tvar idn = \"\";\r\n\t\tidn += this.string({ pool: idn_pool, length: 6 });\r\n\t\tidn += this.string({ pool: idn_chrs, length: 2 });\r\n\t\treturn idn;\r\n\t};\r\n\r\n\tChance.prototype.ssn = function (options) {\r\n\t\toptions = initOptions(options, { ssnFour: false, dashes: true });\r\n\t\tvar ssn_pool = \"**********\",\r\n\t\t\tssn,\r\n\t\t\tdash = options.dashes ? \"-\" : \"\";\r\n\r\n\t\tif (!options.ssnFour) {\r\n\t\t\tssn = this.string({ pool: ssn_pool, length: 3 }) + dash + this.string({ pool: ssn_pool, length: 2 }) + dash + this.string({ pool: ssn_pool, length: 4 });\r\n\t\t} else {\r\n\t\t\tssn = this.string({ pool: ssn_pool, length: 4 });\r\n\t\t}\r\n\t\treturn ssn;\r\n\t};\r\n\r\n\t// Return the list of available name suffixes\r\n\t// @todo introduce internationalization\r\n\tChance.prototype.name_suffixes = function () {\r\n\t\tvar suffixes = [\r\n\t\t\t{ name: \"Doctor of Osteopathic Medicine\", abbreviation: \"D.O.\" },\r\n\t\t\t{ name: \"Doctor of Philosophy\", abbreviation: \"Ph.D.\" },\r\n\t\t\t{ name: \"Esquire\", abbreviation: \"Esq.\" },\r\n\t\t\t{ name: \"Junior\", abbreviation: \"Jr.\" },\r\n\t\t\t{ name: \"Juris Doctor\", abbreviation: \"J.D.\" },\r\n\t\t\t{ name: \"Master of Arts\", abbreviation: \"M.A.\" },\r\n\t\t\t{ name: \"Master of Business Administration\", abbreviation: \"M.B.A.\" },\r\n\t\t\t{ name: \"Master of Science\", abbreviation: \"M.S.\" },\r\n\t\t\t{ name: \"Medical Doctor\", abbreviation: \"M.D.\" },\r\n\t\t\t{ name: \"Senior\", abbreviation: \"Sr.\" },\r\n\t\t\t{ name: \"The Third\", abbreviation: \"III\" },\r\n\t\t\t{ name: \"The Fourth\", abbreviation: \"IV\" },\r\n\t\t\t{ name: \"Bachelor of Engineering\", abbreviation: \"B.E\" },\r\n\t\t\t{ name: \"Bachelor of Technology\", abbreviation: \"B.TECH\" },\r\n\t\t];\r\n\t\treturn suffixes;\r\n\t};\r\n\r\n\t// Alias for name_suffix\r\n\tChance.prototype.suffix = function (options) {\r\n\t\treturn this.name_suffix(options);\r\n\t};\r\n\r\n\tChance.prototype.name_suffix = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\treturn options.full ? this.pick(this.name_suffixes()).name : this.pick(this.name_suffixes()).abbreviation;\r\n\t};\r\n\r\n\tChance.prototype.nationalities = function () {\r\n\t\treturn this.get(\"nationalities\");\r\n\t};\r\n\r\n\t// Generate random nationality based on json list\r\n\tChance.prototype.nationality = function () {\r\n\t\tvar nationality = this.pick(this.nationalities());\r\n\t\treturn nationality.name;\r\n\t};\r\n\r\n\t// -- End Person --\r\n\r\n\t// -- Mobile --\r\n\t// Android GCM Registration ID\r\n\tChance.prototype.android_id = function () {\r\n\t\treturn \"APA91\" + this.string({ pool: \"**********abcefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_\", length: 178 });\r\n\t};\r\n\r\n\t// Apple Push Token\r\n\tChance.prototype.apple_token = function () {\r\n\t\treturn this.string({ pool: \"abcdef**********\", length: 64 });\r\n\t};\r\n\r\n\t// Windows Phone 8 ANID2\r\n\tChance.prototype.wp8_anid2 = function () {\r\n\t\treturn base64(this.hash({ length: 32 }));\r\n\t};\r\n\r\n\t// Windows Phone 7 ANID\r\n\tChance.prototype.wp7_anid = function () {\r\n\t\treturn \"A=\" + this.guid().replace(/-/g, \"\").toUpperCase() + \"&E=\" + this.hash({ length: 3 }) + \"&W=\" + this.integer({ min: 0, max: 9 });\r\n\t};\r\n\r\n\t// BlackBerry Device PIN\r\n\tChance.prototype.bb_pin = function () {\r\n\t\treturn this.hash({ length: 8 });\r\n\t};\r\n\r\n\t// -- End Mobile --\r\n\r\n\t// -- Web --\r\n\tChance.prototype.avatar = function (options) {\r\n\t\tvar url = null;\r\n\t\tvar URL_BASE = \"//www.gravatar.com/avatar/\";\r\n\t\tvar PROTOCOLS = {\r\n\t\t\thttp: \"http\",\r\n\t\t\thttps: \"https\",\r\n\t\t};\r\n\t\tvar FILE_TYPES = {\r\n\t\t\tbmp: \"bmp\",\r\n\t\t\tgif: \"gif\",\r\n\t\t\tjpg: \"jpg\",\r\n\t\t\tpng: \"png\",\r\n\t\t};\r\n\t\tvar FALLBACKS = {\r\n\t\t\t404: \"404\", // Return 404 if not found\r\n\t\t\tmm: \"mm\", // Mystery man\r\n\t\t\tidenticon: \"identicon\", // Geometric pattern based on hash\r\n\t\t\tmonsterid: \"monsterid\", // A generated monster icon\r\n\t\t\twavatar: \"wavatar\", // A generated face\r\n\t\t\tretro: \"retro\", // 8-bit icon\r\n\t\t\tblank: \"blank\", // A transparent png\r\n\t\t};\r\n\t\tvar RATINGS = {\r\n\t\t\tg: \"g\",\r\n\t\t\tpg: \"pg\",\r\n\t\t\tr: \"r\",\r\n\t\t\tx: \"x\",\r\n\t\t};\r\n\t\tvar opts = {\r\n\t\t\tprotocol: null,\r\n\t\t\temail: null,\r\n\t\t\tfileExtension: null,\r\n\t\t\tsize: null,\r\n\t\t\tfallback: null,\r\n\t\t\trating: null,\r\n\t\t};\r\n\r\n\t\tif (!options) {\r\n\t\t\t// Set to a random email\r\n\t\t\topts.email = this.email();\r\n\t\t\toptions = {};\r\n\t\t} else if (typeof options === \"string\") {\r\n\t\t\topts.email = options;\r\n\t\t\toptions = {};\r\n\t\t} else if (typeof options !== \"object\") {\r\n\t\t\treturn null;\r\n\t\t} else if (options.constructor === \"Array\") {\r\n\t\t\treturn null;\r\n\t\t}\r\n\r\n\t\topts = initOptions(options, opts);\r\n\r\n\t\tif (!opts.email) {\r\n\t\t\t// Set to a random email\r\n\t\t\topts.email = this.email();\r\n\t\t}\r\n\r\n\t\t// Safe checking for params\r\n\t\topts.protocol = PROTOCOLS[opts.protocol] ? opts.protocol + \":\" : \"\";\r\n\t\topts.size = parseInt(opts.size, 0) ? opts.size : \"\";\r\n\t\topts.rating = RATINGS[opts.rating] ? opts.rating : \"\";\r\n\t\topts.fallback = FALLBACKS[opts.fallback] ? opts.fallback : \"\";\r\n\t\topts.fileExtension = FILE_TYPES[opts.fileExtension] ? opts.fileExtension : \"\";\r\n\r\n\t\turl = opts.protocol + URL_BASE + this.bimd5.md5(opts.email) + (opts.fileExtension ? \".\" + opts.fileExtension : \"\") + (opts.size || opts.rating || opts.fallback ? \"?\" : \"\") + (opts.size ? \"&s=\" + opts.size.toString() : \"\") + (opts.rating ? \"&r=\" + opts.rating : \"\") + (opts.fallback ? \"&d=\" + opts.fallback : \"\");\r\n\r\n\t\treturn url;\r\n\t};\r\n\r\n\t/**\r\n\t * #Description:\r\n\t * ===============================================\r\n\t * Generate random color value base on color type:\r\n\t * -> hex\r\n\t * -> rgb\r\n\t * -> rgba\r\n\t * -> 0x\r\n\t * -> named color\r\n\t *\r\n\t * #Examples:\r\n\t * ===============================================\r\n\t * * Geerate random hex color\r\n\t * chance.color() => '#79c157' / 'rgb(110,52,164)' / '0x67ae0b' / '#e2e2e2' / '#29CFA7'\r\n\t *\r\n\t * * Generate Hex based color value\r\n\t * chance.color({format: 'hex'})    => '#d67118'\r\n\t *\r\n\t * * Generate simple rgb value\r\n\t * chance.color({format: 'rgb'})    => 'rgb(110,52,164)'\r\n\t *\r\n\t * * Generate Ox based color value\r\n\t * chance.color({format: '0x'})     => '0x67ae0b'\r\n\t *\r\n\t * * Generate graiscale based value\r\n\t * chance.color({grayscale: true})  => '#e2e2e2'\r\n\t *\r\n\t * * Return valide color name\r\n\t * chance.color({format: 'name'})   => 'red'\r\n\t *\r\n\t * * Make color uppercase\r\n\t * chance.color({casing: 'upper'})  => '#29CFA7'\r\n\t *\r\n\t * * Min Max values for RGBA\r\n\t * var light_red = chance.color({format: 'hex', min_red: 200, max_red: 255, max_green: 0, max_blue: 0, min_alpha: .2, max_alpha: .3});\r\n\t *\r\n\t * @param  [object] options\r\n\t * @return [string] color value\r\n\t */\r\n\tChance.prototype.color = function (options) {\r\n\t\tfunction gray(value, delimiter) {\r\n\t\t\treturn [value, value, value].join(delimiter || \"\");\r\n\t\t}\r\n\r\n\t\tfunction rgb(hasAlpha) {\r\n\t\t\tvar rgbValue = hasAlpha ? \"rgba\" : \"rgb\";\r\n\t\t\tvar alphaChannel = hasAlpha ? \",\" + this.floating({ min: min_alpha, max: max_alpha }) : \"\";\r\n\t\t\tvar colorValue = isGrayscale ? gray(this.natural({ min: min_rgb, max: max_rgb }), \",\") : this.natural({ min: min_green, max: max_green }) + \",\" + this.natural({ min: min_blue, max: max_blue }) + \",\" + this.natural({ max: 255 });\r\n\t\t\treturn rgbValue + \"(\" + colorValue + alphaChannel + \")\";\r\n\t\t}\r\n\r\n\t\tfunction hex(start, end, withHash) {\r\n\t\t\tvar symbol = withHash ? \"#\" : \"\";\r\n\t\t\tvar hexstring = \"\";\r\n\r\n\t\t\tif (isGrayscale) {\r\n\t\t\t\thexstring = gray(this.pad(this.hex({ min: min_rgb, max: max_rgb }), 2));\r\n\t\t\t\tif (options.format === \"shorthex\") {\r\n\t\t\t\t\thexstring = gray(this.hex({ min: 0, max: 15 }));\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (options.format === \"shorthex\") {\r\n\t\t\t\t\thexstring = this.pad(this.hex({ min: Math.floor(min_red / 16), max: Math.floor(max_red / 16) }), 1) + this.pad(this.hex({ min: Math.floor(min_green / 16), max: Math.floor(max_green / 16) }), 1) + this.pad(this.hex({ min: Math.floor(min_blue / 16), max: Math.floor(max_blue / 16) }), 1);\r\n\t\t\t\t} else if (min_red !== undefined || max_red !== undefined || min_green !== undefined || max_green !== undefined || min_blue !== undefined || max_blue !== undefined) {\r\n\t\t\t\t\thexstring = this.pad(this.hex({ min: min_red, max: max_red }), 2) + this.pad(this.hex({ min: min_green, max: max_green }), 2) + this.pad(this.hex({ min: min_blue, max: max_blue }), 2);\r\n\t\t\t\t} else {\r\n\t\t\t\t\thexstring = this.pad(this.hex({ min: min_rgb, max: max_rgb }), 2) + this.pad(this.hex({ min: min_rgb, max: max_rgb }), 2) + this.pad(this.hex({ min: min_rgb, max: max_rgb }), 2);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn symbol + hexstring;\r\n\t\t}\r\n\r\n\t\toptions = initOptions(options, {\r\n\t\t\tformat: this.pick([\"hex\", \"shorthex\", \"rgb\", \"rgba\", \"0x\", \"name\"]),\r\n\t\t\tgrayscale: false,\r\n\t\t\tcasing: \"lower\",\r\n\t\t\tmin: 0,\r\n\t\t\tmax: 255,\r\n\t\t\tmin_red: undefined,\r\n\t\t\tmax_red: undefined,\r\n\t\t\tmin_green: undefined,\r\n\t\t\tmax_green: undefined,\r\n\t\t\tmin_blue: undefined,\r\n\t\t\tmax_blue: undefined,\r\n\t\t\tmin_alpha: 0,\r\n\t\t\tmax_alpha: 1,\r\n\t\t});\r\n\r\n\t\tvar isGrayscale = options.grayscale;\r\n\t\tvar min_rgb = options.min;\r\n\t\tvar max_rgb = options.max;\r\n\t\tvar min_red = options.min_red;\r\n\t\tvar max_red = options.max_red;\r\n\t\tvar min_green = options.min_green;\r\n\t\tvar max_green = options.max_green;\r\n\t\tvar min_blue = options.min_blue;\r\n\t\tvar max_blue = options.max_blue;\r\n\t\tvar min_alpha = options.min_alpha;\r\n\t\tvar max_alpha = options.max_alpha;\r\n\t\tif (options.min_red === undefined) {\r\n\t\t\tmin_red = min_rgb;\r\n\t\t}\r\n\t\tif (options.max_red === undefined) {\r\n\t\t\tmax_red = max_rgb;\r\n\t\t}\r\n\t\tif (options.min_green === undefined) {\r\n\t\t\tmin_green = min_rgb;\r\n\t\t}\r\n\t\tif (options.max_green === undefined) {\r\n\t\t\tmax_green = max_rgb;\r\n\t\t}\r\n\t\tif (options.min_blue === undefined) {\r\n\t\t\tmin_blue = min_rgb;\r\n\t\t}\r\n\t\tif (options.max_blue === undefined) {\r\n\t\t\tmax_blue = max_rgb;\r\n\t\t}\r\n\t\tif (options.min_alpha === undefined) {\r\n\t\t\tmin_alpha = 0;\r\n\t\t}\r\n\t\tif (options.max_alpha === undefined) {\r\n\t\t\tmax_alpha = 1;\r\n\t\t}\r\n\t\tif (isGrayscale && min_rgb === 0 && max_rgb === 255 && min_red !== undefined && max_red !== undefined) {\r\n\t\t\tmin_rgb = (min_red + min_green + min_blue) / 3;\r\n\t\t\tmax_rgb = (max_red + max_green + max_blue) / 3;\r\n\t\t}\r\n\t\tvar colorValue;\r\n\r\n\t\tif (options.format === \"hex\") {\r\n\t\t\tcolorValue = hex.call(this, 2, 6, true);\r\n\t\t} else if (options.format === \"shorthex\") {\r\n\t\t\tcolorValue = hex.call(this, 1, 3, true);\r\n\t\t} else if (options.format === \"rgb\") {\r\n\t\t\tcolorValue = rgb.call(this, false);\r\n\t\t} else if (options.format === \"rgba\") {\r\n\t\t\tcolorValue = rgb.call(this, true);\r\n\t\t} else if (options.format === \"0x\") {\r\n\t\t\tcolorValue = \"0x\" + hex.call(this, 2, 6);\r\n\t\t} else if (options.format === \"name\") {\r\n\t\t\treturn this.pick(this.get(\"colorNames\"));\r\n\t\t} else {\r\n\t\t\tthrow new RangeError('Invalid format provided. Please provide one of \"hex\", \"shorthex\", \"rgb\", \"rgba\", \"0x\" or \"name\".');\r\n\t\t}\r\n\r\n\t\tif (options.casing === \"upper\") {\r\n\t\t\tcolorValue = colorValue.toUpperCase();\r\n\t\t}\r\n\r\n\t\treturn colorValue;\r\n\t};\r\n\r\n\tChance.prototype.domain = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\treturn this.word() + \".\" + (options.tld || this.tld());\r\n\t};\r\n\r\n\tChance.prototype.email = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\treturn this.word({ length: options.length }) + \"@\" + (options.domain || this.domain());\r\n\t};\r\n\r\n\t/**\r\n\t * #Description:\r\n\t * ===============================================\r\n\t * Generate a random Facebook id, aka fbid.\r\n\t *\r\n\t * NOTE: At the moment (Sep 2017), Facebook ids are\r\n\t * \"numeric strings\" of length 16.\r\n\t * However, Facebook Graph API documentation states that\r\n\t * \"it is extremely likely to change over time\".\r\n\t * @see https://developers.facebook.com/docs/graph-api/overview/\r\n\t *\r\n\t * #Examples:\r\n\t * ===============================================\r\n\t * chance.fbid() => '****************'\r\n\t *\r\n\t * @return [string] facebook id\r\n\t */\r\n\tChance.prototype.fbid = function () {\r\n\t\treturn \"10000\" + this.string({ pool: \"**********\", length: 11 });\r\n\t};\r\n\r\n\tChance.prototype.google_analytics = function () {\r\n\t\tvar account = this.pad(this.natural({ max: 999999 }), 6);\r\n\t\tvar property = this.pad(this.natural({ max: 99 }), 2);\r\n\r\n\t\treturn \"UA-\" + account + \"-\" + property;\r\n\t};\r\n\r\n\tChance.prototype.hashtag = function () {\r\n\t\treturn \"#\" + this.word();\r\n\t};\r\n\r\n\tChance.prototype.ip = function () {\r\n\t\t// Todo: This could return some reserved IPs. See http://vq.io/137dgYy\r\n\t\t// this should probably be updated to account for that rare as it may be\r\n\t\treturn this.natural({ min: 1, max: 254 }) + \".\" + this.natural({ max: 255 }) + \".\" + this.natural({ max: 255 }) + \".\" + this.natural({ min: 1, max: 254 });\r\n\t};\r\n\r\n\tChance.prototype.ipv6 = function () {\r\n\t\tvar ip_addr = this.n(this.hash, 8, { length: 4 });\r\n\r\n\t\treturn ip_addr.join(\":\");\r\n\t};\r\n\r\n\tChance.prototype.klout = function () {\r\n\t\treturn this.natural({ min: 1, max: 99 });\r\n\t};\r\n\r\n\tChance.prototype.semver = function (options) {\r\n\t\toptions = initOptions(options, { include_prerelease: true });\r\n\r\n\t\tvar range = this.pickone([\"^\", \"~\", \"<\", \">\", \"<=\", \">=\", \"=\"]);\r\n\t\tif (options.range) {\r\n\t\t\trange = options.range;\r\n\t\t}\r\n\r\n\t\tvar prerelease = \"\";\r\n\t\tif (options.include_prerelease) {\r\n\t\t\tprerelease = this.weighted([\"\", \"-dev\", \"-beta\", \"-alpha\"], [50, 10, 5, 1]);\r\n\t\t}\r\n\t\treturn range + this.rpg(\"3d10\").join(\".\") + prerelease;\r\n\t};\r\n\r\n\tChance.prototype.tlds = function () {\r\n\t\treturn [\"com\", \"org\", \"edu\", \"gov\", \"co.uk\", \"net\", \"io\", \"ac\", \"ad\", \"ae\", \"af\", \"ag\", \"ai\", \"al\", \"am\", \"an\", \"ao\", \"aq\", \"ar\", \"as\", \"at\", \"au\", \"aw\", \"ax\", \"az\", \"ba\", \"bb\", \"bd\", \"be\", \"bf\", \"bg\", \"bh\", \"bi\", \"bj\", \"bm\", \"bn\", \"bo\", \"bq\", \"br\", \"bs\", \"bt\", \"bv\", \"bw\", \"by\", \"bz\", \"ca\", \"cc\", \"cd\", \"cf\", \"cg\", \"ch\", \"ci\", \"ck\", \"cl\", \"cm\", \"cn\", \"co\", \"cr\", \"cu\", \"cv\", \"cw\", \"cx\", \"cy\", \"cz\", \"de\", \"dj\", \"dk\", \"dm\", \"do\", \"dz\", \"ec\", \"ee\", \"eg\", \"eh\", \"er\", \"es\", \"et\", \"eu\", \"fi\", \"fj\", \"fk\", \"fm\", \"fo\", \"fr\", \"ga\", \"gb\", \"gd\", \"ge\", \"gf\", \"gg\", \"gh\", \"gi\", \"gl\", \"gm\", \"gn\", \"gp\", \"gq\", \"gr\", \"gs\", \"gt\", \"gu\", \"gw\", \"gy\", \"hk\", \"hm\", \"hn\", \"hr\", \"ht\", \"hu\", \"id\", \"ie\", \"il\", \"im\", \"in\", \"io\", \"iq\", \"ir\", \"is\", \"it\", \"je\", \"jm\", \"jo\", \"jp\", \"ke\", \"kg\", \"kh\", \"ki\", \"km\", \"kn\", \"kp\", \"kr\", \"kw\", \"ky\", \"kz\", \"la\", \"lb\", \"lc\", \"li\", \"lk\", \"lr\", \"ls\", \"lt\", \"lu\", \"lv\", \"ly\", \"ma\", \"mc\", \"md\", \"me\", \"mg\", \"mh\", \"mk\", \"ml\", \"mm\", \"mn\", \"mo\", \"mp\", \"mq\", \"mr\", \"ms\", \"mt\", \"mu\", \"mv\", \"mw\", \"mx\", \"my\", \"mz\", \"na\", \"nc\", \"ne\", \"nf\", \"ng\", \"ni\", \"nl\", \"no\", \"np\", \"nr\", \"nu\", \"nz\", \"om\", \"pa\", \"pe\", \"pf\", \"pg\", \"ph\", \"pk\", \"pl\", \"pm\", \"pn\", \"pr\", \"ps\", \"pt\", \"pw\", \"py\", \"qa\", \"re\", \"ro\", \"rs\", \"ru\", \"rw\", \"sa\", \"sb\", \"sc\", \"sd\", \"se\", \"sg\", \"sh\", \"si\", \"sj\", \"sk\", \"sl\", \"sm\", \"sn\", \"so\", \"sr\", \"ss\", \"st\", \"su\", \"sv\", \"sx\", \"sy\", \"sz\", \"tc\", \"td\", \"tf\", \"tg\", \"th\", \"tj\", \"tk\", \"tl\", \"tm\", \"tn\", \"to\", \"tp\", \"tr\", \"tt\", \"tv\", \"tw\", \"tz\", \"ua\", \"ug\", \"uk\", \"us\", \"uy\", \"uz\", \"va\", \"vc\", \"ve\", \"vg\", \"vi\", \"vn\", \"vu\", \"wf\", \"ws\", \"ye\", \"yt\", \"za\", \"zm\", \"zw\"];\r\n\t};\r\n\r\n\tChance.prototype.tld = function () {\r\n\t\treturn this.pick(this.tlds());\r\n\t};\r\n\r\n\tChance.prototype.twitter = function () {\r\n\t\treturn \"@\" + this.word();\r\n\t};\r\n\r\n\tChance.prototype.url = function (options) {\r\n\t\toptions = initOptions(options, { protocol: \"http\", domain: this.domain(options), domain_prefix: \"\", path: this.word(), extensions: [] });\r\n\r\n\t\tvar extension = options.extensions.length > 0 ? \".\" + this.pick(options.extensions) : \"\";\r\n\t\tvar domain = options.domain_prefix ? options.domain_prefix + \".\" + options.domain : options.domain;\r\n\r\n\t\treturn options.protocol + \"://\" + domain + \"/\" + options.path + extension;\r\n\t};\r\n\r\n\tChance.prototype.port = function () {\r\n\t\treturn this.integer({ min: 0, max: 65535 });\r\n\t};\r\n\r\n\tChance.prototype.locale = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tif (options.region) {\r\n\t\t\treturn this.pick(this.get(\"locale_regions\"));\r\n\t\t} else {\r\n\t\t\treturn this.pick(this.get(\"locale_languages\"));\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.locales = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tif (options.region) {\r\n\t\t\treturn this.get(\"locale_regions\");\r\n\t\t} else {\r\n\t\t\treturn this.get(\"locale_languages\");\r\n\t\t}\r\n\t};\r\n\r\n\t// -- End Web --\r\n\r\n\t// -- Location --\r\n\r\n\tChance.prototype.address = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\treturn this.natural({ min: 5, max: 2000 }) + \" \" + this.street(options);\r\n\t};\r\n\r\n\tChance.prototype.altitude = function (options) {\r\n\t\toptions = initOptions(options, { fixed: 5, min: 0, max: 8848 });\r\n\t\treturn this.floating({\r\n\t\t\tmin: options.min,\r\n\t\t\tmax: options.max,\r\n\t\t\tfixed: options.fixed,\r\n\t\t});\r\n\t};\r\n\r\n\tChance.prototype.areacode = function (options) {\r\n\t\toptions = initOptions(options, { parens: true });\r\n\t\t// Don't want area codes to start with 1, or have a 9 as the second digit\r\n\t\tvar areacode = this.natural({ min: 2, max: 9 }).toString() + this.natural({ min: 0, max: 8 }).toString() + this.natural({ min: 0, max: 9 }).toString();\r\n\r\n\t\treturn options.parens ? \"(\" + areacode + \")\" : areacode;\r\n\t};\r\n\r\n\tChance.prototype.city = function () {\r\n\t\treturn this.capitalize(this.word({ syllables: 3 }));\r\n\t};\r\n\r\n\tChance.prototype.coordinates = function (options) {\r\n\t\treturn this.latitude(options) + \", \" + this.longitude(options);\r\n\t};\r\n\r\n\tChance.prototype.countries = function () {\r\n\t\treturn this.get(\"countries\");\r\n\t};\r\n\r\n\tChance.prototype.country = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tvar country = this.pick(this.countries());\r\n\t\treturn options.full ? country.name : country.abbreviation;\r\n\t};\r\n\r\n\tChance.prototype.depth = function (options) {\r\n\t\toptions = initOptions(options, { fixed: 5, min: -10994, max: 0 });\r\n\t\treturn this.floating({\r\n\t\t\tmin: options.min,\r\n\t\t\tmax: options.max,\r\n\t\t\tfixed: options.fixed,\r\n\t\t});\r\n\t};\r\n\r\n\tChance.prototype.geohash = function (options) {\r\n\t\toptions = initOptions(options, { length: 7 });\r\n\t\treturn this.string({ length: options.length, pool: \"**********bcdefghjkmnpqrstuvwxyz\" });\r\n\t};\r\n\r\n\tChance.prototype.geojson = function (options) {\r\n\t\treturn this.latitude(options) + \", \" + this.longitude(options) + \", \" + this.altitude(options);\r\n\t};\r\n\r\n\tChance.prototype.latitude = function (options) {\r\n\t\toptions = initOptions(options, { fixed: 5, min: -90, max: 90 });\r\n\t\treturn this.floating({ min: options.min, max: options.max, fixed: options.fixed });\r\n\t};\r\n\r\n\tChance.prototype.longitude = function (options) {\r\n\t\toptions = initOptions(options, { fixed: 5, min: -180, max: 180 });\r\n\t\treturn this.floating({ min: options.min, max: options.max, fixed: options.fixed });\r\n\t};\r\n\r\n\tChance.prototype.phone = function (options) {\r\n\t\tvar self = this,\r\n\t\t\tnumPick,\r\n\t\t\tukNum = function (parts) {\r\n\t\t\t\tvar section = [];\r\n\t\t\t\t//fills the section part of the phone number with random numbers.\r\n\t\t\t\tparts.sections.forEach(function (n) {\r\n\t\t\t\t\tsection.push(self.string({ pool: \"**********\", length: n }));\r\n\t\t\t\t});\r\n\t\t\t\treturn parts.area + section.join(\" \");\r\n\t\t\t};\r\n\t\toptions = initOptions(options, {\r\n\t\t\tformatted: true,\r\n\t\t\tcountry: \"us\",\r\n\t\t\tmobile: false,\r\n\t\t});\r\n\t\tif (!options.formatted) {\r\n\t\t\toptions.parens = false;\r\n\t\t}\r\n\t\tvar phone;\r\n\t\tswitch (options.country) {\r\n\t\t\tcase \"fr\":\r\n\t\t\t\tif (!options.mobile) {\r\n\t\t\t\t\tnumPick = this.pick([\r\n\t\t\t\t\t\t// Valid zone and département codes.\r\n\t\t\t\t\t\t\"01\" + this.pick([\"30\", \"34\", \"39\", \"40\", \"41\", \"42\", \"43\", \"44\", \"45\", \"46\", \"47\", \"48\", \"49\", \"53\", \"55\", \"56\", \"58\", \"60\", \"64\", \"69\", \"70\", \"72\", \"73\", \"74\", \"75\", \"76\", \"77\", \"78\", \"79\", \"80\", \"81\", \"82\", \"83\"]) + self.string({ pool: \"**********\", length: 6 }),\r\n\t\t\t\t\t\t\"02\" + this.pick([\"14\", \"18\", \"22\", \"23\", \"28\", \"29\", \"30\", \"31\", \"32\", \"33\", \"34\", \"35\", \"36\", \"37\", \"38\", \"40\", \"41\", \"43\", \"44\", \"45\", \"46\", \"47\", \"48\", \"49\", \"50\", \"51\", \"52\", \"53\", \"54\", \"56\", \"57\", \"61\", \"62\", \"69\", \"72\", \"76\", \"77\", \"78\", \"85\", \"90\", \"96\", \"97\", \"98\", \"99\"]) + self.string({ pool: \"**********\", length: 6 }),\r\n\t\t\t\t\t\t\"03\" + this.pick([\"10\", \"20\", \"21\", \"22\", \"23\", \"24\", \"25\", \"26\", \"27\", \"28\", \"29\", \"39\", \"44\", \"45\", \"51\", \"52\", \"54\", \"55\", \"57\", \"58\", \"59\", \"60\", \"61\", \"62\", \"63\", \"64\", \"65\", \"66\", \"67\", \"68\", \"69\", \"70\", \"71\", \"72\", \"73\", \"80\", \"81\", \"82\", \"83\", \"84\", \"85\", \"86\", \"87\", \"88\", \"89\", \"90\"]) + self.string({ pool: \"**********\", length: 6 }),\r\n\t\t\t\t\t\t\"04\" + this.pick([\"11\", \"13\", \"15\", \"20\", \"22\", \"26\", \"27\", \"30\", \"32\", \"34\", \"37\", \"42\", \"43\", \"44\", \"50\", \"56\", \"57\", \"63\", \"66\", \"67\", \"68\", \"69\", \"70\", \"71\", \"72\", \"73\", \"74\", \"75\", \"76\", \"77\", \"78\", \"79\", \"80\", \"81\", \"82\", \"83\", \"84\", \"85\", \"86\", \"88\", \"89\", \"90\", \"91\", \"92\", \"93\", \"94\", \"95\", \"97\", \"98\"]) + self.string({ pool: \"**********\", length: 6 }),\r\n\t\t\t\t\t\t\"05\" + this.pick([\"08\", \"16\", \"17\", \"19\", \"24\", \"31\", \"32\", \"33\", \"34\", \"35\", \"40\", \"45\", \"46\", \"47\", \"49\", \"53\", \"55\", \"56\", \"57\", \"58\", \"59\", \"61\", \"62\", \"63\", \"64\", \"65\", \"67\", \"79\", \"81\", \"82\", \"86\", \"87\", \"90\", \"94\"]) + self.string({ pool: \"**********\", length: 6 }),\r\n\t\t\t\t\t\t\"09\" + self.string({ pool: \"**********\", length: 8 }),\r\n\t\t\t\t\t]);\r\n\t\t\t\t\tphone = options.formatted ? numPick.match(/../g).join(\" \") : numPick;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnumPick = this.pick([\"06\", \"07\"]) + self.string({ pool: \"**********\", length: 8 });\r\n\t\t\t\t\tphone = options.formatted ? numPick.match(/../g).join(\" \") : numPick;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"uk\":\r\n\t\t\t\tif (!options.mobile) {\r\n\t\t\t\t\tnumPick = this.pick([\r\n\t\t\t\t\t\t//valid area codes of major cities/counties followed by random numbers in required format.\r\n\r\n\t\t\t\t\t\t{ area: \"01\" + this.character({ pool: \"234569\" }) + \"1 \", sections: [3, 4] },\r\n\t\t\t\t\t\t{ area: \"020 \" + this.character({ pool: \"378\" }), sections: [3, 4] },\r\n\t\t\t\t\t\t{ area: \"023 \" + this.character({ pool: \"89\" }), sections: [3, 4] },\r\n\t\t\t\t\t\t{ area: \"024 7\", sections: [3, 4] },\r\n\t\t\t\t\t\t{ area: \"028 \" + this.pick([\"25\", \"28\", \"37\", \"71\", \"82\", \"90\", \"92\", \"95\"]), sections: [2, 4] },\r\n\t\t\t\t\t\t{ area: \"012\" + this.pick([\"04\", \"08\", \"54\", \"76\", \"97\", \"98\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"013\" + this.pick([\"63\", \"64\", \"84\", \"86\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"014\" + this.pick([\"04\", \"20\", \"60\", \"61\", \"80\", \"88\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"015\" + this.pick([\"24\", \"27\", \"62\", \"66\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"016\" + this.pick([\"06\", \"29\", \"35\", \"47\", \"59\", \"95\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"017\" + this.pick([\"26\", \"44\", \"50\", \"68\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"018\" + this.pick([\"27\", \"37\", \"84\", \"97\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t\t{ area: \"019\" + this.pick([\"00\", \"05\", \"35\", \"46\", \"49\", \"63\", \"95\"]) + \" \", sections: [6] },\r\n\t\t\t\t\t]);\r\n\t\t\t\t\tphone = options.formatted ? ukNum(numPick) : ukNum(numPick).replace(\" \", \"\", \"g\");\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnumPick = this.pick([\r\n\t\t\t\t\t\t{ area: \"07\" + this.pick([\"4\", \"5\", \"7\", \"8\", \"9\"]), sections: [2, 6] },\r\n\t\t\t\t\t\t{ area: \"07624 \", sections: [6] },\r\n\t\t\t\t\t]);\r\n\t\t\t\t\tphone = options.formatted ? ukNum(numPick) : ukNum(numPick).replace(\" \", \"\");\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"za\":\r\n\t\t\t\tif (!options.mobile) {\r\n\t\t\t\t\tnumPick = this.pick([\"01\" + this.pick([\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\"]) + self.string({ pool: \"**********\", length: 7 }), \"02\" + this.pick([\"1\", \"2\", \"3\", \"4\", \"7\", \"8\"]) + self.string({ pool: \"**********\", length: 7 }), \"03\" + this.pick([\"1\", \"2\", \"3\", \"5\", \"6\", \"9\"]) + self.string({ pool: \"**********\", length: 7 }), \"04\" + this.pick([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"]) + self.string({ pool: \"**********\", length: 7 }), \"05\" + this.pick([\"1\", \"3\", \"4\", \"6\", \"7\", \"8\"]) + self.string({ pool: \"**********\", length: 7 })]);\r\n\t\t\t\t\tphone = options.formatted || numPick;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tnumPick = this.pick([\"060\" + this.pick([\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"]) + self.string({ pool: \"**********\", length: 6 }), \"061\" + this.pick([\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"8\"]) + self.string({ pool: \"**********\", length: 6 }), \"06\" + self.string({ pool: \"**********\", length: 7 }), \"071\" + this.pick([\"0\", \"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"]) + self.string({ pool: \"**********\", length: 6 }), \"07\" + this.pick([\"2\", \"3\", \"4\", \"6\", \"7\", \"8\", \"9\"]) + self.string({ pool: \"**********\", length: 7 }), \"08\" + this.pick([\"0\", \"1\", \"2\", \"3\", \"4\", \"5\"]) + self.string({ pool: \"**********\", length: 7 })]);\r\n\t\t\t\t\tphone = options.formatted || numPick;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tcase \"us\":\r\n\t\t\t\tvar areacode = this.areacode(options).toString();\r\n\t\t\t\tvar exchange = this.natural({ min: 2, max: 9 }).toString() + this.natural({ min: 0, max: 9 }).toString() + this.natural({ min: 0, max: 9 }).toString();\r\n\t\t\t\tvar subscriber = this.natural({ min: 1000, max: 9999 }).toString(); // this could be random [0-9]{4}\r\n\t\t\t\tphone = options.formatted ? areacode + \" \" + exchange + \"-\" + subscriber : areacode + exchange + subscriber;\r\n\t\t}\r\n\t\treturn phone;\r\n\t};\r\n\r\n\tChance.prototype.postal = function () {\r\n\t\t// Postal District\r\n\t\tvar pd = this.character({ pool: \"XVTSRPNKLMHJGECBA\" });\r\n\t\t// Forward Sortation Area (FSA)\r\n\t\tvar fsa = pd + this.natural({ max: 9 }) + this.character({ alpha: true, casing: \"upper\" });\r\n\t\t// Local Delivery Unut (LDU)\r\n\t\tvar ldu = this.natural({ max: 9 }) + this.character({ alpha: true, casing: \"upper\" }) + this.natural({ max: 9 });\r\n\r\n\t\treturn fsa + \" \" + ldu;\r\n\t};\r\n\r\n\tChance.prototype.counties = function (options) {\r\n\t\toptions = initOptions(options, { country: \"uk\" });\r\n\t\treturn this.get(\"counties\")[options.country.toLowerCase()];\r\n\t};\r\n\r\n\tChance.prototype.county = function (options) {\r\n\t\treturn this.pick(this.counties(options)).name;\r\n\t};\r\n\r\n\tChance.prototype.provinces = function (options) {\r\n\t\toptions = initOptions(options, { country: \"ca\" });\r\n\t\treturn this.get(\"provinces\")[options.country.toLowerCase()];\r\n\t};\r\n\r\n\tChance.prototype.province = function (options) {\r\n\t\treturn options && options.full ? this.pick(this.provinces(options)).name : this.pick(this.provinces(options)).abbreviation;\r\n\t};\r\n\r\n\tChance.prototype.state = function (options) {\r\n\t\treturn options && options.full ? this.pick(this.states(options)).name : this.pick(this.states(options)).abbreviation;\r\n\t};\r\n\r\n\tChance.prototype.states = function (options) {\r\n\t\toptions = initOptions(options, { country: \"us\", us_states_and_dc: true });\r\n\r\n\t\tvar states;\r\n\r\n\t\tswitch (options.country.toLowerCase()) {\r\n\t\t\tcase \"us\":\r\n\t\t\t\tvar us_states_and_dc = this.get(\"us_states_and_dc\"),\r\n\t\t\t\t\tterritories = this.get(\"territories\"),\r\n\t\t\t\t\tarmed_forces = this.get(\"armed_forces\");\r\n\r\n\t\t\t\tstates = [];\r\n\r\n\t\t\t\tif (options.us_states_and_dc) {\r\n\t\t\t\t\tstates = states.concat(us_states_and_dc);\r\n\t\t\t\t}\r\n\t\t\t\tif (options.territories) {\r\n\t\t\t\t\tstates = states.concat(territories);\r\n\t\t\t\t}\r\n\t\t\t\tif (options.armed_forces) {\r\n\t\t\t\t\tstates = states.concat(armed_forces);\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"it\":\r\n\t\t\t\tstates = this.get(\"country_regions\")[options.country.toLowerCase()];\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"uk\":\r\n\t\t\t\tstates = this.get(\"counties\")[options.country.toLowerCase()];\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\treturn states;\r\n\t};\r\n\r\n\tChance.prototype.street = function (options) {\r\n\t\toptions = initOptions(options, { country: \"us\", syllables: 2 });\r\n\t\tvar street;\r\n\r\n\t\tswitch (options.country.toLowerCase()) {\r\n\t\t\tcase \"us\":\r\n\t\t\t\tstreet = this.word({ syllables: options.syllables });\r\n\t\t\t\tstreet = this.capitalize(street);\r\n\t\t\t\tstreet += \" \";\r\n\t\t\t\tstreet += options.short_suffix ? this.street_suffix(options).abbreviation : this.street_suffix(options).name;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"it\":\r\n\t\t\t\tstreet = this.word({ syllables: options.syllables });\r\n\t\t\t\tstreet = this.capitalize(street);\r\n\t\t\t\tstreet = (options.short_suffix ? this.street_suffix(options).abbreviation : this.street_suffix(options).name) + \" \" + street;\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t\treturn street;\r\n\t};\r\n\r\n\tChance.prototype.street_suffix = function (options) {\r\n\t\toptions = initOptions(options, { country: \"us\" });\r\n\t\treturn this.pick(this.street_suffixes(options));\r\n\t};\r\n\r\n\tChance.prototype.street_suffixes = function (options) {\r\n\t\toptions = initOptions(options, { country: \"us\" });\r\n\t\t// These are the most common suffixes.\r\n\t\treturn this.get(\"street_suffixes\")[options.country.toLowerCase()];\r\n\t};\r\n\r\n\t// Note: only returning US zip codes, internationalization will be a whole\r\n\t// other beast to tackle at some point.\r\n\tChance.prototype.zip = function (options) {\r\n\t\tvar zip = this.n(this.natural, 5, { max: 9 });\r\n\r\n\t\tif (options && options.plusfour === true) {\r\n\t\t\tzip.push(\"-\");\r\n\t\t\tzip = zip.concat(this.n(this.natural, 4, { max: 9 }));\r\n\t\t}\r\n\r\n\t\treturn zip.join(\"\");\r\n\t};\r\n\r\n\t// -- End Location --\r\n\r\n\t// -- Time\r\n\r\n\tChance.prototype.ampm = function () {\r\n\t\treturn this.bool() ? \"am\" : \"pm\";\r\n\t};\r\n\r\n\tChance.prototype.date = function (options) {\r\n\t\tvar date_string, date;\r\n\r\n\t\t// If interval is specified we ignore preset\r\n\t\tif (options && (options.min || options.max)) {\r\n\t\t\toptions = initOptions(options, {\r\n\t\t\t\tamerican: true,\r\n\t\t\t\tstring: false,\r\n\t\t\t});\r\n\t\t\tvar min = typeof options.min !== \"undefined\" ? options.min.getTime() : 1;\r\n\t\t\t// 100,000,000 days measured relative to midnight at the beginning of 01 January, 1970 UTC. http://es5.github.io/#x15.9.1.1\r\n\t\t\tvar max = typeof options.max !== \"undefined\" ? options.max.getTime() : 8640000000000000;\r\n\r\n\t\t\tdate = new Date(this.integer({ min: min, max: max }));\r\n\t\t} else {\r\n\t\t\tvar m = this.month({ raw: true });\r\n\t\t\tvar daysInMonth = m.days;\r\n\r\n\t\t\tif (options && options.month) {\r\n\t\t\t\t// Mod 12 to allow months outside range of 0-11 (not encouraged, but also not prevented).\r\n\t\t\t\tdaysInMonth = this.get(\"months\")[((options.month % 12) + 12) % 12].days;\r\n\t\t\t}\r\n\r\n\t\t\toptions = initOptions(options, {\r\n\t\t\t\tyear: parseInt(this.year(), 10),\r\n\t\t\t\t// Necessary to subtract 1 because Date() 0-indexes month but not day or year\r\n\t\t\t\t// for some reason.\r\n\t\t\t\tmonth: m.numeric - 1,\r\n\t\t\t\tday: this.natural({ min: 1, max: daysInMonth }),\r\n\t\t\t\thour: this.hour({ twentyfour: true }),\r\n\t\t\t\tminute: this.minute(),\r\n\t\t\t\tsecond: this.second(),\r\n\t\t\t\tmillisecond: this.millisecond(),\r\n\t\t\t\tamerican: true,\r\n\t\t\t\tstring: false,\r\n\t\t\t});\r\n\r\n\t\t\tdate = new Date(options.year, options.month, options.day, options.hour, options.minute, options.second, options.millisecond);\r\n\t\t}\r\n\r\n\t\tif (options.american) {\r\n\t\t\t// Adding 1 to the month is necessary because Date() 0-indexes\r\n\t\t\t// months but not day for some odd reason.\r\n\t\t\tdate_string = date.getMonth() + 1 + \"/\" + date.getDate() + \"/\" + date.getFullYear();\r\n\t\t} else {\r\n\t\t\tdate_string = date.getDate() + \"/\" + (date.getMonth() + 1) + \"/\" + date.getFullYear();\r\n\t\t}\r\n\r\n\t\treturn options.string ? date_string : date;\r\n\t};\r\n\r\n\tChance.prototype.hammertime = function (options) {\r\n\t\treturn this.date(options).getTime();\r\n\t};\r\n\r\n\tChance.prototype.hour = function (options) {\r\n\t\toptions = initOptions(options, {\r\n\t\t\tmin: options && options.twentyfour ? 0 : 1,\r\n\t\t\tmax: options && options.twentyfour ? 23 : 12,\r\n\t\t});\r\n\r\n\t\ttestRange(options.min < 0, \"Chance: Min cannot be less than 0.\");\r\n\t\ttestRange(options.twentyfour && options.max > 23, \"Chance: Max cannot be greater than 23 for twentyfour option.\");\r\n\t\ttestRange(!options.twentyfour && options.max > 12, \"Chance: Max cannot be greater than 12.\");\r\n\t\ttestRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\r\n\r\n\t\treturn this.natural({ min: options.min, max: options.max });\r\n\t};\r\n\r\n\tChance.prototype.millisecond = function () {\r\n\t\treturn this.natural({ max: 999 });\r\n\t};\r\n\r\n\tChance.prototype.minute = Chance.prototype.second = function (options) {\r\n\t\toptions = initOptions(options, { min: 0, max: 59 });\r\n\r\n\t\ttestRange(options.min < 0, \"Chance: Min cannot be less than 0.\");\r\n\t\ttestRange(options.max > 59, \"Chance: Max cannot be greater than 59.\");\r\n\t\ttestRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\r\n\r\n\t\treturn this.natural({ min: options.min, max: options.max });\r\n\t};\r\n\r\n\tChance.prototype.month = function (options) {\r\n\t\toptions = initOptions(options, { min: 1, max: 12 });\r\n\r\n\t\ttestRange(options.min < 1, \"Chance: Min cannot be less than 1.\");\r\n\t\ttestRange(options.max > 12, \"Chance: Max cannot be greater than 12.\");\r\n\t\ttestRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\r\n\r\n\t\tvar month = this.pick(this.months().slice(options.min - 1, options.max));\r\n\t\treturn options.raw ? month : month.name;\r\n\t};\r\n\r\n\tChance.prototype.months = function () {\r\n\t\treturn this.get(\"months\");\r\n\t};\r\n\r\n\tChance.prototype.second = function () {\r\n\t\treturn this.natural({ max: 59 });\r\n\t};\r\n\r\n\tChance.prototype.timestamp = function () {\r\n\t\treturn this.natural({ min: 1, max: parseInt(new Date().getTime() / 1000, 10) });\r\n\t};\r\n\r\n\tChance.prototype.weekday = function (options) {\r\n\t\toptions = initOptions(options, { weekday_only: false });\r\n\t\tvar weekdays = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"];\r\n\t\tif (!options.weekday_only) {\r\n\t\t\tweekdays.push(\"Saturday\");\r\n\t\t\tweekdays.push(\"Sunday\");\r\n\t\t}\r\n\t\treturn this.pickone(weekdays);\r\n\t};\r\n\r\n\tChance.prototype.year = function (options) {\r\n\t\t// Default to current year as min if none specified\r\n\t\toptions = initOptions(options, { min: new Date().getFullYear() });\r\n\r\n\t\t// Default to one century after current year as max if none specified\r\n\t\toptions.max = typeof options.max !== \"undefined\" ? options.max : options.min + 100;\r\n\r\n\t\treturn this.natural(options).toString();\r\n\t};\r\n\r\n\t// -- End Time\r\n\r\n\t// -- Finance --\r\n\r\n\tChance.prototype.cc = function (options) {\r\n\t\toptions = initOptions(options);\r\n\r\n\t\tvar type, number, to_generate;\r\n\r\n\t\ttype = options.type ? this.cc_type({ name: options.type, raw: true }) : this.cc_type({ raw: true });\r\n\r\n\t\tnumber = type.prefix.split(\"\");\r\n\t\tto_generate = type.length - type.prefix.length - 1;\r\n\r\n\t\t// Generates n - 1 digits\r\n\t\tnumber = number.concat(this.n(this.integer, to_generate, { min: 0, max: 9 }));\r\n\r\n\t\t// Generates the last digit according to Luhn algorithm\r\n\t\tnumber.push(this.luhn_calculate(number.join(\"\")));\r\n\r\n\t\treturn number.join(\"\");\r\n\t};\r\n\r\n\tChance.prototype.cc_types = function () {\r\n\t\t// http://en.wikipedia.org/wiki/Bank_card_number#Issuer_identification_number_.28IIN.29\r\n\t\treturn this.get(\"cc_types\");\r\n\t};\r\n\r\n\tChance.prototype.cc_type = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tvar types = this.cc_types(),\r\n\t\t\ttype = null;\r\n\r\n\t\tif (options.name) {\r\n\t\t\tfor (var i = 0; i < types.length; i++) {\r\n\t\t\t\t// Accept either name or short_name to specify card type\r\n\t\t\t\tif (types[i].name === options.name || types[i].short_name === options.name) {\r\n\t\t\t\t\ttype = types[i];\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (type === null) {\r\n\t\t\t\tthrow new RangeError(\"Chance: Credit card type '\" + options.name + \"' is not supported\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\ttype = this.pick(types);\r\n\t\t}\r\n\r\n\t\treturn options.raw ? type : type.name;\r\n\t};\r\n\r\n\t// return all world currency by ISO 4217\r\n\tChance.prototype.currency_types = function () {\r\n\t\treturn this.get(\"currency_types\");\r\n\t};\r\n\r\n\t// return random world currency by ISO 4217\r\n\tChance.prototype.currency = function () {\r\n\t\treturn this.pick(this.currency_types());\r\n\t};\r\n\r\n\t// return all timezones available\r\n\tChance.prototype.timezones = function () {\r\n\t\treturn this.get(\"timezones\");\r\n\t};\r\n\r\n\t// return random timezone\r\n\tChance.prototype.timezone = function () {\r\n\t\treturn this.pick(this.timezones());\r\n\t};\r\n\r\n\t//Return random correct currency exchange pair (e.g. EUR/USD) or array of currency code\r\n\tChance.prototype.currency_pair = function (returnAsString) {\r\n\t\tvar currencies = this.unique(this.currency, 2, {\r\n\t\t\tcomparator: function (arr, val) {\r\n\t\t\t\treturn arr.reduce(function (acc, item) {\r\n\t\t\t\t\t// If a match has been found, short circuit check and just return\r\n\t\t\t\t\treturn acc || item.code === val.code;\r\n\t\t\t\t}, false);\r\n\t\t\t},\r\n\t\t});\r\n\r\n\t\tif (returnAsString) {\r\n\t\t\treturn currencies[0].code + \"/\" + currencies[1].code;\r\n\t\t} else {\r\n\t\t\treturn currencies;\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.dollar = function (options) {\r\n\t\t// By default, a somewhat more sane max for dollar than all available numbers\r\n\t\toptions = initOptions(options, { max: 10000, min: 0 });\r\n\r\n\t\tvar dollar = this.floating({ min: options.min, max: options.max, fixed: 2 }).toString(),\r\n\t\t\tcents = dollar.split(\".\")[1];\r\n\r\n\t\tif (cents === undefined) {\r\n\t\t\tdollar += \".00\";\r\n\t\t} else if (cents.length < 2) {\r\n\t\t\tdollar = dollar + \"0\";\r\n\t\t}\r\n\r\n\t\tif (dollar < 0) {\r\n\t\t\treturn \"-$\" + dollar.replace(\"-\", \"\");\r\n\t\t} else {\r\n\t\t\treturn \"$\" + dollar;\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.euro = function (options) {\r\n\t\treturn Number(this.dollar(options).replace(\"$\", \"\")).toLocaleString() + \"€\";\r\n\t};\r\n\r\n\tChance.prototype.exp = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tvar exp = {};\r\n\r\n\t\texp.year = this.exp_year();\r\n\r\n\t\t// If the year is this year, need to ensure month is greater than the\r\n\t\t// current month or this expiration will not be valid\r\n\t\tif (exp.year === new Date().getFullYear().toString()) {\r\n\t\t\texp.month = this.exp_month({ future: true });\r\n\t\t} else {\r\n\t\t\texp.month = this.exp_month();\r\n\t\t}\r\n\r\n\t\treturn options.raw ? exp : exp.month + \"/\" + exp.year;\r\n\t};\r\n\r\n\tChance.prototype.exp_month = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\tvar month,\r\n\t\t\tmonth_int,\r\n\t\t\t// Date object months are 0 indexed\r\n\t\t\tcurMonth = new Date().getMonth() + 1;\r\n\r\n\t\tif (options.future && curMonth !== 12) {\r\n\t\t\tdo {\r\n\t\t\t\tmonth = this.month({ raw: true }).numeric;\r\n\t\t\t\tmonth_int = parseInt(month, 10);\r\n\t\t\t} while (month_int <= curMonth);\r\n\t\t} else {\r\n\t\t\tmonth = this.month({ raw: true }).numeric;\r\n\t\t}\r\n\r\n\t\treturn month;\r\n\t};\r\n\r\n\tChance.prototype.exp_year = function () {\r\n\t\tvar curMonth = new Date().getMonth() + 1,\r\n\t\t\tcurYear = new Date().getFullYear();\r\n\r\n\t\treturn this.year({ min: curMonth === 12 ? curYear + 1 : curYear, max: curYear + 10 });\r\n\t};\r\n\r\n\tChance.prototype.vat = function (options) {\r\n\t\toptions = initOptions(options, { country: \"it\" });\r\n\t\tswitch (options.country.toLowerCase()) {\r\n\t\t\tcase \"it\":\r\n\t\t\t\treturn this.it_vat();\r\n\t\t}\r\n\t};\r\n\r\n\t/**\r\n\t * Generate a string matching IBAN pattern (https://en.wikipedia.org/wiki/International_Bank_Account_Number).\r\n\t * No country-specific formats support (yet)\r\n\t */\r\n\tChance.prototype.iban = function () {\r\n\t\tvar alpha = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\";\r\n\t\tvar alphanum = alpha + \"**********\";\r\n\t\tvar iban = this.string({ length: 2, pool: alpha }) + this.pad(this.integer({ min: 0, max: 99 }), 2) + this.string({ length: 4, pool: alphanum }) + this.pad(this.natural(), this.natural({ min: 6, max: 26 }));\r\n\t\treturn iban;\r\n\t};\r\n\r\n\t// -- End Finance\r\n\r\n\t// -- Regional\r\n\r\n\tChance.prototype.it_vat = function () {\r\n\t\tvar it_vat = this.natural({ min: 1, max: 1800000 });\r\n\r\n\t\tit_vat = this.pad(it_vat, 7) + this.pad(this.pick(this.provinces({ country: \"it\" })).code, 3);\r\n\t\treturn it_vat + this.luhn_calculate(it_vat);\r\n\t};\r\n\r\n\t/*\r\n     * this generator is written following the official algorithm\r\n     * all data can be passed explicitely or randomized by calling chance.cf() without options\r\n     * the code does not check that the input data is valid (it goes beyond the scope of the generator)\r\n     *\r\n     * @param  [Object] options = { first: first name,\r\n     *                              last: last name,\r\n     *                              gender: female|male,\r\n                                    birthday: JavaScript date object,\r\n                                    city: string(4), 1 letter + 3 numbers\r\n                                   }\r\n     * @return [string] codice fiscale\r\n     *\r\n    */\r\n\tChance.prototype.cf = function (options) {\r\n\t\toptions = options || {};\r\n\t\tvar gender = !!options.gender ? options.gender : this.gender(),\r\n\t\t\tfirst = !!options.first ? options.first : this.first({ gender: gender, nationality: \"it\" }),\r\n\t\t\tlast = !!options.last ? options.last : this.last({ nationality: \"it\" }),\r\n\t\t\tbirthday = !!options.birthday ? options.birthday : this.birthday(),\r\n\t\t\tcity = !!options.city ? options.city : this.pickone([\"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\", \"H\", \"I\", \"L\", \"M\", \"Z\"]) + this.pad(this.natural({ max: 999 }), 3),\r\n\t\t\tcf = [],\r\n\t\t\tname_generator = function (name, isLast) {\r\n\t\t\t\tvar temp,\r\n\t\t\t\t\treturn_value = [];\r\n\r\n\t\t\t\tif (name.length < 3) {\r\n\t\t\t\t\treturn_value = name.split(\"\").concat(\"XXX\".split(\"\")).splice(0, 3);\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttemp = name\r\n\t\t\t\t\t\t.toUpperCase()\r\n\t\t\t\t\t\t.split(\"\")\r\n\t\t\t\t\t\t.map(function (c) {\r\n\t\t\t\t\t\t\treturn \"BCDFGHJKLMNPRSTVWZ\".indexOf(c) !== -1 ? c : undefined;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.join(\"\");\r\n\t\t\t\t\tif (temp.length > 3) {\r\n\t\t\t\t\t\tif (isLast) {\r\n\t\t\t\t\t\t\ttemp = temp.substr(0, 3);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\ttemp = temp[0] + temp.substr(2, 2);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (temp.length < 3) {\r\n\t\t\t\t\t\treturn_value = temp;\r\n\t\t\t\t\t\ttemp = name\r\n\t\t\t\t\t\t\t.toUpperCase()\r\n\t\t\t\t\t\t\t.split(\"\")\r\n\t\t\t\t\t\t\t.map(function (c) {\r\n\t\t\t\t\t\t\t\treturn \"AEIOU\".indexOf(c) !== -1 ? c : undefined;\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.join(\"\")\r\n\t\t\t\t\t\t\t.substr(0, 3 - return_value.length);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn_value = return_value + temp;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn return_value;\r\n\t\t\t},\r\n\t\t\tdate_generator = function (birthday, gender, that) {\r\n\t\t\t\tvar lettermonths = [\"A\", \"B\", \"C\", \"D\", \"E\", \"H\", \"L\", \"M\", \"P\", \"R\", \"S\", \"T\"];\r\n\r\n\t\t\t\treturn birthday.getFullYear().toString().substr(2) + lettermonths[birthday.getMonth()] + that.pad(birthday.getDate() + (gender.toLowerCase() === \"female\" ? 40 : 0), 2);\r\n\t\t\t},\r\n\t\t\tcheckdigit_generator = function (cf) {\r\n\t\t\t\tvar range1 = \"**********ABCDEFGHIJKLMNOPQRSTUVWXYZ\",\r\n\t\t\t\t\trange2 = \"ABCDEFGHIJABCDEFGHIJKLMNOPQRSTUVWXYZ\",\r\n\t\t\t\t\tevens = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\",\r\n\t\t\t\t\todds = \"BAKPLCQDREVOSFTGUHMINJWZYX\",\r\n\t\t\t\t\tdigit = 0;\r\n\r\n\t\t\t\tfor (var i = 0; i < 15; i++) {\r\n\t\t\t\t\tif (i % 2 !== 0) {\r\n\t\t\t\t\t\tdigit += evens.indexOf(range2[range1.indexOf(cf[i])]);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tdigit += odds.indexOf(range2[range1.indexOf(cf[i])]);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn evens[digit % 26];\r\n\t\t\t};\r\n\r\n\t\tcf = cf.concat(name_generator(last, true), name_generator(first), date_generator(birthday, gender, this), city.toUpperCase().split(\"\")).join(\"\");\r\n\t\tcf += checkdigit_generator(cf.toUpperCase(), this);\r\n\r\n\t\treturn cf.toUpperCase();\r\n\t};\r\n\r\n\tChance.prototype.pl_pesel = function () {\r\n\t\tvar number = this.natural({ min: 1, max: *********9 });\r\n\t\tvar arr = this.pad(number, 10).split(\"\");\r\n\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\tarr[i] = parseInt(arr[i]);\r\n\t\t}\r\n\r\n\t\tvar controlNumber = (1 * arr[0] + 3 * arr[1] + 7 * arr[2] + 9 * arr[3] + 1 * arr[4] + 3 * arr[5] + 7 * arr[6] + 9 * arr[7] + 1 * arr[8] + 3 * arr[9]) % 10;\r\n\t\tif (controlNumber !== 0) {\r\n\t\t\tcontrolNumber = 10 - controlNumber;\r\n\t\t}\r\n\r\n\t\treturn arr.join(\"\") + controlNumber;\r\n\t};\r\n\r\n\tChance.prototype.pl_nip = function () {\r\n\t\tvar number = this.natural({ min: 1, max: ********* });\r\n\t\tvar arr = this.pad(number, 9).split(\"\");\r\n\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\tarr[i] = parseInt(arr[i]);\r\n\t\t}\r\n\r\n\t\tvar controlNumber = (6 * arr[0] + 5 * arr[1] + 7 * arr[2] + 2 * arr[3] + 3 * arr[4] + 4 * arr[5] + 5 * arr[6] + 6 * arr[7] + 7 * arr[8]) % 11;\r\n\t\tif (controlNumber === 10) {\r\n\t\t\treturn this.pl_nip();\r\n\t\t}\r\n\r\n\t\treturn arr.join(\"\") + controlNumber;\r\n\t};\r\n\r\n\tChance.prototype.pl_regon = function () {\r\n\t\tvar number = this.natural({ min: 1, max: 99999999 });\r\n\t\tvar arr = this.pad(number, 8).split(\"\");\r\n\t\tfor (var i = 0; i < arr.length; i++) {\r\n\t\t\tarr[i] = parseInt(arr[i]);\r\n\t\t}\r\n\r\n\t\tvar controlNumber = (8 * arr[0] + 9 * arr[1] + 2 * arr[2] + 3 * arr[3] + 4 * arr[4] + 5 * arr[5] + 6 * arr[6] + 7 * arr[7]) % 11;\r\n\t\tif (controlNumber === 10) {\r\n\t\t\tcontrolNumber = 0;\r\n\t\t}\r\n\r\n\t\treturn arr.join(\"\") + controlNumber;\r\n\t};\r\n\r\n\t// -- End Regional\r\n\r\n\t// -- Music --\r\n\r\n\tChance.prototype.note = function (options) {\r\n\t\t// choices for 'notes' option:\r\n\t\t// flatKey - chromatic scale with flat notes (default)\r\n\t\t// sharpKey - chromatic scale with sharp notes\r\n\t\t// flats - just flat notes\r\n\t\t// sharps - just sharp notes\r\n\t\t// naturals - just natural notes\r\n\t\t// all - naturals, sharps and flats\r\n\t\toptions = initOptions(options, { notes: \"flatKey\" });\r\n\t\tvar scales = {\r\n\t\t\tnaturals: [\"C\", \"D\", \"E\", \"F\", \"G\", \"A\", \"B\"],\r\n\t\t\tflats: [\"D♭\", \"E♭\", \"G♭\", \"A♭\", \"B♭\"],\r\n\t\t\tsharps: [\"C♯\", \"D♯\", \"F♯\", \"G♯\", \"A♯\"],\r\n\t\t};\r\n\t\tscales.all = scales.naturals.concat(scales.flats.concat(scales.sharps));\r\n\t\tscales.flatKey = scales.naturals.concat(scales.flats);\r\n\t\tscales.sharpKey = scales.naturals.concat(scales.sharps);\r\n\t\treturn this.pickone(scales[options.notes]);\r\n\t};\r\n\r\n\tChance.prototype.midi_note = function (options) {\r\n\t\tvar min = 0;\r\n\t\tvar max = 127;\r\n\t\toptions = initOptions(options, { min: min, max: max });\r\n\t\treturn this.integer({ min: options.min, max: options.max });\r\n\t};\r\n\r\n\tChance.prototype.chord_quality = function (options) {\r\n\t\toptions = initOptions(options, { jazz: true });\r\n\t\tvar chord_qualities = [\"maj\", \"min\", \"aug\", \"dim\"];\r\n\t\tif (options.jazz) {\r\n\t\t\tchord_qualities = [\"maj7\", \"min7\", \"7\", \"sus\", \"dim\", \"ø\"];\r\n\t\t}\r\n\t\treturn this.pickone(chord_qualities);\r\n\t};\r\n\r\n\tChance.prototype.chord = function (options) {\r\n\t\toptions = initOptions(options);\r\n\t\treturn this.note(options) + this.chord_quality(options);\r\n\t};\r\n\r\n\tChance.prototype.tempo = function (options) {\r\n\t\tvar min = 40;\r\n\t\tvar max = 320;\r\n\t\toptions = initOptions(options, { min: min, max: max });\r\n\t\treturn this.integer({ min: options.min, max: options.max });\r\n\t};\r\n\r\n\t// -- End Music\r\n\r\n\t// -- Miscellaneous --\r\n\r\n\t// Coin - Flip, flip, flipadelphia\r\n\tChance.prototype.coin = function (options) {\r\n\t\treturn this.bool() ? \"heads\" : \"tails\";\r\n\t};\r\n\r\n\t// Dice - For all the board game geeks out there, myself included ;)\r\n\tfunction diceFn(range) {\r\n\t\treturn function () {\r\n\t\t\treturn this.natural(range);\r\n\t\t};\r\n\t}\r\n\tChance.prototype.d4 = diceFn({ min: 1, max: 4 });\r\n\tChance.prototype.d6 = diceFn({ min: 1, max: 6 });\r\n\tChance.prototype.d8 = diceFn({ min: 1, max: 8 });\r\n\tChance.prototype.d10 = diceFn({ min: 1, max: 10 });\r\n\tChance.prototype.d12 = diceFn({ min: 1, max: 12 });\r\n\tChance.prototype.d20 = diceFn({ min: 1, max: 20 });\r\n\tChance.prototype.d30 = diceFn({ min: 1, max: 30 });\r\n\tChance.prototype.d100 = diceFn({ min: 1, max: 100 });\r\n\r\n\tChance.prototype.rpg = function (thrown, options) {\r\n\t\toptions = initOptions(options);\r\n\t\tif (!thrown) {\r\n\t\t\tthrow new RangeError(\"Chance: A type of die roll must be included\");\r\n\t\t} else {\r\n\t\t\tvar bits = thrown.toLowerCase().split(\"d\"),\r\n\t\t\t\trolls = [];\r\n\r\n\t\t\tif (bits.length !== 2 || !parseInt(bits[0], 10) || !parseInt(bits[1], 10)) {\r\n\t\t\t\tthrow new Error(\"Chance: Invalid format provided. Please provide #d# where the first # is the number of dice to roll, the second # is the max of each die\");\r\n\t\t\t}\r\n\t\t\tfor (var i = bits[0]; i > 0; i--) {\r\n\t\t\t\trolls[i - 1] = this.natural({ min: 1, max: bits[1] });\r\n\t\t\t}\r\n\t\t\treturn typeof options.sum !== \"undefined\" && options.sum\r\n\t\t\t\t? rolls.reduce(function (p, c) {\r\n\t\t\t\t\t\treturn p + c;\r\n\t\t\t\t  })\r\n\t\t\t\t: rolls;\r\n\t\t}\r\n\t};\r\n\r\n\t// Guid\r\n\tChance.prototype.guid = function (options) {\r\n\t\toptions = initOptions(options, { version: 5 });\r\n\r\n\t\tvar guid_pool = \"abcdef**********\",\r\n\t\t\tvariant_pool = \"ab89\",\r\n\t\t\tguid =\r\n\t\t\t\tthis.string({ pool: guid_pool, length: 8 }) +\r\n\t\t\t\t\"-\" +\r\n\t\t\t\tthis.string({ pool: guid_pool, length: 4 }) +\r\n\t\t\t\t\"-\" +\r\n\t\t\t\t// The Version\r\n\t\t\t\toptions.version +\r\n\t\t\t\tthis.string({ pool: guid_pool, length: 3 }) +\r\n\t\t\t\t\"-\" +\r\n\t\t\t\t// The Variant\r\n\t\t\t\tthis.string({ pool: variant_pool, length: 1 }) +\r\n\t\t\t\tthis.string({ pool: guid_pool, length: 3 }) +\r\n\t\t\t\t\"-\" +\r\n\t\t\t\tthis.string({ pool: guid_pool, length: 12 });\r\n\t\treturn guid;\r\n\t};\r\n\r\n\t// Hash\r\n\tChance.prototype.hash = function (options) {\r\n\t\toptions = initOptions(options, { length: 40, casing: \"lower\" });\r\n\t\tvar pool = options.casing === \"upper\" ? HEX_POOL.toUpperCase() : HEX_POOL;\r\n\t\treturn this.string({ pool: pool, length: options.length });\r\n\t};\r\n\r\n\tChance.prototype.luhn_check = function (num) {\r\n\t\tvar str = num.toString();\r\n\t\tvar checkDigit = +str.substring(str.length - 1);\r\n\t\treturn checkDigit === this.luhn_calculate(+str.substring(0, str.length - 1));\r\n\t};\r\n\r\n\tChance.prototype.luhn_calculate = function (num) {\r\n\t\tvar digits = num.toString().split(\"\").reverse();\r\n\t\tvar sum = 0;\r\n\t\tvar digit;\r\n\r\n\t\tfor (var i = 0, l = digits.length; l > i; ++i) {\r\n\t\t\tdigit = +digits[i];\r\n\t\t\tif (i % 2 === 0) {\r\n\t\t\t\tdigit *= 2;\r\n\t\t\t\tif (digit > 9) {\r\n\t\t\t\t\tdigit -= 9;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tsum += digit;\r\n\t\t}\r\n\t\treturn (sum * 9) % 10;\r\n\t};\r\n\r\n\t// MD5 Hash\r\n\tChance.prototype.md5 = function (options) {\r\n\t\tvar opts = { str: \"\", key: null, raw: false };\r\n\r\n\t\tif (!options) {\r\n\t\t\topts.str = this.string();\r\n\t\t\toptions = {};\r\n\t\t} else if (typeof options === \"string\") {\r\n\t\t\topts.str = options;\r\n\t\t\toptions = {};\r\n\t\t} else if (typeof options !== \"object\") {\r\n\t\t\treturn null;\r\n\t\t} else if (options.constructor === \"Array\") {\r\n\t\t\treturn null;\r\n\t\t}\r\n\r\n\t\topts = initOptions(options, opts);\r\n\r\n\t\tif (!opts.str) {\r\n\t\t\tthrow new Error(\"A parameter is required to return an md5 hash.\");\r\n\t\t}\r\n\r\n\t\treturn this.bimd5.md5(opts.str, opts.key, opts.raw);\r\n\t};\r\n\r\n\t/**\r\n\t * #Description:\r\n\t * =====================================================\r\n\t * Generate random file name with extension\r\n\t *\r\n\t * The argument provide extension type\r\n\t * -> raster\r\n\t * -> vector\r\n\t * -> 3d\r\n\t * -> document\r\n\t *\r\n\t * If nothing is provided the function return random file name with random\r\n\t * extension type of any kind\r\n\t *\r\n\t * The user can validate the file name length range\r\n\t * If nothing provided the generated file name is random\r\n\t *\r\n\t * #Extension Pool :\r\n\t * * Currently the supported extensions are\r\n\t *  -> some of the most popular raster image extensions\r\n\t *  -> some of the most popular vector image extensions\r\n\t *  -> some of the most popular 3d image extensions\r\n\t *  -> some of the most popular document extensions\r\n\t *\r\n\t * #Examples :\r\n\t * =====================================================\r\n\t *\r\n\t * Return random file name with random extension. The file extension\r\n\t * is provided by a predefined collection of extensions. More about the extension\r\n\t * pool can be found in #Extension Pool section\r\n\t *\r\n\t * chance.file()\r\n\t * => dsfsdhjf.xml\r\n\t *\r\n\t * In order to generate a file name with specific length, specify the\r\n\t * length property and integer value. The extension is going to be random\r\n\t *\r\n\t * chance.file({length : 10})\r\n\t * => asrtineqos.pdf\r\n\t *\r\n\t * In order to generate file with extension from some of the predefined groups\r\n\t * of the extension pool just specify the extension pool category in fileType property\r\n\t *\r\n\t * chance.file({fileType : 'raster'})\r\n\t * => dshgssds.psd\r\n\t *\r\n\t * You can provide specific extension for your files\r\n\t * chance.file({extension : 'html'})\r\n\t * => djfsd.html\r\n\t *\r\n\t * Or you could pass custom collection of extensions by array or by object\r\n\t * chance.file({extensions : [...]})\r\n\t * => dhgsdsd.psd\r\n\t *\r\n\t * chance.file({extensions : { key : [...], key : [...]}})\r\n\t * => djsfksdjsd.xml\r\n\t *\r\n\t * @param  [collection] options\r\n\t * @return [string]\r\n\t *\r\n\t */\r\n\tChance.prototype.file = function (options) {\r\n\t\tvar fileOptions = options || {};\r\n\t\tvar poolCollectionKey = \"fileExtension\";\r\n\t\tvar typeRange = Object.keys(this.get(\"fileExtension\")); //['raster', 'vector', '3d', 'document'];\r\n\t\tvar fileName;\r\n\t\tvar fileExtension;\r\n\r\n\t\t// Generate random file name\r\n\t\tfileName = this.word({ length: fileOptions.length });\r\n\r\n\t\t// Generate file by specific extension provided by the user\r\n\t\tif (fileOptions.extension) {\r\n\t\t\tfileExtension = fileOptions.extension;\r\n\t\t\treturn fileName + \".\" + fileExtension;\r\n\t\t}\r\n\r\n\t\t// Generate file by specific extension collection\r\n\t\tif (fileOptions.extensions) {\r\n\t\t\tif (Array.isArray(fileOptions.extensions)) {\r\n\t\t\t\tfileExtension = this.pickone(fileOptions.extensions);\r\n\t\t\t\treturn fileName + \".\" + fileExtension;\r\n\t\t\t} else if (fileOptions.extensions.constructor === Object) {\r\n\t\t\t\tvar extensionObjectCollection = fileOptions.extensions;\r\n\t\t\t\tvar keys = Object.keys(extensionObjectCollection);\r\n\r\n\t\t\t\tfileExtension = this.pickone(extensionObjectCollection[this.pickone(keys)]);\r\n\t\t\t\treturn fileName + \".\" + fileExtension;\r\n\t\t\t}\r\n\r\n\t\t\tthrow new Error(\"Chance: Extensions must be an Array or Object\");\r\n\t\t}\r\n\r\n\t\t// Generate file extension based on specific file type\r\n\t\tif (fileOptions.fileType) {\r\n\t\t\tvar fileType = fileOptions.fileType;\r\n\t\t\tif (typeRange.indexOf(fileType) !== -1) {\r\n\t\t\t\tfileExtension = this.pickone(this.get(poolCollectionKey)[fileType]);\r\n\t\t\t\treturn fileName + \".\" + fileExtension;\r\n\t\t\t}\r\n\r\n\t\t\tthrow new RangeError(\"Chance: Expect file type value to be 'raster', 'vector', '3d' or 'document'\");\r\n\t\t}\r\n\r\n\t\t// Generate random file name if no extension options are passed\r\n\t\tfileExtension = this.pickone(this.get(poolCollectionKey)[this.pickone(typeRange)]);\r\n\t\treturn fileName + \".\" + fileExtension;\r\n\t};\r\n\r\n\tvar data = {\r\n\t\tfirstNames: {\r\n\t\t\tmale: {\r\n\t\t\t\ten: [\"James\", \"John\", \"Robert\", \"Michael\", \"William\", \"David\", \"Richard\", \"Joseph\", \"Charles\", \"Thomas\", \"Christopher\", \"Daniel\", \"Matthew\", \"George\", \"Donald\", \"Anthony\", \"Paul\", \"Mark\", \"Edward\", \"Steven\", \"Kenneth\", \"Andrew\", \"Brian\", \"Joshua\", \"Kevin\", \"Ronald\", \"Timothy\", \"Jason\", \"Jeffrey\", \"Frank\", \"Gary\", \"Ryan\", \"Nicholas\", \"Eric\", \"Stephen\", \"Jacob\", \"Larry\", \"Jonathan\", \"Scott\", \"Raymond\", \"Justin\", \"Brandon\", \"Gregory\", \"Samuel\", \"Benjamin\", \"Patrick\", \"Jack\", \"Henry\", \"Walter\", \"Dennis\", \"Jerry\", \"Alexander\", \"Peter\", \"Tyler\", \"Douglas\", \"Harold\", \"Aaron\", \"Jose\", \"Adam\", \"Arthur\", \"Zachary\", \"Carl\", \"Nathan\", \"Albert\", \"Kyle\", \"Lawrence\", \"Joe\", \"Willie\", \"Gerald\", \"Roger\", \"Keith\", \"Jeremy\", \"Terry\", \"Harry\", \"Ralph\", \"Sean\", \"Jesse\", \"Roy\", \"Louis\", \"Billy\", \"Austin\", \"Bruce\", \"Eugene\", \"Christian\", \"Bryan\", \"Wayne\", \"Russell\", \"Howard\", \"Fred\", \"Ethan\", \"Jordan\", \"Philip\", \"Alan\", \"Juan\", \"Randy\", \"Vincent\", \"Bobby\", \"Dylan\", \"Johnny\", \"Phillip\", \"Victor\", \"Clarence\", \"Ernest\", \"Martin\", \"Craig\", \"Stanley\", \"Shawn\", \"Travis\", \"Bradley\", \"Leonard\", \"Earl\", \"Gabriel\", \"Jimmy\", \"Francis\", \"Todd\", \"Noah\", \"Danny\", \"Dale\", \"Cody\", \"Carlos\", \"Allen\", \"Frederick\", \"Logan\", \"Curtis\", \"Alex\", \"Joel\", \"Luis\", \"Norman\", \"Marvin\", \"Glenn\", \"Tony\", \"Nathaniel\", \"Rodney\", \"Melvin\", \"Alfred\", \"Steve\", \"Cameron\", \"Chad\", \"Edwin\", \"Caleb\", \"Evan\", \"Antonio\", \"Lee\", \"Herbert\", \"Jeffery\", \"Isaac\", \"Derek\", \"Ricky\", \"Marcus\", \"Theodore\", \"Elijah\", \"Luke\", \"Jesus\", \"Eddie\", \"Troy\", \"Mike\", \"Dustin\", \"Ray\", \"Adrian\", \"Bernard\", \"Leroy\", \"Angel\", \"Randall\", \"Wesley\", \"Ian\", \"Jared\", \"Mason\", \"Hunter\", \"Calvin\", \"Oscar\", \"Clifford\", \"Jay\", \"Shane\", \"Ronnie\", \"Barry\", \"Lucas\", \"Corey\", \"Manuel\", \"Leo\", \"Tommy\", \"Warren\", \"Jackson\", \"Isaiah\", \"Connor\", \"Don\", \"Dean\", \"Jon\", \"Julian\", \"Miguel\", \"Bill\", \"Lloyd\", \"Charlie\", \"Mitchell\", \"Leon\", \"Jerome\", \"Darrell\", \"Jeremiah\", \"Alvin\", \"Brett\", \"Seth\", \"Floyd\", \"Jim\", \"Blake\", \"Micheal\", \"Gordon\", \"Trevor\", \"Lewis\", \"Erik\", \"Edgar\", \"Vernon\", \"Devin\", \"Gavin\", \"Jayden\", \"Chris\", \"Clyde\", \"Tom\", \"Derrick\", \"Mario\", \"Brent\", \"Marc\", \"Herman\", \"Chase\", \"Dominic\", \"Ricardo\", \"Franklin\", \"Maurice\", \"Max\", \"Aiden\", \"Owen\", \"Lester\", \"Gilbert\", \"Elmer\", \"Gene\", \"Francisco\", \"Glen\", \"Cory\", \"Garrett\", \"Clayton\", \"Sam\", \"Jorge\", \"Chester\", \"Alejandro\", \"Jeff\", \"Harvey\", \"Milton\", \"Cole\", \"Ivan\", \"Andre\", \"Duane\", \"Landon\"],\r\n\t\t\t\t// Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0163\r\n\t\t\t\tit: [\"Adolfo\", \"Alberto\", \"Aldo\", \"Alessandro\", \"Alessio\", \"Alfredo\", \"Alvaro\", \"Andrea\", \"Angelo\", \"Angiolo\", \"Antonino\", \"Antonio\", \"Attilio\", \"Benito\", \"Bernardo\", \"Bruno\", \"Carlo\", \"Cesare\", \"Christian\", \"Claudio\", \"Corrado\", \"Cosimo\", \"Cristian\", \"Cristiano\", \"Daniele\", \"Dario\", \"David\", \"Davide\", \"Diego\", \"Dino\", \"Domenico\", \"Duccio\", \"Edoardo\", \"Elia\", \"Elio\", \"Emanuele\", \"Emiliano\", \"Emilio\", \"Enrico\", \"Enzo\", \"Ettore\", \"Fabio\", \"Fabrizio\", \"Federico\", \"Ferdinando\", \"Fernando\", \"Filippo\", \"Francesco\", \"Franco\", \"Gabriele\", \"Giacomo\", \"Giampaolo\", \"Giampiero\", \"Giancarlo\", \"Gianfranco\", \"Gianluca\", \"Gianmarco\", \"Gianni\", \"Gino\", \"Giorgio\", \"Giovanni\", \"Giuliano\", \"Giulio\", \"Giuseppe\", \"Graziano\", \"Gregorio\", \"Guido\", \"Iacopo\", \"Jacopo\", \"Lapo\", \"Leonardo\", \"Lorenzo\", \"Luca\", \"Luciano\", \"Luigi\", \"Manuel\", \"Marcello\", \"Marco\", \"Marino\", \"Mario\", \"Massimiliano\", \"Massimo\", \"Matteo\", \"Mattia\", \"Maurizio\", \"Mauro\", \"Michele\", \"Mirko\", \"Mohamed\", \"Nello\", \"Neri\", \"Niccolò\", \"Nicola\", \"Osvaldo\", \"Otello\", \"Paolo\", \"Pier Luigi\", \"Piero\", \"Pietro\", \"Raffaele\", \"Remo\", \"Renato\", \"Renzo\", \"Riccardo\", \"Roberto\", \"Rolando\", \"Romano\", \"Salvatore\", \"Samuele\", \"Sandro\", \"Sergio\", \"Silvano\", \"Simone\", \"Stefano\", \"Thomas\", \"Tommaso\", \"Ubaldo\", \"Ugo\", \"Umberto\", \"Valerio\", \"Valter\", \"Vasco\", \"Vincenzo\", \"Vittorio\"],\r\n\t\t\t\t// Data taken from http://www.svbkindernamen.nl/int/nl/kindernamen/index.html\r\n\t\t\t\tnl: [\"Aaron\", \"Abel\", \"Adam\", \"Adriaan\", \"Albert\", \"Alexander\", \"Ali\", \"Arjen\", \"Arno\", \"Bart\", \"Bas\", \"Bastiaan\", \"Benjamin\", \"Bob\", \"Boris\", \"Bram\", \"Brent\", \"Cas\", \"Casper\", \"Chris\", \"Christiaan\", \"Cornelis\", \"Daan\", \"Daley\", \"Damian\", \"Dani\", \"Daniel\", \"Daniël\", \"David\", \"Dean\", \"Dirk\", \"Dylan\", \"Egbert\", \"Elijah\", \"Erik\", \"Erwin\", \"Evert\", \"Ezra\", \"Fabian\", \"Fedde\", \"Finn\", \"Florian\", \"Floris\", \"Frank\", \"Frans\", \"Frederik\", \"Freek\", \"Geert\", \"Gerard\", \"Gerben\", \"Gerrit\", \"Gijs\", \"Guus\", \"Hans\", \"Hendrik\", \"Henk\", \"Herman\", \"Hidde\", \"Hugo\", \"Jaap\", \"Jan Jaap\", \"Jan-Willem\", \"Jack\", \"Jacob\", \"Jan\", \"Jason\", \"Jasper\", \"Jayden\", \"Jelle\", \"Jelte\", \"Jens\", \"Jeroen\", \"Jesse\", \"Jim\", \"Job\", \"Joep\", \"Johannes\", \"John\", \"Jonathan\", \"Joris\", \"Joshua\", \"Joël\", \"Julian\", \"Kees\", \"Kevin\", \"Koen\", \"Lars\", \"Laurens\", \"Leendert\", \"Lennard\", \"Lodewijk\", \"Luc\", \"Luca\", \"Lucas\", \"Lukas\", \"Luuk\", \"Maarten\", \"Marcus\", \"Martijn\", \"Martin\", \"Matthijs\", \"Maurits\", \"Max\", \"Mees\", \"Melle\", \"Mick\", \"Mika\", \"Milan\", \"Mohamed\", \"Mohammed\", \"Morris\", \"Muhammed\", \"Nathan\", \"Nick\", \"Nico\", \"Niek\", \"Niels\", \"Noah\", \"Noud\", \"Olivier\", \"Oscar\", \"Owen\", \"Paul\", \"Pepijn\", \"Peter\", \"Pieter\", \"Pim\", \"Quinten\", \"Reinier\", \"Rens\", \"Robin\", \"Ruben\", \"Sam\", \"Samuel\", \"Sander\", \"Sebastiaan\", \"Sem\", \"Sep\", \"Sepp\", \"Siem\", \"Simon\", \"Stan\", \"Stef\", \"Steven\", \"Stijn\", \"Sven\", \"Teun\", \"Thijmen\", \"Thijs\", \"Thomas\", \"Tijn\", \"Tim\", \"Timo\", \"Tobias\", \"Tom\", \"Victor\", \"Vince\", \"Willem\", \"Wim\", \"Wouter\", \"Yusuf\"],\r\n\t\t\t},\r\n\r\n\t\t\tfemale: {\r\n\t\t\t\ten: [\"Mary\", \"Emma\", \"Elizabeth\", \"Minnie\", \"Margaret\", \"Ida\", \"Alice\", \"Bertha\", \"Sarah\", \"Annie\", \"Clara\", \"Ella\", \"Florence\", \"Cora\", \"Martha\", \"Laura\", \"Nellie\", \"Grace\", \"Carrie\", \"Maude\", \"Mabel\", \"Bessie\", \"Jennie\", \"Gertrude\", \"Julia\", \"Hattie\", \"Edith\", \"Mattie\", \"Rose\", \"Catherine\", \"Lillian\", \"Ada\", \"Lillie\", \"Helen\", \"Jessie\", \"Louise\", \"Ethel\", \"Lula\", \"Myrtle\", \"Eva\", \"Frances\", \"Lena\", \"Lucy\", \"Edna\", \"Maggie\", \"Pearl\", \"Daisy\", \"Fannie\", \"Josephine\", \"Dora\", \"Rosa\", \"Katherine\", \"Agnes\", \"Marie\", \"Nora\", \"May\", \"Mamie\", \"Blanche\", \"Stella\", \"Ellen\", \"Nancy\", \"Effie\", \"Sallie\", \"Nettie\", \"Della\", \"Lizzie\", \"Flora\", \"Susie\", \"Maud\", \"Mae\", \"Etta\", \"Harriet\", \"Sadie\", \"Caroline\", \"Katie\", \"Lydia\", \"Elsie\", \"Kate\", \"Susan\", \"Mollie\", \"Alma\", \"Addie\", \"Georgia\", \"Eliza\", \"Lulu\", \"Nannie\", \"Lottie\", \"Amanda\", \"Belle\", \"Charlotte\", \"Rebecca\", \"Ruth\", \"Viola\", \"Olive\", \"Amelia\", \"Hannah\", \"Jane\", \"Virginia\", \"Emily\", \"Matilda\", \"Irene\", \"Kathryn\", \"Esther\", \"Willie\", \"Henrietta\", \"Ollie\", \"Amy\", \"Rachel\", \"Sara\", \"Estella\", \"Theresa\", \"Augusta\", \"Ora\", \"Pauline\", \"Josie\", \"Lola\", \"Sophia\", \"Leona\", \"Anne\", \"Mildred\", \"Ann\", \"Beulah\", \"Callie\", \"Lou\", \"Delia\", \"Eleanor\", \"Barbara\", \"Iva\", \"Louisa\", \"Maria\", \"Mayme\", \"Evelyn\", \"Estelle\", \"Nina\", \"Betty\", \"Marion\", \"Bettie\", \"Dorothy\", \"Luella\", \"Inez\", \"Lela\", \"Rosie\", \"Allie\", \"Millie\", \"Janie\", \"Cornelia\", \"Victoria\", \"Ruby\", \"Winifred\", \"Alta\", \"Celia\", \"Christine\", \"Beatrice\", \"Birdie\", \"Harriett\", \"Mable\", \"Myra\", \"Sophie\", \"Tillie\", \"Isabel\", \"Sylvia\", \"Carolyn\", \"Isabelle\", \"Leila\", \"Sally\", \"Ina\", \"Essie\", \"Bertie\", \"Nell\", \"Alberta\", \"Katharine\", \"Lora\", \"Rena\", \"Mina\", \"Rhoda\", \"Mathilda\", \"Abbie\", \"Eula\", \"Dollie\", \"Hettie\", \"Eunice\", \"Fanny\", \"Ola\", \"Lenora\", \"Adelaide\", \"Christina\", \"Lelia\", \"Nelle\", \"Sue\", \"Johanna\", \"Lilly\", \"Lucinda\", \"Minerva\", \"Lettie\", \"Roxie\", \"Cynthia\", \"Helena\", \"Hilda\", \"Hulda\", \"Bernice\", \"Genevieve\", \"Jean\", \"Cordelia\", \"Marian\", \"Francis\", \"Jeanette\", \"Adeline\", \"Gussie\", \"Leah\", \"Lois\", \"Lura\", \"Mittie\", \"Hallie\", \"Isabella\", \"Olga\", \"Phoebe\", \"Teresa\", \"Hester\", \"Lida\", \"Lina\", \"Winnie\", \"Claudia\", \"Marguerite\", \"Vera\", \"Cecelia\", \"Bess\", \"Emilie\", \"Rosetta\", \"Verna\", \"Myrtie\", \"Cecilia\", \"Elva\", \"Olivia\", \"Ophelia\", \"Georgie\", \"Elnora\", \"Violet\", \"Adele\", \"Lily\", \"Linnie\", \"Loretta\", \"Madge\", \"Polly\", \"Virgie\", \"Eugenia\", \"Lucile\", \"Lucille\", \"Mabelle\", \"Rosalie\"],\r\n\t\t\t\t// Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0162\r\n\t\t\t\tit: [\"Ada\", \"Adriana\", \"Alessandra\", \"Alessia\", \"Alice\", \"Angela\", \"Anna\", \"Anna Maria\", \"Annalisa\", \"Annita\", \"Annunziata\", \"Antonella\", \"Arianna\", \"Asia\", \"Assunta\", \"Aurora\", \"Barbara\", \"Beatrice\", \"Benedetta\", \"Bianca\", \"Bruna\", \"Camilla\", \"Carla\", \"Carlotta\", \"Carmela\", \"Carolina\", \"Caterina\", \"Catia\", \"Cecilia\", \"Chiara\", \"Cinzia\", \"Clara\", \"Claudia\", \"Costanza\", \"Cristina\", \"Daniela\", \"Debora\", \"Diletta\", \"Dina\", \"Donatella\", \"Elena\", \"Eleonora\", \"Elisa\", \"Elisabetta\", \"Emanuela\", \"Emma\", \"Eva\", \"Federica\", \"Fernanda\", \"Fiorella\", \"Fiorenza\", \"Flora\", \"Franca\", \"Francesca\", \"Gabriella\", \"Gaia\", \"Gemma\", \"Giada\", \"Gianna\", \"Gina\", \"Ginevra\", \"Giorgia\", \"Giovanna\", \"Giulia\", \"Giuliana\", \"Giuseppa\", \"Giuseppina\", \"Grazia\", \"Graziella\", \"Greta\", \"Ida\", \"Ilaria\", \"Ines\", \"Iolanda\", \"Irene\", \"Irma\", \"Isabella\", \"Jessica\", \"Laura\", \"Lea\", \"Letizia\", \"Licia\", \"Lidia\", \"Liliana\", \"Lina\", \"Linda\", \"Lisa\", \"Livia\", \"Loretta\", \"Luana\", \"Lucia\", \"Luciana\", \"Lucrezia\", \"Luisa\", \"Manuela\", \"Mara\", \"Marcella\", \"Margherita\", \"Maria\", \"Maria Cristina\", \"Maria Grazia\", \"Maria Luisa\", \"Maria Pia\", \"Maria Teresa\", \"Marina\", \"Marisa\", \"Marta\", \"Martina\", \"Marzia\", \"Matilde\", \"Melissa\", \"Michela\", \"Milena\", \"Mirella\", \"Monica\", \"Natalina\", \"Nella\", \"Nicoletta\", \"Noemi\", \"Olga\", \"Paola\", \"Patrizia\", \"Piera\", \"Pierina\", \"Raffaella\", \"Rebecca\", \"Renata\", \"Rina\", \"Rita\", \"Roberta\", \"Rosa\", \"Rosanna\", \"Rossana\", \"Rossella\", \"Sabrina\", \"Sandra\", \"Sara\", \"Serena\", \"Silvana\", \"Silvia\", \"Simona\", \"Simonetta\", \"Sofia\", \"Sonia\", \"Stefania\", \"Susanna\", \"Teresa\", \"Tina\", \"Tiziana\", \"Tosca\", \"Valentina\", \"Valeria\", \"Vanda\", \"Vanessa\", \"Vanna\", \"Vera\", \"Veronica\", \"Vilma\", \"Viola\", \"Virginia\", \"Vittoria\"],\r\n\t\t\t\t// Data taken from http://www.svbkindernamen.nl/int/nl/kindernamen/index.html\r\n\t\t\t\tnl: [\"Ada\", \"Arianne\", \"Afke\", \"Amanda\", \"Amber\", \"Amy\", \"Aniek\", \"Anita\", \"Anja\", \"Anna\", \"Anne\", \"Annelies\", \"Annemarie\", \"Annette\", \"Anouk\", \"Astrid\", \"Aukje\", \"Barbara\", \"Bianca\", \"Carla\", \"Carlijn\", \"Carolien\", \"Chantal\", \"Charlotte\", \"Claudia\", \"Daniëlle\", \"Debora\", \"Diane\", \"Dora\", \"Eline\", \"Elise\", \"Ella\", \"Ellen\", \"Emma\", \"Esmee\", \"Evelien\", \"Esther\", \"Erica\", \"Eva\", \"Femke\", \"Fleur\", \"Floor\", \"Froukje\", \"Gea\", \"Gerda\", \"Hanna\", \"Hanneke\", \"Heleen\", \"Hilde\", \"Ilona\", \"Ina\", \"Inge\", \"Ingrid\", \"Iris\", \"Isabel\", \"Isabelle\", \"Janneke\", \"Jasmijn\", \"Jeanine\", \"Jennifer\", \"Jessica\", \"Johanna\", \"Joke\", \"Julia\", \"Julie\", \"Karen\", \"Karin\", \"Katja\", \"Kim\", \"Lara\", \"Laura\", \"Lena\", \"Lianne\", \"Lieke\", \"Lilian\", \"Linda\", \"Lisa\", \"Lisanne\", \"Lotte\", \"Louise\", \"Maaike\", \"Manon\", \"Marga\", \"Maria\", \"Marissa\", \"Marit\", \"Marjolein\", \"Martine\", \"Marleen\", \"Melissa\", \"Merel\", \"Miranda\", \"Michelle\", \"Mirjam\", \"Mirthe\", \"Naomi\", \"Natalie\", \"Nienke\", \"Nina\", \"Noortje\", \"Olivia\", \"Patricia\", \"Paula\", \"Paulien\", \"Ramona\", \"Ria\", \"Rianne\", \"Roos\", \"Rosanne\", \"Ruth\", \"Sabrina\", \"Sandra\", \"Sanne\", \"Sara\", \"Saskia\", \"Silvia\", \"Sofia\", \"Sophie\", \"Sonja\", \"Suzanne\", \"Tamara\", \"Tess\", \"Tessa\", \"Tineke\", \"Valerie\", \"Vanessa\", \"Veerle\", \"Vera\", \"Victoria\", \"Wendy\", \"Willeke\", \"Yvonne\", \"Zoë\"],\r\n\t\t\t},\r\n\t\t},\r\n\r\n\t\tlastNames: {\r\n\t\t\ten: [\"Smith\", \"Johnson\", \"Williams\", \"Jones\", \"Brown\", \"Davis\", \"Miller\", \"Wilson\", \"Moore\", \"Taylor\", \"Anderson\", \"Thomas\", \"Jackson\", \"White\", \"Harris\", \"Martin\", \"Thompson\", \"Garcia\", \"Martinez\", \"Robinson\", \"Clark\", \"Rodriguez\", \"Lewis\", \"Lee\", \"Walker\", \"Hall\", \"Allen\", \"Young\", \"Hernandez\", \"King\", \"Wright\", \"Lopez\", \"Hill\", \"Scott\", \"Green\", \"Adams\", \"Baker\", \"Gonzalez\", \"Nelson\", \"Carter\", \"Mitchell\", \"Perez\", \"Roberts\", \"Turner\", \"Phillips\", \"Campbell\", \"Parker\", \"Evans\", \"Edwards\", \"Collins\", \"Stewart\", \"Sanchez\", \"Morris\", \"Rogers\", \"Reed\", \"Cook\", \"Morgan\", \"Bell\", \"Murphy\", \"Bailey\", \"Rivera\", \"Cooper\", \"Richardson\", \"Cox\", \"Howard\", \"Ward\", \"Torres\", \"Peterson\", \"Gray\", \"Ramirez\", \"James\", \"Watson\", \"Brooks\", \"Kelly\", \"Sanders\", \"Price\", \"Bennett\", \"Wood\", \"Barnes\", \"Ross\", \"Henderson\", \"Coleman\", \"Jenkins\", \"Perry\", \"Powell\", \"Long\", \"Patterson\", \"Hughes\", \"Flores\", \"Washington\", \"Butler\", \"Simmons\", \"Foster\", \"Gonzales\", \"Bryant\", \"Alexander\", \"Russell\", \"Griffin\", \"Diaz\", \"Hayes\", \"Myers\", \"Ford\", \"Hamilton\", \"Graham\", \"Sullivan\", \"Wallace\", \"Woods\", \"Cole\", \"West\", \"Jordan\", \"Owens\", \"Reynolds\", \"Fisher\", \"Ellis\", \"Harrison\", \"Gibson\", \"McDonald\", \"Cruz\", \"Marshall\", \"Ortiz\", \"Gomez\", \"Murray\", \"Freeman\", \"Wells\", \"Webb\", \"Simpson\", \"Stevens\", \"Tucker\", \"Porter\", \"Hunter\", \"Hicks\", \"Crawford\", \"Henry\", \"Boyd\", \"Mason\", \"Morales\", \"Kennedy\", \"Warren\", \"Dixon\", \"Ramos\", \"Reyes\", \"Burns\", \"Gordon\", \"Shaw\", \"Holmes\", \"Rice\", \"Robertson\", \"Hunt\", \"Black\", \"Daniels\", \"Palmer\", \"Mills\", \"Nichols\", \"Grant\", \"Knight\", \"Ferguson\", \"Rose\", \"Stone\", \"Hawkins\", \"Dunn\", \"Perkins\", \"Hudson\", \"Spencer\", \"Gardner\", \"Stephens\", \"Payne\", \"Pierce\", \"Berry\", \"Matthews\", \"Arnold\", \"Wagner\", \"Willis\", \"Ray\", \"Watkins\", \"Olson\", \"Carroll\", \"Duncan\", \"Snyder\", \"Hart\", \"Cunningham\", \"Bradley\", \"Lane\", \"Andrews\", \"Ruiz\", \"Harper\", \"Fox\", \"Riley\", \"Armstrong\", \"Carpenter\", \"Weaver\", \"Greene\", \"Lawrence\", \"Elliott\", \"Chavez\", \"Sims\", \"Austin\", \"Peters\", \"Kelley\", \"Franklin\", \"Lawson\", \"Fields\", \"Gutierrez\", \"Ryan\", \"Schmidt\", \"Carr\", \"Vasquez\", \"Castillo\", \"Wheeler\", \"Chapman\", \"Oliver\", \"Montgomery\", \"Richards\", \"Williamson\", \"Johnston\", \"Banks\", \"Meyer\", \"Bishop\", \"McCoy\", \"Howell\", \"Alvarez\", \"Morrison\", \"Hansen\", \"Fernandez\", \"Garza\", \"Harvey\", \"Little\", \"Burton\", \"Stanley\", \"Nguyen\", \"George\", \"Jacobs\", \"Reid\", \"Kim\", \"Fuller\", \"Lynch\", \"Dean\", \"Gilbert\", \"Garrett\", \"Romero\", \"Welch\", \"Larson\", \"Frazier\", \"Burke\", \"Hanson\", \"Day\", \"Mendoza\", \"Moreno\", \"Bowman\", \"Medina\", \"Fowler\", \"Brewer\", \"Hoffman\", \"Carlson\", \"Silva\", \"Pearson\", \"Holland\", \"Douglas\", \"Fleming\", \"Jensen\", \"Vargas\", \"Byrd\", \"Davidson\", \"Hopkins\", \"May\", \"Terry\", \"Herrera\", \"Wade\", \"Soto\", \"Walters\", \"Curtis\", \"Neal\", \"Caldwell\", \"Lowe\", \"Jennings\", \"Barnett\", \"Graves\", \"Jimenez\", \"Horton\", \"Shelton\", \"Barrett\", \"Obrien\", \"Castro\", \"Sutton\", \"Gregory\", \"McKinney\", \"Lucas\", \"Miles\", \"Craig\", \"Rodriquez\", \"Chambers\", \"Holt\", \"Lambert\", \"Fletcher\", \"Watts\", \"Bates\", \"Hale\", \"Rhodes\", \"Pena\", \"Beck\", \"Newman\", \"Haynes\", \"McDaniel\", \"Mendez\", \"Bush\", \"Vaughn\", \"Parks\", \"Dawson\", \"Santiago\", \"Norris\", \"Hardy\", \"Love\", \"Steele\", \"Curry\", \"Powers\", \"Schultz\", \"Barker\", \"Guzman\", \"Page\", \"Munoz\", \"Ball\", \"Keller\", \"Chandler\", \"Weber\", \"Leonard\", \"Walsh\", \"Lyons\", \"Ramsey\", \"Wolfe\", \"Schneider\", \"Mullins\", \"Benson\", \"Sharp\", \"Bowen\", \"Daniel\", \"Barber\", \"Cummings\", \"Hines\", \"Baldwin\", \"Griffith\", \"Valdez\", \"Hubbard\", \"Salazar\", \"Reeves\", \"Warner\", \"Stevenson\", \"Burgess\", \"Santos\", \"Tate\", \"Cross\", \"Garner\", \"Mann\", \"Mack\", \"Moss\", \"Thornton\", \"Dennis\", \"McGee\", \"Farmer\", \"Delgado\", \"Aguilar\", \"Vega\", \"Glover\", \"Manning\", \"Cohen\", \"Harmon\", \"Rodgers\", \"Robbins\", \"Newton\", \"Todd\", \"Blair\", \"Higgins\", \"Ingram\", \"Reese\", \"Cannon\", \"Strickland\", \"Townsend\", \"Potter\", \"Goodwin\", \"Walton\", \"Rowe\", \"Hampton\", \"Ortega\", \"Patton\", \"Swanson\", \"Joseph\", \"Francis\", \"Goodman\", \"Maldonado\", \"Yates\", \"Becker\", \"Erickson\", \"Hodges\", \"Rios\", \"Conner\", \"Adkins\", \"Webster\", \"Norman\", \"Malone\", \"Hammond\", \"Flowers\", \"Cobb\", \"Moody\", \"Quinn\", \"Blake\", \"Maxwell\", \"Pope\", \"Floyd\", \"Osborne\", \"Paul\", \"McCarthy\", \"Guerrero\", \"Lindsey\", \"Estrada\", \"Sandoval\", \"Gibbs\", \"Tyler\", \"Gross\", \"Fitzgerald\", \"Stokes\", \"Doyle\", \"Sherman\", \"Saunders\", \"Wise\", \"Colon\", \"Gill\", \"Alvarado\", \"Greer\", \"Padilla\", \"Simon\", \"Waters\", \"Nunez\", \"Ballard\", \"Schwartz\", \"McBride\", \"Houston\", \"Christensen\", \"Klein\", \"Pratt\", \"Briggs\", \"Parsons\", \"McLaughlin\", \"Zimmerman\", \"French\", \"Buchanan\", \"Moran\", \"Copeland\", \"Roy\", \"Pittman\", \"Brady\", \"McCormick\", \"Holloway\", \"Brock\", \"Poole\", \"Frank\", \"Logan\", \"Owen\", \"Bass\", \"Marsh\", \"Drake\", \"Wong\", \"Jefferson\", \"Park\", \"Morton\", \"Abbott\", \"Sparks\", \"Patrick\", \"Norton\", \"Huff\", \"Clayton\", \"Massey\", \"Lloyd\", \"Figueroa\", \"Carson\", \"Bowers\", \"Roberson\", \"Barton\", \"Tran\", \"Lamb\", \"Harrington\", \"Casey\", \"Boone\", \"Cortez\", \"Clarke\", \"Mathis\", \"Singleton\", \"Wilkins\", \"Cain\", \"Bryan\", \"Underwood\", \"Hogan\", \"McKenzie\", \"Collier\", \"Luna\", \"Phelps\", \"McGuire\", \"Allison\", \"Bridges\", \"Wilkerson\", \"Nash\", \"Summers\", \"Atkins\"],\r\n\t\t\t// Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0164 (first 1000)\r\n\t\t\tit: [\"Acciai\", \"Aglietti\", \"Agostini\", \"Agresti\", \"Ahmed\", \"Aiazzi\", \"Albanese\", \"Alberti\", \"Alessi\", \"Alfani\", \"Alinari\", \"Alterini\", \"Amato\", \"Ammannati\", \"Ancillotti\", \"Andrei\", \"Andreini\", \"Andreoni\", \"Angeli\", \"Anichini\", \"Antonelli\", \"Antonini\", \"Arena\", \"Ariani\", \"Arnetoli\", \"Arrighi\", \"Baccani\", \"Baccetti\", \"Bacci\", \"Bacherini\", \"Badii\", \"Baggiani\", \"Baglioni\", \"Bagni\", \"Bagnoli\", \"Baldassini\", \"Baldi\", \"Baldini\", \"Ballerini\", \"Balli\", \"Ballini\", \"Balloni\", \"Bambi\", \"Banchi\", \"Bandinelli\", \"Bandini\", \"Bani\", \"Barbetti\", \"Barbieri\", \"Barchielli\", \"Bardazzi\", \"Bardelli\", \"Bardi\", \"Barducci\", \"Bargellini\", \"Bargiacchi\", \"Barni\", \"Baroncelli\", \"Baroncini\", \"Barone\", \"Baroni\", \"Baronti\", \"Bartalesi\", \"Bartoletti\", \"Bartoli\", \"Bartolini\", \"Bartoloni\", \"Bartolozzi\", \"Basagni\", \"Basile\", \"Bassi\", \"Batacchi\", \"Battaglia\", \"Battaglini\", \"Bausi\", \"Becagli\", \"Becattini\", \"Becchi\", \"Becucci\", \"Bellandi\", \"Bellesi\", \"Belli\", \"Bellini\", \"Bellucci\", \"Bencini\", \"Benedetti\", \"Benelli\", \"Beni\", \"Benini\", \"Bensi\", \"Benucci\", \"Benvenuti\", \"Berlincioni\", \"Bernacchioni\", \"Bernardi\", \"Bernardini\", \"Berni\", \"Bernini\", \"Bertelli\", \"Berti\", \"Bertini\", \"Bessi\", \"Betti\", \"Bettini\", \"Biagi\", \"Biagini\", \"Biagioni\", \"Biagiotti\", \"Biancalani\", \"Bianchi\", \"Bianchini\", \"Bianco\", \"Biffoli\", \"Bigazzi\", \"Bigi\", \"Biliotti\", \"Billi\", \"Binazzi\", \"Bindi\", \"Bini\", \"Biondi\", \"Bizzarri\", \"Bocci\", \"Bogani\", \"Bolognesi\", \"Bonaiuti\", \"Bonanni\", \"Bonciani\", \"Boncinelli\", \"Bondi\", \"Bonechi\", \"Bongini\", \"Boni\", \"Bonini\", \"Borchi\", \"Boretti\", \"Borghi\", \"Borghini\", \"Borgioli\", \"Borri\", \"Borselli\", \"Boschi\", \"Bottai\", \"Bracci\", \"Braccini\", \"Brandi\", \"Braschi\", \"Bravi\", \"Brazzini\", \"Breschi\", \"Brilli\", \"Brizzi\", \"Brogelli\", \"Brogi\", \"Brogioni\", \"Brunelli\", \"Brunetti\", \"Bruni\", \"Bruno\", \"Brunori\", \"Bruschi\", \"Bucci\", \"Bucciarelli\", \"Buccioni\", \"Bucelli\", \"Bulli\", \"Burberi\", \"Burchi\", \"Burgassi\", \"Burroni\", \"Bussotti\", \"Buti\", \"Caciolli\", \"Caiani\", \"Calabrese\", \"Calamai\", \"Calamandrei\", \"Caldini\", \"Calo'\", \"Calonaci\", \"Calosi\", \"Calvelli\", \"Cambi\", \"Camiciottoli\", \"Cammelli\", \"Cammilli\", \"Campolmi\", \"Cantini\", \"Capanni\", \"Capecchi\", \"Caponi\", \"Cappelletti\", \"Cappelli\", \"Cappellini\", \"Cappugi\", \"Capretti\", \"Caputo\", \"Carbone\", \"Carboni\", \"Cardini\", \"Carlesi\", \"Carletti\", \"Carli\", \"Caroti\", \"Carotti\", \"Carrai\", \"Carraresi\", \"Carta\", \"Caruso\", \"Casalini\", \"Casati\", \"Caselli\", \"Casini\", \"Castagnoli\", \"Castellani\", \"Castelli\", \"Castellucci\", \"Catalano\", \"Catarzi\", \"Catelani\", \"Cavaciocchi\", \"Cavallaro\", \"Cavallini\", \"Cavicchi\", \"Cavini\", \"Ceccarelli\", \"Ceccatelli\", \"Ceccherelli\", \"Ceccherini\", \"Cecchi\", \"Cecchini\", \"Cecconi\", \"Cei\", \"Cellai\", \"Celli\", \"Cellini\", \"Cencetti\", \"Ceni\", \"Cenni\", \"Cerbai\", \"Cesari\", \"Ceseri\", \"Checcacci\", \"Checchi\", \"Checcucci\", \"Cheli\", \"Chellini\", \"Chen\", \"Cheng\", \"Cherici\", \"Cherubini\", \"Chiaramonti\", \"Chiarantini\", \"Chiarelli\", \"Chiari\", \"Chiarini\", \"Chiarugi\", \"Chiavacci\", \"Chiesi\", \"Chimenti\", \"Chini\", \"Chirici\", \"Chiti\", \"Ciabatti\", \"Ciampi\", \"Cianchi\", \"Cianfanelli\", \"Cianferoni\", \"Ciani\", \"Ciapetti\", \"Ciappi\", \"Ciardi\", \"Ciatti\", \"Cicali\", \"Ciccone\", \"Cinelli\", \"Cini\", \"Ciobanu\", \"Ciolli\", \"Cioni\", \"Cipriani\", \"Cirillo\", \"Cirri\", \"Ciucchi\", \"Ciuffi\", \"Ciulli\", \"Ciullini\", \"Clemente\", \"Cocchi\", \"Cognome\", \"Coli\", \"Collini\", \"Colombo\", \"Colzi\", \"Comparini\", \"Conforti\", \"Consigli\", \"Conte\", \"Conti\", \"Contini\", \"Coppini\", \"Coppola\", \"Corsi\", \"Corsini\", \"Corti\", \"Cortini\", \"Cosi\", \"Costa\", \"Costantini\", \"Costantino\", \"Cozzi\", \"Cresci\", \"Crescioli\", \"Cresti\", \"Crini\", \"Curradi\", \"D'Agostino\", \"D'Alessandro\", \"D'Amico\", \"D'Angelo\", \"Daddi\", \"Dainelli\", \"Dallai\", \"Danti\", \"Davitti\", \"De Angelis\", \"De Luca\", \"De Marco\", \"De Rosa\", \"De Santis\", \"De Simone\", \"De Vita\", \"Degl'Innocenti\", \"Degli Innocenti\", \"Dei\", \"Del Lungo\", \"Del Re\", \"Di Marco\", \"Di Stefano\", \"Dini\", \"Diop\", \"Dobre\", \"Dolfi\", \"Donati\", \"Dondoli\", \"Dong\", \"Donnini\", \"Ducci\", \"Dumitru\", \"Ermini\", \"Esposito\", \"Evangelisti\", \"Fabbri\", \"Fabbrini\", \"Fabbrizzi\", \"Fabbroni\", \"Fabbrucci\", \"Fabiani\", \"Facchini\", \"Faggi\", \"Fagioli\", \"Failli\", \"Faini\", \"Falciani\", \"Falcini\", \"Falcone\", \"Fallani\", \"Falorni\", \"Falsini\", \"Falugiani\", \"Fancelli\", \"Fanelli\", \"Fanetti\", \"Fanfani\", \"Fani\", \"Fantappie'\", \"Fantechi\", \"Fanti\", \"Fantini\", \"Fantoni\", \"Farina\", \"Fattori\", \"Favilli\", \"Fedi\", \"Fei\", \"Ferrante\", \"Ferrara\", \"Ferrari\", \"Ferraro\", \"Ferretti\", \"Ferri\", \"Ferrini\", \"Ferroni\", \"Fiaschi\", \"Fibbi\", \"Fiesoli\", \"Filippi\", \"Filippini\", \"Fini\", \"Fioravanti\", \"Fiore\", \"Fiorentini\", \"Fiorini\", \"Fissi\", \"Focardi\", \"Foggi\", \"Fontana\", \"Fontanelli\", \"Fontani\", \"Forconi\", \"Formigli\", \"Forte\", \"Forti\", \"Fortini\", \"Fossati\", \"Fossi\", \"Francalanci\", \"Franceschi\", \"Franceschini\", \"Franchi\", \"Franchini\", \"Franci\", \"Francini\", \"Francioni\", \"Franco\", \"Frassineti\", \"Frati\", \"Fratini\", \"Frilli\", \"Frizzi\", \"Frosali\", \"Frosini\", \"Frullini\", \"Fusco\", \"Fusi\", \"Gabbrielli\", \"Gabellini\", \"Gagliardi\", \"Galanti\", \"Galardi\", \"Galeotti\", \"Galletti\", \"Galli\", \"Gallo\", \"Gallori\", \"Gambacciani\", \"Gargani\", \"Garofalo\", \"Garuglieri\", \"Gashi\", \"Gasperini\", \"Gatti\", \"Gelli\", \"Gensini\", \"Gentile\", \"Gentili\", \"Geri\", \"Gerini\", \"Gheri\", \"Ghini\", \"Giachetti\", \"Giachi\", \"Giacomelli\", \"Gianassi\", \"Giani\", \"Giannelli\", \"Giannetti\", \"Gianni\", \"Giannini\", \"Giannoni\", \"Giannotti\", \"Giannozzi\", \"Gigli\", \"Giordano\", \"Giorgetti\", \"Giorgi\", \"Giovacchini\", \"Giovannelli\", \"Giovannetti\", \"Giovannini\", \"Giovannoni\", \"Giuliani\", \"Giunti\", \"Giuntini\", \"Giusti\", \"Gonnelli\", \"Goretti\", \"Gori\", \"Gradi\", \"Gramigni\", \"Grassi\", \"Grasso\", \"Graziani\", \"Grazzini\", \"Greco\", \"Grifoni\", \"Grillo\", \"Grimaldi\", \"Grossi\", \"Gualtieri\", \"Guarducci\", \"Guarino\", \"Guarnieri\", \"Guasti\", \"Guerra\", \"Guerri\", \"Guerrini\", \"Guidi\", \"Guidotti\", \"He\", \"Hoxha\", \"Hu\", \"Huang\", \"Iandelli\", \"Ignesti\", \"Innocenti\", \"Jin\", \"La Rosa\", \"Lai\", \"Landi\", \"Landini\", \"Lanini\", \"Lapi\", \"Lapini\", \"Lari\", \"Lascialfari\", \"Lastrucci\", \"Latini\", \"Lazzeri\", \"Lazzerini\", \"Lelli\", \"Lenzi\", \"Leonardi\", \"Leoncini\", \"Leone\", \"Leoni\", \"Lepri\", \"Li\", \"Liao\", \"Lin\", \"Linari\", \"Lippi\", \"Lisi\", \"Livi\", \"Lombardi\", \"Lombardini\", \"Lombardo\", \"Longo\", \"Lopez\", \"Lorenzi\", \"Lorenzini\", \"Lorini\", \"Lotti\", \"Lu\", \"Lucchesi\", \"Lucherini\", \"Lunghi\", \"Lupi\", \"Madiai\", \"Maestrini\", \"Maffei\", \"Maggi\", \"Maggini\", \"Magherini\", \"Magini\", \"Magnani\", \"Magnelli\", \"Magni\", \"Magnolfi\", \"Magrini\", \"Malavolti\", \"Malevolti\", \"Manca\", \"Mancini\", \"Manetti\", \"Manfredi\", \"Mangani\", \"Mannelli\", \"Manni\", \"Mannini\", \"Mannucci\", \"Manuelli\", \"Manzini\", \"Marcelli\", \"Marchese\", \"Marchetti\", \"Marchi\", \"Marchiani\", \"Marchionni\", \"Marconi\", \"Marcucci\", \"Margheri\", \"Mari\", \"Mariani\", \"Marilli\", \"Marinai\", \"Marinari\", \"Marinelli\", \"Marini\", \"Marino\", \"Mariotti\", \"Marsili\", \"Martelli\", \"Martinelli\", \"Martini\", \"Martino\", \"Marzi\", \"Masi\", \"Masini\", \"Masoni\", \"Massai\", \"Materassi\", \"Mattei\", \"Matteini\", \"Matteucci\", \"Matteuzzi\", \"Mattioli\", \"Mattolini\", \"Matucci\", \"Mauro\", \"Mazzanti\", \"Mazzei\", \"Mazzetti\", \"Mazzi\", \"Mazzini\", \"Mazzocchi\", \"Mazzoli\", \"Mazzoni\", \"Mazzuoli\", \"Meacci\", \"Mecocci\", \"Meini\", \"Melani\", \"Mele\", \"Meli\", \"Mengoni\", \"Menichetti\", \"Meoni\", \"Merlini\", \"Messeri\", \"Messina\", \"Meucci\", \"Miccinesi\", \"Miceli\", \"Micheli\", \"Michelini\", \"Michelozzi\", \"Migliori\", \"Migliorini\", \"Milani\", \"Miniati\", \"Misuri\", \"Monaco\", \"Montagnani\", \"Montagni\", \"Montanari\", \"Montelatici\", \"Monti\", \"Montigiani\", \"Montini\", \"Morandi\", \"Morandini\", \"Morelli\", \"Moretti\", \"Morganti\", \"Mori\", \"Morini\", \"Moroni\", \"Morozzi\", \"Mugnai\", \"Mugnaini\", \"Mustafa\", \"Naldi\", \"Naldini\", \"Nannelli\", \"Nanni\", \"Nannini\", \"Nannucci\", \"Nardi\", \"Nardini\", \"Nardoni\", \"Natali\", \"Ndiaye\", \"Nencetti\", \"Nencini\", \"Nencioni\", \"Neri\", \"Nesi\", \"Nesti\", \"Niccolai\", \"Niccoli\", \"Niccolini\", \"Nigi\", \"Nistri\", \"Nocentini\", \"Noferini\", \"Novelli\", \"Nucci\", \"Nuti\", \"Nutini\", \"Oliva\", \"Olivieri\", \"Olmi\", \"Orlandi\", \"Orlandini\", \"Orlando\", \"Orsini\", \"Ortolani\", \"Ottanelli\", \"Pacciani\", \"Pace\", \"Paci\", \"Pacini\", \"Pagani\", \"Pagano\", \"Paggetti\", \"Pagliai\", \"Pagni\", \"Pagnini\", \"Paladini\", \"Palagi\", \"Palchetti\", \"Palloni\", \"Palmieri\", \"Palumbo\", \"Pampaloni\", \"Pancani\", \"Pandolfi\", \"Pandolfini\", \"Panerai\", \"Panichi\", \"Paoletti\", \"Paoli\", \"Paolini\", \"Papi\", \"Papini\", \"Papucci\", \"Parenti\", \"Parigi\", \"Parisi\", \"Parri\", \"Parrini\", \"Pasquini\", \"Passeri\", \"Pecchioli\", \"Pecorini\", \"Pellegrini\", \"Pepi\", \"Perini\", \"Perrone\", \"Peruzzi\", \"Pesci\", \"Pestelli\", \"Petri\", \"Petrini\", \"Petrucci\", \"Pettini\", \"Pezzati\", \"Pezzatini\", \"Piani\", \"Piazza\", \"Piazzesi\", \"Piazzini\", \"Piccardi\", \"Picchi\", \"Piccini\", \"Piccioli\", \"Pieraccini\", \"Pieraccioni\", \"Pieralli\", \"Pierattini\", \"Pieri\", \"Pierini\", \"Pieroni\", \"Pietrini\", \"Pini\", \"Pinna\", \"Pinto\", \"Pinzani\", \"Pinzauti\", \"Piras\", \"Pisani\", \"Pistolesi\", \"Poggesi\", \"Poggi\", \"Poggiali\", \"Poggiolini\", \"Poli\", \"Pollastri\", \"Porciani\", \"Pozzi\", \"Pratellesi\", \"Pratesi\", \"Prosperi\", \"Pruneti\", \"Pucci\", \"Puccini\", \"Puccioni\", \"Pugi\", \"Pugliese\", \"Puliti\", \"Querci\", \"Quercioli\", \"Raddi\", \"Radu\", \"Raffaelli\", \"Ragazzini\", \"Ranfagni\", \"Ranieri\", \"Rastrelli\", \"Raugei\", \"Raveggi\", \"Renai\", \"Renzi\", \"Rettori\", \"Ricci\", \"Ricciardi\", \"Ridi\", \"Ridolfi\", \"Rigacci\", \"Righi\", \"Righini\", \"Rinaldi\", \"Risaliti\", \"Ristori\", \"Rizzo\", \"Rocchi\", \"Rocchini\", \"Rogai\", \"Romagnoli\", \"Romanelli\", \"Romani\", \"Romano\", \"Romei\", \"Romeo\", \"Romiti\", \"Romoli\", \"Romolini\", \"Rontini\", \"Rosati\", \"Roselli\", \"Rosi\", \"Rossetti\", \"Rossi\", \"Rossini\", \"Rovai\", \"Ruggeri\", \"Ruggiero\", \"Russo\", \"Sabatini\", \"Saccardi\", \"Sacchetti\", \"Sacchi\", \"Sacco\", \"Salerno\", \"Salimbeni\", \"Salucci\", \"Salvadori\", \"Salvestrini\", \"Salvi\", \"Salvini\", \"Sanesi\", \"Sani\", \"Sanna\", \"Santi\", \"Santini\", \"Santoni\", \"Santoro\", \"Santucci\", \"Sardi\", \"Sarri\", \"Sarti\", \"Sassi\", \"Sbolci\", \"Scali\", \"Scarpelli\", \"Scarselli\", \"Scopetani\", \"Secci\", \"Selvi\", \"Senatori\", \"Senesi\", \"Serafini\", \"Sereni\", \"Serra\", \"Sestini\", \"Sguanci\", \"Sieni\", \"Signorini\", \"Silvestri\", \"Simoncini\", \"Simonetti\", \"Simoni\", \"Singh\", \"Sodi\", \"Soldi\", \"Somigli\", \"Sorbi\", \"Sorelli\", \"Sorrentino\", \"Sottili\", \"Spina\", \"Spinelli\", \"Staccioli\", \"Staderini\", \"Stefanelli\", \"Stefani\", \"Stefanini\", \"Stella\", \"Susini\", \"Tacchi\", \"Tacconi\", \"Taddei\", \"Tagliaferri\", \"Tamburini\", \"Tanganelli\", \"Tani\", \"Tanini\", \"Tapinassi\", \"Tarchi\", \"Tarchiani\", \"Targioni\", \"Tassi\", \"Tassini\", \"Tempesti\", \"Terzani\", \"Tesi\", \"Testa\", \"Testi\", \"Tilli\", \"Tinti\", \"Tirinnanzi\", \"Toccafondi\", \"Tofanari\", \"Tofani\", \"Tognaccini\", \"Tonelli\", \"Tonini\", \"Torelli\", \"Torrini\", \"Tosi\", \"Toti\", \"Tozzi\", \"Trambusti\", \"Trapani\", \"Tucci\", \"Turchi\", \"Ugolini\", \"Ulivi\", \"Valente\", \"Valenti\", \"Valentini\", \"Vangelisti\", \"Vanni\", \"Vannini\", \"Vannoni\", \"Vannozzi\", \"Vannucchi\", \"Vannucci\", \"Ventura\", \"Venturi\", \"Venturini\", \"Vestri\", \"Vettori\", \"Vichi\", \"Viciani\", \"Vieri\", \"Vigiani\", \"Vignoli\", \"Vignolini\", \"Vignozzi\", \"Villani\", \"Vinci\", \"Visani\", \"Vitale\", \"Vitali\", \"Viti\", \"Viviani\", \"Vivoli\", \"Volpe\", \"Volpi\", \"Wang\", \"Wu\", \"Xu\", \"Yang\", \"Ye\", \"Zagli\", \"Zani\", \"Zanieri\", \"Zanobini\", \"Zecchi\", \"Zetti\", \"Zhang\", \"Zheng\", \"Zhou\", \"Zhu\", \"Zingoni\", \"Zini\", \"Zoppi\"],\r\n\t\t\t// http://www.voornamelijk.nl/meest-voorkomende-achternamen-in-nederland-en-amsterdam/\r\n\t\t\tnl: [\"Albers\", \"Alblas\", \"Appelman\", \"Baars\", \"Baas\", \"Bakker\", \"Blank\", \"Bleeker\", \"Blok\", \"Blom\", \"Boer\", \"Boers\", \"Boldewijn\", \"Boon\", \"Boot\", \"Bos\", \"Bosch\", \"Bosma\", \"Bosman\", \"Bouma\", \"Bouman\", \"Bouwman\", \"Brands\", \"Brouwer\", \"Burger\", \"Buijs\", \"Buitenhuis\", \"Ceder\", \"Cohen\", \"Dekker\", \"Dekkers\", \"Dijkman\", \"Dijkstra\", \"Driessen\", \"Drost\", \"Engel\", \"Evers\", \"Faber\", \"Franke\", \"Gerritsen\", \"Goedhart\", \"Goossens\", \"Groen\", \"Groenenberg\", \"Groot\", \"Haan\", \"Hart\", \"Heemskerk\", \"Hendriks\", \"Hermans\", \"Hoekstra\", \"Hofman\", \"Hopman\", \"Huisman\", \"Jacobs\", \"Jansen\", \"Janssen\", \"Jonker\", \"Jaspers\", \"Keijzer\", \"Klaassen\", \"Klein\", \"Koek\", \"Koenders\", \"Kok\", \"Kool\", \"Koopman\", \"Koopmans\", \"Koning\", \"Koster\", \"Kramer\", \"Kroon\", \"Kuijpers\", \"Kuiper\", \"Kuipers\", \"Kurt\", \"Koster\", \"Kwakman\", \"Los\", \"Lubbers\", \"Maas\", \"Markus\", \"Martens\", \"Meijer\", \"Mol\", \"Molenaar\", \"Mulder\", \"Nieuwenhuis\", \"Peeters\", \"Peters\", \"Pengel\", \"Pieters\", \"Pool\", \"Post\", \"Postma\", \"Prins\", \"Pronk\", \"Reijnders\", \"Rietveld\", \"Roest\", \"Roos\", \"Sanders\", \"Schaap\", \"Scheffer\", \"Schenk\", \"Schilder\", \"Schipper\", \"Schmidt\", \"Scholten\", \"Schouten\", \"Schut\", \"Schutte\", \"Schuurman\", \"Simons\", \"Smeets\", \"Smit\", \"Smits\", \"Snel\", \"Swinkels\", \"Tas\", \"Terpstra\", \"Timmermans\", \"Tol\", \"Tromp\", \"Troost\", \"Valk\", \"Veenstra\", \"Veldkamp\", \"Verbeek\", \"Verheul\", \"Verhoeven\", \"Vermeer\", \"Vermeulen\", \"Verweij\", \"Vink\", \"Visser\", \"Voorn\", \"Vos\", \"Wagenaar\", \"Wiersema\", \"Willems\", \"Willemsen\", \"Witteveen\", \"Wolff\", \"Wolters\", \"Zijlstra\", \"Zwart\", \"de Beer\", \"de Boer\", \"de Bruijn\", \"de Bruin\", \"de Graaf\", \"de Groot\", \"de Haan\", \"de Haas\", \"de Jager\", \"de Jong\", \"de Jonge\", \"de Koning\", \"de Lange\", \"de Leeuw\", \"de Ridder\", \"de Rooij\", \"de Ruiter\", \"de Vos\", \"de Vries\", \"de Waal\", \"de Wit\", \"de Zwart\", \"van Beek\", \"van Boven\", \"van Dam\", \"van Dijk\", \"van Dongen\", \"van Doorn\", \"van Egmond\", \"van Eijk\", \"van Es\", \"van Gelder\", \"van Gelderen\", \"van Houten\", \"van Hulst\", \"van Kempen\", \"van Kesteren\", \"van Leeuwen\", \"van Loon\", \"van Mill\", \"van Noord\", \"van Ommen\", \"van Ommeren\", \"van Oosten\", \"van Oostveen\", \"van Rijn\", \"van Schaik\", \"van Veen\", \"van Vliet\", \"van Wijk\", \"van Wijngaarden\", \"van den Poel\", \"van de Pol\", \"van den Ploeg\", \"van de Ven\", \"van den Berg\", \"van den Bosch\", \"van den Brink\", \"van den Broek\", \"van den Heuvel\", \"van der Heijden\", \"van der Horst\", \"van der Hulst\", \"van der Kroon\", \"van der Laan\", \"van der Linden\", \"van der Meer\", \"van der Meij\", \"van der Meulen\", \"van der Molen\", \"van der Sluis\", \"van der Spek\", \"van der Veen\", \"van der Velde\", \"van der Velden\", \"van der Vliet\", \"van der Wal\"],\r\n\t\t},\r\n\r\n\t\t// Data taken from https://github.com/umpirsky/country-list/blob/master/data/en_US/country.json\r\n\t\tcountries: [\r\n\t\t\t{ name: \"Afghanistan\", abbreviation: \"AF\" },\r\n\t\t\t{ name: \"Åland Islands\", abbreviation: \"AX\" },\r\n\t\t\t{ name: \"Albania\", abbreviation: \"AL\" },\r\n\t\t\t{ name: \"Algeria\", abbreviation: \"DZ\" },\r\n\t\t\t{ name: \"American Samoa\", abbreviation: \"AS\" },\r\n\t\t\t{ name: \"Andorra\", abbreviation: \"AD\" },\r\n\t\t\t{ name: \"Angola\", abbreviation: \"AO\" },\r\n\t\t\t{ name: \"Anguilla\", abbreviation: \"AI\" },\r\n\t\t\t{ name: \"Antarctica\", abbreviation: \"AQ\" },\r\n\t\t\t{ name: \"Antigua & Barbuda\", abbreviation: \"AG\" },\r\n\t\t\t{ name: \"Argentina\", abbreviation: \"AR\" },\r\n\t\t\t{ name: \"Armenia\", abbreviation: \"AM\" },\r\n\t\t\t{ name: \"Aruba\", abbreviation: \"AW\" },\r\n\t\t\t{ name: \"Ascension Island\", abbreviation: \"AC\" },\r\n\t\t\t{ name: \"Australia\", abbreviation: \"AU\" },\r\n\t\t\t{ name: \"Austria\", abbreviation: \"AT\" },\r\n\t\t\t{ name: \"Azerbaijan\", abbreviation: \"AZ\" },\r\n\t\t\t{ name: \"Bahamas\", abbreviation: \"BS\" },\r\n\t\t\t{ name: \"Bahrain\", abbreviation: \"BH\" },\r\n\t\t\t{ name: \"Bangladesh\", abbreviation: \"BD\" },\r\n\t\t\t{ name: \"Barbados\", abbreviation: \"BB\" },\r\n\t\t\t{ name: \"Belarus\", abbreviation: \"BY\" },\r\n\t\t\t{ name: \"Belgium\", abbreviation: \"BE\" },\r\n\t\t\t{ name: \"Belize\", abbreviation: \"BZ\" },\r\n\t\t\t{ name: \"Benin\", abbreviation: \"BJ\" },\r\n\t\t\t{ name: \"Bermuda\", abbreviation: \"BM\" },\r\n\t\t\t{ name: \"Bhutan\", abbreviation: \"BT\" },\r\n\t\t\t{ name: \"Bolivia\", abbreviation: \"BO\" },\r\n\t\t\t{ name: \"Bosnia & Herzegovina\", abbreviation: \"BA\" },\r\n\t\t\t{ name: \"Botswana\", abbreviation: \"BW\" },\r\n\t\t\t{ name: \"Brazil\", abbreviation: \"BR\" },\r\n\t\t\t{ name: \"British Indian Ocean Territory\", abbreviation: \"IO\" },\r\n\t\t\t{ name: \"British Virgin Islands\", abbreviation: \"VG\" },\r\n\t\t\t{ name: \"Brunei\", abbreviation: \"BN\" },\r\n\t\t\t{ name: \"Bulgaria\", abbreviation: \"BG\" },\r\n\t\t\t{ name: \"Burkina Faso\", abbreviation: \"BF\" },\r\n\t\t\t{ name: \"Burundi\", abbreviation: \"BI\" },\r\n\t\t\t{ name: \"Cambodia\", abbreviation: \"KH\" },\r\n\t\t\t{ name: \"Cameroon\", abbreviation: \"CM\" },\r\n\t\t\t{ name: \"Canada\", abbreviation: \"CA\" },\r\n\t\t\t{ name: \"Canary Islands\", abbreviation: \"IC\" },\r\n\t\t\t{ name: \"Cape Verde\", abbreviation: \"CV\" },\r\n\t\t\t{ name: \"Caribbean Netherlands\", abbreviation: \"BQ\" },\r\n\t\t\t{ name: \"Cayman Islands\", abbreviation: \"KY\" },\r\n\t\t\t{ name: \"Central African Republic\", abbreviation: \"CF\" },\r\n\t\t\t{ name: \"Ceuta & Melilla\", abbreviation: \"EA\" },\r\n\t\t\t{ name: \"Chad\", abbreviation: \"TD\" },\r\n\t\t\t{ name: \"Chile\", abbreviation: \"CL\" },\r\n\t\t\t{ name: \"China\", abbreviation: \"CN\" },\r\n\t\t\t{ name: \"Christmas Island\", abbreviation: \"CX\" },\r\n\t\t\t{ name: \"Cocos (Keeling) Islands\", abbreviation: \"CC\" },\r\n\t\t\t{ name: \"Colombia\", abbreviation: \"CO\" },\r\n\t\t\t{ name: \"Comoros\", abbreviation: \"KM\" },\r\n\t\t\t{ name: \"Congo - Brazzaville\", abbreviation: \"CG\" },\r\n\t\t\t{ name: \"Congo - Kinshasa\", abbreviation: \"CD\" },\r\n\t\t\t{ name: \"Cook Islands\", abbreviation: \"CK\" },\r\n\t\t\t{ name: \"Costa Rica\", abbreviation: \"CR\" },\r\n\t\t\t{ name: \"Côte d'Ivoire\", abbreviation: \"CI\" },\r\n\t\t\t{ name: \"Croatia\", abbreviation: \"HR\" },\r\n\t\t\t{ name: \"Cuba\", abbreviation: \"CU\" },\r\n\t\t\t{ name: \"Curaçao\", abbreviation: \"CW\" },\r\n\t\t\t{ name: \"Cyprus\", abbreviation: \"CY\" },\r\n\t\t\t{ name: \"Czech Republic\", abbreviation: \"CZ\" },\r\n\t\t\t{ name: \"Denmark\", abbreviation: \"DK\" },\r\n\t\t\t{ name: \"Diego Garcia\", abbreviation: \"DG\" },\r\n\t\t\t{ name: \"Djibouti\", abbreviation: \"DJ\" },\r\n\t\t\t{ name: \"Dominica\", abbreviation: \"DM\" },\r\n\t\t\t{ name: \"Dominican Republic\", abbreviation: \"DO\" },\r\n\t\t\t{ name: \"Ecuador\", abbreviation: \"EC\" },\r\n\t\t\t{ name: \"Egypt\", abbreviation: \"EG\" },\r\n\t\t\t{ name: \"El Salvador\", abbreviation: \"SV\" },\r\n\t\t\t{ name: \"Equatorial Guinea\", abbreviation: \"GQ\" },\r\n\t\t\t{ name: \"Eritrea\", abbreviation: \"ER\" },\r\n\t\t\t{ name: \"Estonia\", abbreviation: \"EE\" },\r\n\t\t\t{ name: \"Ethiopia\", abbreviation: \"ET\" },\r\n\t\t\t{ name: \"Falkland Islands\", abbreviation: \"FK\" },\r\n\t\t\t{ name: \"Faroe Islands\", abbreviation: \"FO\" },\r\n\t\t\t{ name: \"Fiji\", abbreviation: \"FJ\" },\r\n\t\t\t{ name: \"Finland\", abbreviation: \"FI\" },\r\n\t\t\t{ name: \"France\", abbreviation: \"FR\" },\r\n\t\t\t{ name: \"French Guiana\", abbreviation: \"GF\" },\r\n\t\t\t{ name: \"French Polynesia\", abbreviation: \"PF\" },\r\n\t\t\t{ name: \"French Southern Territories\", abbreviation: \"TF\" },\r\n\t\t\t{ name: \"Gabon\", abbreviation: \"GA\" },\r\n\t\t\t{ name: \"Gambia\", abbreviation: \"GM\" },\r\n\t\t\t{ name: \"Georgia\", abbreviation: \"GE\" },\r\n\t\t\t{ name: \"Germany\", abbreviation: \"DE\" },\r\n\t\t\t{ name: \"Ghana\", abbreviation: \"GH\" },\r\n\t\t\t{ name: \"Gibraltar\", abbreviation: \"GI\" },\r\n\t\t\t{ name: \"Greece\", abbreviation: \"GR\" },\r\n\t\t\t{ name: \"Greenland\", abbreviation: \"GL\" },\r\n\t\t\t{ name: \"Grenada\", abbreviation: \"GD\" },\r\n\t\t\t{ name: \"Guadeloupe\", abbreviation: \"GP\" },\r\n\t\t\t{ name: \"Guam\", abbreviation: \"GU\" },\r\n\t\t\t{ name: \"Guatemala\", abbreviation: \"GT\" },\r\n\t\t\t{ name: \"Guernsey\", abbreviation: \"GG\" },\r\n\t\t\t{ name: \"Guinea\", abbreviation: \"GN\" },\r\n\t\t\t{ name: \"Guinea-Bissau\", abbreviation: \"GW\" },\r\n\t\t\t{ name: \"Guyana\", abbreviation: \"GY\" },\r\n\t\t\t{ name: \"Haiti\", abbreviation: \"HT\" },\r\n\t\t\t{ name: \"Honduras\", abbreviation: \"HN\" },\r\n\t\t\t{ name: \"Hong Kong SAR China\", abbreviation: \"HK\" },\r\n\t\t\t{ name: \"Hungary\", abbreviation: \"HU\" },\r\n\t\t\t{ name: \"Iceland\", abbreviation: \"IS\" },\r\n\t\t\t{ name: \"India\", abbreviation: \"IN\" },\r\n\t\t\t{ name: \"Indonesia\", abbreviation: \"ID\" },\r\n\t\t\t{ name: \"Iran\", abbreviation: \"IR\" },\r\n\t\t\t{ name: \"Iraq\", abbreviation: \"IQ\" },\r\n\t\t\t{ name: \"Ireland\", abbreviation: \"IE\" },\r\n\t\t\t{ name: \"Isle of Man\", abbreviation: \"IM\" },\r\n\t\t\t{ name: \"Israel\", abbreviation: \"IL\" },\r\n\t\t\t{ name: \"Italy\", abbreviation: \"IT\" },\r\n\t\t\t{ name: \"Jamaica\", abbreviation: \"JM\" },\r\n\t\t\t{ name: \"Japan\", abbreviation: \"JP\" },\r\n\t\t\t{ name: \"Jersey\", abbreviation: \"JE\" },\r\n\t\t\t{ name: \"Jordan\", abbreviation: \"JO\" },\r\n\t\t\t{ name: \"Kazakhstan\", abbreviation: \"KZ\" },\r\n\t\t\t{ name: \"Kenya\", abbreviation: \"KE\" },\r\n\t\t\t{ name: \"Kiribati\", abbreviation: \"KI\" },\r\n\t\t\t{ name: \"Kosovo\", abbreviation: \"XK\" },\r\n\t\t\t{ name: \"Kuwait\", abbreviation: \"KW\" },\r\n\t\t\t{ name: \"Kyrgyzstan\", abbreviation: \"KG\" },\r\n\t\t\t{ name: \"Laos\", abbreviation: \"LA\" },\r\n\t\t\t{ name: \"Latvia\", abbreviation: \"LV\" },\r\n\t\t\t{ name: \"Lebanon\", abbreviation: \"LB\" },\r\n\t\t\t{ name: \"Lesotho\", abbreviation: \"LS\" },\r\n\t\t\t{ name: \"Liberia\", abbreviation: \"LR\" },\r\n\t\t\t{ name: \"Libya\", abbreviation: \"LY\" },\r\n\t\t\t{ name: \"Liechtenstein\", abbreviation: \"LI\" },\r\n\t\t\t{ name: \"Lithuania\", abbreviation: \"LT\" },\r\n\t\t\t{ name: \"Luxembourg\", abbreviation: \"LU\" },\r\n\t\t\t{ name: \"Macau SAR China\", abbreviation: \"MO\" },\r\n\t\t\t{ name: \"Macedonia\", abbreviation: \"MK\" },\r\n\t\t\t{ name: \"Madagascar\", abbreviation: \"MG\" },\r\n\t\t\t{ name: \"Malawi\", abbreviation: \"MW\" },\r\n\t\t\t{ name: \"Malaysia\", abbreviation: \"MY\" },\r\n\t\t\t{ name: \"Maldives\", abbreviation: \"MV\" },\r\n\t\t\t{ name: \"Mali\", abbreviation: \"ML\" },\r\n\t\t\t{ name: \"Malta\", abbreviation: \"MT\" },\r\n\t\t\t{ name: \"Marshall Islands\", abbreviation: \"MH\" },\r\n\t\t\t{ name: \"Martinique\", abbreviation: \"MQ\" },\r\n\t\t\t{ name: \"Mauritania\", abbreviation: \"MR\" },\r\n\t\t\t{ name: \"Mauritius\", abbreviation: \"MU\" },\r\n\t\t\t{ name: \"Mayotte\", abbreviation: \"YT\" },\r\n\t\t\t{ name: \"Mexico\", abbreviation: \"MX\" },\r\n\t\t\t{ name: \"Micronesia\", abbreviation: \"FM\" },\r\n\t\t\t{ name: \"Moldova\", abbreviation: \"MD\" },\r\n\t\t\t{ name: \"Monaco\", abbreviation: \"MC\" },\r\n\t\t\t{ name: \"Mongolia\", abbreviation: \"MN\" },\r\n\t\t\t{ name: \"Montenegro\", abbreviation: \"ME\" },\r\n\t\t\t{ name: \"Montserrat\", abbreviation: \"MS\" },\r\n\t\t\t{ name: \"Morocco\", abbreviation: \"MA\" },\r\n\t\t\t{ name: \"Mozambique\", abbreviation: \"MZ\" },\r\n\t\t\t{ name: \"Myanmar (Burma)\", abbreviation: \"MM\" },\r\n\t\t\t{ name: \"Namibia\", abbreviation: \"NA\" },\r\n\t\t\t{ name: \"Nauru\", abbreviation: \"NR\" },\r\n\t\t\t{ name: \"Nepal\", abbreviation: \"NP\" },\r\n\t\t\t{ name: \"Netherlands\", abbreviation: \"NL\" },\r\n\t\t\t{ name: \"New Caledonia\", abbreviation: \"NC\" },\r\n\t\t\t{ name: \"New Zealand\", abbreviation: \"NZ\" },\r\n\t\t\t{ name: \"Nicaragua\", abbreviation: \"NI\" },\r\n\t\t\t{ name: \"Niger\", abbreviation: \"NE\" },\r\n\t\t\t{ name: \"Nigeria\", abbreviation: \"NG\" },\r\n\t\t\t{ name: \"Niue\", abbreviation: \"NU\" },\r\n\t\t\t{ name: \"Norfolk Island\", abbreviation: \"NF\" },\r\n\t\t\t{ name: \"North Korea\", abbreviation: \"KP\" },\r\n\t\t\t{ name: \"Northern Mariana Islands\", abbreviation: \"MP\" },\r\n\t\t\t{ name: \"Norway\", abbreviation: \"NO\" },\r\n\t\t\t{ name: \"Oman\", abbreviation: \"OM\" },\r\n\t\t\t{ name: \"Pakistan\", abbreviation: \"PK\" },\r\n\t\t\t{ name: \"Palau\", abbreviation: \"PW\" },\r\n\t\t\t{ name: \"Palestinian Territories\", abbreviation: \"PS\" },\r\n\t\t\t{ name: \"Panama\", abbreviation: \"PA\" },\r\n\t\t\t{ name: \"Papua New Guinea\", abbreviation: \"PG\" },\r\n\t\t\t{ name: \"Paraguay\", abbreviation: \"PY\" },\r\n\t\t\t{ name: \"Peru\", abbreviation: \"PE\" },\r\n\t\t\t{ name: \"Philippines\", abbreviation: \"PH\" },\r\n\t\t\t{ name: \"Pitcairn Islands\", abbreviation: \"PN\" },\r\n\t\t\t{ name: \"Poland\", abbreviation: \"PL\" },\r\n\t\t\t{ name: \"Portugal\", abbreviation: \"PT\" },\r\n\t\t\t{ name: \"Puerto Rico\", abbreviation: \"PR\" },\r\n\t\t\t{ name: \"Qatar\", abbreviation: \"QA\" },\r\n\t\t\t{ name: \"Réunion\", abbreviation: \"RE\" },\r\n\t\t\t{ name: \"Romania\", abbreviation: \"RO\" },\r\n\t\t\t{ name: \"Russia\", abbreviation: \"RU\" },\r\n\t\t\t{ name: \"Rwanda\", abbreviation: \"RW\" },\r\n\t\t\t{ name: \"Samoa\", abbreviation: \"WS\" },\r\n\t\t\t{ name: \"San Marino\", abbreviation: \"SM\" },\r\n\t\t\t{ name: \"São Tomé and Príncipe\", abbreviation: \"ST\" },\r\n\t\t\t{ name: \"Saudi Arabia\", abbreviation: \"SA\" },\r\n\t\t\t{ name: \"Senegal\", abbreviation: \"SN\" },\r\n\t\t\t{ name: \"Serbia\", abbreviation: \"RS\" },\r\n\t\t\t{ name: \"Seychelles\", abbreviation: \"SC\" },\r\n\t\t\t{ name: \"Sierra Leone\", abbreviation: \"SL\" },\r\n\t\t\t{ name: \"Singapore\", abbreviation: \"SG\" },\r\n\t\t\t{ name: \"Sint Maarten\", abbreviation: \"SX\" },\r\n\t\t\t{ name: \"Slovakia\", abbreviation: \"SK\" },\r\n\t\t\t{ name: \"Slovenia\", abbreviation: \"SI\" },\r\n\t\t\t{ name: \"Solomon Islands\", abbreviation: \"SB\" },\r\n\t\t\t{ name: \"Somalia\", abbreviation: \"SO\" },\r\n\t\t\t{ name: \"South Africa\", abbreviation: \"ZA\" },\r\n\t\t\t{ name: \"South Georgia & South Sandwich Islands\", abbreviation: \"GS\" },\r\n\t\t\t{ name: \"South Korea\", abbreviation: \"KR\" },\r\n\t\t\t{ name: \"South Sudan\", abbreviation: \"SS\" },\r\n\t\t\t{ name: \"Spain\", abbreviation: \"ES\" },\r\n\t\t\t{ name: \"Sri Lanka\", abbreviation: \"LK\" },\r\n\t\t\t{ name: \"St. Barthélemy\", abbreviation: \"BL\" },\r\n\t\t\t{ name: \"St. Helena\", abbreviation: \"SH\" },\r\n\t\t\t{ name: \"St. Kitts & Nevis\", abbreviation: \"KN\" },\r\n\t\t\t{ name: \"St. Lucia\", abbreviation: \"LC\" },\r\n\t\t\t{ name: \"St. Martin\", abbreviation: \"MF\" },\r\n\t\t\t{ name: \"St. Pierre & Miquelon\", abbreviation: \"PM\" },\r\n\t\t\t{ name: \"St. Vincent & Grenadines\", abbreviation: \"VC\" },\r\n\t\t\t{ name: \"Sudan\", abbreviation: \"SD\" },\r\n\t\t\t{ name: \"Suriname\", abbreviation: \"SR\" },\r\n\t\t\t{ name: \"Svalbard & Jan Mayen\", abbreviation: \"SJ\" },\r\n\t\t\t{ name: \"Swaziland\", abbreviation: \"SZ\" },\r\n\t\t\t{ name: \"Sweden\", abbreviation: \"SE\" },\r\n\t\t\t{ name: \"Switzerland\", abbreviation: \"CH\" },\r\n\t\t\t{ name: \"Syria\", abbreviation: \"SY\" },\r\n\t\t\t{ name: \"Taiwan\", abbreviation: \"TW\" },\r\n\t\t\t{ name: \"Tajikistan\", abbreviation: \"TJ\" },\r\n\t\t\t{ name: \"Tanzania\", abbreviation: \"TZ\" },\r\n\t\t\t{ name: \"Thailand\", abbreviation: \"TH\" },\r\n\t\t\t{ name: \"Timor-Leste\", abbreviation: \"TL\" },\r\n\t\t\t{ name: \"Togo\", abbreviation: \"TG\" },\r\n\t\t\t{ name: \"Tokelau\", abbreviation: \"TK\" },\r\n\t\t\t{ name: \"Tonga\", abbreviation: \"TO\" },\r\n\t\t\t{ name: \"Trinidad & Tobago\", abbreviation: \"TT\" },\r\n\t\t\t{ name: \"Tristan da Cunha\", abbreviation: \"TA\" },\r\n\t\t\t{ name: \"Tunisia\", abbreviation: \"TN\" },\r\n\t\t\t{ name: \"Turkey\", abbreviation: \"TR\" },\r\n\t\t\t{ name: \"Turkmenistan\", abbreviation: \"TM\" },\r\n\t\t\t{ name: \"Turks & Caicos Islands\", abbreviation: \"TC\" },\r\n\t\t\t{ name: \"Tuvalu\", abbreviation: \"TV\" },\r\n\t\t\t{ name: \"U.S. Outlying Islands\", abbreviation: \"UM\" },\r\n\t\t\t{ name: \"U.S. Virgin Islands\", abbreviation: \"VI\" },\r\n\t\t\t{ name: \"Uganda\", abbreviation: \"UG\" },\r\n\t\t\t{ name: \"Ukraine\", abbreviation: \"UA\" },\r\n\t\t\t{ name: \"United Arab Emirates\", abbreviation: \"AE\" },\r\n\t\t\t{ name: \"United Kingdom\", abbreviation: \"GB\" },\r\n\t\t\t{ name: \"United States\", abbreviation: \"US\" },\r\n\t\t\t{ name: \"Uruguay\", abbreviation: \"UY\" },\r\n\t\t\t{ name: \"Uzbekistan\", abbreviation: \"UZ\" },\r\n\t\t\t{ name: \"Vanuatu\", abbreviation: \"VU\" },\r\n\t\t\t{ name: \"Vatican City\", abbreviation: \"VA\" },\r\n\t\t\t{ name: \"Venezuela\", abbreviation: \"VE\" },\r\n\t\t\t{ name: \"Vietnam\", abbreviation: \"VN\" },\r\n\t\t\t{ name: \"Wallis & Futuna\", abbreviation: \"WF\" },\r\n\t\t\t{ name: \"Western Sahara\", abbreviation: \"EH\" },\r\n\t\t\t{ name: \"Yemen\", abbreviation: \"YE\" },\r\n\t\t\t{ name: \"Zambia\", abbreviation: \"ZM\" },\r\n\t\t\t{ name: \"Zimbabwe\", abbreviation: \"ZW\" },\r\n\t\t],\r\n\r\n\t\tcounties: {\r\n\t\t\t// Data taken from http://www.downloadexcelfiles.com/gb_en/download-excel-file-list-counties-uk\r\n\t\t\tuk: [{ name: \"Bath and North East Somerset\" }, { name: \"Aberdeenshire\" }, { name: \"Anglesey\" }, { name: \"Angus\" }, { name: \"Bedford\" }, { name: \"Blackburn with Darwen\" }, { name: \"Blackpool\" }, { name: \"Bournemouth\" }, { name: \"Bracknell Forest\" }, { name: \"Brighton & Hove\" }, { name: \"Bristol\" }, { name: \"Buckinghamshire\" }, { name: \"Cambridgeshire\" }, { name: \"Carmarthenshire\" }, { name: \"Central Bedfordshire\" }, { name: \"Ceredigion\" }, { name: \"Cheshire East\" }, { name: \"Cheshire West and Chester\" }, { name: \"Clackmannanshire\" }, { name: \"Conwy\" }, { name: \"Cornwall\" }, { name: \"County Antrim\" }, { name: \"County Armagh\" }, { name: \"County Down\" }, { name: \"County Durham\" }, { name: \"County Fermanagh\" }, { name: \"County Londonderry\" }, { name: \"County Tyrone\" }, { name: \"Cumbria\" }, { name: \"Darlington\" }, { name: \"Denbighshire\" }, { name: \"Derby\" }, { name: \"Derbyshire\" }, { name: \"Devon\" }, { name: \"Dorset\" }, { name: \"Dumfries and Galloway\" }, { name: \"Dundee\" }, { name: \"East Lothian\" }, { name: \"East Riding of Yorkshire\" }, { name: \"East Sussex\" }, { name: \"Edinburgh?\" }, { name: \"Essex\" }, { name: \"Falkirk\" }, { name: \"Fife\" }, { name: \"Flintshire\" }, { name: \"Gloucestershire\" }, { name: \"Greater London\" }, { name: \"Greater Manchester\" }, { name: \"Gwent\" }, { name: \"Gwynedd\" }, { name: \"Halton\" }, { name: \"Hampshire\" }, { name: \"Hartlepool\" }, { name: \"Herefordshire\" }, { name: \"Hertfordshire\" }, { name: \"Highlands\" }, { name: \"Hull\" }, { name: \"Isle of Wight\" }, { name: \"Isles of Scilly\" }, { name: \"Kent\" }, { name: \"Lancashire\" }, { name: \"Leicester\" }, { name: \"Leicestershire\" }, { name: \"Lincolnshire\" }, { name: \"Lothian\" }, { name: \"Luton\" }, { name: \"Medway\" }, { name: \"Merseyside\" }, { name: \"Mid Glamorgan\" }, { name: \"Middlesbrough\" }, { name: \"Milton Keynes\" }, { name: \"Monmouthshire\" }, { name: \"Moray\" }, { name: \"Norfolk\" }, { name: \"North East Lincolnshire\" }, { name: \"North Lincolnshire\" }, { name: \"North Somerset\" }, { name: \"North Yorkshire\" }, { name: \"Northamptonshire\" }, { name: \"Northumberland\" }, { name: \"Nottingham\" }, { name: \"Nottinghamshire\" }, { name: \"Oxfordshire\" }, { name: \"Pembrokeshire\" }, { name: \"Perth and Kinross\" }, { name: \"Peterborough\" }, { name: \"Plymouth\" }, { name: \"Poole\" }, { name: \"Portsmouth\" }, { name: \"Powys\" }, { name: \"Reading\" }, { name: \"Redcar and Cleveland\" }, { name: \"Rutland\" }, { name: \"Scottish Borders\" }, { name: \"Shropshire\" }, { name: \"Slough\" }, { name: \"Somerset\" }, { name: \"South Glamorgan\" }, { name: \"South Gloucestershire\" }, { name: \"South Yorkshire\" }, { name: \"Southampton\" }, { name: \"Southend-on-Sea\" }, { name: \"Staffordshire\" }, { name: \"Stirlingshire\" }, { name: \"Stockton-on-Tees\" }, { name: \"Stoke-on-Trent\" }, { name: \"Strathclyde\" }, { name: \"Suffolk\" }, { name: \"Surrey\" }, { name: \"Swindon\" }, { name: \"Telford and Wrekin\" }, { name: \"Thurrock\" }, { name: \"Torbay\" }, { name: \"Tyne and Wear\" }, { name: \"Warrington\" }, { name: \"Warwickshire\" }, { name: \"West Berkshire\" }, { name: \"West Glamorgan\" }, { name: \"West Lothian\" }, { name: \"West Midlands\" }, { name: \"West Sussex\" }, { name: \"West Yorkshire\" }, { name: \"Western Isles\" }, { name: \"Wiltshire\" }, { name: \"Windsor and Maidenhead\" }, { name: \"Wokingham\" }, { name: \"Worcestershire\" }, { name: \"Wrexham\" }, { name: \"York\" }],\r\n\t\t},\r\n\t\tprovinces: {\r\n\t\t\tca: [\r\n\t\t\t\t{ name: \"Alberta\", abbreviation: \"AB\" },\r\n\t\t\t\t{ name: \"British Columbia\", abbreviation: \"BC\" },\r\n\t\t\t\t{ name: \"Manitoba\", abbreviation: \"MB\" },\r\n\t\t\t\t{ name: \"New Brunswick\", abbreviation: \"NB\" },\r\n\t\t\t\t{ name: \"Newfoundland and Labrador\", abbreviation: \"NL\" },\r\n\t\t\t\t{ name: \"Nova Scotia\", abbreviation: \"NS\" },\r\n\t\t\t\t{ name: \"Ontario\", abbreviation: \"ON\" },\r\n\t\t\t\t{ name: \"Prince Edward Island\", abbreviation: \"PE\" },\r\n\t\t\t\t{ name: \"Quebec\", abbreviation: \"QC\" },\r\n\t\t\t\t{ name: \"Saskatchewan\", abbreviation: \"SK\" },\r\n\r\n\t\t\t\t// The case could be made that the following are not actually provinces\r\n\t\t\t\t// since they are technically considered \"territories\" however they all\r\n\t\t\t\t// look the same on an envelope!\r\n\t\t\t\t{ name: \"Northwest Territories\", abbreviation: \"NT\" },\r\n\t\t\t\t{ name: \"Nunavut\", abbreviation: \"NU\" },\r\n\t\t\t\t{ name: \"Yukon\", abbreviation: \"YT\" },\r\n\t\t\t],\r\n\t\t\tit: [\r\n\t\t\t\t{ name: \"Agrigento\", abbreviation: \"AG\", code: 84 },\r\n\t\t\t\t{ name: \"Alessandria\", abbreviation: \"AL\", code: 6 },\r\n\t\t\t\t{ name: \"Ancona\", abbreviation: \"AN\", code: 42 },\r\n\t\t\t\t{ name: \"Aosta\", abbreviation: \"AO\", code: 7 },\r\n\t\t\t\t{ name: \"L'Aquila\", abbreviation: \"AQ\", code: 66 },\r\n\t\t\t\t{ name: \"Arezzo\", abbreviation: \"AR\", code: 51 },\r\n\t\t\t\t{ name: \"Ascoli-Piceno\", abbreviation: \"AP\", code: 44 },\r\n\t\t\t\t{ name: \"Asti\", abbreviation: \"AT\", code: 5 },\r\n\t\t\t\t{ name: \"Avellino\", abbreviation: \"AV\", code: 64 },\r\n\t\t\t\t{ name: \"Bari\", abbreviation: \"BA\", code: 72 },\r\n\t\t\t\t{ name: \"Barletta-Andria-Trani\", abbreviation: \"BT\", code: 72 },\r\n\t\t\t\t{ name: \"Belluno\", abbreviation: \"BL\", code: 25 },\r\n\t\t\t\t{ name: \"Benevento\", abbreviation: \"BN\", code: 62 },\r\n\t\t\t\t{ name: \"Bergamo\", abbreviation: \"BG\", code: 16 },\r\n\t\t\t\t{ name: \"Biella\", abbreviation: \"BI\", code: 96 },\r\n\t\t\t\t{ name: \"Bologna\", abbreviation: \"BO\", code: 37 },\r\n\t\t\t\t{ name: \"Bolzano\", abbreviation: \"BZ\", code: 21 },\r\n\t\t\t\t{ name: \"Brescia\", abbreviation: \"BS\", code: 17 },\r\n\t\t\t\t{ name: \"Brindisi\", abbreviation: \"BR\", code: 74 },\r\n\t\t\t\t{ name: \"Cagliari\", abbreviation: \"CA\", code: 92 },\r\n\t\t\t\t{ name: \"Caltanissetta\", abbreviation: \"CL\", code: 85 },\r\n\t\t\t\t{ name: \"Campobasso\", abbreviation: \"CB\", code: 70 },\r\n\t\t\t\t{ name: \"Carbonia Iglesias\", abbreviation: \"CI\", code: 70 },\r\n\t\t\t\t{ name: \"Caserta\", abbreviation: \"CE\", code: 61 },\r\n\t\t\t\t{ name: \"Catania\", abbreviation: \"CT\", code: 87 },\r\n\t\t\t\t{ name: \"Catanzaro\", abbreviation: \"CZ\", code: 79 },\r\n\t\t\t\t{ name: \"Chieti\", abbreviation: \"CH\", code: 69 },\r\n\t\t\t\t{ name: \"Como\", abbreviation: \"CO\", code: 13 },\r\n\t\t\t\t{ name: \"Cosenza\", abbreviation: \"CS\", code: 78 },\r\n\t\t\t\t{ name: \"Cremona\", abbreviation: \"CR\", code: 19 },\r\n\t\t\t\t{ name: \"Crotone\", abbreviation: \"KR\", code: 101 },\r\n\t\t\t\t{ name: \"Cuneo\", abbreviation: \"CN\", code: 4 },\r\n\t\t\t\t{ name: \"Enna\", abbreviation: \"EN\", code: 86 },\r\n\t\t\t\t{ name: \"Fermo\", abbreviation: \"FM\", code: 86 },\r\n\t\t\t\t{ name: \"Ferrara\", abbreviation: \"FE\", code: 38 },\r\n\t\t\t\t{ name: \"Firenze\", abbreviation: \"FI\", code: 48 },\r\n\t\t\t\t{ name: \"Foggia\", abbreviation: \"FG\", code: 71 },\r\n\t\t\t\t{ name: \"Forli-Cesena\", abbreviation: \"FC\", code: 71 },\r\n\t\t\t\t{ name: \"Frosinone\", abbreviation: \"FR\", code: 60 },\r\n\t\t\t\t{ name: \"Genova\", abbreviation: \"GE\", code: 10 },\r\n\t\t\t\t{ name: \"Gorizia\", abbreviation: \"GO\", code: 31 },\r\n\t\t\t\t{ name: \"Grosseto\", abbreviation: \"GR\", code: 53 },\r\n\t\t\t\t{ name: \"Imperia\", abbreviation: \"IM\", code: 8 },\r\n\t\t\t\t{ name: \"Isernia\", abbreviation: \"IS\", code: 94 },\r\n\t\t\t\t{ name: \"La-Spezia\", abbreviation: \"SP\", code: 66 },\r\n\t\t\t\t{ name: \"Latina\", abbreviation: \"LT\", code: 59 },\r\n\t\t\t\t{ name: \"Lecce\", abbreviation: \"LE\", code: 75 },\r\n\t\t\t\t{ name: \"Lecco\", abbreviation: \"LC\", code: 97 },\r\n\t\t\t\t{ name: \"Livorno\", abbreviation: \"LI\", code: 49 },\r\n\t\t\t\t{ name: \"Lodi\", abbreviation: \"LO\", code: 98 },\r\n\t\t\t\t{ name: \"Lucca\", abbreviation: \"LU\", code: 46 },\r\n\t\t\t\t{ name: \"Macerata\", abbreviation: \"MC\", code: 43 },\r\n\t\t\t\t{ name: \"Mantova\", abbreviation: \"MN\", code: 20 },\r\n\t\t\t\t{ name: \"Massa-Carrara\", abbreviation: \"MS\", code: 45 },\r\n\t\t\t\t{ name: \"Matera\", abbreviation: \"MT\", code: 77 },\r\n\t\t\t\t{ name: \"Medio Campidano\", abbreviation: \"VS\", code: 77 },\r\n\t\t\t\t{ name: \"Messina\", abbreviation: \"ME\", code: 83 },\r\n\t\t\t\t{ name: \"Milano\", abbreviation: \"MI\", code: 15 },\r\n\t\t\t\t{ name: \"Modena\", abbreviation: \"MO\", code: 36 },\r\n\t\t\t\t{ name: \"Monza-Brianza\", abbreviation: \"MB\", code: 36 },\r\n\t\t\t\t{ name: \"Napoli\", abbreviation: \"NA\", code: 63 },\r\n\t\t\t\t{ name: \"Novara\", abbreviation: \"NO\", code: 3 },\r\n\t\t\t\t{ name: \"Nuoro\", abbreviation: \"NU\", code: 91 },\r\n\t\t\t\t{ name: \"Ogliastra\", abbreviation: \"OG\", code: 91 },\r\n\t\t\t\t{ name: \"Olbia Tempio\", abbreviation: \"OT\", code: 91 },\r\n\t\t\t\t{ name: \"Oristano\", abbreviation: \"OR\", code: 95 },\r\n\t\t\t\t{ name: \"Padova\", abbreviation: \"PD\", code: 28 },\r\n\t\t\t\t{ name: \"Palermo\", abbreviation: \"PA\", code: 82 },\r\n\t\t\t\t{ name: \"Parma\", abbreviation: \"PR\", code: 34 },\r\n\t\t\t\t{ name: \"Pavia\", abbreviation: \"PV\", code: 18 },\r\n\t\t\t\t{ name: \"Perugia\", abbreviation: \"PG\", code: 54 },\r\n\t\t\t\t{ name: \"Pesaro-Urbino\", abbreviation: \"PU\", code: 41 },\r\n\t\t\t\t{ name: \"Pescara\", abbreviation: \"PE\", code: 68 },\r\n\t\t\t\t{ name: \"Piacenza\", abbreviation: \"PC\", code: 33 },\r\n\t\t\t\t{ name: \"Pisa\", abbreviation: \"PI\", code: 50 },\r\n\t\t\t\t{ name: \"Pistoia\", abbreviation: \"PT\", code: 47 },\r\n\t\t\t\t{ name: \"Pordenone\", abbreviation: \"PN\", code: 93 },\r\n\t\t\t\t{ name: \"Potenza\", abbreviation: \"PZ\", code: 76 },\r\n\t\t\t\t{ name: \"Prato\", abbreviation: \"PO\", code: 100 },\r\n\t\t\t\t{ name: \"Ragusa\", abbreviation: \"RG\", code: 88 },\r\n\t\t\t\t{ name: \"Ravenna\", abbreviation: \"RA\", code: 39 },\r\n\t\t\t\t{ name: \"Reggio-Calabria\", abbreviation: \"RC\", code: 35 },\r\n\t\t\t\t{ name: \"Reggio-Emilia\", abbreviation: \"RE\", code: 35 },\r\n\t\t\t\t{ name: \"Rieti\", abbreviation: \"RI\", code: 57 },\r\n\t\t\t\t{ name: \"Rimini\", abbreviation: \"RN\", code: 99 },\r\n\t\t\t\t{ name: \"Roma\", abbreviation: \"Roma\", code: 58 },\r\n\t\t\t\t{ name: \"Rovigo\", abbreviation: \"RO\", code: 29 },\r\n\t\t\t\t{ name: \"Salerno\", abbreviation: \"SA\", code: 65 },\r\n\t\t\t\t{ name: \"Sassari\", abbreviation: \"SS\", code: 90 },\r\n\t\t\t\t{ name: \"Savona\", abbreviation: \"SV\", code: 9 },\r\n\t\t\t\t{ name: \"Siena\", abbreviation: \"SI\", code: 52 },\r\n\t\t\t\t{ name: \"Siracusa\", abbreviation: \"SR\", code: 89 },\r\n\t\t\t\t{ name: \"Sondrio\", abbreviation: \"SO\", code: 14 },\r\n\t\t\t\t{ name: \"Taranto\", abbreviation: \"TA\", code: 73 },\r\n\t\t\t\t{ name: \"Teramo\", abbreviation: \"TE\", code: 67 },\r\n\t\t\t\t{ name: \"Terni\", abbreviation: \"TR\", code: 55 },\r\n\t\t\t\t{ name: \"Torino\", abbreviation: \"TO\", code: 1 },\r\n\t\t\t\t{ name: \"Trapani\", abbreviation: \"TP\", code: 81 },\r\n\t\t\t\t{ name: \"Trento\", abbreviation: \"TN\", code: 22 },\r\n\t\t\t\t{ name: \"Treviso\", abbreviation: \"TV\", code: 26 },\r\n\t\t\t\t{ name: \"Trieste\", abbreviation: \"TS\", code: 32 },\r\n\t\t\t\t{ name: \"Udine\", abbreviation: \"UD\", code: 30 },\r\n\t\t\t\t{ name: \"Varese\", abbreviation: \"VA\", code: 12 },\r\n\t\t\t\t{ name: \"Venezia\", abbreviation: \"VE\", code: 27 },\r\n\t\t\t\t{ name: \"Verbania\", abbreviation: \"VB\", code: 27 },\r\n\t\t\t\t{ name: \"Vercelli\", abbreviation: \"VC\", code: 2 },\r\n\t\t\t\t{ name: \"Verona\", abbreviation: \"VR\", code: 23 },\r\n\t\t\t\t{ name: \"Vibo-Valentia\", abbreviation: \"VV\", code: 102 },\r\n\t\t\t\t{ name: \"Vicenza\", abbreviation: \"VI\", code: 24 },\r\n\t\t\t\t{ name: \"Viterbo\", abbreviation: \"VT\", code: 56 },\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\t// from: https://github.com/samsargent/Useful-Autocomplete-Data/blob/master/data/nationalities.json\r\n\t\tnationalities: [{ name: \"Afghan\" }, { name: \"Albanian\" }, { name: \"Algerian\" }, { name: \"American\" }, { name: \"Andorran\" }, { name: \"Angolan\" }, { name: \"Antiguans\" }, { name: \"Argentinean\" }, { name: \"Armenian\" }, { name: \"Australian\" }, { name: \"Austrian\" }, { name: \"Azerbaijani\" }, { name: \"Bahami\" }, { name: \"Bahraini\" }, { name: \"Bangladeshi\" }, { name: \"Barbadian\" }, { name: \"Barbudans\" }, { name: \"Batswana\" }, { name: \"Belarusian\" }, { name: \"Belgian\" }, { name: \"Belizean\" }, { name: \"Beninese\" }, { name: \"Bhutanese\" }, { name: \"Bolivian\" }, { name: \"Bosnian\" }, { name: \"Brazilian\" }, { name: \"British\" }, { name: \"Bruneian\" }, { name: \"Bulgarian\" }, { name: \"Burkinabe\" }, { name: \"Burmese\" }, { name: \"Burundian\" }, { name: \"Cambodian\" }, { name: \"Cameroonian\" }, { name: \"Canadian\" }, { name: \"Cape Verdean\" }, { name: \"Central African\" }, { name: \"Chadian\" }, { name: \"Chilean\" }, { name: \"Chinese\" }, { name: \"Colombian\" }, { name: \"Comoran\" }, { name: \"Congolese\" }, { name: \"Costa Rican\" }, { name: \"Croatian\" }, { name: \"Cuban\" }, { name: \"Cypriot\" }, { name: \"Czech\" }, { name: \"Danish\" }, { name: \"Djibouti\" }, { name: \"Dominican\" }, { name: \"Dutch\" }, { name: \"East Timorese\" }, { name: \"Ecuadorean\" }, { name: \"Egyptian\" }, { name: \"Emirian\" }, { name: \"Equatorial Guinean\" }, { name: \"Eritrean\" }, { name: \"Estonian\" }, { name: \"Ethiopian\" }, { name: \"Fijian\" }, { name: \"Filipino\" }, { name: \"Finnish\" }, { name: \"French\" }, { name: \"Gabonese\" }, { name: \"Gambian\" }, { name: \"Georgian\" }, { name: \"German\" }, { name: \"Ghanaian\" }, { name: \"Greek\" }, { name: \"Grenadian\" }, { name: \"Guatemalan\" }, { name: \"Guinea-Bissauan\" }, { name: \"Guinean\" }, { name: \"Guyanese\" }, { name: \"Haitian\" }, { name: \"Herzegovinian\" }, { name: \"Honduran\" }, { name: \"Hungarian\" }, { name: \"I-Kiribati\" }, { name: \"Icelander\" }, { name: \"Indian\" }, { name: \"Indonesian\" }, { name: \"Iranian\" }, { name: \"Iraqi\" }, { name: \"Irish\" }, { name: \"Israeli\" }, { name: \"Italian\" }, { name: \"Ivorian\" }, { name: \"Jamaican\" }, { name: \"Japanese\" }, { name: \"Jordanian\" }, { name: \"Kazakhstani\" }, { name: \"Kenyan\" }, { name: \"Kittian and Nevisian\" }, { name: \"Kuwaiti\" }, { name: \"Kyrgyz\" }, { name: \"Laotian\" }, { name: \"Latvian\" }, { name: \"Lebanese\" }, { name: \"Liberian\" }, { name: \"Libyan\" }, { name: \"Liechtensteiner\" }, { name: \"Lithuanian\" }, { name: \"Luxembourger\" }, { name: \"Macedonian\" }, { name: \"Malagasy\" }, { name: \"Malawian\" }, { name: \"Malaysian\" }, { name: \"Maldivan\" }, { name: \"Malian\" }, { name: \"Maltese\" }, { name: \"Marshallese\" }, { name: \"Mauritanian\" }, { name: \"Mauritian\" }, { name: \"Mexican\" }, { name: \"Micronesian\" }, { name: \"Moldovan\" }, { name: \"Monacan\" }, { name: \"Mongolian\" }, { name: \"Moroccan\" }, { name: \"Mosotho\" }, { name: \"Motswana\" }, { name: \"Mozambican\" }, { name: \"Namibian\" }, { name: \"Nauruan\" }, { name: \"Nepalese\" }, { name: \"New Zealander\" }, { name: \"Nicaraguan\" }, { name: \"Nigerian\" }, { name: \"Nigerien\" }, { name: \"North Korean\" }, { name: \"Northern Irish\" }, { name: \"Norwegian\" }, { name: \"Omani\" }, { name: \"Pakistani\" }, { name: \"Palauan\" }, { name: \"Panamanian\" }, { name: \"Papua New Guinean\" }, { name: \"Paraguayan\" }, { name: \"Peruvian\" }, { name: \"Polish\" }, { name: \"Portuguese\" }, { name: \"Qatari\" }, { name: \"Romani\" }, { name: \"Russian\" }, { name: \"Rwandan\" }, { name: \"Saint Lucian\" }, { name: \"Salvadoran\" }, { name: \"Samoan\" }, { name: \"San Marinese\" }, { name: \"Sao Tomean\" }, { name: \"Saudi\" }, { name: \"Scottish\" }, { name: \"Senegalese\" }, { name: \"Serbian\" }, { name: \"Seychellois\" }, { name: \"Sierra Leonean\" }, { name: \"Singaporean\" }, { name: \"Slovakian\" }, { name: \"Slovenian\" }, { name: \"Solomon Islander\" }, { name: \"Somali\" }, { name: \"South African\" }, { name: \"South Korean\" }, { name: \"Spanish\" }, { name: \"Sri Lankan\" }, { name: \"Sudanese\" }, { name: \"Surinamer\" }, { name: \"Swazi\" }, { name: \"Swedish\" }, { name: \"Swiss\" }, { name: \"Syrian\" }, { name: \"Taiwanese\" }, { name: \"Tajik\" }, { name: \"Tanzanian\" }, { name: \"Thai\" }, { name: \"Togolese\" }, { name: \"Tongan\" }, { name: \"Trinidadian or Tobagonian\" }, { name: \"Tunisian\" }, { name: \"Turkish\" }, { name: \"Tuvaluan\" }, { name: \"Ugandan\" }, { name: \"Ukrainian\" }, { name: \"Uruguaya\" }, { name: \"Uzbekistani\" }, { name: \"Venezuela\" }, { name: \"Vietnamese\" }, { name: \"Wels\" }, { name: \"Yemenit\" }, { name: \"Zambia\" }, { name: \"Zimbabwe\" }],\r\n\t\t// http://www.loc.gov/standards/iso639-2/php/code_list.php (ISO-639-1 codes)\r\n\t\tlocale_languages: [\"aa\", \"ab\", \"ae\", \"af\", \"ak\", \"am\", \"an\", \"ar\", \"as\", \"av\", \"ay\", \"az\", \"ba\", \"be\", \"bg\", \"bh\", \"bi\", \"bm\", \"bn\", \"bo\", \"br\", \"bs\", \"ca\", \"ce\", \"ch\", \"co\", \"cr\", \"cs\", \"cu\", \"cv\", \"cy\", \"da\", \"de\", \"dv\", \"dz\", \"ee\", \"el\", \"en\", \"eo\", \"es\", \"et\", \"eu\", \"fa\", \"ff\", \"fi\", \"fj\", \"fo\", \"fr\", \"fy\", \"ga\", \"gd\", \"gl\", \"gn\", \"gu\", \"gv\", \"ha\", \"he\", \"hi\", \"ho\", \"hr\", \"ht\", \"hu\", \"hy\", \"hz\", \"ia\", \"id\", \"ie\", \"ig\", \"ii\", \"ik\", \"io\", \"is\", \"it\", \"iu\", \"ja\", \"jv\", \"ka\", \"kg\", \"ki\", \"kj\", \"kk\", \"kl\", \"km\", \"kn\", \"ko\", \"kr\", \"ks\", \"ku\", \"kv\", \"kw\", \"ky\", \"la\", \"lb\", \"lg\", \"li\", \"ln\", \"lo\", \"lt\", \"lu\", \"lv\", \"mg\", \"mh\", \"mi\", \"mk\", \"ml\", \"mn\", \"mr\", \"ms\", \"mt\", \"my\", \"na\", \"nb\", \"nd\", \"ne\", \"ng\", \"nl\", \"nn\", \"no\", \"nr\", \"nv\", \"ny\", \"oc\", \"oj\", \"om\", \"or\", \"os\", \"pa\", \"pi\", \"pl\", \"ps\", \"pt\", \"qu\", \"rm\", \"rn\", \"ro\", \"ru\", \"rw\", \"sa\", \"sc\", \"sd\", \"se\", \"sg\", \"si\", \"sk\", \"sl\", \"sm\", \"sn\", \"so\", \"sq\", \"sr\", \"ss\", \"st\", \"su\", \"sv\", \"sw\", \"ta\", \"te\", \"tg\", \"th\", \"ti\", \"tk\", \"tl\", \"tn\", \"to\", \"tr\", \"ts\", \"tt\", \"tw\", \"ty\", \"ug\", \"uk\", \"ur\", \"uz\", \"ve\", \"vi\", \"vo\", \"wa\", \"wo\", \"xh\", \"yi\", \"yo\", \"za\", \"zh\", \"zu\"],\r\n\r\n\t\t// From http://data.okfn.org/data/core/language-codes#resource-language-codes-full (IETF language tags)\r\n\t\tlocale_regions: [\"agq-CM\", \"asa-TZ\", \"ast-ES\", \"bas-CM\", \"bem-ZM\", \"bez-TZ\", \"brx-IN\", \"cgg-UG\", \"chr-US\", \"dav-KE\", \"dje-NE\", \"dsb-DE\", \"dua-CM\", \"dyo-SN\", \"ebu-KE\", \"ewo-CM\", \"fil-PH\", \"fur-IT\", \"gsw-CH\", \"gsw-FR\", \"gsw-LI\", \"guz-KE\", \"haw-US\", \"hsb-DE\", \"jgo-CM\", \"jmc-TZ\", \"kab-DZ\", \"kam-KE\", \"kde-TZ\", \"kea-CV\", \"khq-ML\", \"kkj-CM\", \"kln-KE\", \"kok-IN\", \"ksb-TZ\", \"ksf-CM\", \"ksh-DE\", \"lag-TZ\", \"lkt-US\", \"luo-KE\", \"luy-KE\", \"mas-KE\", \"mas-TZ\", \"mer-KE\", \"mfe-MU\", \"mgh-MZ\", \"mgo-CM\", \"mua-CM\", \"naq-NA\", \"nmg-CM\", \"nnh-CM\", \"nus-SD\", \"nyn-UG\", \"rof-TZ\", \"rwk-TZ\", \"sah-RU\", \"saq-KE\", \"sbp-TZ\", \"seh-MZ\", \"ses-ML\", \"shi-Latn\", \"shi-Latn-MA\", \"shi-Tfng\", \"shi-Tfng-MA\", \"smn-FI\", \"teo-KE\", \"teo-UG\", \"twq-NE\", \"tzm-Latn\", \"tzm-Latn-MA\", \"vai-Latn\", \"vai-Latn-LR\", \"vai-Vaii\", \"vai-Vaii-LR\", \"vun-TZ\", \"wae-CH\", \"xog-UG\", \"yav-CM\", \"zgh-MA\", \"af-NA\", \"af-ZA\", \"ak-GH\", \"am-ET\", \"ar-001\", \"ar-AE\", \"ar-BH\", \"ar-DJ\", \"ar-DZ\", \"ar-EG\", \"ar-EH\", \"ar-ER\", \"ar-IL\", \"ar-IQ\", \"ar-JO\", \"ar-KM\", \"ar-KW\", \"ar-LB\", \"ar-LY\", \"ar-MA\", \"ar-MR\", \"ar-OM\", \"ar-PS\", \"ar-QA\", \"ar-SA\", \"ar-SD\", \"ar-SO\", \"ar-SS\", \"ar-SY\", \"ar-TD\", \"ar-TN\", \"ar-YE\", \"as-IN\", \"az-Cyrl\", \"az-Cyrl-AZ\", \"az-Latn\", \"az-Latn-AZ\", \"be-BY\", \"bg-BG\", \"bm-Latn\", \"bm-Latn-ML\", \"bn-BD\", \"bn-IN\", \"bo-CN\", \"bo-IN\", \"br-FR\", \"bs-Cyrl\", \"bs-Cyrl-BA\", \"bs-Latn\", \"bs-Latn-BA\", \"ca-AD\", \"ca-ES\", \"ca-ES-VALENCIA\", \"ca-FR\", \"ca-IT\", \"cs-CZ\", \"cy-GB\", \"da-DK\", \"da-GL\", \"de-AT\", \"de-BE\", \"de-CH\", \"de-DE\", \"de-LI\", \"de-LU\", \"dz-BT\", \"ee-GH\", \"ee-TG\", \"el-CY\", \"el-GR\", \"en-001\", \"en-150\", \"en-AG\", \"en-AI\", \"en-AS\", \"en-AU\", \"en-BB\", \"en-BE\", \"en-BM\", \"en-BS\", \"en-BW\", \"en-BZ\", \"en-CA\", \"en-CC\", \"en-CK\", \"en-CM\", \"en-CX\", \"en-DG\", \"en-DM\", \"en-ER\", \"en-FJ\", \"en-FK\", \"en-FM\", \"en-GB\", \"en-GD\", \"en-GG\", \"en-GH\", \"en-GI\", \"en-GM\", \"en-GU\", \"en-GY\", \"en-HK\", \"en-IE\", \"en-IM\", \"en-IN\", \"en-IO\", \"en-JE\", \"en-JM\", \"en-KE\", \"en-KI\", \"en-KN\", \"en-KY\", \"en-LC\", \"en-LR\", \"en-LS\", \"en-MG\", \"en-MH\", \"en-MO\", \"en-MP\", \"en-MS\", \"en-MT\", \"en-MU\", \"en-MW\", \"en-MY\", \"en-NA\", \"en-NF\", \"en-NG\", \"en-NR\", \"en-NU\", \"en-NZ\", \"en-PG\", \"en-PH\", \"en-PK\", \"en-PN\", \"en-PR\", \"en-PW\", \"en-RW\", \"en-SB\", \"en-SC\", \"en-SD\", \"en-SG\", \"en-SH\", \"en-SL\", \"en-SS\", \"en-SX\", \"en-SZ\", \"en-TC\", \"en-TK\", \"en-TO\", \"en-TT\", \"en-TV\", \"en-TZ\", \"en-UG\", \"en-UM\", \"en-US\", \"en-US-POSIX\", \"en-VC\", \"en-VG\", \"en-VI\", \"en-VU\", \"en-WS\", \"en-ZA\", \"en-ZM\", \"en-ZW\", \"eo-001\", \"es-419\", \"es-AR\", \"es-BO\", \"es-CL\", \"es-CO\", \"es-CR\", \"es-CU\", \"es-DO\", \"es-EA\", \"es-EC\", \"es-ES\", \"es-GQ\", \"es-GT\", \"es-HN\", \"es-IC\", \"es-MX\", \"es-NI\", \"es-PA\", \"es-PE\", \"es-PH\", \"es-PR\", \"es-PY\", \"es-SV\", \"es-US\", \"es-UY\", \"es-VE\", \"et-EE\", \"eu-ES\", \"fa-AF\", \"fa-IR\", \"ff-CM\", \"ff-GN\", \"ff-MR\", \"ff-SN\", \"fi-FI\", \"fo-FO\", \"fr-BE\", \"fr-BF\", \"fr-BI\", \"fr-BJ\", \"fr-BL\", \"fr-CA\", \"fr-CD\", \"fr-CF\", \"fr-CG\", \"fr-CH\", \"fr-CI\", \"fr-CM\", \"fr-DJ\", \"fr-DZ\", \"fr-FR\", \"fr-GA\", \"fr-GF\", \"fr-GN\", \"fr-GP\", \"fr-GQ\", \"fr-HT\", \"fr-KM\", \"fr-LU\", \"fr-MA\", \"fr-MC\", \"fr-MF\", \"fr-MG\", \"fr-ML\", \"fr-MQ\", \"fr-MR\", \"fr-MU\", \"fr-NC\", \"fr-NE\", \"fr-PF\", \"fr-PM\", \"fr-RE\", \"fr-RW\", \"fr-SC\", \"fr-SN\", \"fr-SY\", \"fr-TD\", \"fr-TG\", \"fr-TN\", \"fr-VU\", \"fr-WF\", \"fr-YT\", \"fy-NL\", \"ga-IE\", \"gd-GB\", \"gl-ES\", \"gu-IN\", \"gv-IM\", \"ha-Latn\", \"ha-Latn-GH\", \"ha-Latn-NE\", \"ha-Latn-NG\", \"he-IL\", \"hi-IN\", \"hr-BA\", \"hr-HR\", \"hu-HU\", \"hy-AM\", \"id-ID\", \"ig-NG\", \"ii-CN\", \"is-IS\", \"it-CH\", \"it-IT\", \"it-SM\", \"ja-JP\", \"ka-GE\", \"ki-KE\", \"kk-Cyrl\", \"kk-Cyrl-KZ\", \"kl-GL\", \"km-KH\", \"kn-IN\", \"ko-KP\", \"ko-KR\", \"ks-Arab\", \"ks-Arab-IN\", \"kw-GB\", \"ky-Cyrl\", \"ky-Cyrl-KG\", \"lb-LU\", \"lg-UG\", \"ln-AO\", \"ln-CD\", \"ln-CF\", \"ln-CG\", \"lo-LA\", \"lt-LT\", \"lu-CD\", \"lv-LV\", \"mg-MG\", \"mk-MK\", \"ml-IN\", \"mn-Cyrl\", \"mn-Cyrl-MN\", \"mr-IN\", \"ms-Latn\", \"ms-Latn-BN\", \"ms-Latn-MY\", \"ms-Latn-SG\", \"mt-MT\", \"my-MM\", \"nb-NO\", \"nb-SJ\", \"nd-ZW\", \"ne-IN\", \"ne-NP\", \"nl-AW\", \"nl-BE\", \"nl-BQ\", \"nl-CW\", \"nl-NL\", \"nl-SR\", \"nl-SX\", \"nn-NO\", \"om-ET\", \"om-KE\", \"or-IN\", \"os-GE\", \"os-RU\", \"pa-Arab\", \"pa-Arab-PK\", \"pa-Guru\", \"pa-Guru-IN\", \"pl-PL\", \"ps-AF\", \"pt-AO\", \"pt-BR\", \"pt-CV\", \"pt-GW\", \"pt-MO\", \"pt-MZ\", \"pt-PT\", \"pt-ST\", \"pt-TL\", \"qu-BO\", \"qu-EC\", \"qu-PE\", \"rm-CH\", \"rn-BI\", \"ro-MD\", \"ro-RO\", \"ru-BY\", \"ru-KG\", \"ru-KZ\", \"ru-MD\", \"ru-RU\", \"ru-UA\", \"rw-RW\", \"se-FI\", \"se-NO\", \"se-SE\", \"sg-CF\", \"si-LK\", \"sk-SK\", \"sl-SI\", \"sn-ZW\", \"so-DJ\", \"so-ET\", \"so-KE\", \"so-SO\", \"sq-AL\", \"sq-MK\", \"sq-XK\", \"sr-Cyrl\", \"sr-Cyrl-BA\", \"sr-Cyrl-ME\", \"sr-Cyrl-RS\", \"sr-Cyrl-XK\", \"sr-Latn\", \"sr-Latn-BA\", \"sr-Latn-ME\", \"sr-Latn-RS\", \"sr-Latn-XK\", \"sv-AX\", \"sv-FI\", \"sv-SE\", \"sw-CD\", \"sw-KE\", \"sw-TZ\", \"sw-UG\", \"ta-IN\", \"ta-LK\", \"ta-MY\", \"ta-SG\", \"te-IN\", \"th-TH\", \"ti-ER\", \"ti-ET\", \"to-TO\", \"tr-CY\", \"tr-TR\", \"ug-Arab\", \"ug-Arab-CN\", \"uk-UA\", \"ur-IN\", \"ur-PK\", \"uz-Arab\", \"uz-Arab-AF\", \"uz-Cyrl\", \"uz-Cyrl-UZ\", \"uz-Latn\", \"uz-Latn-UZ\", \"vi-VN\", \"yi-001\", \"yo-BJ\", \"yo-NG\", \"zh-Hans\", \"zh-Hans-CN\", \"zh-Hans-HK\", \"zh-Hans-MO\", \"zh-Hans-SG\", \"zh-Hant\", \"zh-Hant-HK\", \"zh-Hant-MO\", \"zh-Hant-TW\", \"zu-ZA\"],\r\n\r\n\t\tus_states_and_dc: [\r\n\t\t\t{ name: \"Alabama\", abbreviation: \"AL\" },\r\n\t\t\t{ name: \"Alaska\", abbreviation: \"AK\" },\r\n\t\t\t{ name: \"Arizona\", abbreviation: \"AZ\" },\r\n\t\t\t{ name: \"Arkansas\", abbreviation: \"AR\" },\r\n\t\t\t{ name: \"California\", abbreviation: \"CA\" },\r\n\t\t\t{ name: \"Colorado\", abbreviation: \"CO\" },\r\n\t\t\t{ name: \"Connecticut\", abbreviation: \"CT\" },\r\n\t\t\t{ name: \"Delaware\", abbreviation: \"DE\" },\r\n\t\t\t{ name: \"District of Columbia\", abbreviation: \"DC\" },\r\n\t\t\t{ name: \"Florida\", abbreviation: \"FL\" },\r\n\t\t\t{ name: \"Georgia\", abbreviation: \"GA\" },\r\n\t\t\t{ name: \"Hawaii\", abbreviation: \"HI\" },\r\n\t\t\t{ name: \"Idaho\", abbreviation: \"ID\" },\r\n\t\t\t{ name: \"Illinois\", abbreviation: \"IL\" },\r\n\t\t\t{ name: \"Indiana\", abbreviation: \"IN\" },\r\n\t\t\t{ name: \"Iowa\", abbreviation: \"IA\" },\r\n\t\t\t{ name: \"Kansas\", abbreviation: \"KS\" },\r\n\t\t\t{ name: \"Kentucky\", abbreviation: \"KY\" },\r\n\t\t\t{ name: \"Louisiana\", abbreviation: \"LA\" },\r\n\t\t\t{ name: \"Maine\", abbreviation: \"ME\" },\r\n\t\t\t{ name: \"Maryland\", abbreviation: \"MD\" },\r\n\t\t\t{ name: \"Massachusetts\", abbreviation: \"MA\" },\r\n\t\t\t{ name: \"Michigan\", abbreviation: \"MI\" },\r\n\t\t\t{ name: \"Minnesota\", abbreviation: \"MN\" },\r\n\t\t\t{ name: \"Mississippi\", abbreviation: \"MS\" },\r\n\t\t\t{ name: \"Missouri\", abbreviation: \"MO\" },\r\n\t\t\t{ name: \"Montana\", abbreviation: \"MT\" },\r\n\t\t\t{ name: \"Nebraska\", abbreviation: \"NE\" },\r\n\t\t\t{ name: \"Nevada\", abbreviation: \"NV\" },\r\n\t\t\t{ name: \"New Hampshire\", abbreviation: \"NH\" },\r\n\t\t\t{ name: \"New Jersey\", abbreviation: \"NJ\" },\r\n\t\t\t{ name: \"New Mexico\", abbreviation: \"NM\" },\r\n\t\t\t{ name: \"New York\", abbreviation: \"NY\" },\r\n\t\t\t{ name: \"North Carolina\", abbreviation: \"NC\" },\r\n\t\t\t{ name: \"North Dakota\", abbreviation: \"ND\" },\r\n\t\t\t{ name: \"Ohio\", abbreviation: \"OH\" },\r\n\t\t\t{ name: \"Oklahoma\", abbreviation: \"OK\" },\r\n\t\t\t{ name: \"Oregon\", abbreviation: \"OR\" },\r\n\t\t\t{ name: \"Pennsylvania\", abbreviation: \"PA\" },\r\n\t\t\t{ name: \"Rhode Island\", abbreviation: \"RI\" },\r\n\t\t\t{ name: \"South Carolina\", abbreviation: \"SC\" },\r\n\t\t\t{ name: \"South Dakota\", abbreviation: \"SD\" },\r\n\t\t\t{ name: \"Tennessee\", abbreviation: \"TN\" },\r\n\t\t\t{ name: \"Texas\", abbreviation: \"TX\" },\r\n\t\t\t{ name: \"Utah\", abbreviation: \"UT\" },\r\n\t\t\t{ name: \"Vermont\", abbreviation: \"VT\" },\r\n\t\t\t{ name: \"Virginia\", abbreviation: \"VA\" },\r\n\t\t\t{ name: \"Washington\", abbreviation: \"WA\" },\r\n\t\t\t{ name: \"West Virginia\", abbreviation: \"WV\" },\r\n\t\t\t{ name: \"Wisconsin\", abbreviation: \"WI\" },\r\n\t\t\t{ name: \"Wyoming\", abbreviation: \"WY\" },\r\n\t\t],\r\n\r\n\t\tterritories: [\r\n\t\t\t{ name: \"American Samoa\", abbreviation: \"AS\" },\r\n\t\t\t{ name: \"Federated States of Micronesia\", abbreviation: \"FM\" },\r\n\t\t\t{ name: \"Guam\", abbreviation: \"GU\" },\r\n\t\t\t{ name: \"Marshall Islands\", abbreviation: \"MH\" },\r\n\t\t\t{ name: \"Northern Mariana Islands\", abbreviation: \"MP\" },\r\n\t\t\t{ name: \"Puerto Rico\", abbreviation: \"PR\" },\r\n\t\t\t{ name: \"Virgin Islands, U.S.\", abbreviation: \"VI\" },\r\n\t\t],\r\n\r\n\t\tarmed_forces: [\r\n\t\t\t{ name: \"Armed Forces Europe\", abbreviation: \"AE\" },\r\n\t\t\t{ name: \"Armed Forces Pacific\", abbreviation: \"AP\" },\r\n\t\t\t{ name: \"Armed Forces the Americas\", abbreviation: \"AA\" },\r\n\t\t],\r\n\r\n\t\tcountry_regions: {\r\n\t\t\tit: [\r\n\t\t\t\t{ name: \"Valle d'Aosta\", abbreviation: \"VDA\" },\r\n\t\t\t\t{ name: \"Piemonte\", abbreviation: \"PIE\" },\r\n\t\t\t\t{ name: \"Lombardia\", abbreviation: \"LOM\" },\r\n\t\t\t\t{ name: \"Veneto\", abbreviation: \"VEN\" },\r\n\t\t\t\t{ name: \"Trentino Alto Adige\", abbreviation: \"TAA\" },\r\n\t\t\t\t{ name: \"Friuli Venezia Giulia\", abbreviation: \"FVG\" },\r\n\t\t\t\t{ name: \"Liguria\", abbreviation: \"LIG\" },\r\n\t\t\t\t{ name: \"Emilia Romagna\", abbreviation: \"EMR\" },\r\n\t\t\t\t{ name: \"Toscana\", abbreviation: \"TOS\" },\r\n\t\t\t\t{ name: \"Umbria\", abbreviation: \"UMB\" },\r\n\t\t\t\t{ name: \"Marche\", abbreviation: \"MAR\" },\r\n\t\t\t\t{ name: \"Abruzzo\", abbreviation: \"ABR\" },\r\n\t\t\t\t{ name: \"Lazio\", abbreviation: \"LAZ\" },\r\n\t\t\t\t{ name: \"Campania\", abbreviation: \"CAM\" },\r\n\t\t\t\t{ name: \"Puglia\", abbreviation: \"PUG\" },\r\n\t\t\t\t{ name: \"Basilicata\", abbreviation: \"BAS\" },\r\n\t\t\t\t{ name: \"Molise\", abbreviation: \"MOL\" },\r\n\t\t\t\t{ name: \"Calabria\", abbreviation: \"CAL\" },\r\n\t\t\t\t{ name: \"Sicilia\", abbreviation: \"SIC\" },\r\n\t\t\t\t{ name: \"Sardegna\", abbreviation: \"SAR\" },\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\tstreet_suffixes: {\r\n\t\t\tus: [\r\n\t\t\t\t{ name: \"Avenue\", abbreviation: \"Ave\" },\r\n\t\t\t\t{ name: \"Boulevard\", abbreviation: \"Blvd\" },\r\n\t\t\t\t{ name: \"Center\", abbreviation: \"Ctr\" },\r\n\t\t\t\t{ name: \"Circle\", abbreviation: \"Cir\" },\r\n\t\t\t\t{ name: \"Court\", abbreviation: \"Ct\" },\r\n\t\t\t\t{ name: \"Drive\", abbreviation: \"Dr\" },\r\n\t\t\t\t{ name: \"Extension\", abbreviation: \"Ext\" },\r\n\t\t\t\t{ name: \"Glen\", abbreviation: \"Gln\" },\r\n\t\t\t\t{ name: \"Grove\", abbreviation: \"Grv\" },\r\n\t\t\t\t{ name: \"Heights\", abbreviation: \"Hts\" },\r\n\t\t\t\t{ name: \"Highway\", abbreviation: \"Hwy\" },\r\n\t\t\t\t{ name: \"Junction\", abbreviation: \"Jct\" },\r\n\t\t\t\t{ name: \"Key\", abbreviation: \"Key\" },\r\n\t\t\t\t{ name: \"Lane\", abbreviation: \"Ln\" },\r\n\t\t\t\t{ name: \"Loop\", abbreviation: \"Loop\" },\r\n\t\t\t\t{ name: \"Manor\", abbreviation: \"Mnr\" },\r\n\t\t\t\t{ name: \"Mill\", abbreviation: \"Mill\" },\r\n\t\t\t\t{ name: \"Park\", abbreviation: \"Park\" },\r\n\t\t\t\t{ name: \"Parkway\", abbreviation: \"Pkwy\" },\r\n\t\t\t\t{ name: \"Pass\", abbreviation: \"Pass\" },\r\n\t\t\t\t{ name: \"Path\", abbreviation: \"Path\" },\r\n\t\t\t\t{ name: \"Pike\", abbreviation: \"Pike\" },\r\n\t\t\t\t{ name: \"Place\", abbreviation: \"Pl\" },\r\n\t\t\t\t{ name: \"Plaza\", abbreviation: \"Plz\" },\r\n\t\t\t\t{ name: \"Point\", abbreviation: \"Pt\" },\r\n\t\t\t\t{ name: \"Ridge\", abbreviation: \"Rdg\" },\r\n\t\t\t\t{ name: \"River\", abbreviation: \"Riv\" },\r\n\t\t\t\t{ name: \"Road\", abbreviation: \"Rd\" },\r\n\t\t\t\t{ name: \"Square\", abbreviation: \"Sq\" },\r\n\t\t\t\t{ name: \"Street\", abbreviation: \"St\" },\r\n\t\t\t\t{ name: \"Terrace\", abbreviation: \"Ter\" },\r\n\t\t\t\t{ name: \"Trail\", abbreviation: \"Trl\" },\r\n\t\t\t\t{ name: \"Turnpike\", abbreviation: \"Tpke\" },\r\n\t\t\t\t{ name: \"View\", abbreviation: \"Vw\" },\r\n\t\t\t\t{ name: \"Way\", abbreviation: \"Way\" },\r\n\t\t\t],\r\n\t\t\tit: [\r\n\t\t\t\t{ name: \"Accesso\", abbreviation: \"Acc.\" },\r\n\t\t\t\t{ name: \"Alzaia\", abbreviation: \"Alz.\" },\r\n\t\t\t\t{ name: \"Arco\", abbreviation: \"Arco\" },\r\n\t\t\t\t{ name: \"Archivolto\", abbreviation: \"Acv.\" },\r\n\t\t\t\t{ name: \"Arena\", abbreviation: \"Arena\" },\r\n\t\t\t\t{ name: \"Argine\", abbreviation: \"Argine\" },\r\n\t\t\t\t{ name: \"Bacino\", abbreviation: \"Bacino\" },\r\n\t\t\t\t{ name: \"Banchi\", abbreviation: \"Banchi\" },\r\n\t\t\t\t{ name: \"Banchina\", abbreviation: \"Ban.\" },\r\n\t\t\t\t{ name: \"Bastioni\", abbreviation: \"Bas.\" },\r\n\t\t\t\t{ name: \"Belvedere\", abbreviation: \"Belv.\" },\r\n\t\t\t\t{ name: \"Borgata\", abbreviation: \"B.ta\" },\r\n\t\t\t\t{ name: \"Borgo\", abbreviation: \"B.go\" },\r\n\t\t\t\t{ name: \"Calata\", abbreviation: \"Cal.\" },\r\n\t\t\t\t{ name: \"Calle\", abbreviation: \"Calle\" },\r\n\t\t\t\t{ name: \"Campiello\", abbreviation: \"Cam.\" },\r\n\t\t\t\t{ name: \"Campo\", abbreviation: \"Cam.\" },\r\n\t\t\t\t{ name: \"Canale\", abbreviation: \"Can.\" },\r\n\t\t\t\t{ name: \"Carraia\", abbreviation: \"Carr.\" },\r\n\t\t\t\t{ name: \"Cascina\", abbreviation: \"Cascina\" },\r\n\t\t\t\t{ name: \"Case sparse\", abbreviation: \"c.s.\" },\r\n\t\t\t\t{ name: \"Cavalcavia\", abbreviation: \"Cv.\" },\r\n\t\t\t\t{ name: \"Circonvallazione\", abbreviation: \"Cv.\" },\r\n\t\t\t\t{ name: \"Complanare\", abbreviation: \"C.re\" },\r\n\t\t\t\t{ name: \"Contrada\", abbreviation: \"C.da\" },\r\n\t\t\t\t{ name: \"Corso\", abbreviation: \"C.so\" },\r\n\t\t\t\t{ name: \"Corte\", abbreviation: \"C.te\" },\r\n\t\t\t\t{ name: \"Cortile\", abbreviation: \"C.le\" },\r\n\t\t\t\t{ name: \"Diramazione\", abbreviation: \"Dir.\" },\r\n\t\t\t\t{ name: \"Fondaco\", abbreviation: \"F.co\" },\r\n\t\t\t\t{ name: \"Fondamenta\", abbreviation: \"F.ta\" },\r\n\t\t\t\t{ name: \"Fondo\", abbreviation: \"F.do\" },\r\n\t\t\t\t{ name: \"Frazione\", abbreviation: \"Fr.\" },\r\n\t\t\t\t{ name: \"Isola\", abbreviation: \"Is.\" },\r\n\t\t\t\t{ name: \"Largo\", abbreviation: \"L.go\" },\r\n\t\t\t\t{ name: \"Litoranea\", abbreviation: \"Lit.\" },\r\n\t\t\t\t{ name: \"Lungolago\", abbreviation: \"L.go lago\" },\r\n\t\t\t\t{ name: \"Lungo Po\", abbreviation: \"l.go Po\" },\r\n\t\t\t\t{ name: \"Molo\", abbreviation: \"Molo\" },\r\n\t\t\t\t{ name: \"Mura\", abbreviation: \"Mura\" },\r\n\t\t\t\t{ name: \"Passaggio privato\", abbreviation: \"pass. priv.\" },\r\n\t\t\t\t{ name: \"Passeggiata\", abbreviation: \"Pass.\" },\r\n\t\t\t\t{ name: \"Piazza\", abbreviation: \"P.zza\" },\r\n\t\t\t\t{ name: \"Piazzale\", abbreviation: \"P.le\" },\r\n\t\t\t\t{ name: \"Ponte\", abbreviation: \"P.te\" },\r\n\t\t\t\t{ name: \"Portico\", abbreviation: \"P.co\" },\r\n\t\t\t\t{ name: \"Rampa\", abbreviation: \"Rampa\" },\r\n\t\t\t\t{ name: \"Regione\", abbreviation: \"Reg.\" },\r\n\t\t\t\t{ name: \"Rione\", abbreviation: \"R.ne\" },\r\n\t\t\t\t{ name: \"Rio\", abbreviation: \"Rio\" },\r\n\t\t\t\t{ name: \"Ripa\", abbreviation: \"Ripa\" },\r\n\t\t\t\t{ name: \"Riva\", abbreviation: \"Riva\" },\r\n\t\t\t\t{ name: \"Rondò\", abbreviation: \"Rondò\" },\r\n\t\t\t\t{ name: \"Rotonda\", abbreviation: \"Rot.\" },\r\n\t\t\t\t{ name: \"Sagrato\", abbreviation: \"Sagr.\" },\r\n\t\t\t\t{ name: \"Salita\", abbreviation: \"Sal.\" },\r\n\t\t\t\t{ name: \"Scalinata\", abbreviation: \"Scal.\" },\r\n\t\t\t\t{ name: \"Scalone\", abbreviation: \"Scal.\" },\r\n\t\t\t\t{ name: \"Slargo\", abbreviation: \"Sl.\" },\r\n\t\t\t\t{ name: \"Sottoportico\", abbreviation: \"Sott.\" },\r\n\t\t\t\t{ name: \"Strada\", abbreviation: \"Str.\" },\r\n\t\t\t\t{ name: \"Stradale\", abbreviation: \"Str.le\" },\r\n\t\t\t\t{ name: \"Strettoia\", abbreviation: \"Strett.\" },\r\n\t\t\t\t{ name: \"Traversa\", abbreviation: \"Trav.\" },\r\n\t\t\t\t{ name: \"Via\", abbreviation: \"V.\" },\r\n\t\t\t\t{ name: \"Viale\", abbreviation: \"V.le\" },\r\n\t\t\t\t{ name: \"Vicinale\", abbreviation: \"Vic.le\" },\r\n\t\t\t\t{ name: \"Vicolo\", abbreviation: \"Vic.\" },\r\n\t\t\t],\r\n\t\t\tuk: [\r\n\t\t\t\t{ name: \"Avenue\", abbreviation: \"Ave\" },\r\n\t\t\t\t{ name: \"Close\", abbreviation: \"Cl\" },\r\n\t\t\t\t{ name: \"Court\", abbreviation: \"Ct\" },\r\n\t\t\t\t{ name: \"Crescent\", abbreviation: \"Cr\" },\r\n\t\t\t\t{ name: \"Drive\", abbreviation: \"Dr\" },\r\n\t\t\t\t{ name: \"Garden\", abbreviation: \"Gdn\" },\r\n\t\t\t\t{ name: \"Gardens\", abbreviation: \"Gdns\" },\r\n\t\t\t\t{ name: \"Green\", abbreviation: \"Gn\" },\r\n\t\t\t\t{ name: \"Grove\", abbreviation: \"Gr\" },\r\n\t\t\t\t{ name: \"Lane\", abbreviation: \"Ln\" },\r\n\t\t\t\t{ name: \"Mount\", abbreviation: \"Mt\" },\r\n\t\t\t\t{ name: \"Place\", abbreviation: \"Pl\" },\r\n\t\t\t\t{ name: \"Park\", abbreviation: \"Pk\" },\r\n\t\t\t\t{ name: \"Ridge\", abbreviation: \"Rdg\" },\r\n\t\t\t\t{ name: \"Road\", abbreviation: \"Rd\" },\r\n\t\t\t\t{ name: \"Square\", abbreviation: \"Sq\" },\r\n\t\t\t\t{ name: \"Street\", abbreviation: \"St\" },\r\n\t\t\t\t{ name: \"Terrace\", abbreviation: \"Ter\" },\r\n\t\t\t\t{ name: \"Valley\", abbreviation: \"Val\" },\r\n\t\t\t],\r\n\t\t},\r\n\r\n\t\tmonths: [\r\n\t\t\t{ name: \"January\", short_name: \"Jan\", numeric: \"01\", days: 31 },\r\n\t\t\t// Not messing with leap years...\r\n\t\t\t{ name: \"February\", short_name: \"Feb\", numeric: \"02\", days: 28 },\r\n\t\t\t{ name: \"March\", short_name: \"Mar\", numeric: \"03\", days: 31 },\r\n\t\t\t{ name: \"April\", short_name: \"Apr\", numeric: \"04\", days: 30 },\r\n\t\t\t{ name: \"May\", short_name: \"May\", numeric: \"05\", days: 31 },\r\n\t\t\t{ name: \"June\", short_name: \"Jun\", numeric: \"06\", days: 30 },\r\n\t\t\t{ name: \"July\", short_name: \"Jul\", numeric: \"07\", days: 31 },\r\n\t\t\t{ name: \"August\", short_name: \"Aug\", numeric: \"08\", days: 31 },\r\n\t\t\t{ name: \"September\", short_name: \"Sep\", numeric: \"09\", days: 30 },\r\n\t\t\t{ name: \"October\", short_name: \"Oct\", numeric: \"10\", days: 31 },\r\n\t\t\t{ name: \"November\", short_name: \"Nov\", numeric: \"11\", days: 30 },\r\n\t\t\t{ name: \"December\", short_name: \"Dec\", numeric: \"12\", days: 31 },\r\n\t\t],\r\n\r\n\t\t// http://en.wikipedia.org/wiki/Bank_card_number#Issuer_identification_number_.28IIN.29\r\n\t\tcc_types: [\r\n\t\t\t{ name: \"American Express\", short_name: \"amex\", prefix: \"34\", length: 15 },\r\n\t\t\t{ name: \"Bankcard\", short_name: \"bankcard\", prefix: \"5610\", length: 16 },\r\n\t\t\t{ name: \"China UnionPay\", short_name: \"chinaunion\", prefix: \"62\", length: 16 },\r\n\t\t\t{ name: \"Diners Club Carte Blanche\", short_name: \"dccarte\", prefix: \"300\", length: 14 },\r\n\t\t\t{ name: \"Diners Club enRoute\", short_name: \"dcenroute\", prefix: \"2014\", length: 15 },\r\n\t\t\t{ name: \"Diners Club International\", short_name: \"dcintl\", prefix: \"36\", length: 14 },\r\n\t\t\t{ name: \"Diners Club United States & Canada\", short_name: \"dcusc\", prefix: \"54\", length: 16 },\r\n\t\t\t{ name: \"Discover Card\", short_name: \"discover\", prefix: \"6011\", length: 16 },\r\n\t\t\t{ name: \"InstaPayment\", short_name: \"instapay\", prefix: \"637\", length: 16 },\r\n\t\t\t{ name: \"JCB\", short_name: \"jcb\", prefix: \"3528\", length: 16 },\r\n\t\t\t{ name: \"Laser\", short_name: \"laser\", prefix: \"6304\", length: 16 },\r\n\t\t\t{ name: \"Maestro\", short_name: \"maestro\", prefix: \"5018\", length: 16 },\r\n\t\t\t{ name: \"Mastercard\", short_name: \"mc\", prefix: \"51\", length: 16 },\r\n\t\t\t{ name: \"Solo\", short_name: \"solo\", prefix: \"6334\", length: 16 },\r\n\t\t\t{ name: \"Switch\", short_name: \"switch\", prefix: \"4903\", length: 16 },\r\n\t\t\t{ name: \"Visa\", short_name: \"visa\", prefix: \"4\", length: 16 },\r\n\t\t\t{ name: \"Visa Electron\", short_name: \"electron\", prefix: \"4026\", length: 16 },\r\n\t\t],\r\n\r\n\t\t//return all world currency by ISO 4217\r\n\t\tcurrency_types: [\r\n\t\t\t{ code: \"AED\", name: \"United Arab Emirates Dirham\" },\r\n\t\t\t{ code: \"AFN\", name: \"Afghanistan Afghani\" },\r\n\t\t\t{ code: \"ALL\", name: \"Albania Lek\" },\r\n\t\t\t{ code: \"AMD\", name: \"Armenia Dram\" },\r\n\t\t\t{ code: \"ANG\", name: \"Netherlands Antilles Guilder\" },\r\n\t\t\t{ code: \"AOA\", name: \"Angola Kwanza\" },\r\n\t\t\t{ code: \"ARS\", name: \"Argentina Peso\" },\r\n\t\t\t{ code: \"AUD\", name: \"Australia Dollar\" },\r\n\t\t\t{ code: \"AWG\", name: \"Aruba Guilder\" },\r\n\t\t\t{ code: \"AZN\", name: \"Azerbaijan New Manat\" },\r\n\t\t\t{ code: \"BAM\", name: \"Bosnia and Herzegovina Convertible Marka\" },\r\n\t\t\t{ code: \"BBD\", name: \"Barbados Dollar\" },\r\n\t\t\t{ code: \"BDT\", name: \"Bangladesh Taka\" },\r\n\t\t\t{ code: \"BGN\", name: \"Bulgaria Lev\" },\r\n\t\t\t{ code: \"BHD\", name: \"Bahrain Dinar\" },\r\n\t\t\t{ code: \"BIF\", name: \"Burundi Franc\" },\r\n\t\t\t{ code: \"BMD\", name: \"Bermuda Dollar\" },\r\n\t\t\t{ code: \"BND\", name: \"Brunei Darussalam Dollar\" },\r\n\t\t\t{ code: \"BOB\", name: \"Bolivia Boliviano\" },\r\n\t\t\t{ code: \"BRL\", name: \"Brazil Real\" },\r\n\t\t\t{ code: \"BSD\", name: \"Bahamas Dollar\" },\r\n\t\t\t{ code: \"BTN\", name: \"Bhutan Ngultrum\" },\r\n\t\t\t{ code: \"BWP\", name: \"Botswana Pula\" },\r\n\t\t\t{ code: \"BYR\", name: \"Belarus Ruble\" },\r\n\t\t\t{ code: \"BZD\", name: \"Belize Dollar\" },\r\n\t\t\t{ code: \"CAD\", name: \"Canada Dollar\" },\r\n\t\t\t{ code: \"CDF\", name: \"Congo/Kinshasa Franc\" },\r\n\t\t\t{ code: \"CHF\", name: \"Switzerland Franc\" },\r\n\t\t\t{ code: \"CLP\", name: \"Chile Peso\" },\r\n\t\t\t{ code: \"CNY\", name: \"China Yuan Renminbi\" },\r\n\t\t\t{ code: \"COP\", name: \"Colombia Peso\" },\r\n\t\t\t{ code: \"CRC\", name: \"Costa Rica Colon\" },\r\n\t\t\t{ code: \"CUC\", name: \"Cuba Convertible Peso\" },\r\n\t\t\t{ code: \"CUP\", name: \"Cuba Peso\" },\r\n\t\t\t{ code: \"CVE\", name: \"Cape Verde Escudo\" },\r\n\t\t\t{ code: \"CZK\", name: \"Czech Republic Koruna\" },\r\n\t\t\t{ code: \"DJF\", name: \"Djibouti Franc\" },\r\n\t\t\t{ code: \"DKK\", name: \"Denmark Krone\" },\r\n\t\t\t{ code: \"DOP\", name: \"Dominican Republic Peso\" },\r\n\t\t\t{ code: \"DZD\", name: \"Algeria Dinar\" },\r\n\t\t\t{ code: \"EGP\", name: \"Egypt Pound\" },\r\n\t\t\t{ code: \"ERN\", name: \"Eritrea Nakfa\" },\r\n\t\t\t{ code: \"ETB\", name: \"Ethiopia Birr\" },\r\n\t\t\t{ code: \"EUR\", name: \"Euro Member Countries\" },\r\n\t\t\t{ code: \"FJD\", name: \"Fiji Dollar\" },\r\n\t\t\t{ code: \"FKP\", name: \"Falkland Islands (Malvinas) Pound\" },\r\n\t\t\t{ code: \"GBP\", name: \"United Kingdom Pound\" },\r\n\t\t\t{ code: \"GEL\", name: \"Georgia Lari\" },\r\n\t\t\t{ code: \"GGP\", name: \"Guernsey Pound\" },\r\n\t\t\t{ code: \"GHS\", name: \"Ghana Cedi\" },\r\n\t\t\t{ code: \"GIP\", name: \"Gibraltar Pound\" },\r\n\t\t\t{ code: \"GMD\", name: \"Gambia Dalasi\" },\r\n\t\t\t{ code: \"GNF\", name: \"Guinea Franc\" },\r\n\t\t\t{ code: \"GTQ\", name: \"Guatemala Quetzal\" },\r\n\t\t\t{ code: \"GYD\", name: \"Guyana Dollar\" },\r\n\t\t\t{ code: \"HKD\", name: \"Hong Kong Dollar\" },\r\n\t\t\t{ code: \"HNL\", name: \"Honduras Lempira\" },\r\n\t\t\t{ code: \"HRK\", name: \"Croatia Kuna\" },\r\n\t\t\t{ code: \"HTG\", name: \"Haiti Gourde\" },\r\n\t\t\t{ code: \"HUF\", name: \"Hungary Forint\" },\r\n\t\t\t{ code: \"IDR\", name: \"Indonesia Rupiah\" },\r\n\t\t\t{ code: \"ILS\", name: \"Israel Shekel\" },\r\n\t\t\t{ code: \"IMP\", name: \"Isle of Man Pound\" },\r\n\t\t\t{ code: \"INR\", name: \"India Rupee\" },\r\n\t\t\t{ code: \"IQD\", name: \"Iraq Dinar\" },\r\n\t\t\t{ code: \"IRR\", name: \"Iran Rial\" },\r\n\t\t\t{ code: \"ISK\", name: \"Iceland Krona\" },\r\n\t\t\t{ code: \"JEP\", name: \"Jersey Pound\" },\r\n\t\t\t{ code: \"JMD\", name: \"Jamaica Dollar\" },\r\n\t\t\t{ code: \"JOD\", name: \"Jordan Dinar\" },\r\n\t\t\t{ code: \"JPY\", name: \"Japan Yen\" },\r\n\t\t\t{ code: \"KES\", name: \"Kenya Shilling\" },\r\n\t\t\t{ code: \"KGS\", name: \"Kyrgyzstan Som\" },\r\n\t\t\t{ code: \"KHR\", name: \"Cambodia Riel\" },\r\n\t\t\t{ code: \"KMF\", name: \"Comoros Franc\" },\r\n\t\t\t{ code: \"KPW\", name: \"Korea (North) Won\" },\r\n\t\t\t{ code: \"KRW\", name: \"Korea (South) Won\" },\r\n\t\t\t{ code: \"KWD\", name: \"Kuwait Dinar\" },\r\n\t\t\t{ code: \"KYD\", name: \"Cayman Islands Dollar\" },\r\n\t\t\t{ code: \"KZT\", name: \"Kazakhstan Tenge\" },\r\n\t\t\t{ code: \"LAK\", name: \"Laos Kip\" },\r\n\t\t\t{ code: \"LBP\", name: \"Lebanon Pound\" },\r\n\t\t\t{ code: \"LKR\", name: \"Sri Lanka Rupee\" },\r\n\t\t\t{ code: \"LRD\", name: \"Liberia Dollar\" },\r\n\t\t\t{ code: \"LSL\", name: \"Lesotho Loti\" },\r\n\t\t\t{ code: \"LTL\", name: \"Lithuania Litas\" },\r\n\t\t\t{ code: \"LYD\", name: \"Libya Dinar\" },\r\n\t\t\t{ code: \"MAD\", name: \"Morocco Dirham\" },\r\n\t\t\t{ code: \"MDL\", name: \"Moldova Leu\" },\r\n\t\t\t{ code: \"MGA\", name: \"Madagascar Ariary\" },\r\n\t\t\t{ code: \"MKD\", name: \"Macedonia Denar\" },\r\n\t\t\t{ code: \"MMK\", name: \"Myanmar (Burma) Kyat\" },\r\n\t\t\t{ code: \"MNT\", name: \"Mongolia Tughrik\" },\r\n\t\t\t{ code: \"MOP\", name: \"Macau Pataca\" },\r\n\t\t\t{ code: \"MRO\", name: \"Mauritania Ouguiya\" },\r\n\t\t\t{ code: \"MUR\", name: \"Mauritius Rupee\" },\r\n\t\t\t{ code: \"MVR\", name: \"Maldives (Maldive Islands) Rufiyaa\" },\r\n\t\t\t{ code: \"MWK\", name: \"Malawi Kwacha\" },\r\n\t\t\t{ code: \"MXN\", name: \"Mexico Peso\" },\r\n\t\t\t{ code: \"MYR\", name: \"Malaysia Ringgit\" },\r\n\t\t\t{ code: \"MZN\", name: \"Mozambique Metical\" },\r\n\t\t\t{ code: \"NAD\", name: \"Namibia Dollar\" },\r\n\t\t\t{ code: \"NGN\", name: \"Nigeria Naira\" },\r\n\t\t\t{ code: \"NIO\", name: \"Nicaragua Cordoba\" },\r\n\t\t\t{ code: \"NOK\", name: \"Norway Krone\" },\r\n\t\t\t{ code: \"NPR\", name: \"Nepal Rupee\" },\r\n\t\t\t{ code: \"NZD\", name: \"New Zealand Dollar\" },\r\n\t\t\t{ code: \"OMR\", name: \"Oman Rial\" },\r\n\t\t\t{ code: \"PAB\", name: \"Panama Balboa\" },\r\n\t\t\t{ code: \"PEN\", name: \"Peru Nuevo Sol\" },\r\n\t\t\t{ code: \"PGK\", name: \"Papua New Guinea Kina\" },\r\n\t\t\t{ code: \"PHP\", name: \"Philippines Peso\" },\r\n\t\t\t{ code: \"PKR\", name: \"Pakistan Rupee\" },\r\n\t\t\t{ code: \"PLN\", name: \"Poland Zloty\" },\r\n\t\t\t{ code: \"PYG\", name: \"Paraguay Guarani\" },\r\n\t\t\t{ code: \"QAR\", name: \"Qatar Riyal\" },\r\n\t\t\t{ code: \"RON\", name: \"Romania New Leu\" },\r\n\t\t\t{ code: \"RSD\", name: \"Serbia Dinar\" },\r\n\t\t\t{ code: \"RUB\", name: \"Russia Ruble\" },\r\n\t\t\t{ code: \"RWF\", name: \"Rwanda Franc\" },\r\n\t\t\t{ code: \"SAR\", name: \"Saudi Arabia Riyal\" },\r\n\t\t\t{ code: \"SBD\", name: \"Solomon Islands Dollar\" },\r\n\t\t\t{ code: \"SCR\", name: \"Seychelles Rupee\" },\r\n\t\t\t{ code: \"SDG\", name: \"Sudan Pound\" },\r\n\t\t\t{ code: \"SEK\", name: \"Sweden Krona\" },\r\n\t\t\t{ code: \"SGD\", name: \"Singapore Dollar\" },\r\n\t\t\t{ code: \"SHP\", name: \"Saint Helena Pound\" },\r\n\t\t\t{ code: \"SLL\", name: \"Sierra Leone Leone\" },\r\n\t\t\t{ code: \"SOS\", name: \"Somalia Shilling\" },\r\n\t\t\t{ code: \"SPL\", name: \"Seborga Luigino\" },\r\n\t\t\t{ code: \"SRD\", name: \"Suriname Dollar\" },\r\n\t\t\t{ code: \"STD\", name: \"São Tomé and Príncipe Dobra\" },\r\n\t\t\t{ code: \"SVC\", name: \"El Salvador Colon\" },\r\n\t\t\t{ code: \"SYP\", name: \"Syria Pound\" },\r\n\t\t\t{ code: \"SZL\", name: \"Swaziland Lilangeni\" },\r\n\t\t\t{ code: \"THB\", name: \"Thailand Baht\" },\r\n\t\t\t{ code: \"TJS\", name: \"Tajikistan Somoni\" },\r\n\t\t\t{ code: \"TMT\", name: \"Turkmenistan Manat\" },\r\n\t\t\t{ code: \"TND\", name: \"Tunisia Dinar\" },\r\n\t\t\t{ code: \"TOP\", name: \"Tonga Pa'anga\" },\r\n\t\t\t{ code: \"TRY\", name: \"Turkey Lira\" },\r\n\t\t\t{ code: \"TTD\", name: \"Trinidad and Tobago Dollar\" },\r\n\t\t\t{ code: \"TVD\", name: \"Tuvalu Dollar\" },\r\n\t\t\t{ code: \"TWD\", name: \"Taiwan New Dollar\" },\r\n\t\t\t{ code: \"TZS\", name: \"Tanzania Shilling\" },\r\n\t\t\t{ code: \"UAH\", name: \"Ukraine Hryvnia\" },\r\n\t\t\t{ code: \"UGX\", name: \"Uganda Shilling\" },\r\n\t\t\t{ code: \"USD\", name: \"United States Dollar\" },\r\n\t\t\t{ code: \"UYU\", name: \"Uruguay Peso\" },\r\n\t\t\t{ code: \"UZS\", name: \"Uzbekistan Som\" },\r\n\t\t\t{ code: \"VEF\", name: \"Venezuela Bolivar\" },\r\n\t\t\t{ code: \"VND\", name: \"Viet Nam Dong\" },\r\n\t\t\t{ code: \"VUV\", name: \"Vanuatu Vatu\" },\r\n\t\t\t{ code: \"WST\", name: \"Samoa Tala\" },\r\n\t\t\t{ code: \"XAF\", name: \"Communauté Financière Africaine (BEAC) CFA Franc BEAC\" },\r\n\t\t\t{ code: \"XCD\", name: \"East Caribbean Dollar\" },\r\n\t\t\t{ code: \"XDR\", name: \"International Monetary Fund (IMF) Special Drawing Rights\" },\r\n\t\t\t{ code: \"XOF\", name: \"Communauté Financière Africaine (BCEAO) Franc\" },\r\n\t\t\t{ code: \"XPF\", name: \"Comptoirs Français du Pacifique (CFP) Franc\" },\r\n\t\t\t{ code: \"YER\", name: \"Yemen Rial\" },\r\n\t\t\t{ code: \"ZAR\", name: \"South Africa Rand\" },\r\n\t\t\t{ code: \"ZMW\", name: \"Zambia Kwacha\" },\r\n\t\t\t{ code: \"ZWD\", name: \"Zimbabwe Dollar\" },\r\n\t\t],\r\n\r\n\t\t// return the names of all valide colors\r\n\t\tcolorNames: [\"AliceBlue\", \"Black\", \"Navy\", \"DarkBlue\", \"MediumBlue\", \"Blue\", \"DarkGreen\", \"Green\", \"Teal\", \"DarkCyan\", \"DeepSkyBlue\", \"DarkTurquoise\", \"MediumSpringGreen\", \"Lime\", \"SpringGreen\", \"Aqua\", \"Cyan\", \"MidnightBlue\", \"DodgerBlue\", \"LightSeaGreen\", \"ForestGreen\", \"SeaGreen\", \"DarkSlateGray\", \"LimeGreen\", \"MediumSeaGreen\", \"Turquoise\", \"RoyalBlue\", \"SteelBlue\", \"DarkSlateBlue\", \"MediumTurquoise\", \"Indigo\", \"DarkOliveGreen\", \"CadetBlue\", \"CornflowerBlue\", \"RebeccaPurple\", \"MediumAquaMarine\", \"DimGray\", \"SlateBlue\", \"OliveDrab\", \"SlateGray\", \"LightSlateGray\", \"MediumSlateBlue\", \"LawnGreen\", \"Chartreuse\", \"Aquamarine\", \"Maroon\", \"Purple\", \"Olive\", \"Gray\", \"SkyBlue\", \"LightSkyBlue\", \"BlueViolet\", \"DarkRed\", \"DarkMagenta\", \"SaddleBrown\", \"Ivory\", \"White\", \"DarkSeaGreen\", \"LightGreen\", \"MediumPurple\", \"DarkViolet\", \"PaleGreen\", \"DarkOrchid\", \"YellowGreen\", \"Sienna\", \"Brown\", \"DarkGray\", \"LightBlue\", \"GreenYellow\", \"PaleTurquoise\", \"LightSteelBlue\", \"PowderBlue\", \"FireBrick\", \"DarkGoldenRod\", \"MediumOrchid\", \"RosyBrown\", \"DarkKhaki\", \"Silver\", \"MediumVioletRed\", \"IndianRed\", \"Peru\", \"Chocolate\", \"Tan\", \"LightGray\", \"Thistle\", \"Orchid\", \"GoldenRod\", \"PaleVioletRed\", \"Crimson\", \"Gainsboro\", \"Plum\", \"BurlyWood\", \"LightCyan\", \"Lavender\", \"DarkSalmon\", \"Violet\", \"PaleGoldenRod\", \"LightCoral\", \"Khaki\", \"AliceBlue\", \"HoneyDew\", \"Azure\", \"SandyBrown\", \"Wheat\", \"Beige\", \"WhiteSmoke\", \"MintCream\", \"GhostWhite\", \"Salmon\", \"AntiqueWhite\", \"Linen\", \"LightGoldenRodYellow\", \"OldLace\", \"Red\", \"Fuchsia\", \"Magenta\", \"DeepPink\", \"OrangeRed\", \"Tomato\", \"HotPink\", \"Coral\", \"DarkOrange\", \"LightSalmon\", \"Orange\", \"LightPink\", \"Pink\", \"Gold\", \"PeachPuff\", \"NavajoWhite\", \"Moccasin\", \"Bisque\", \"MistyRose\", \"BlanchedAlmond\", \"PapayaWhip\", \"LavenderBlush\", \"SeaShell\", \"Cornsilk\", \"LemonChiffon\", \"FloralWhite\", \"Snow\", \"Yellow\", \"LightYellow\"],\r\n\r\n\t\t// Data taken from https://www.sec.gov/rules/other/4-460list.htm\r\n\t\tcompany: [\"3Com Corp\", \"3M Company\", \"A.G. Edwards Inc.\", \"Abbott Laboratories\", \"Abercrombie & Fitch Co.\", \"ABM Industries Incorporated\", \"Ace Hardware Corporation\", \"ACT Manufacturing Inc.\", \"Acterna Corp.\", \"Adams Resources & Energy, Inc.\", \"ADC Telecommunications, Inc.\", \"Adelphia Communications Corporation\", \"Administaff, Inc.\", \"Adobe Systems Incorporated\", \"Adolph Coors Company\", \"Advance Auto Parts, Inc.\", \"Advanced Micro Devices, Inc.\", \"AdvancePCS, Inc.\", \"Advantica Restaurant Group, Inc.\", \"The AES Corporation\", \"Aetna Inc.\", \"Affiliated Computer Services, Inc.\", \"AFLAC Incorporated\", \"AGCO Corporation\", \"Agilent Technologies, Inc.\", \"Agway Inc.\", \"Apartment Investment and Management Company\", \"Air Products and Chemicals, Inc.\", \"Airborne, Inc.\", \"Airgas, Inc.\", \"AK Steel Holding Corporation\", \"Alaska Air Group, Inc.\", \"Alberto-Culver Company\", \"Albertson's, Inc.\", \"Alcoa Inc.\", \"Alleghany Corporation\", \"Allegheny Energy, Inc.\", \"Allegheny Technologies Incorporated\", \"Allergan, Inc.\", \"ALLETE, Inc.\", \"Alliant Energy Corporation\", \"Allied Waste Industries, Inc.\", \"Allmerica Financial Corporation\", \"The Allstate Corporation\", \"ALLTEL Corporation\", \"The Alpine Group, Inc.\", \"Amazon.com, Inc.\", \"AMC Entertainment Inc.\", \"American Power Conversion Corporation\", \"Amerada Hess Corporation\", \"AMERCO\", \"Ameren Corporation\", \"America West Holdings Corporation\", \"American Axle & Manufacturing Holdings, Inc.\", \"American Eagle Outfitters, Inc.\", \"American Electric Power Company, Inc.\", \"American Express Company\", \"American Financial Group, Inc.\", \"American Greetings Corporation\", \"American International Group, Inc.\", \"American Standard Companies Inc.\", \"American Water Works Company, Inc.\", \"AmerisourceBergen Corporation\", \"Ames Department Stores, Inc.\", \"Amgen Inc.\", \"Amkor Technology, Inc.\", \"AMR Corporation\", \"AmSouth Bancorp.\", \"Amtran, Inc.\", \"Anadarko Petroleum Corporation\", \"Analog Devices, Inc.\", \"Anheuser-Busch Companies, Inc.\", \"Anixter International Inc.\", \"AnnTaylor Inc.\", \"Anthem, Inc.\", \"AOL Time Warner Inc.\", \"Aon Corporation\", \"Apache Corporation\", \"Apple Computer, Inc.\", \"Applera Corporation\", \"Applied Industrial Technologies, Inc.\", \"Applied Materials, Inc.\", \"Aquila, Inc.\", \"ARAMARK Corporation\", \"Arch Coal, Inc.\", \"Archer Daniels Midland Company\", \"Arkansas Best Corporation\", \"Armstrong Holdings, Inc.\", \"Arrow Electronics, Inc.\", \"ArvinMeritor, Inc.\", \"Ashland Inc.\", \"Astoria Financial Corporation\", \"AT&T Corp.\", \"Atmel Corporation\", \"Atmos Energy Corporation\", \"Audiovox Corporation\", \"Autoliv, Inc.\", \"Automatic Data Processing, Inc.\", \"AutoNation, Inc.\", \"AutoZone, Inc.\", \"Avaya Inc.\", \"Avery Dennison Corporation\", \"Avista Corporation\", \"Avnet, Inc.\", \"Avon Products, Inc.\", \"Baker Hughes Incorporated\", \"Ball Corporation\", \"Bank of America Corporation\", \"The Bank of New York Company, Inc.\", \"Bank One Corporation\", \"Banknorth Group, Inc.\", \"Banta Corporation\", \"Barnes & Noble, Inc.\", \"Bausch & Lomb Incorporated\", \"Baxter International Inc.\", \"BB&T Corporation\", \"The Bear Stearns Companies Inc.\", \"Beazer Homes USA, Inc.\", \"Beckman Coulter, Inc.\", \"Becton, Dickinson and Company\", \"Bed Bath & Beyond Inc.\", \"Belk, Inc.\", \"Bell Microproducts Inc.\", \"BellSouth Corporation\", \"Belo Corp.\", \"Bemis Company, Inc.\", \"Benchmark Electronics, Inc.\", \"Berkshire Hathaway Inc.\", \"Best Buy Co., Inc.\", \"Bethlehem Steel Corporation\", \"Beverly Enterprises, Inc.\", \"Big Lots, Inc.\", \"BJ Services Company\", \"BJ's Wholesale Club, Inc.\", \"The Black & Decker Corporation\", \"Black Hills Corporation\", \"BMC Software, Inc.\", \"The Boeing Company\", \"Boise Cascade Corporation\", \"Borders Group, Inc.\", \"BorgWarner Inc.\", \"Boston Scientific Corporation\", \"Bowater Incorporated\", \"Briggs & Stratton Corporation\", \"Brightpoint, Inc.\", \"Brinker International, Inc.\", \"Bristol-Myers Squibb Company\", \"Broadwing, Inc.\", \"Brown Shoe Company, Inc.\", \"Brown-Forman Corporation\", \"Brunswick Corporation\", \"Budget Group, Inc.\", \"Burlington Coat Factory Warehouse Corporation\", \"Burlington Industries, Inc.\", \"Burlington Northern Santa Fe Corporation\", \"Burlington Resources Inc.\", \"C. H. Robinson Worldwide Inc.\", \"Cablevision Systems Corp\", \"Cabot Corp\", \"Cadence Design Systems, Inc.\", \"Calpine Corp.\", \"Campbell Soup Co.\", \"Capital One Financial Corp.\", \"Cardinal Health Inc.\", \"Caremark Rx Inc.\", \"Carlisle Cos. Inc.\", \"Carpenter Technology Corp.\", \"Casey's General Stores Inc.\", \"Caterpillar Inc.\", \"CBRL Group Inc.\", \"CDI Corp.\", \"CDW Computer Centers Inc.\", \"CellStar Corp.\", \"Cendant Corp\", \"Cenex Harvest States Cooperatives\", \"Centex Corp.\", \"CenturyTel Inc.\", \"Ceridian Corp.\", \"CH2M Hill Cos. Ltd.\", \"Champion Enterprises Inc.\", \"Charles Schwab Corp.\", \"Charming Shoppes Inc.\", \"Charter Communications Inc.\", \"Charter One Financial Inc.\", \"ChevronTexaco Corp.\", \"Chiquita Brands International Inc.\", \"Chubb Corp\", \"Ciena Corp.\", \"Cigna Corp\", \"Cincinnati Financial Corp.\", \"Cinergy Corp.\", \"Cintas Corp.\", \"Circuit City Stores Inc.\", \"Cisco Systems Inc.\", \"Citigroup, Inc\", \"Citizens Communications Co.\", \"CKE Restaurants Inc.\", \"Clear Channel Communications Inc.\", \"The Clorox Co.\", \"CMGI Inc.\", \"CMS Energy Corp.\", \"CNF Inc.\", \"Coca-Cola Co.\", \"Coca-Cola Enterprises Inc.\", \"Colgate-Palmolive Co.\", \"Collins & Aikman Corp.\", \"Comcast Corp.\", \"Comdisco Inc.\", \"Comerica Inc.\", \"Comfort Systems USA Inc.\", \"Commercial Metals Co.\", \"Community Health Systems Inc.\", \"Compass Bancshares Inc\", \"Computer Associates International Inc.\", \"Computer Sciences Corp.\", \"Compuware Corp.\", \"Comverse Technology Inc.\", \"ConAgra Foods Inc.\", \"Concord EFS Inc.\", \"Conectiv, Inc\", \"Conoco Inc\", \"Conseco Inc.\", \"Consolidated Freightways Corp.\", \"Consolidated Edison Inc.\", \"Constellation Brands Inc.\", \"Constellation Emergy Group Inc.\", \"Continental Airlines Inc.\", \"Convergys Corp.\", \"Cooper Cameron Corp.\", \"Cooper Industries Ltd.\", \"Cooper Tire & Rubber Co.\", \"Corn Products International Inc.\", \"Corning Inc.\", \"Costco Wholesale Corp.\", \"Countrywide Credit Industries Inc.\", \"Coventry Health Care Inc.\", \"Cox Communications Inc.\", \"Crane Co.\", \"Crompton Corp.\", \"Crown Cork & Seal Co. Inc.\", \"CSK Auto Corp.\", \"CSX Corp.\", \"Cummins Inc.\", \"CVS Corp.\", \"Cytec Industries Inc.\", \"D&K Healthcare Resources, Inc.\", \"D.R. Horton Inc.\", \"Dana Corporation\", \"Danaher Corporation\", \"Darden Restaurants Inc.\", \"DaVita Inc.\", \"Dean Foods Company\", \"Deere & Company\", \"Del Monte Foods Co\", \"Dell Computer Corporation\", \"Delphi Corp.\", \"Delta Air Lines Inc.\", \"Deluxe Corporation\", \"Devon Energy Corporation\", \"Di Giorgio Corporation\", \"Dial Corporation\", \"Diebold Incorporated\", \"Dillard's Inc.\", \"DIMON Incorporated\", \"Dole Food Company, Inc.\", \"Dollar General Corporation\", \"Dollar Tree Stores, Inc.\", \"Dominion Resources, Inc.\", \"Domino's Pizza LLC\", \"Dover Corporation, Inc.\", \"Dow Chemical Company\", \"Dow Jones & Company, Inc.\", \"DPL Inc.\", \"DQE Inc.\", \"Dreyer's Grand Ice Cream, Inc.\", \"DST Systems, Inc.\", \"DTE Energy Co.\", \"E.I. Du Pont de Nemours and Company\", \"Duke Energy Corp\", \"Dun & Bradstreet Inc.\", \"DURA Automotive Systems Inc.\", \"DynCorp\", \"Dynegy Inc.\", \"E*Trade Group, Inc.\", \"E.W. Scripps Company\", \"Earthlink, Inc.\", \"Eastman Chemical Company\", \"Eastman Kodak Company\", \"Eaton Corporation\", \"Echostar Communications Corporation\", \"Ecolab Inc.\", \"Edison International\", \"EGL Inc.\", \"El Paso Corporation\", \"Electronic Arts Inc.\", \"Electronic Data Systems Corp.\", \"Eli Lilly and Company\", \"EMC Corporation\", \"Emcor Group Inc.\", \"Emerson Electric Co.\", \"Encompass Services Corporation\", \"Energizer Holdings Inc.\", \"Energy East Corporation\", \"Engelhard Corporation\", \"Enron Corp.\", \"Entergy Corporation\", \"Enterprise Products Partners L.P.\", \"EOG Resources, Inc.\", \"Equifax Inc.\", \"Equitable Resources Inc.\", \"Equity Office Properties Trust\", \"Equity Residential Properties Trust\", \"Estee Lauder Companies Inc.\", \"Exelon Corporation\", \"Exide Technologies\", \"Expeditors International of Washington Inc.\", \"Express Scripts Inc.\", \"ExxonMobil Corporation\", \"Fairchild Semiconductor International Inc.\", \"Family Dollar Stores Inc.\", \"Farmland Industries Inc.\", \"Federal Mogul Corp.\", \"Federated Department Stores Inc.\", \"Federal Express Corp.\", \"Felcor Lodging Trust Inc.\", \"Ferro Corp.\", \"Fidelity National Financial Inc.\", \"Fifth Third Bancorp\", \"First American Financial Corp.\", \"First Data Corp.\", \"First National of Nebraska Inc.\", \"First Tennessee National Corp.\", \"FirstEnergy Corp.\", \"Fiserv Inc.\", \"Fisher Scientific International Inc.\", \"FleetBoston Financial Co.\", \"Fleetwood Enterprises Inc.\", \"Fleming Companies Inc.\", \"Flowers Foods Inc.\", \"Flowserv Corp\", \"Fluor Corp\", \"FMC Corp\", \"Foamex International Inc\", \"Foot Locker Inc\", \"Footstar Inc.\", \"Ford Motor Co\", \"Forest Laboratories Inc.\", \"Fortune Brands Inc.\", \"Foster Wheeler Ltd.\", \"FPL Group Inc.\", \"Franklin Resources Inc.\", \"Freeport McMoran Copper & Gold Inc.\", \"Frontier Oil Corp\", \"Furniture Brands International Inc.\", \"Gannett Co., Inc.\", \"Gap Inc.\", \"Gateway Inc.\", \"GATX Corporation\", \"Gemstar-TV Guide International Inc.\", \"GenCorp Inc.\", \"General Cable Corporation\", \"General Dynamics Corporation\", \"General Electric Company\", \"General Mills Inc\", \"General Motors Corporation\", \"Genesis Health Ventures Inc.\", \"Gentek Inc.\", \"Gentiva Health Services Inc.\", \"Genuine Parts Company\", \"Genuity Inc.\", \"Genzyme Corporation\", \"Georgia Gulf Corporation\", \"Georgia-Pacific Corporation\", \"Gillette Company\", \"Gold Kist Inc.\", \"Golden State Bancorp Inc.\", \"Golden West Financial Corporation\", \"Goldman Sachs Group Inc.\", \"Goodrich Corporation\", \"The Goodyear Tire & Rubber Company\", \"Granite Construction Incorporated\", \"Graybar Electric Company Inc.\", \"Great Lakes Chemical Corporation\", \"Great Plains Energy Inc.\", \"GreenPoint Financial Corp.\", \"Greif Bros. Corporation\", \"Grey Global Group Inc.\", \"Group 1 Automotive Inc.\", \"Guidant Corporation\", \"H&R Block Inc.\", \"H.B. Fuller Company\", \"H.J. Heinz Company\", \"Halliburton Co.\", \"Harley-Davidson Inc.\", \"Harman International Industries Inc.\", \"Harrah's Entertainment Inc.\", \"Harris Corp.\", \"Harsco Corp.\", \"Hartford Financial Services Group Inc.\", \"Hasbro Inc.\", \"Hawaiian Electric Industries Inc.\", \"HCA Inc.\", \"Health Management Associates Inc.\", \"Health Net Inc.\", \"Healthsouth Corp\", \"Henry Schein Inc.\", \"Hercules Inc.\", \"Herman Miller Inc.\", \"Hershey Foods Corp.\", \"Hewlett-Packard Company\", \"Hibernia Corp.\", \"Hillenbrand Industries Inc.\", \"Hilton Hotels Corp.\", \"Hollywood Entertainment Corp.\", \"Home Depot Inc.\", \"Hon Industries Inc.\", \"Honeywell International Inc.\", \"Hormel Foods Corp.\", \"Host Marriott Corp.\", \"Household International Corp.\", \"Hovnanian Enterprises Inc.\", \"Hub Group Inc.\", \"Hubbell Inc.\", \"Hughes Supply Inc.\", \"Humana Inc.\", \"Huntington Bancshares Inc.\", \"Idacorp Inc.\", \"IDT Corporation\", \"IKON Office Solutions Inc.\", \"Illinois Tool Works Inc.\", \"IMC Global Inc.\", \"Imperial Sugar Company\", \"IMS Health Inc.\", \"Ingles Market Inc\", \"Ingram Micro Inc.\", \"Insight Enterprises Inc.\", \"Integrated Electrical Services Inc.\", \"Intel Corporation\", \"International Paper Co.\", \"Interpublic Group of Companies Inc.\", \"Interstate Bakeries Corporation\", \"International Business Machines Corp.\", \"International Flavors & Fragrances Inc.\", \"International Multifoods Corporation\", \"Intuit Inc.\", \"IT Group Inc.\", \"ITT Industries Inc.\", \"Ivax Corp.\", \"J.B. Hunt Transport Services Inc.\", \"J.C. Penny Co.\", \"J.P. Morgan Chase & Co.\", \"Jabil Circuit Inc.\", \"Jack In The Box Inc.\", \"Jacobs Engineering Group Inc.\", \"JDS Uniphase Corp.\", \"Jefferson-Pilot Co.\", \"John Hancock Financial Services Inc.\", \"Johnson & Johnson\", \"Johnson Controls Inc.\", \"Jones Apparel Group Inc.\", \"KB Home\", \"Kellogg Company\", \"Kellwood Company\", \"Kelly Services Inc.\", \"Kemet Corp.\", \"Kennametal Inc.\", \"Kerr-McGee Corporation\", \"KeyCorp\", \"KeySpan Corp.\", \"Kimball International Inc.\", \"Kimberly-Clark Corporation\", \"Kindred Healthcare Inc.\", \"KLA-Tencor Corporation\", \"K-Mart Corp.\", \"Knight-Ridder Inc.\", \"Kohl's Corp.\", \"KPMG Consulting Inc.\", \"Kroger Co.\", \"L-3 Communications Holdings Inc.\", \"Laboratory Corporation of America Holdings\", \"Lam Research Corporation\", \"LandAmerica Financial Group Inc.\", \"Lands' End Inc.\", \"Landstar System Inc.\", \"La-Z-Boy Inc.\", \"Lear Corporation\", \"Legg Mason Inc.\", \"Leggett & Platt Inc.\", \"Lehman Brothers Holdings Inc.\", \"Lennar Corporation\", \"Lennox International Inc.\", \"Level 3 Communications Inc.\", \"Levi Strauss & Co.\", \"Lexmark International Inc.\", \"Limited Inc.\", \"Lincoln National Corporation\", \"Linens 'n Things Inc.\", \"Lithia Motors Inc.\", \"Liz Claiborne Inc.\", \"Lockheed Martin Corporation\", \"Loews Corporation\", \"Longs Drug Stores Corporation\", \"Louisiana-Pacific Corporation\", \"Lowe's Companies Inc.\", \"LSI Logic Corporation\", \"The LTV Corporation\", \"The Lubrizol Corporation\", \"Lucent Technologies Inc.\", \"Lyondell Chemical Company\", \"M & T Bank Corporation\", \"Magellan Health Services Inc.\", \"Mail-Well Inc.\", \"Mandalay Resort Group\", \"Manor Care Inc.\", \"Manpower Inc.\", \"Marathon Oil Corporation\", \"Mariner Health Care Inc.\", \"Markel Corporation\", \"Marriott International Inc.\", \"Marsh & McLennan Companies Inc.\", \"Marsh Supermarkets Inc.\", \"Marshall & Ilsley Corporation\", \"Martin Marietta Materials Inc.\", \"Masco Corporation\", \"Massey Energy Company\", \"MasTec Inc.\", \"Mattel Inc.\", \"Maxim Integrated Products Inc.\", \"Maxtor Corporation\", \"Maxxam Inc.\", \"The May Department Stores Company\", \"Maytag Corporation\", \"MBNA Corporation\", \"McCormick & Company Incorporated\", \"McDonald's Corporation\", \"The McGraw-Hill Companies Inc.\", \"McKesson Corporation\", \"McLeodUSA Incorporated\", \"M.D.C. Holdings Inc.\", \"MDU Resources Group Inc.\", \"MeadWestvaco Corporation\", \"Medtronic Inc.\", \"Mellon Financial Corporation\", \"The Men's Wearhouse Inc.\", \"Merck & Co., Inc.\", \"Mercury General Corporation\", \"Merrill Lynch & Co. Inc.\", \"Metaldyne Corporation\", \"Metals USA Inc.\", \"MetLife Inc.\", \"Metris Companies Inc\", \"MGIC Investment Corporation\", \"MGM Mirage\", \"Michaels Stores Inc.\", \"Micron Technology Inc.\", \"Microsoft Corporation\", \"Milacron Inc.\", \"Millennium Chemicals Inc.\", \"Mirant Corporation\", \"Mohawk Industries Inc.\", \"Molex Incorporated\", \"The MONY Group Inc.\", \"Morgan Stanley Dean Witter & Co.\", \"Motorola Inc.\", \"MPS Group Inc.\", \"Murphy Oil Corporation\", \"Nabors Industries Inc\", \"Nacco Industries Inc\", \"Nash Finch Company\", \"National City Corp.\", \"National Commerce Financial Corporation\", \"National Fuel Gas Company\", \"National Oilwell Inc\", \"National Rural Utilities Cooperative Finance Corporation\", \"National Semiconductor Corporation\", \"National Service Industries Inc\", \"Navistar International Corporation\", \"NCR Corporation\", \"The Neiman Marcus Group Inc.\", \"New Jersey Resources Corporation\", \"New York Times Company\", \"Newell Rubbermaid Inc\", \"Newmont Mining Corporation\", \"Nextel Communications Inc\", \"Nicor Inc\", \"Nike Inc\", \"NiSource Inc\", \"Noble Energy Inc\", \"Nordstrom Inc\", \"Norfolk Southern Corporation\", \"Nortek Inc\", \"North Fork Bancorporation Inc\", \"Northeast Utilities System\", \"Northern Trust Corporation\", \"Northrop Grumman Corporation\", \"NorthWestern Corporation\", \"Novellus Systems Inc\", \"NSTAR\", \"NTL Incorporated\", \"Nucor Corp\", \"Nvidia Corp\", \"NVR Inc\", \"Northwest Airlines Corp\", \"Occidental Petroleum Corp\", \"Ocean Energy Inc\", \"Office Depot Inc.\", \"OfficeMax Inc\", \"OGE Energy Corp\", \"Oglethorpe Power Corp.\", \"Ohio Casualty Corp.\", \"Old Republic International Corp.\", \"Olin Corp.\", \"OM Group Inc\", \"Omnicare Inc\", \"Omnicom Group\", \"On Semiconductor Corp\", \"ONEOK Inc\", \"Oracle Corp\", \"Oshkosh Truck Corp\", \"Outback Steakhouse Inc.\", \"Owens & Minor Inc.\", \"Owens Corning\", \"Owens-Illinois Inc\", \"Oxford Health Plans Inc\", \"Paccar Inc\", \"PacifiCare Health Systems Inc\", \"Packaging Corp. of America\", \"Pactiv Corp\", \"Pall Corp\", \"Pantry Inc\", \"Park Place Entertainment Corp\", \"Parker Hannifin Corp.\", \"Pathmark Stores Inc.\", \"Paychex Inc\", \"Payless Shoesource Inc\", \"Penn Traffic Co.\", \"Pennzoil-Quaker State Company\", \"Pentair Inc\", \"Peoples Energy Corp.\", \"PeopleSoft Inc\", \"Pep Boys Manny, Moe & Jack\", \"Potomac Electric Power Co.\", \"Pepsi Bottling Group Inc.\", \"PepsiAmericas Inc.\", \"PepsiCo Inc.\", \"Performance Food Group Co.\", \"Perini Corp\", \"PerkinElmer Inc\", \"Perot Systems Corp\", \"Petco Animal Supplies Inc.\", \"Peter Kiewit Sons', Inc.\", \"PETsMART Inc\", \"Pfizer Inc\", \"Pacific Gas & Electric Corp.\", \"Pharmacia Corp\", \"Phar Mor Inc.\", \"Phelps Dodge Corp.\", \"Philip Morris Companies Inc.\", \"Phillips Petroleum Co\", \"Phillips Van Heusen Corp.\", \"Phoenix Companies Inc\", \"Pier 1 Imports Inc.\", \"Pilgrim's Pride Corporation\", \"Pinnacle West Capital Corp\", \"Pioneer-Standard Electronics Inc.\", \"Pitney Bowes Inc.\", \"Pittston Brinks Group\", \"Plains All American Pipeline LP\", \"PNC Financial Services Group Inc.\", \"PNM Resources Inc\", \"Polaris Industries Inc.\", \"Polo Ralph Lauren Corp\", \"PolyOne Corp\", \"Popular Inc\", \"Potlatch Corp\", \"PPG Industries Inc\", \"PPL Corp\", \"Praxair Inc\", \"Precision Castparts Corp\", \"Premcor Inc.\", \"Pride International Inc\", \"Primedia Inc\", \"Principal Financial Group Inc.\", \"Procter & Gamble Co.\", \"Pro-Fac Cooperative Inc.\", \"Progress Energy Inc\", \"Progressive Corporation\", \"Protective Life Corp\", \"Provident Financial Group\", \"Providian Financial Corp.\", \"Prudential Financial Inc.\", \"PSS World Medical Inc\", \"Public Service Enterprise Group Inc.\", \"Publix Super Markets Inc.\", \"Puget Energy Inc.\", \"Pulte Homes Inc\", \"Qualcomm Inc\", \"Quanta Services Inc.\", \"Quantum Corp\", \"Quest Diagnostics Inc.\", \"Questar Corp\", \"Quintiles Transnational\", \"Qwest Communications Intl Inc\", \"R.J. Reynolds Tobacco Company\", \"R.R. Donnelley & Sons Company\", \"Radio Shack Corporation\", \"Raymond James Financial Inc.\", \"Raytheon Company\", \"Reader's Digest Association Inc.\", \"Reebok International Ltd.\", \"Regions Financial Corp.\", \"Regis Corporation\", \"Reliance Steel & Aluminum Co.\", \"Reliant Energy Inc.\", \"Rent A Center Inc\", \"Republic Services Inc\", \"Revlon Inc\", \"RGS Energy Group Inc\", \"Rite Aid Corp\", \"Riverwood Holding Inc.\", \"RoadwayCorp\", \"Robert Half International Inc.\", \"Rock-Tenn Co\", \"Rockwell Automation Inc\", \"Rockwell Collins Inc\", \"Rohm & Haas Co.\", \"Ross Stores Inc\", \"RPM Inc.\", \"Ruddick Corp\", \"Ryder System Inc\", \"Ryerson Tull Inc\", \"Ryland Group Inc.\", \"Sabre Holdings Corp\", \"Safeco Corp\", \"Safeguard Scientifics Inc.\", \"Safeway Inc\", \"Saks Inc\", \"Sanmina-SCI Inc\", \"Sara Lee Corp\", \"SBC Communications Inc\", \"Scana Corp.\", \"Schering-Plough Corp\", \"Scholastic Corp\", \"SCI Systems Onc.\", \"Science Applications Intl. Inc.\", \"Scientific-Atlanta Inc\", \"Scotts Company\", \"Seaboard Corp\", \"Sealed Air Corp\", \"Sears Roebuck & Co\", \"Sempra Energy\", \"Sequa Corp\", \"Service Corp. International\", \"ServiceMaster Co\", \"Shaw Group Inc\", \"Sherwin-Williams Company\", \"Shopko Stores Inc\", \"Siebel Systems Inc\", \"Sierra Health Services Inc\", \"Sierra Pacific Resources\", \"Silgan Holdings Inc.\", \"Silicon Graphics Inc\", \"Simon Property Group Inc\", \"SLM Corporation\", \"Smith International Inc\", \"Smithfield Foods Inc\", \"Smurfit-Stone Container Corp\", \"Snap-On Inc\", \"Solectron Corp\", \"Solutia Inc\", \"Sonic Automotive Inc.\", \"Sonoco Products Co.\", \"Southern Company\", \"Southern Union Company\", \"SouthTrust Corp.\", \"Southwest Airlines Co\", \"Southwest Gas Corp\", \"Sovereign Bancorp Inc.\", \"Spartan Stores Inc\", \"Spherion Corp\", \"Sports Authority Inc\", \"Sprint Corp.\", \"SPX Corp\", \"St. Jude Medical Inc\", \"St. Paul Cos.\", \"Staff Leasing Inc.\", \"StanCorp Financial Group Inc\", \"Standard Pacific Corp.\", \"Stanley Works\", \"Staples Inc\", \"Starbucks Corp\", \"Starwood Hotels & Resorts Worldwide Inc\", \"State Street Corp.\", \"Stater Bros. Holdings Inc.\", \"Steelcase Inc\", \"Stein Mart Inc\", \"Stewart & Stevenson Services Inc\", \"Stewart Information Services Corp\", \"Stilwell Financial Inc\", \"Storage Technology Corporation\", \"Stryker Corp\", \"Sun Healthcare Group Inc.\", \"Sun Microsystems Inc.\", \"SunGard Data Systems Inc.\", \"Sunoco Inc.\", \"SunTrust Banks Inc\", \"Supervalu Inc\", \"Swift Transportation, Co., Inc\", \"Symbol Technologies Inc\", \"Synovus Financial Corp.\", \"Sysco Corp\", \"Systemax Inc.\", \"Target Corp.\", \"Tech Data Corporation\", \"TECO Energy Inc\", \"Tecumseh Products Company\", \"Tektronix Inc\", \"Teleflex Incorporated\", \"Telephone & Data Systems Inc\", \"Tellabs Inc.\", \"Temple-Inland Inc\", \"Tenet Healthcare Corporation\", \"Tenneco Automotive Inc.\", \"Teradyne Inc\", \"Terex Corp\", \"Tesoro Petroleum Corp.\", \"Texas Industries Inc.\", \"Texas Instruments Incorporated\", \"Textron Inc\", \"Thermo Electron Corporation\", \"Thomas & Betts Corporation\", \"Tiffany & Co\", \"Timken Company\", \"TJX Companies Inc\", \"TMP Worldwide Inc\", \"Toll Brothers Inc\", \"Torchmark Corporation\", \"Toro Company\", \"Tower Automotive Inc.\", \"Toys 'R' Us Inc\", \"Trans World Entertainment Corp.\", \"TransMontaigne Inc\", \"Transocean Inc\", \"TravelCenters of America Inc.\", \"Triad Hospitals Inc\", \"Tribune Company\", \"Trigon Healthcare Inc.\", \"Trinity Industries Inc\", \"Trump Hotels & Casino Resorts Inc.\", \"TruServ Corporation\", \"TRW Inc\", \"TXU Corp\", \"Tyson Foods Inc\", \"U.S. Bancorp\", \"U.S. Industries Inc.\", \"UAL Corporation\", \"UGI Corporation\", \"Unified Western Grocers Inc\", \"Union Pacific Corporation\", \"Union Planters Corp\", \"Unisource Energy Corp\", \"Unisys Corporation\", \"United Auto Group Inc\", \"United Defense Industries Inc.\", \"United Parcel Service Inc\", \"United Rentals Inc\", \"United Stationers Inc\", \"United Technologies Corporation\", \"UnitedHealth Group Incorporated\", \"Unitrin Inc\", \"Universal Corporation\", \"Universal Forest Products Inc\", \"Universal Health Services Inc\", \"Unocal Corporation\", \"Unova Inc\", \"UnumProvident Corporation\", \"URS Corporation\", \"US Airways Group Inc\", \"US Oncology Inc\", \"USA Interactive\", \"USFreighways Corporation\", \"USG Corporation\", \"UST Inc\", \"Valero Energy Corporation\", \"Valspar Corporation\", \"Value City Department Stores Inc\", \"Varco International Inc\", \"Vectren Corporation\", \"Veritas Software Corporation\", \"Verizon Communications Inc\", \"VF Corporation\", \"Viacom Inc\", \"Viad Corp\", \"Viasystems Group Inc\", \"Vishay Intertechnology Inc\", \"Visteon Corporation\", \"Volt Information Sciences Inc\", \"Vulcan Materials Company\", \"W.R. Berkley Corporation\", \"W.R. Grace & Co\", \"W.W. Grainger Inc\", \"Wachovia Corporation\", \"Wakenhut Corporation\", \"Walgreen Co\", \"Wallace Computer Services Inc\", \"Wal-Mart Stores Inc\", \"Walt Disney Co\", \"Walter Industries Inc\", \"Washington Mutual Inc\", \"Washington Post Co.\", \"Waste Management Inc\", \"Watsco Inc\", \"Weatherford International Inc\", \"Weis Markets Inc.\", \"Wellpoint Health Networks Inc\", \"Wells Fargo & Company\", \"Wendy's International Inc\", \"Werner Enterprises Inc\", \"WESCO International Inc\", \"Western Digital Inc\", \"Western Gas Resources Inc\", \"WestPoint Stevens Inc\", \"Weyerhauser Company\", \"WGL Holdings Inc\", \"Whirlpool Corporation\", \"Whole Foods Market Inc\", \"Willamette Industries Inc.\", \"Williams Companies Inc\", \"Williams Sonoma Inc\", \"Winn Dixie Stores Inc\", \"Wisconsin Energy Corporation\", \"Wm Wrigley Jr Company\", \"World Fuel Services Corporation\", \"WorldCom Inc\", \"Worthington Industries Inc\", \"WPS Resources Corporation\", \"Wyeth\", \"Wyndham International Inc\", \"Xcel Energy Inc\", \"Xerox Corp\", \"Xilinx Inc\", \"XO Communications Inc\", \"Yellow Corporation\", \"York International Corp\", \"Yum Brands Inc.\", \"Zale Corporation\", \"Zions Bancorporation\"],\r\n\r\n\t\tfileExtension: {\r\n\t\t\traster: [\"bmp\", \"gif\", \"gpl\", \"ico\", \"jpeg\", \"psd\", \"png\", \"psp\", \"raw\", \"tiff\"],\r\n\t\t\tvector: [\"3dv\", \"amf\", \"awg\", \"ai\", \"cgm\", \"cdr\", \"cmx\", \"dxf\", \"e2d\", \"egt\", \"eps\", \"fs\", \"odg\", \"svg\", \"xar\"],\r\n\t\t\t\"3d\": [\"3dmf\", \"3dm\", \"3mf\", \"3ds\", \"an8\", \"aoi\", \"blend\", \"cal3d\", \"cob\", \"ctm\", \"iob\", \"jas\", \"max\", \"mb\", \"mdx\", \"obj\", \"x\", \"x3d\"],\r\n\t\t\tdocument: [\"doc\", \"docx\", \"dot\", \"html\", \"xml\", \"odt\", \"odm\", \"ott\", \"csv\", \"rtf\", \"tex\", \"xhtml\", \"xps\"],\r\n\t\t},\r\n\r\n\t\t// Data taken from https://github.com/dmfilipenko/timezones.json/blob/master/timezones.json\r\n\t\ttimezones: [\r\n\t\t\t{\r\n\t\t\t\tname: \"Dateline Standard Time\",\r\n\t\t\t\tabbr: \"DST\",\r\n\t\t\t\toffset: -12,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-12:00) International Date Line West\",\r\n\t\t\t\tutc: [\"Etc/GMT+12\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"UTC-11\",\r\n\t\t\t\tabbr: \"U\",\r\n\t\t\t\toffset: -11,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-11:00) Coordinated Universal Time-11\",\r\n\t\t\t\tutc: [\"Etc/GMT+11\", \"Pacific/Midway\", \"Pacific/Niue\", \"Pacific/Pago_Pago\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Hawaiian Standard Time\",\r\n\t\t\t\tabbr: \"HST\",\r\n\t\t\t\toffset: -10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-10:00) Hawaii\",\r\n\t\t\t\tutc: [\"Etc/GMT+10\", \"Pacific/Honolulu\", \"Pacific/Johnston\", \"Pacific/Rarotonga\", \"Pacific/Tahiti\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Alaskan Standard Time\",\r\n\t\t\t\tabbr: \"AKDT\",\r\n\t\t\t\toffset: -8,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-09:00) Alaska\",\r\n\t\t\t\tutc: [\"America/Anchorage\", \"America/Juneau\", \"America/Nome\", \"America/Sitka\", \"America/Yakutat\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Pacific Standard Time (Mexico)\",\r\n\t\t\t\tabbr: \"PDT\",\r\n\t\t\t\toffset: -7,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-08:00) Baja California\",\r\n\t\t\t\tutc: [\"America/Santa_Isabel\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Pacific Standard Time\",\r\n\t\t\t\tabbr: \"PDT\",\r\n\t\t\t\toffset: -7,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-08:00) Pacific Time (US & Canada)\",\r\n\t\t\t\tutc: [\"America/Dawson\", \"America/Los_Angeles\", \"America/Tijuana\", \"America/Vancouver\", \"America/Whitehorse\", \"PST8PDT\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"US Mountain Standard Time\",\r\n\t\t\t\tabbr: \"UMST\",\r\n\t\t\t\toffset: -7,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-07:00) Arizona\",\r\n\t\t\t\tutc: [\"America/Creston\", \"America/Dawson_Creek\", \"America/Hermosillo\", \"America/Phoenix\", \"Etc/GMT+7\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Mountain Standard Time (Mexico)\",\r\n\t\t\t\tabbr: \"MDT\",\r\n\t\t\t\toffset: -6,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-07:00) Chihuahua, La Paz, Mazatlan\",\r\n\t\t\t\tutc: [\"America/Chihuahua\", \"America/Mazatlan\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Mountain Standard Time\",\r\n\t\t\t\tabbr: \"MDT\",\r\n\t\t\t\toffset: -6,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-07:00) Mountain Time (US & Canada)\",\r\n\t\t\t\tutc: [\"America/Boise\", \"America/Cambridge_Bay\", \"America/Denver\", \"America/Edmonton\", \"America/Inuvik\", \"America/Ojinaga\", \"America/Yellowknife\", \"MST7MDT\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central America Standard Time\",\r\n\t\t\t\tabbr: \"CAST\",\r\n\t\t\t\toffset: -6,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-06:00) Central America\",\r\n\t\t\t\tutc: [\"America/Belize\", \"America/Costa_Rica\", \"America/El_Salvador\", \"America/Guatemala\", \"America/Managua\", \"America/Tegucigalpa\", \"Etc/GMT+6\", \"Pacific/Galapagos\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Standard Time\",\r\n\t\t\t\tabbr: \"CDT\",\r\n\t\t\t\toffset: -5,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-06:00) Central Time (US & Canada)\",\r\n\t\t\t\tutc: [\"America/Chicago\", \"America/Indiana/Knox\", \"America/Indiana/Tell_City\", \"America/Matamoros\", \"America/Menominee\", \"America/North_Dakota/Beulah\", \"America/North_Dakota/Center\", \"America/North_Dakota/New_Salem\", \"America/Rainy_River\", \"America/Rankin_Inlet\", \"America/Resolute\", \"America/Winnipeg\", \"CST6CDT\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Standard Time (Mexico)\",\r\n\t\t\t\tabbr: \"CDT\",\r\n\t\t\t\toffset: -5,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-06:00) Guadalajara, Mexico City, Monterrey\",\r\n\t\t\t\tutc: [\"America/Bahia_Banderas\", \"America/Cancun\", \"America/Merida\", \"America/Mexico_City\", \"America/Monterrey\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Canada Central Standard Time\",\r\n\t\t\t\tabbr: \"CCST\",\r\n\t\t\t\toffset: -6,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-06:00) Saskatchewan\",\r\n\t\t\t\tutc: [\"America/Regina\", \"America/Swift_Current\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"SA Pacific Standard Time\",\r\n\t\t\t\tabbr: \"SPST\",\r\n\t\t\t\toffset: -5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-05:00) Bogota, Lima, Quito\",\r\n\t\t\t\tutc: [\"America/Bogota\", \"America/Cayman\", \"America/Coral_Harbour\", \"America/Eirunepe\", \"America/Guayaquil\", \"America/Jamaica\", \"America/Lima\", \"America/Panama\", \"America/Rio_Branco\", \"Etc/GMT+5\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Eastern Standard Time\",\r\n\t\t\t\tabbr: \"EDT\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-05:00) Eastern Time (US & Canada)\",\r\n\t\t\t\tutc: [\"America/Detroit\", \"America/Havana\", \"America/Indiana/Petersburg\", \"America/Indiana/Vincennes\", \"America/Indiana/Winamac\", \"America/Iqaluit\", \"America/Kentucky/Monticello\", \"America/Louisville\", \"America/Montreal\", \"America/Nassau\", \"America/New_York\", \"America/Nipigon\", \"America/Pangnirtung\", \"America/Port-au-Prince\", \"America/Thunder_Bay\", \"America/Toronto\", \"EST5EDT\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"US Eastern Standard Time\",\r\n\t\t\t\tabbr: \"UEDT\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-05:00) Indiana (East)\",\r\n\t\t\t\tutc: [\"America/Indiana/Marengo\", \"America/Indiana/Vevay\", \"America/Indianapolis\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Venezuela Standard Time\",\r\n\t\t\t\tabbr: \"VST\",\r\n\t\t\t\toffset: -4.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-04:30) Caracas\",\r\n\t\t\t\tutc: [\"America/Caracas\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Paraguay Standard Time\",\r\n\t\t\t\tabbr: \"PST\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-04:00) Asuncion\",\r\n\t\t\t\tutc: [\"America/Asuncion\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Atlantic Standard Time\",\r\n\t\t\t\tabbr: \"ADT\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-04:00) Atlantic Time (Canada)\",\r\n\t\t\t\tutc: [\"America/Glace_Bay\", \"America/Goose_Bay\", \"America/Halifax\", \"America/Moncton\", \"America/Thule\", \"Atlantic/Bermuda\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Brazilian Standard Time\",\r\n\t\t\t\tabbr: \"CBST\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-04:00) Cuiaba\",\r\n\t\t\t\tutc: [\"America/Campo_Grande\", \"America/Cuiaba\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"SA Western Standard Time\",\r\n\t\t\t\tabbr: \"SWST\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-04:00) Georgetown, La Paz, Manaus, San Juan\",\r\n\t\t\t\tutc: [\"America/Anguilla\", \"America/Antigua\", \"America/Aruba\", \"America/Barbados\", \"America/Blanc-Sablon\", \"America/Boa_Vista\", \"America/Curacao\", \"America/Dominica\", \"America/Grand_Turk\", \"America/Grenada\", \"America/Guadeloupe\", \"America/Guyana\", \"America/Kralendijk\", \"America/La_Paz\", \"America/Lower_Princes\", \"America/Manaus\", \"America/Marigot\", \"America/Martinique\", \"America/Montserrat\", \"America/Port_of_Spain\", \"America/Porto_Velho\", \"America/Puerto_Rico\", \"America/Santo_Domingo\", \"America/St_Barthelemy\", \"America/St_Kitts\", \"America/St_Lucia\", \"America/St_Thomas\", \"America/St_Vincent\", \"America/Tortola\", \"Etc/GMT+4\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Pacific SA Standard Time\",\r\n\t\t\t\tabbr: \"PSST\",\r\n\t\t\t\toffset: -4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-04:00) Santiago\",\r\n\t\t\t\tutc: [\"America/Santiago\", \"Antarctica/Palmer\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Newfoundland Standard Time\",\r\n\t\t\t\tabbr: \"NDT\",\r\n\t\t\t\toffset: -2.5,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-03:30) Newfoundland\",\r\n\t\t\t\tutc: [\"America/St_Johns\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"E. South America Standard Time\",\r\n\t\t\t\tabbr: \"ESAST\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-03:00) Brasilia\",\r\n\t\t\t\tutc: [\"America/Sao_Paulo\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Argentina Standard Time\",\r\n\t\t\t\tabbr: \"AST\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-03:00) Buenos Aires\",\r\n\t\t\t\tutc: [\"America/Argentina/La_Rioja\", \"America/Argentina/Rio_Gallegos\", \"America/Argentina/Salta\", \"America/Argentina/San_Juan\", \"America/Argentina/San_Luis\", \"America/Argentina/Tucuman\", \"America/Argentina/Ushuaia\", \"America/Buenos_Aires\", \"America/Catamarca\", \"America/Cordoba\", \"America/Jujuy\", \"America/Mendoza\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"SA Eastern Standard Time\",\r\n\t\t\t\tabbr: \"SEST\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-03:00) Cayenne, Fortaleza\",\r\n\t\t\t\tutc: [\"America/Araguaina\", \"America/Belem\", \"America/Cayenne\", \"America/Fortaleza\", \"America/Maceio\", \"America/Paramaribo\", \"America/Recife\", \"America/Santarem\", \"Antarctica/Rothera\", \"Atlantic/Stanley\", \"Etc/GMT+3\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Greenland Standard Time\",\r\n\t\t\t\tabbr: \"GDT\",\r\n\t\t\t\toffset: -2,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-03:00) Greenland\",\r\n\t\t\t\tutc: [\"America/Godthab\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Montevideo Standard Time\",\r\n\t\t\t\tabbr: \"MST\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-03:00) Montevideo\",\r\n\t\t\t\tutc: [\"America/Montevideo\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Bahia Standard Time\",\r\n\t\t\t\tabbr: \"BST\",\r\n\t\t\t\toffset: -3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-03:00) Salvador\",\r\n\t\t\t\tutc: [\"America/Bahia\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"UTC-02\",\r\n\t\t\t\tabbr: \"U\",\r\n\t\t\t\toffset: -2,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-02:00) Coordinated Universal Time-02\",\r\n\t\t\t\tutc: [\"America/Noronha\", \"Atlantic/South_Georgia\", \"Etc/GMT+2\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Mid-Atlantic Standard Time\",\r\n\t\t\t\tabbr: \"MDT\",\r\n\t\t\t\toffset: -1,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-02:00) Mid-Atlantic - Old\",\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Azores Standard Time\",\r\n\t\t\t\tabbr: \"ADT\",\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC-01:00) Azores\",\r\n\t\t\t\tutc: [\"America/Scoresbysund\", \"Atlantic/Azores\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Cape Verde Standard Time\",\r\n\t\t\t\tabbr: \"CVST\",\r\n\t\t\t\toffset: -1,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC-01:00) Cape Verde Is.\",\r\n\t\t\t\tutc: [\"Atlantic/Cape_Verde\", \"Etc/GMT+1\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Morocco Standard Time\",\r\n\t\t\t\tabbr: \"MDT\",\r\n\t\t\t\toffset: 1,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC) Casablanca\",\r\n\t\t\t\tutc: [\"Africa/Casablanca\", \"Africa/El_Aaiun\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"UTC\",\r\n\t\t\t\tabbr: \"CUT\",\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC) Coordinated Universal Time\",\r\n\t\t\t\tutc: [\"America/Danmarkshavn\", \"Etc/GMT\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"GMT Standard Time\",\r\n\t\t\t\tabbr: \"GDT\",\r\n\t\t\t\toffset: 1,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC) Dublin, Edinburgh, Lisbon, London\",\r\n\t\t\t\tutc: [\"Atlantic/Canary\", \"Atlantic/Faeroe\", \"Atlantic/Madeira\", \"Europe/Dublin\", \"Europe/Guernsey\", \"Europe/Isle_of_Man\", \"Europe/Jersey\", \"Europe/Lisbon\", \"Europe/London\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Greenwich Standard Time\",\r\n\t\t\t\tabbr: \"GST\",\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC) Monrovia, Reykjavik\",\r\n\t\t\t\tutc: [\"Africa/Abidjan\", \"Africa/Accra\", \"Africa/Bamako\", \"Africa/Banjul\", \"Africa/Bissau\", \"Africa/Conakry\", \"Africa/Dakar\", \"Africa/Freetown\", \"Africa/Lome\", \"Africa/Monrovia\", \"Africa/Nouakchott\", \"Africa/Ouagadougou\", \"Africa/Sao_Tome\", \"Atlantic/Reykjavik\", \"Atlantic/St_Helena\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"W. Europe Standard Time\",\r\n\t\t\t\tabbr: \"WEDT\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna\",\r\n\t\t\t\tutc: [\"Arctic/Longyearbyen\", \"Europe/Amsterdam\", \"Europe/Andorra\", \"Europe/Berlin\", \"Europe/Busingen\", \"Europe/Gibraltar\", \"Europe/Luxembourg\", \"Europe/Malta\", \"Europe/Monaco\", \"Europe/Oslo\", \"Europe/Rome\", \"Europe/San_Marino\", \"Europe/Stockholm\", \"Europe/Vaduz\", \"Europe/Vatican\", \"Europe/Vienna\", \"Europe/Zurich\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Europe Standard Time\",\r\n\t\t\t\tabbr: \"CEDT\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague\",\r\n\t\t\t\tutc: [\"Europe/Belgrade\", \"Europe/Bratislava\", \"Europe/Budapest\", \"Europe/Ljubljana\", \"Europe/Podgorica\", \"Europe/Prague\", \"Europe/Tirane\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Romance Standard Time\",\r\n\t\t\t\tabbr: \"RDT\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+01:00) Brussels, Copenhagen, Madrid, Paris\",\r\n\t\t\t\tutc: [\"Africa/Ceuta\", \"Europe/Brussels\", \"Europe/Copenhagen\", \"Europe/Madrid\", \"Europe/Paris\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central European Standard Time\",\r\n\t\t\t\tabbr: \"CEDT\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb\",\r\n\t\t\t\tutc: [\"Europe/Sarajevo\", \"Europe/Skopje\", \"Europe/Warsaw\", \"Europe/Zagreb\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"W. Central Africa Standard Time\",\r\n\t\t\t\tabbr: \"WCAST\",\r\n\t\t\t\toffset: 1,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+01:00) West Central Africa\",\r\n\t\t\t\tutc: [\"Africa/Algiers\", \"Africa/Bangui\", \"Africa/Brazzaville\", \"Africa/Douala\", \"Africa/Kinshasa\", \"Africa/Lagos\", \"Africa/Libreville\", \"Africa/Luanda\", \"Africa/Malabo\", \"Africa/Ndjamena\", \"Africa/Niamey\", \"Africa/Porto-Novo\", \"Africa/Tunis\", \"Etc/GMT-1\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Namibia Standard Time\",\r\n\t\t\t\tabbr: \"NST\",\r\n\t\t\t\toffset: 1,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+01:00) Windhoek\",\r\n\t\t\t\tutc: [\"Africa/Windhoek\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"GTB Standard Time\",\r\n\t\t\t\tabbr: \"GDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Athens, Bucharest\",\r\n\t\t\t\tutc: [\"Asia/Nicosia\", \"Europe/Athens\", \"Europe/Bucharest\", \"Europe/Chisinau\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Middle East Standard Time\",\r\n\t\t\t\tabbr: \"MEDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Beirut\",\r\n\t\t\t\tutc: [\"Asia/Beirut\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Egypt Standard Time\",\r\n\t\t\t\tabbr: \"EST\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+02:00) Cairo\",\r\n\t\t\t\tutc: [\"Africa/Cairo\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Syria Standard Time\",\r\n\t\t\t\tabbr: \"SDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Damascus\",\r\n\t\t\t\tutc: [\"Asia/Damascus\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"E. Europe Standard Time\",\r\n\t\t\t\tabbr: \"EEDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) E. Europe\",\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"South Africa Standard Time\",\r\n\t\t\t\tabbr: \"SAST\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+02:00) Harare, Pretoria\",\r\n\t\t\t\tutc: [\"Africa/Blantyre\", \"Africa/Bujumbura\", \"Africa/Gaborone\", \"Africa/Harare\", \"Africa/Johannesburg\", \"Africa/Kigali\", \"Africa/Lubumbashi\", \"Africa/Lusaka\", \"Africa/Maputo\", \"Africa/Maseru\", \"Africa/Mbabane\", \"Etc/GMT-2\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"FLE Standard Time\",\r\n\t\t\t\tabbr: \"FDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius\",\r\n\t\t\t\tutc: [\"Europe/Helsinki\", \"Europe/Kiev\", \"Europe/Mariehamn\", \"Europe/Riga\", \"Europe/Sofia\", \"Europe/Tallinn\", \"Europe/Uzhgorod\", \"Europe/Vilnius\", \"Europe/Zaporozhye\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Turkey Standard Time\",\r\n\t\t\t\tabbr: \"TDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Istanbul\",\r\n\t\t\t\tutc: [\"Europe/Istanbul\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Israel Standard Time\",\r\n\t\t\t\tabbr: \"JDT\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+02:00) Jerusalem\",\r\n\t\t\t\tutc: [\"Asia/Jerusalem\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Libya Standard Time\",\r\n\t\t\t\tabbr: \"LST\",\r\n\t\t\t\toffset: 2,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+02:00) Tripoli\",\r\n\t\t\t\tutc: [\"Africa/Tripoli\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Jordan Standard Time\",\r\n\t\t\t\tabbr: \"JST\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+03:00) Amman\",\r\n\t\t\t\tutc: [\"Asia/Amman\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Arabic Standard Time\",\r\n\t\t\t\tabbr: \"AST\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+03:00) Baghdad\",\r\n\t\t\t\tutc: [\"Asia/Baghdad\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Kaliningrad Standard Time\",\r\n\t\t\t\tabbr: \"KST\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+03:00) Kaliningrad, Minsk\",\r\n\t\t\t\tutc: [\"Europe/Kaliningrad\", \"Europe/Minsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Arab Standard Time\",\r\n\t\t\t\tabbr: \"AST\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+03:00) Kuwait, Riyadh\",\r\n\t\t\t\tutc: [\"Asia/Aden\", \"Asia/Bahrain\", \"Asia/Kuwait\", \"Asia/Qatar\", \"Asia/Riyadh\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"E. Africa Standard Time\",\r\n\t\t\t\tabbr: \"EAST\",\r\n\t\t\t\toffset: 3,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+03:00) Nairobi\",\r\n\t\t\t\tutc: [\"Africa/Addis_Ababa\", \"Africa/Asmera\", \"Africa/Dar_es_Salaam\", \"Africa/Djibouti\", \"Africa/Juba\", \"Africa/Kampala\", \"Africa/Khartoum\", \"Africa/Mogadishu\", \"Africa/Nairobi\", \"Antarctica/Syowa\", \"Etc/GMT-3\", \"Indian/Antananarivo\", \"Indian/Comoro\", \"Indian/Mayotte\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Iran Standard Time\",\r\n\t\t\t\tabbr: \"IDT\",\r\n\t\t\t\toffset: 4.5,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+03:30) Tehran\",\r\n\t\t\t\tutc: [\"Asia/Tehran\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Arabian Standard Time\",\r\n\t\t\t\tabbr: \"AST\",\r\n\t\t\t\toffset: 4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:00) Abu Dhabi, Muscat\",\r\n\t\t\t\tutc: [\"Asia/Dubai\", \"Asia/Muscat\", \"Etc/GMT-4\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Azerbaijan Standard Time\",\r\n\t\t\t\tabbr: \"ADT\",\r\n\t\t\t\toffset: 5,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+04:00) Baku\",\r\n\t\t\t\tutc: [\"Asia/Baku\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Russian Standard Time\",\r\n\t\t\t\tabbr: \"RST\",\r\n\t\t\t\toffset: 4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:00) Moscow, St. Petersburg, Volgograd\",\r\n\t\t\t\tutc: [\"Europe/Moscow\", \"Europe/Samara\", \"Europe/Simferopol\", \"Europe/Volgograd\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Mauritius Standard Time\",\r\n\t\t\t\tabbr: \"MST\",\r\n\t\t\t\toffset: 4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:00) Port Louis\",\r\n\t\t\t\tutc: [\"Indian/Mahe\", \"Indian/Mauritius\", \"Indian/Reunion\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Georgian Standard Time\",\r\n\t\t\t\tabbr: \"GST\",\r\n\t\t\t\toffset: 4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:00) Tbilisi\",\r\n\t\t\t\tutc: [\"Asia/Tbilisi\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Caucasus Standard Time\",\r\n\t\t\t\tabbr: \"CST\",\r\n\t\t\t\toffset: 4,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:00) Yerevan\",\r\n\t\t\t\tutc: [\"Asia/Yerevan\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Afghanistan Standard Time\",\r\n\t\t\t\tabbr: \"AST\",\r\n\t\t\t\toffset: 4.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+04:30) Kabul\",\r\n\t\t\t\tutc: [\"Asia/Kabul\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"West Asia Standard Time\",\r\n\t\t\t\tabbr: \"WAST\",\r\n\t\t\t\toffset: 5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+05:00) Ashgabat, Tashkent\",\r\n\t\t\t\tutc: [\"Antarctica/Mawson\", \"Asia/Aqtau\", \"Asia/Aqtobe\", \"Asia/Ashgabat\", \"Asia/Dushanbe\", \"Asia/Oral\", \"Asia/Samarkand\", \"Asia/Tashkent\", \"Etc/GMT-5\", \"Indian/Kerguelen\", \"Indian/Maldives\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Pakistan Standard Time\",\r\n\t\t\t\tabbr: \"PST\",\r\n\t\t\t\toffset: 5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+05:00) Islamabad, Karachi\",\r\n\t\t\t\tutc: [\"Asia/Karachi\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"India Standard Time\",\r\n\t\t\t\tabbr: \"IST\",\r\n\t\t\t\toffset: 5.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\r\n\t\t\t\tutc: [\"Asia/Calcutta\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Sri Lanka Standard Time\",\r\n\t\t\t\tabbr: \"SLST\",\r\n\t\t\t\toffset: 5.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+05:30) Sri Jayawardenepura\",\r\n\t\t\t\tutc: [\"Asia/Colombo\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Nepal Standard Time\",\r\n\t\t\t\tabbr: \"NST\",\r\n\t\t\t\toffset: 5.75,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+05:45) Kathmandu\",\r\n\t\t\t\tutc: [\"Asia/Katmandu\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Asia Standard Time\",\r\n\t\t\t\tabbr: \"CAST\",\r\n\t\t\t\toffset: 6,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+06:00) Astana\",\r\n\t\t\t\tutc: [\"Antarctica/Vostok\", \"Asia/Almaty\", \"Asia/Bishkek\", \"Asia/Qyzylorda\", \"Asia/Urumqi\", \"Etc/GMT-6\", \"Indian/Chagos\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Bangladesh Standard Time\",\r\n\t\t\t\tabbr: \"BST\",\r\n\t\t\t\toffset: 6,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+06:00) Dhaka\",\r\n\t\t\t\tutc: [\"Asia/Dhaka\", \"Asia/Thimphu\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Ekaterinburg Standard Time\",\r\n\t\t\t\tabbr: \"EST\",\r\n\t\t\t\toffset: 6,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+06:00) Ekaterinburg\",\r\n\t\t\t\tutc: [\"Asia/Yekaterinburg\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Myanmar Standard Time\",\r\n\t\t\t\tabbr: \"MST\",\r\n\t\t\t\toffset: 6.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+06:30) Yangon (Rangoon)\",\r\n\t\t\t\tutc: [\"Asia/Rangoon\", \"Indian/Cocos\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"SE Asia Standard Time\",\r\n\t\t\t\tabbr: \"SAST\",\r\n\t\t\t\toffset: 7,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+07:00) Bangkok, Hanoi, Jakarta\",\r\n\t\t\t\tutc: [\"Antarctica/Davis\", \"Asia/Bangkok\", \"Asia/Hovd\", \"Asia/Jakarta\", \"Asia/Phnom_Penh\", \"Asia/Pontianak\", \"Asia/Saigon\", \"Asia/Vientiane\", \"Etc/GMT-7\", \"Indian/Christmas\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"N. Central Asia Standard Time\",\r\n\t\t\t\tabbr: \"NCAST\",\r\n\t\t\t\toffset: 7,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+07:00) Novosibirsk\",\r\n\t\t\t\tutc: [\"Asia/Novokuznetsk\", \"Asia/Novosibirsk\", \"Asia/Omsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"China Standard Time\",\r\n\t\t\t\tabbr: \"CST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi\",\r\n\t\t\t\tutc: [\"Asia/Hong_Kong\", \"Asia/Macau\", \"Asia/Shanghai\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"North Asia Standard Time\",\r\n\t\t\t\tabbr: \"NAST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Krasnoyarsk\",\r\n\t\t\t\tutc: [\"Asia/Krasnoyarsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Singapore Standard Time\",\r\n\t\t\t\tabbr: \"MPST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Kuala Lumpur, Singapore\",\r\n\t\t\t\tutc: [\"Asia/Brunei\", \"Asia/Kuala_Lumpur\", \"Asia/Kuching\", \"Asia/Makassar\", \"Asia/Manila\", \"Asia/Singapore\", \"Etc/GMT-8\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"W. Australia Standard Time\",\r\n\t\t\t\tabbr: \"WAST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Perth\",\r\n\t\t\t\tutc: [\"Antarctica/Casey\", \"Australia/Perth\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Taipei Standard Time\",\r\n\t\t\t\tabbr: \"TST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Taipei\",\r\n\t\t\t\tutc: [\"Asia/Taipei\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Ulaanbaatar Standard Time\",\r\n\t\t\t\tabbr: \"UST\",\r\n\t\t\t\toffset: 8,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+08:00) Ulaanbaatar\",\r\n\t\t\t\tutc: [\"Asia/Choibalsan\", \"Asia/Ulaanbaatar\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"North Asia East Standard Time\",\r\n\t\t\t\tabbr: \"NAEST\",\r\n\t\t\t\toffset: 9,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+09:00) Irkutsk\",\r\n\t\t\t\tutc: [\"Asia/Irkutsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Tokyo Standard Time\",\r\n\t\t\t\tabbr: \"TST\",\r\n\t\t\t\toffset: 9,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+09:00) Osaka, Sapporo, Tokyo\",\r\n\t\t\t\tutc: [\"Asia/Dili\", \"Asia/Jayapura\", \"Asia/Tokyo\", \"Etc/GMT-9\", \"Pacific/Palau\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Korea Standard Time\",\r\n\t\t\t\tabbr: \"KST\",\r\n\t\t\t\toffset: 9,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+09:00) Seoul\",\r\n\t\t\t\tutc: [\"Asia/Pyongyang\", \"Asia/Seoul\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Cen. Australia Standard Time\",\r\n\t\t\t\tabbr: \"CAST\",\r\n\t\t\t\toffset: 9.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+09:30) Adelaide\",\r\n\t\t\t\tutc: [\"Australia/Adelaide\", \"Australia/Broken_Hill\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"AUS Central Standard Time\",\r\n\t\t\t\tabbr: \"ACST\",\r\n\t\t\t\toffset: 9.5,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+09:30) Darwin\",\r\n\t\t\t\tutc: [\"Australia/Darwin\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"E. Australia Standard Time\",\r\n\t\t\t\tabbr: \"EAST\",\r\n\t\t\t\toffset: 10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+10:00) Brisbane\",\r\n\t\t\t\tutc: [\"Australia/Brisbane\", \"Australia/Lindeman\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"AUS Eastern Standard Time\",\r\n\t\t\t\tabbr: \"AEST\",\r\n\t\t\t\toffset: 10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+10:00) Canberra, Melbourne, Sydney\",\r\n\t\t\t\tutc: [\"Australia/Melbourne\", \"Australia/Sydney\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"West Pacific Standard Time\",\r\n\t\t\t\tabbr: \"WPST\",\r\n\t\t\t\toffset: 10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+10:00) Guam, Port Moresby\",\r\n\t\t\t\tutc: [\"Antarctica/DumontDUrville\", \"Etc/GMT-10\", \"Pacific/Guam\", \"Pacific/Port_Moresby\", \"Pacific/Saipan\", \"Pacific/Truk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Tasmania Standard Time\",\r\n\t\t\t\tabbr: \"TST\",\r\n\t\t\t\toffset: 10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+10:00) Hobart\",\r\n\t\t\t\tutc: [\"Australia/Currie\", \"Australia/Hobart\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Yakutsk Standard Time\",\r\n\t\t\t\tabbr: \"YST\",\r\n\t\t\t\toffset: 10,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+10:00) Yakutsk\",\r\n\t\t\t\tutc: [\"Asia/Chita\", \"Asia/Khandyga\", \"Asia/Yakutsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Central Pacific Standard Time\",\r\n\t\t\t\tabbr: \"CPST\",\r\n\t\t\t\toffset: 11,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+11:00) Solomon Is., New Caledonia\",\r\n\t\t\t\tutc: [\"Antarctica/Macquarie\", \"Etc/GMT-11\", \"Pacific/Efate\", \"Pacific/Guadalcanal\", \"Pacific/Kosrae\", \"Pacific/Noumea\", \"Pacific/Ponape\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Vladivostok Standard Time\",\r\n\t\t\t\tabbr: \"VST\",\r\n\t\t\t\toffset: 11,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+11:00) Vladivostok\",\r\n\t\t\t\tutc: [\"Asia/Sakhalin\", \"Asia/Ust-Nera\", \"Asia/Vladivostok\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"New Zealand Standard Time\",\r\n\t\t\t\tabbr: \"NZST\",\r\n\t\t\t\toffset: 12,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+12:00) Auckland, Wellington\",\r\n\t\t\t\tutc: [\"Antarctica/McMurdo\", \"Pacific/Auckland\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"UTC+12\",\r\n\t\t\t\tabbr: \"U\",\r\n\t\t\t\toffset: 12,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+12:00) Coordinated Universal Time+12\",\r\n\t\t\t\tutc: [\"Etc/GMT-12\", \"Pacific/Funafuti\", \"Pacific/Kwajalein\", \"Pacific/Majuro\", \"Pacific/Nauru\", \"Pacific/Tarawa\", \"Pacific/Wake\", \"Pacific/Wallis\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Fiji Standard Time\",\r\n\t\t\t\tabbr: \"FST\",\r\n\t\t\t\toffset: 12,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+12:00) Fiji\",\r\n\t\t\t\tutc: [\"Pacific/Fiji\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Magadan Standard Time\",\r\n\t\t\t\tabbr: \"MST\",\r\n\t\t\t\toffset: 12,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+12:00) Magadan\",\r\n\t\t\t\tutc: [\"Asia/Anadyr\", \"Asia/Kamchatka\", \"Asia/Magadan\", \"Asia/Srednekolymsk\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Kamchatka Standard Time\",\r\n\t\t\t\tabbr: \"KDT\",\r\n\t\t\t\toffset: 13,\r\n\t\t\t\tisdst: true,\r\n\t\t\t\ttext: \"(UTC+12:00) Petropavlovsk-Kamchatsky - Old\",\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Tonga Standard Time\",\r\n\t\t\t\tabbr: \"TST\",\r\n\t\t\t\toffset: 13,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+13:00) Nuku'alofa\",\r\n\t\t\t\tutc: [\"Etc/GMT-13\", \"Pacific/Enderbury\", \"Pacific/Fakaofo\", \"Pacific/Tongatapu\"],\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tname: \"Samoa Standard Time\",\r\n\t\t\t\tabbr: \"SST\",\r\n\t\t\t\toffset: 13,\r\n\t\t\t\tisdst: false,\r\n\t\t\t\ttext: \"(UTC+13:00) Samoa\",\r\n\t\t\t\tutc: [\"Pacific/Apia\"],\r\n\t\t\t},\r\n\t\t],\r\n\t\t//List source: http://answers.google.com/answers/threadview/id/589312.html\r\n\t\tprofession: [\"Airline Pilot\", \"Academic Team\", \"Accountant\", \"Account Executive\", \"Actor\", \"Actuary\", \"Acquisition Analyst\", \"Administrative Asst.\", \"Administrative Analyst\", \"Administrator\", \"Advertising Director\", \"Aerospace Engineer\", \"Agent\", \"Agricultural Inspector\", \"Agricultural Scientist\", \"Air Traffic Controller\", \"Animal Trainer\", \"Anthropologist\", \"Appraiser\", \"Architect\", \"Art Director\", \"Artist\", \"Astronomer\", \"Athletic Coach\", \"Auditor\", \"Author\", \"Baker\", \"Banker\", \"Bankruptcy Attorney\", \"Benefits Manager\", \"Biologist\", \"Bio-feedback Specialist\", \"Biomedical Engineer\", \"Biotechnical Researcher\", \"Broadcaster\", \"Broker\", \"Building Manager\", \"Building Contractor\", \"Building Inspector\", \"Business Analyst\", \"Business Planner\", \"Business Manager\", \"Buyer\", \"Call Center Manager\", \"Career Counselor\", \"Cash Manager\", \"Ceramic Engineer\", \"Chief Executive Officer\", \"Chief Operation Officer\", \"Chef\", \"Chemical Engineer\", \"Chemist\", \"Child Care Manager\", \"Chief Medical Officer\", \"Chiropractor\", \"Cinematographer\", \"City Housing Manager\", \"City Manager\", \"Civil Engineer\", \"Claims Manager\", \"Clinical Research Assistant\", \"Collections Manager.\", \"Compliance Manager\", \"Comptroller\", \"Computer Manager\", \"Commercial Artist\", \"Communications Affairs Director\", \"Communications Director\", \"Communications Engineer\", \"Compensation Analyst\", \"Computer Programmer\", \"Computer Ops. Manager\", \"Computer Engineer\", \"Computer Operator\", \"Computer Graphics Specialist\", \"Construction Engineer\", \"Construction Manager\", \"Consultant\", \"Consumer Relations Manager\", \"Contract Administrator\", \"Copyright Attorney\", \"Copywriter\", \"Corporate Planner\", \"Corrections Officer\", \"Cosmetologist\", \"Credit Analyst\", \"Cruise Director\", \"Chief Information Officer\", \"Chief Technology Officer\", \"Customer Service Manager\", \"Cryptologist\", \"Dancer\", \"Data Security Manager\", \"Database Manager\", \"Day Care Instructor\", \"Dentist\", \"Designer\", \"Design Engineer\", \"Desktop Publisher\", \"Developer\", \"Development Officer\", \"Diamond Merchant\", \"Dietitian\", \"Direct Marketer\", \"Director\", \"Distribution Manager\", \"Diversity Manager\", \"Economist\", \"EEO Compliance Manager\", \"Editor\", \"Education Adminator\", \"Electrical Engineer\", \"Electro Optical Engineer\", \"Electronics Engineer\", \"Embassy Management\", \"Employment Agent\", \"Engineer Technician\", \"Entrepreneur\", \"Environmental Analyst\", \"Environmental Attorney\", \"Environmental Engineer\", \"Environmental Specialist\", \"Escrow Officer\", \"Estimator\", \"Executive Assistant\", \"Executive Director\", \"Executive Recruiter\", \"Facilities Manager\", \"Family Counselor\", \"Fashion Events Manager\", \"Fashion Merchandiser\", \"Fast Food Manager\", \"Film Producer\", \"Film Production Assistant\", \"Financial Analyst\", \"Financial Planner\", \"Financier\", \"Fine Artist\", \"Wildlife Specialist\", \"Fitness Consultant\", \"Flight Attendant\", \"Flight Engineer\", \"Floral Designer\", \"Food & Beverage Director\", \"Food Service Manager\", \"Forestry Technician\", \"Franchise Management\", \"Franchise Sales\", \"Fraud Investigator\", \"Freelance Writer\", \"Fund Raiser\", \"General Manager\", \"Geologist\", \"General Counsel\", \"Geriatric Specialist\", \"Gerontologist\", \"Glamour Photographer\", \"Golf Club Manager\", \"Gourmet Chef\", \"Graphic Designer\", \"Grounds Keeper\", \"Hazardous Waste Manager\", \"Health Care Manager\", \"Health Therapist\", \"Health Service Administrator\", \"Hearing Officer\", \"Home Economist\", \"Horticulturist\", \"Hospital Administrator\", \"Hotel Manager\", \"Human Resources Manager\", \"Importer\", \"Industrial Designer\", \"Industrial Engineer\", \"Information Director\", \"Inside Sales\", \"Insurance Adjuster\", \"Interior Decorator\", \"Internal Controls Director\", \"International Acct.\", \"International Courier\", \"International Lawyer\", \"Interpreter\", \"Investigator\", \"Investment Banker\", \"Investment Manager\", \"IT Architect\", \"IT Project Manager\", \"IT Systems Analyst\", \"Jeweler\", \"Joint Venture Manager\", \"Journalist\", \"Labor Negotiator\", \"Labor Organizer\", \"Labor Relations Manager\", \"Lab Services Director\", \"Lab Technician\", \"Land Developer\", \"Landscape Architect\", \"Law Enforcement Officer\", \"Lawyer\", \"Lead Software Engineer\", \"Lead Software Test Engineer\", \"Leasing Manager\", \"Legal Secretary\", \"Library Manager\", \"Litigation Attorney\", \"Loan Officer\", \"Lobbyist\", \"Logistics Manager\", \"Maintenance Manager\", \"Management Consultant\", \"Managed Care Director\", \"Managing Partner\", \"Manufacturing Director\", \"Manpower Planner\", \"Marine Biologist\", \"Market Res. Analyst\", \"Marketing Director\", \"Materials Manager\", \"Mathematician\", \"Membership Chairman\", \"Mechanic\", \"Mechanical Engineer\", \"Media Buyer\", \"Medical Investor\", \"Medical Secretary\", \"Medical Technician\", \"Mental Health Counselor\", \"Merchandiser\", \"Metallurgical Engineering\", \"Meteorologist\", \"Microbiologist\", \"MIS Manager\", \"Motion Picture Director\", \"Multimedia Director\", \"Musician\", \"Network Administrator\", \"Network Specialist\", \"Network Operator\", \"New Product Manager\", \"Novelist\", \"Nuclear Engineer\", \"Nuclear Specialist\", \"Nutritionist\", \"Nursing Administrator\", \"Occupational Therapist\", \"Oceanographer\", \"Office Manager\", \"Operations Manager\", \"Operations Research Director\", \"Optical Technician\", \"Optometrist\", \"Organizational Development Manager\", \"Outplacement Specialist\", \"Paralegal\", \"Park Ranger\", \"Patent Attorney\", \"Payroll Specialist\", \"Personnel Specialist\", \"Petroleum Engineer\", \"Pharmacist\", \"Photographer\", \"Physical Therapist\", \"Physician\", \"Physician Assistant\", \"Physicist\", \"Planning Director\", \"Podiatrist\", \"Political Analyst\", \"Political Scientist\", \"Politician\", \"Portfolio Manager\", \"Preschool Management\", \"Preschool Teacher\", \"Principal\", \"Private Banker\", \"Private Investigator\", \"Probation Officer\", \"Process Engineer\", \"Producer\", \"Product Manager\", \"Product Engineer\", \"Production Engineer\", \"Production Planner\", \"Professional Athlete\", \"Professional Coach\", \"Professor\", \"Project Engineer\", \"Project Manager\", \"Program Manager\", \"Property Manager\", \"Public Administrator\", \"Public Safety Director\", \"PR Specialist\", \"Publisher\", \"Purchasing Agent\", \"Publishing Director\", \"Quality Assurance Specialist\", \"Quality Control Engineer\", \"Quality Control Inspector\", \"Radiology Manager\", \"Railroad Engineer\", \"Real Estate Broker\", \"Recreational Director\", \"Recruiter\", \"Redevelopment Specialist\", \"Regulatory Affairs Manager\", \"Registered Nurse\", \"Rehabilitation Counselor\", \"Relocation Manager\", \"Reporter\", \"Research Specialist\", \"Restaurant Manager\", \"Retail Store Manager\", \"Risk Analyst\", \"Safety Engineer\", \"Sales Engineer\", \"Sales Trainer\", \"Sales Promotion Manager\", \"Sales Representative\", \"Sales Manager\", \"Service Manager\", \"Sanitation Engineer\", \"Scientific Programmer\", \"Scientific Writer\", \"Securities Analyst\", \"Security Consultant\", \"Security Director\", \"Seminar Presenter\", \"Ship's Officer\", \"Singer\", \"Social Director\", \"Social Program Planner\", \"Social Research\", \"Social Scientist\", \"Social Worker\", \"Sociologist\", \"Software Developer\", \"Software Engineer\", \"Software Test Engineer\", \"Soil Scientist\", \"Special Events Manager\", \"Special Education Teacher\", \"Special Projects Director\", \"Speech Pathologist\", \"Speech Writer\", \"Sports Event Manager\", \"Statistician\", \"Store Manager\", \"Strategic Alliance Director\", \"Strategic Planning Director\", \"Stress Reduction Specialist\", \"Stockbroker\", \"Surveyor\", \"Structural Engineer\", \"Superintendent\", \"Supply Chain Director\", \"System Engineer\", \"Systems Analyst\", \"Systems Programmer\", \"System Administrator\", \"Tax Specialist\", \"Teacher\", \"Technical Support Specialist\", \"Technical Illustrator\", \"Technical Writer\", \"Technology Director\", \"Telecom Analyst\", \"Telemarketer\", \"Theatrical Director\", \"Title Examiner\", \"Tour Escort\", \"Tour Guide Director\", \"Traffic Manager\", \"Trainer Translator\", \"Transportation Manager\", \"Travel Agent\", \"Treasurer\", \"TV Programmer\", \"Underwriter\", \"Union Representative\", \"University Administrator\", \"University Dean\", \"Urban Planner\", \"Veterinarian\", \"Vendor Relations Director\", \"Viticulturist\", \"Warehouse Manager\"],\r\n\t\tanimals: {\r\n\t\t\t//list of ocean animals comes from https://owlcation.com/stem/list-of-ocean-animals\r\n\t\t\tocean: [\"Acantharea\", \"Anemone\", \"Angelfish King\", \"Ahi Tuna\", \"Albacore\", \"American Oyster\", \"Anchovy\", \"Armored Snail\", \"Arctic Char\", \"Atlantic Bluefin Tuna\", \"Atlantic Cod\", \"Atlantic Goliath Grouper\", \"Atlantic Trumpetfish\", \"Atlantic Wolffish\", \"Baleen Whale\", \"Banded Butterflyfish\", \"Banded Coral Shrimp\", \"Banded Sea Krait\", \"Barnacle\", \"Barndoor Skate\", \"Barracuda\", \"Basking Shark\", \"Bass\", \"Beluga Whale\", \"Bluebanded Goby\", \"Bluehead Wrasse\", \"Bluefish\", \"Bluestreak Cleaner-Wrasse\", \"Blue Marlin\", \"Blue Shark\", \"Blue Spiny Lobster\", \"Blue Tang\", \"Blue Whale\", \"Broadclub Cuttlefish\", \"Bull Shark\", \"Chambered Nautilus\", \"Chilean Basket Star\", \"Chilean Jack Mackerel\", \"Chinook Salmon\", \"Christmas Tree Worm\", \"Clam\", \"Clown Anemonefish\", \"Clown Triggerfish\", \"Cod\", \"Coelacanth\", \"Cockscomb Cup Coral\", \"Common Fangtooth\", \"Conch\", \"Cookiecutter Shark\", \"Copepod\", \"Coral\", \"Corydoras\", \"Cownose Ray\", \"Crab\", \"Crown-of-Thorns Starfish\", \"Cushion Star\", \"Cuttlefish\", \"California Sea Otters\", \"Dolphin\", \"Dolphinfish\", \"Dory\", \"Devil Fish\", \"Dugong\", \"Dumbo Octopus\", \"Dungeness Crab\", \"Eccentric Sand Dollar\", \"Edible Sea Cucumber\", \"Eel\", \"Elephant Seal\", \"Elkhorn Coral\", \"Emperor Shrimp\", \"Estuarine Crocodile\", \"Fathead Sculpin\", \"Fiddler Crab\", \"Fin Whale\", \"Flameback\", \"Flamingo Tongue Snail\", \"Flashlight Fish\", \"Flatback Turtle\", \"Flatfish\", \"Flying Fish\", \"Flounder\", \"Fluke\", \"French Angelfish\", \"Frilled Shark\", \"Fugu (also called Pufferfish)\", \"Gar\", \"Geoduck\", \"Giant Barrel Sponge\", \"Giant Caribbean Sea Anemone\", \"Giant Clam\", \"Giant Isopod\", \"Giant Kingfish\", \"Giant Oarfish\", \"Giant Pacific Octopus\", \"Giant Pyrosome\", \"Giant Sea Star\", \"Giant Squid\", \"Glowing Sucker Octopus\", \"Giant Tube Worm\", \"Goblin Shark\", \"Goosefish\", \"Great White Shark\", \"Greenland Shark\", \"Grey Atlantic Seal\", \"Grouper\", \"Grunion\", \"Guineafowl Puffer\", \"Haddock\", \"Hake\", \"Halibut\", \"Hammerhead Shark\", \"Hapuka\", \"Harbor Porpoise\", \"Harbor Seal\", \"Hatchetfish\", \"Hawaiian Monk Seal\", \"Hawksbill Turtle\", \"Hector's Dolphin\", \"Hermit Crab\", \"Herring\", \"Hoki\", \"Horn Shark\", \"Horseshoe Crab\", \"Humpback Anglerfish\", \"Humpback Whale\", \"Icefish\", \"Imperator Angelfish\", \"Irukandji Jellyfish\", \"Isopod\", \"Ivory Bush Coral\", \"Japanese Spider Crab\", \"Jellyfish\", \"John Dory\", \"Juan Fernandez Fur Seal\", \"Killer Whale\", \"Kiwa Hirsuta\", \"Krill\", \"Lagoon Triggerfish\", \"Lamprey\", \"Leafy Seadragon\", \"Leopard Seal\", \"Limpet\", \"Ling\", \"Lionfish\", \"Lions Mane Jellyfish\", \"Lobe Coral\", \"Lobster\", \"Loggerhead Turtle\", \"Longnose Sawshark\", \"Longsnout Seahorse\", \"Lophelia Coral\", \"Marrus Orthocanna\", \"Manatee\", \"Manta Ray\", \"Marlin\", \"Megamouth Shark\", \"Mexican Lookdown\", \"Mimic Octopus\", \"Moon Jelly\", \"Mollusk\", \"Monkfish\", \"Moray Eel\", \"Mullet\", \"Mussel\", \"Megaladon\", \"Napoleon Wrasse\", \"Nassau Grouper\", \"Narwhal\", \"Nautilus\", \"Needlefish\", \"Northern Seahorse\", \"North Atlantic Right Whale\", \"Northern Red Snapper\", \"Norway Lobster\", \"Nudibranch\", \"Nurse Shark\", \"Oarfish\", \"Ocean Sunfish\", \"Oceanic Whitetip Shark\", \"Octopus\", \"Olive Sea Snake\", \"Orange Roughy\", \"Ostracod\", \"Otter\", \"Oyster\", \"Pacific Angelshark\", \"Pacific Blackdragon\", \"Pacific Halibut\", \"Pacific Sardine\", \"Pacific Sea Nettle Jellyfish\", \"Pacific White Sided Dolphin\", \"Pantropical Spotted Dolphin\", \"Patagonian Toothfish\", \"Peacock Mantis Shrimp\", \"Pelagic Thresher Shark\", \"Penguin\", \"Peruvian Anchoveta\", \"Pilchard\", \"Pink Salmon\", \"Pinniped\", \"Plankton\", \"Porpoise\", \"Polar Bear\", \"Portuguese Man o' War\", \"Pycnogonid Sea Spider\", \"Quahog\", \"Queen Angelfish\", \"Queen Conch\", \"Queen Parrotfish\", \"Queensland Grouper\", \"Ragfish\", \"Ratfish\", \"Rattail Fish\", \"Ray\", \"Red Drum\", \"Red King Crab\", \"Ringed Seal\", \"Risso's Dolphin\", \"Ross Seals\", \"Sablefish\", \"Salmon\", \"Sand Dollar\", \"Sandbar Shark\", \"Sawfish\", \"Sarcastic Fringehead\", \"Scalloped Hammerhead Shark\", \"Seahorse\", \"Sea Cucumber\", \"Sea Lion\", \"Sea Urchin\", \"Seal\", \"Shark\", \"Shortfin Mako Shark\", \"Shovelnose Guitarfish\", \"Shrimp\", \"Silverside Fish\", \"Skipjack Tuna\", \"Slender Snipe Eel\", \"Smalltooth Sawfish\", \"Smelts\", \"Sockeye Salmon\", \"Southern Stingray\", \"Sponge\", \"Spotted Porcupinefish\", \"Spotted Dolphin\", \"Spotted Eagle Ray\", \"Spotted Moray\", \"Squid\", \"Squidworm\", \"Starfish\", \"Stickleback\", \"Stonefish\", \"Stoplight Loosejaw\", \"Sturgeon\", \"Swordfish\", \"Tan Bristlemouth\", \"Tasseled Wobbegong\", \"Terrible Claw Lobster\", \"Threespot Damselfish\", \"Tiger Prawn\", \"Tiger Shark\", \"Tilefish\", \"Toadfish\", \"Tropical Two-Wing Flyfish\", \"Tuna\", \"Umbrella Squid\", \"Velvet Crab\", \"Venus Flytrap Sea Anemone\", \"Vigtorniella Worm\", \"Viperfish\", \"Vampire Squid\", \"Vaquita\", \"Wahoo\", \"Walrus\", \"West Indian Manatee\", \"Whale\", \"Whale Shark\", \"Whiptail Gulper\", \"White-Beaked Dolphin\", \"White-Ring Garden Eel\", \"White Shrimp\", \"Wobbegong\", \"Wrasse\", \"Wreckfish\", \"Xiphosura\", \"Yellowtail Damselfish\", \"Yelloweye Rockfish\", \"Yellow Cup Black Coral\", \"Yellow Tube Sponge\", \"Yellowfin Tuna\", \"Zebrashark\", \"Zooplankton\"],\r\n\t\t\t//list of desert, grassland, and forest animals comes from http://www.skyenimals.com/\r\n\t\t\tdesert: [\"Aardwolf\", \"Addax\", \"African Wild Ass\", \"Ant\", \"Antelope\", \"Armadillo\", \"Baboon\", \"Badger\", \"Bat\", \"Bearded Dragon\", \"Beetle\", \"Bird\", \"Black-footed Cat\", \"Boa\", \"Brown Bear\", \"Bustard\", \"Butterfly\", \"Camel\", \"Caracal\", \"Caracara\", \"Caterpillar\", \"Centipede\", \"Cheetah\", \"Chipmunk\", \"Chuckwalla\", \"Climbing Mouse\", \"Coati\", \"Cobra\", \"Cotton Rat\", \"Cougar\", \"Courser\", \"Crane Fly\", \"Crow\", \"Dassie Rat\", \"Dove\", \"Dunnart\", \"Eagle\", \"Echidna\", \"Elephant\", \"Emu\", \"Falcon\", \"Fly\", \"Fox\", \"Frogmouth\", \"Gecko\", \"Geoffroy's Cat\", \"Gerbil\", \"Grasshopper\", \"Guanaco\", \"Gundi\", \"Hamster\", \"Hawk\", \"Hedgehog\", \"Hyena\", \"Hyrax\", \"Jackal\", \"Kangaroo\", \"Kangaroo Rat\", \"Kestrel\", \"Kowari\", \"Kultarr\", \"Leopard\", \"Lion\", \"Macaw\", \"Meerkat\", \"Mouse\", \"Oryx\", \"Ostrich\", \"Owl\", \"Pronghorn\", \"Python\", \"Rabbit\", \"Raccoon\", \"Rattlesnake\", \"Rhinoceros\", \"Sand Cat\", \"Spectacled Bear\", \"Spiny Mouse\", \"Starling\", \"Stick Bug\", \"Tarantula\", \"Tit\", \"Toad\", \"Tortoise\", \"Tyrant Flycatcher\", \"Viper\", \"Vulture\", \"Waxwing\", \"Xerus\", \"Zebra\"],\r\n\t\t\tgrassland: [\"Aardvark\", \"Aardwolf\", \"Accentor\", \"African Buffalo\", \"African Wild Dog\", \"Alpaca\", \"Anaconda\", \"Ant\", \"Anteater\", \"Antelope\", \"Armadillo\", \"Baboon\", \"Badger\", \"Bandicoot\", \"Barbet\", \"Bat\", \"Bee\", \"Bee-eater\", \"Beetle\", \"Bird\", \"Bison\", \"Black-footed Cat\", \"Black-footed Ferret\", \"Bluebird\", \"Boa\", \"Bowerbird\", \"Brown Bear\", \"Bush Dog\", \"Bushshrike\", \"Bustard\", \"Butterfly\", \"Buzzard\", \"Caracal\", \"Caracara\", \"Cardinal\", \"Caterpillar\", \"Cheetah\", \"Chipmunk\", \"Civet\", \"Climbing Mouse\", \"Clouded Leopard\", \"Coati\", \"Cobra\", \"Cockatoo\", \"Cockroach\", \"Common Genet\", \"Cotton Rat\", \"Cougar\", \"Courser\", \"Coyote\", \"Crane\", \"Crane Fly\", \"Cricket\", \"Crow\", \"Culpeo\", \"Death Adder\", \"Deer\", \"Deer Mouse\", \"Dingo\", \"Dinosaur\", \"Dove\", \"Drongo\", \"Duck\", \"Duiker\", \"Dunnart\", \"Eagle\", \"Echidna\", \"Elephant\", \"Elk\", \"Emu\", \"Falcon\", \"Finch\", \"Flea\", \"Fly\", \"Flying Frog\", \"Fox\", \"Frog\", \"Frogmouth\", \"Garter Snake\", \"Gazelle\", \"Gecko\", \"Geoffroy's Cat\", \"Gerbil\", \"Giant Tortoise\", \"Giraffe\", \"Grasshopper\", \"Grison\", \"Groundhog\", \"Grouse\", \"Guanaco\", \"Guinea Pig\", \"Hamster\", \"Harrier\", \"Hartebeest\", \"Hawk\", \"Hedgehog\", \"Helmetshrike\", \"Hippopotamus\", \"Hornbill\", \"Hyena\", \"Hyrax\", \"Impala\", \"Jackal\", \"Jaguar\", \"Jaguarundi\", \"Kangaroo\", \"Kangaroo Rat\", \"Kestrel\", \"Kultarr\", \"Ladybug\", \"Leopard\", \"Lion\", \"Macaw\", \"Meerkat\", \"Mouse\", \"Newt\", \"Oryx\", \"Ostrich\", \"Owl\", \"Pangolin\", \"Pheasant\", \"Prairie Dog\", \"Pronghorn\", \"Przewalski's Horse\", \"Python\", \"Quoll\", \"Rabbit\", \"Raven\", \"Rhinoceros\", \"Shelduck\", \"Sloth Bear\", \"Spectacled Bear\", \"Squirrel\", \"Starling\", \"Stick Bug\", \"Tamandua\", \"Tasmanian Devil\", \"Thornbill\", \"Thrush\", \"Toad\", \"Tortoise\"],\r\n\t\t\tforest: [\"Agouti\", \"Anaconda\", \"Anoa\", \"Ant\", \"Anteater\", \"Antelope\", \"Armadillo\", \"Asian Black Bear\", \"Aye-aye\", \"Babirusa\", \"Baboon\", \"Badger\", \"Bandicoot\", \"Banteng\", \"Barbet\", \"Basilisk\", \"Bat\", \"Bearded Dragon\", \"Bee\", \"Bee-eater\", \"Beetle\", \"Bettong\", \"Binturong\", \"Bird-of-paradise\", \"Bongo\", \"Bowerbird\", \"Bulbul\", \"Bush Dog\", \"Bushbaby\", \"Bushshrike\", \"Butterfly\", \"Buzzard\", \"Caecilian\", \"Cardinal\", \"Cassowary\", \"Caterpillar\", \"Centipede\", \"Chameleon\", \"Chimpanzee\", \"Cicada\", \"Civet\", \"Clouded Leopard\", \"Coati\", \"Cobra\", \"Cockatoo\", \"Cockroach\", \"Colugo\", \"Cotinga\", \"Cotton Rat\", \"Cougar\", \"Crane Fly\", \"Cricket\", \"Crocodile\", \"Crow\", \"Cuckoo\", \"Cuscus\", \"Death Adder\", \"Deer\", \"Dhole\", \"Dingo\", \"Dinosaur\", \"Drongo\", \"Duck\", \"Duiker\", \"Eagle\", \"Echidna\", \"Elephant\", \"Finch\", \"Flat-headed Cat\", \"Flea\", \"Flowerpecker\", \"Fly\", \"Flying Frog\", \"Fossa\", \"Frog\", \"Frogmouth\", \"Gaur\", \"Gecko\", \"Gorilla\", \"Grison\", \"Hawaiian Honeycreeper\", \"Hawk\", \"Hedgehog\", \"Helmetshrike\", \"Hornbill\", \"Hyrax\", \"Iguana\", \"Jackal\", \"Jaguar\", \"Jaguarundi\", \"Kestrel\", \"Ladybug\", \"Lemur\", \"Leopard\", \"Lion\", \"Macaw\", \"Mandrill\", \"Margay\", \"Monkey\", \"Mouse\", \"Mouse Deer\", \"Newt\", \"Okapi\", \"Old World Flycatcher\", \"Orangutan\", \"Owl\", \"Pangolin\", \"Peafowl\", \"Pheasant\", \"Possum\", \"Python\", \"Quokka\", \"Rabbit\", \"Raccoon\", \"Red Panda\", \"Red River Hog\", \"Rhinoceros\", \"Sloth Bear\", \"Spectacled Bear\", \"Squirrel\", \"Starling\", \"Stick Bug\", \"Sun Bear\", \"Tamandua\", \"Tamarin\", \"Tapir\", \"Tarantula\", \"Thrush\", \"Tiger\", \"Tit\", \"Toad\", \"Tortoise\", \"Toucan\", \"Trogon\", \"Trumpeter\", \"Turaco\", \"Turtle\", \"Tyrant Flycatcher\", \"Viper\", \"Vulture\", \"Wallaby\", \"Warbler\", \"Wasp\", \"Waxwing\", \"Weaver\", \"Weaver-finch\", \"Whistler\", \"White-eye\", \"Whydah\", \"Woodswallow\", \"Worm\", \"Wren\", \"Xenops\", \"Yellowjacket\", \"Accentor\", \"African Buffalo\", \"American Black Bear\", \"Anole\", \"Bird\", \"Bison\", \"Boa\", \"Brown Bear\", \"Chipmunk\", \"Common Genet\", \"Copperhead\", \"Coyote\", \"Deer Mouse\", \"Dormouse\", \"Elk\", \"Emu\", \"Fisher\", \"Fox\", \"Garter Snake\", \"Giant Panda\", \"Giant Tortoise\", \"Groundhog\", \"Grouse\", \"Guanaco\", \"Himalayan Tahr\", \"Kangaroo\", \"Koala\", \"Numbat\", \"Quoll\", \"Raccoon dog\", \"Tasmanian Devil\", \"Thornbill\", \"Turkey\", \"Vole\", \"Weasel\", \"Wildcat\", \"Wolf\", \"Wombat\", \"Woodchuck\", \"Woodpecker\"],\r\n\t\t\t//list of farm animals comes from https://www.buzzle.com/articles/farm-animals-list.html\r\n\t\t\tfarm: [\"Alpaca\", \"Buffalo\", \"Banteng\", \"Cow\", \"Cat\", \"Chicken\", \"Carp\", \"Camel\", \"Donkey\", \"Dog\", \"Duck\", \"Emu\", \"Goat\", \"Gayal\", \"Guinea\", \"Goose\", \"Horse\", \"Honey\", \"Llama\", \"Pig\", \"Pigeon\", \"Rhea\", \"Rabbit\", \"Sheep\", \"Silkworm\", \"Turkey\", \"Yak\", \"Zebu\"],\r\n\t\t\t//list of pet animals comes from https://www.dogbreedinfo.com/pets/pet.htm\r\n\t\t\tpet: [\"Bearded Dragon\", \"Birds\", \"Burro\", \"Cats\", \"Chameleons\", \"Chickens\", \"Chinchillas\", \"Chinese Water Dragon\", \"Cows\", \"Dogs\", \"Donkey\", \"Ducks\", \"Ferrets\", \"Fish\", \"Geckos\", \"Geese\", \"Gerbils\", \"Goats\", \"Guinea Fowl\", \"Guinea Pigs\", \"Hamsters\", \"Hedgehogs\", \"Horses\", \"Iguanas\", \"Llamas\", \"Lizards\", \"Mice\", \"Mule\", \"Peafowl\", \"Pigs and Hogs\", \"Pigeons\", \"Ponies\", \"Pot Bellied Pig\", \"Rabbits\", \"Rats\", \"Sheep\", \"Skinks\", \"Snakes\", \"Stick Insects\", \"Sugar Gliders\", \"Tarantula\", \"Turkeys\", \"Turtles\"],\r\n\t\t\t//list of zoo animals comes from https://bronxzoo.com/animals\r\n\t\t\tzoo: [\"Aardvark\", \"African Wild Dog\", \"Aldabra Tortoise\", \"American Alligator\", \"American Bison\", \"Amur Tiger\", \"Anaconda\", \"Andean Condor\", \"Asian Elephant\", \"Baby Doll Sheep\", \"Bald Eagle\", \"Barred Owl\", \"Blue Iguana\", \"Boer Goat\", \"California Sea Lion\", \"Caribbean Flamingo\", \"Chinchilla\", \"Collared Lemur\", \"Coquerel's Sifaka\", \"Cuban Amazon Parrot\", \"Ebony Langur\", \"Fennec Fox\", \"Fossa\", \"Gelada\", \"Giant Anteater\", \"Giraffe\", \"Gorilla\", \"Grizzly Bear\", \"Henkel's Leaf-tailed Gecko\", \"Indian Gharial\", \"Indian Rhinoceros\", \"King Cobra\", \"King Vulture\", \"Komodo Dragon\", \"Linne's Two-toed Sloth\", \"Lion\", \"Little Penguin\", \"Madagascar Tree Boa\", \"Magellanic Penguin\", \"Malayan Tapir\", \"Malayan Tiger\", \"Matschies Tree Kangaroo\", \"Mini Donkey\", \"Monarch Butterfly\", \"Nile crocodile\", \"North American Porcupine\", \"Nubian Ibex\", \"Okapi\", \"Poison Dart Frog\", \"Polar Bear\", \"Pygmy Marmoset\", \"Radiated Tortoise\", \"Red Panda\", \"Red Ruffed Lemur\", \"Ring-tailed Lemur\", \"Ring-tailed Mongoose\", \"Rock Hyrax\", \"Small Clawed Asian Otter\", \"Snow Leopard\", \"Snowy Owl\", \"Southern White-faced Owl\", \"Southern White Rhinocerous\", \"Squirrel Monkey\", \"Tufted Puffin\", \"White Cheeked Gibbon\", \"White-throated Bee Eater\", \"Zebra\"],\r\n\t\t},\r\n\t};\r\n\r\n\tvar o_hasOwnProperty = Object.prototype.hasOwnProperty;\r\n\tvar o_keys =\r\n\t\tObject.keys ||\r\n\t\tfunction (obj) {\r\n\t\t\tvar result = [];\r\n\t\t\tfor (var key in obj) {\r\n\t\t\t\tif (o_hasOwnProperty.call(obj, key)) {\r\n\t\t\t\t\tresult.push(key);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn result;\r\n\t\t};\r\n\r\n\tfunction _copyObject(source, target) {\r\n\t\tvar keys = o_keys(source);\r\n\t\tvar key;\r\n\r\n\t\tfor (var i = 0, l = keys.length; i < l; i++) {\r\n\t\t\tkey = keys[i];\r\n\t\t\ttarget[key] = source[key] || target[key];\r\n\t\t}\r\n\t}\r\n\r\n\tfunction _copyArray(source, target) {\r\n\t\tfor (var i = 0, l = source.length; i < l; i++) {\r\n\t\t\ttarget[i] = source[i];\r\n\t\t}\r\n\t}\r\n\r\n\tfunction copyObject(source, _target) {\r\n\t\tvar isArray = Array.isArray(source);\r\n\t\tvar target = _target || (isArray ? new Array(source.length) : {});\r\n\r\n\t\tif (isArray) {\r\n\t\t\t_copyArray(source, target);\r\n\t\t} else {\r\n\t\t\t_copyObject(source, target);\r\n\t\t}\r\n\r\n\t\treturn target;\r\n\t}\r\n\r\n\t/** Get the data based on key**/\r\n\tChance.prototype.get = function (name) {\r\n\t\treturn copyObject(data[name]);\r\n\t};\r\n\r\n\t// Mac Address\r\n\tChance.prototype.mac_address = function (options) {\r\n\t\t// typically mac addresses are separated by \":\"\r\n\t\t// however they can also be separated by \"-\"\r\n\t\t// the network variant uses a dot every fourth byte\r\n\r\n\t\toptions = initOptions(options);\r\n\t\tif (!options.separator) {\r\n\t\t\toptions.separator = options.networkVersion ? \".\" : \":\";\r\n\t\t}\r\n\r\n\t\tvar mac_pool = \"ABCDEF**********\",\r\n\t\t\tmac = \"\";\r\n\t\tif (!options.networkVersion) {\r\n\t\t\tmac = this.n(this.string, 6, { pool: mac_pool, length: 2 }).join(options.separator);\r\n\t\t} else {\r\n\t\t\tmac = this.n(this.string, 3, { pool: mac_pool, length: 4 }).join(options.separator);\r\n\t\t}\r\n\r\n\t\treturn mac;\r\n\t};\r\n\r\n\tChance.prototype.normal = function (options) {\r\n\t\toptions = initOptions(options, { mean: 0, dev: 1, pool: [] });\r\n\r\n\t\ttestRange(options.pool.constructor !== Array, \"Chance: The pool option must be a valid array.\");\r\n\t\ttestRange(typeof options.mean !== \"number\", \"Chance: Mean (mean) must be a number\");\r\n\t\ttestRange(typeof options.dev !== \"number\", \"Chance: Standard deviation (dev) must be a number\");\r\n\r\n\t\t// If a pool has been passed, then we are returning an item from that pool,\r\n\t\t// using the normal distribution settings that were passed in\r\n\t\tif (options.pool.length > 0) {\r\n\t\t\treturn this.normal_pool(options);\r\n\t\t}\r\n\r\n\t\t// The Marsaglia Polar method\r\n\t\tvar s,\r\n\t\t\tu,\r\n\t\t\tv,\r\n\t\t\tnorm,\r\n\t\t\tmean = options.mean,\r\n\t\t\tdev = options.dev;\r\n\r\n\t\tdo {\r\n\t\t\t// U and V are from the uniform distribution on (-1, 1)\r\n\t\t\tu = this.random() * 2 - 1;\r\n\t\t\tv = this.random() * 2 - 1;\r\n\r\n\t\t\ts = u * u + v * v;\r\n\t\t} while (s >= 1);\r\n\r\n\t\t// Compute the standard normal variate\r\n\t\tnorm = u * Math.sqrt((-2 * Math.log(s)) / s);\r\n\r\n\t\t// Shape and scale\r\n\t\treturn dev * norm + mean;\r\n\t};\r\n\r\n\tChance.prototype.normal_pool = function (options) {\r\n\t\tvar performanceCounter = 0;\r\n\t\tdo {\r\n\t\t\tvar idx = Math.round(this.normal({ mean: options.mean, dev: options.dev }));\r\n\t\t\tif (idx < options.pool.length && idx >= 0) {\r\n\t\t\t\treturn options.pool[idx];\r\n\t\t\t} else {\r\n\t\t\t\tperformanceCounter++;\r\n\t\t\t}\r\n\t\t} while (performanceCounter < 100);\r\n\r\n\t\tthrow new RangeError(\"Chance: Your pool is too small for the given mean and standard deviation. Please adjust.\");\r\n\t};\r\n\r\n\tChance.prototype.radio = function (options) {\r\n\t\t// Initial Letter (Typically Designated by Side of Mississippi River)\r\n\t\toptions = initOptions(options, { side: \"?\" });\r\n\t\tvar fl = \"\";\r\n\t\tswitch (options.side.toLowerCase()) {\r\n\t\t\tcase \"east\":\r\n\t\t\tcase \"e\":\r\n\t\t\t\tfl = \"W\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"west\":\r\n\t\t\tcase \"w\":\r\n\t\t\t\tfl = \"K\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tfl = this.character({ pool: \"KW\" });\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\r\n\t\treturn fl + this.character({ alpha: true, casing: \"upper\" }) + this.character({ alpha: true, casing: \"upper\" }) + this.character({ alpha: true, casing: \"upper\" });\r\n\t};\r\n\r\n\t// Set the data as key and data or the data map\r\n\tChance.prototype.set = function (name, values) {\r\n\t\tif (typeof name === \"string\") {\r\n\t\t\tdata[name] = values;\r\n\t\t} else {\r\n\t\t\tdata = copyObject(name, data);\r\n\t\t}\r\n\t};\r\n\r\n\tChance.prototype.tv = function (options) {\r\n\t\treturn this.radio(options);\r\n\t};\r\n\r\n\t// ID number for Brazil companies\r\n\tChance.prototype.cnpj = function () {\r\n\t\tvar n = this.n(this.natural, 8, { max: 9 });\r\n\t\tvar d1 = 2 + n[7] * 6 + n[6] * 7 + n[5] * 8 + n[4] * 9 + n[3] * 2 + n[2] * 3 + n[1] * 4 + n[0] * 5;\r\n\t\td1 = 11 - (d1 % 11);\r\n\t\tif (d1 >= 10) {\r\n\t\t\td1 = 0;\r\n\t\t}\r\n\t\tvar d2 = d1 * 2 + 3 + n[7] * 7 + n[6] * 8 + n[5] * 9 + n[4] * 2 + n[3] * 3 + n[2] * 4 + n[1] * 5 + n[0] * 6;\r\n\t\td2 = 11 - (d2 % 11);\r\n\t\tif (d2 >= 10) {\r\n\t\t\td2 = 0;\r\n\t\t}\r\n\t\treturn \"\" + n[0] + n[1] + \".\" + n[2] + n[3] + n[4] + \".\" + n[5] + n[6] + n[7] + \"/0001-\" + d1 + d2;\r\n\t};\r\n\r\n\t// -- End Miscellaneous --\r\n\r\n\tChance.prototype.mersenne_twister = function (seed) {\r\n\t\treturn new MersenneTwister(seed);\r\n\t};\r\n\r\n\tChance.prototype.blueimp_md5 = function () {\r\n\t\treturn new BlueImpMD5();\r\n\t};\r\n\r\n\t// Mersenne Twister from https://gist.github.com/banksean/300494\r\n\t/*\r\n       A C-program for MT19937, with initialization improved 2002/1/26.\r\n       Coded by Takuji Nishimura and Makoto Matsumoto.\r\n\r\n       Before using, initialize the state by using init_genrand(seed)\r\n       or init_by_array(init_key, key_length).\r\n\r\n       Copyright (C) 1997 - 2002, Makoto Matsumoto and Takuji Nishimura,\r\n       All rights reserved.\r\n\r\n       Redistribution and use in source and binary forms, with or without\r\n       modification, are permitted provided that the following conditions\r\n       are met:\r\n\r\n       1. Redistributions of source code must retain the above copyright\r\n       notice, this list of conditions and the following disclaimer.\r\n\r\n       2. Redistributions in binary form must reproduce the above copyright\r\n       notice, this list of conditions and the following disclaimer in the\r\n       documentation and/or other materials provided with the distribution.\r\n\r\n       3. The names of its contributors may not be used to endorse or promote\r\n       products derived from this software without specific prior written\r\n       permission.\r\n\r\n       THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\r\n       \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\r\n       LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\r\n       A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR\r\n       CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\r\n       EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\r\n       PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\r\n       PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\r\n       LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\r\n       NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\r\n       SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\r\n\r\n\r\n       Any feedback is very welcome.\r\n       http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html\r\n       email: m-mat @ math.sci.hiroshima-u.ac.jp (remove space)\r\n     */\r\n\tvar MersenneTwister = function (seed) {\r\n\t\tif (seed === undefined) {\r\n\t\t\t// kept random number same size as time used previously to ensure no unexpected results downstream\r\n\t\t\tseed = Math.floor(Math.random() * Math.pow(10, 13));\r\n\t\t}\r\n\t\t/* Period parameters */\r\n\t\tthis.N = 624;\r\n\t\tthis.M = 397;\r\n\t\tthis.MATRIX_A = 0x9908b0df; /* constant vector a */\r\n\t\tthis.UPPER_MASK = 0x80000000; /* most significant w-r bits */\r\n\t\tthis.LOWER_MASK = 0x7fffffff; /* least significant r bits */\r\n\r\n\t\tthis.mt = new Array(this.N); /* the array for the state vector */\r\n\t\tthis.mti = this.N + 1; /* mti==N + 1 means mt[N] is not initialized */\r\n\r\n\t\tthis.init_genrand(seed);\r\n\t};\r\n\r\n\t/* initializes mt[N] with a seed */\r\n\tMersenneTwister.prototype.init_genrand = function (s) {\r\n\t\tthis.mt[0] = s >>> 0;\r\n\t\tfor (this.mti = 1; this.mti < this.N; this.mti++) {\r\n\t\t\ts = this.mt[this.mti - 1] ^ (this.mt[this.mti - 1] >>> 30);\r\n\t\t\tthis.mt[this.mti] = ((((s & 0xffff0000) >>> 16) * 1812433253) << 16) + (s & 0x0000ffff) * 1812433253 + this.mti;\r\n\t\t\t/* See Knuth TAOCP Vol2. 3rd Ed. P.106 for multiplier. */\r\n\t\t\t/* In the previous versions, MSBs of the seed affect   */\r\n\t\t\t/* only MSBs of the array mt[].                        */\r\n\t\t\t/* 2002/01/09 modified by Makoto Matsumoto             */\r\n\t\t\tthis.mt[this.mti] >>>= 0;\r\n\t\t\t/* for >32 bit machines */\r\n\t\t}\r\n\t};\r\n\r\n\t/* initialize by an array with array-length */\r\n\t/* init_key is the array for initializing keys */\r\n\t/* key_length is its length */\r\n\t/* slight change for C++, 2004/2/26 */\r\n\tMersenneTwister.prototype.init_by_array = function (init_key, key_length) {\r\n\t\tvar i = 1,\r\n\t\t\tj = 0,\r\n\t\t\tk,\r\n\t\t\ts;\r\n\t\tthis.init_genrand(19650218);\r\n\t\tk = this.N > key_length ? this.N : key_length;\r\n\t\tfor (; k; k--) {\r\n\t\t\ts = this.mt[i - 1] ^ (this.mt[i - 1] >>> 30);\r\n\t\t\tthis.mt[i] = (this.mt[i] ^ (((((s & 0xffff0000) >>> 16) * 1664525) << 16) + (s & 0x0000ffff) * 1664525)) + init_key[j] + j; /* non linear */\r\n\t\t\tthis.mt[i] >>>= 0; /* for WORDSIZE > 32 machines */\r\n\t\t\ti++;\r\n\t\t\tj++;\r\n\t\t\tif (i >= this.N) {\r\n\t\t\t\tthis.mt[0] = this.mt[this.N - 1];\r\n\t\t\t\ti = 1;\r\n\t\t\t}\r\n\t\t\tif (j >= key_length) {\r\n\t\t\t\tj = 0;\r\n\t\t\t}\r\n\t\t}\r\n\t\tfor (k = this.N - 1; k; k--) {\r\n\t\t\ts = this.mt[i - 1] ^ (this.mt[i - 1] >>> 30);\r\n\t\t\tthis.mt[i] = (this.mt[i] ^ (((((s & 0xffff0000) >>> 16) * 1566083941) << 16) + (s & 0x0000ffff) * 1566083941)) - i; /* non linear */\r\n\t\t\tthis.mt[i] >>>= 0; /* for WORDSIZE > 32 machines */\r\n\t\t\ti++;\r\n\t\t\tif (i >= this.N) {\r\n\t\t\t\tthis.mt[0] = this.mt[this.N - 1];\r\n\t\t\t\ti = 1;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tthis.mt[0] = 0x80000000; /* MSB is 1; assuring non-zero initial array */\r\n\t};\r\n\r\n\t/* generates a random number on [0,0xffffffff]-interval */\r\n\tMersenneTwister.prototype.genrand_int32 = function () {\r\n\t\tvar y;\r\n\t\tvar mag01 = new Array(0x0, this.MATRIX_A);\r\n\t\t/* mag01[x] = x * MATRIX_A  for x=0,1 */\r\n\r\n\t\tif (this.mti >= this.N) {\r\n\t\t\t/* generate N words at one time */\r\n\t\t\tvar kk;\r\n\r\n\t\t\tif (this.mti === this.N + 1) {\r\n\t\t\t\t/* if init_genrand() has not been called, */\r\n\t\t\t\tthis.init_genrand(5489); /* a default initial seed is used */\r\n\t\t\t}\r\n\t\t\tfor (kk = 0; kk < this.N - this.M; kk++) {\r\n\t\t\t\ty = (this.mt[kk] & this.UPPER_MASK) | (this.mt[kk + 1] & this.LOWER_MASK);\r\n\t\t\t\tthis.mt[kk] = this.mt[kk + this.M] ^ (y >>> 1) ^ mag01[y & 0x1];\r\n\t\t\t}\r\n\t\t\tfor (; kk < this.N - 1; kk++) {\r\n\t\t\t\ty = (this.mt[kk] & this.UPPER_MASK) | (this.mt[kk + 1] & this.LOWER_MASK);\r\n\t\t\t\tthis.mt[kk] = this.mt[kk + (this.M - this.N)] ^ (y >>> 1) ^ mag01[y & 0x1];\r\n\t\t\t}\r\n\t\t\ty = (this.mt[this.N - 1] & this.UPPER_MASK) | (this.mt[0] & this.LOWER_MASK);\r\n\t\t\tthis.mt[this.N - 1] = this.mt[this.M - 1] ^ (y >>> 1) ^ mag01[y & 0x1];\r\n\r\n\t\t\tthis.mti = 0;\r\n\t\t}\r\n\r\n\t\ty = this.mt[this.mti++];\r\n\r\n\t\t/* Tempering */\r\n\t\ty ^= y >>> 11;\r\n\t\ty ^= (y << 7) & 0x9d2c5680;\r\n\t\ty ^= (y << 15) & 0xefc60000;\r\n\t\ty ^= y >>> 18;\r\n\r\n\t\treturn y >>> 0;\r\n\t};\r\n\r\n\t/* generates a random number on [0,0x7fffffff]-interval */\r\n\tMersenneTwister.prototype.genrand_int31 = function () {\r\n\t\treturn this.genrand_int32() >>> 1;\r\n\t};\r\n\r\n\t/* generates a random number on [0,1]-real-interval */\r\n\tMersenneTwister.prototype.genrand_real1 = function () {\r\n\t\treturn this.genrand_int32() * (1.0 / 4294967295.0);\r\n\t\t/* divided by 2^32-1 */\r\n\t};\r\n\r\n\t/* generates a random number on [0,1)-real-interval */\r\n\tMersenneTwister.prototype.random = function () {\r\n\t\treturn this.genrand_int32() * (1.0 / 4294967296.0);\r\n\t\t/* divided by 2^32 */\r\n\t};\r\n\r\n\t/* generates a random number on (0,1)-real-interval */\r\n\tMersenneTwister.prototype.genrand_real3 = function () {\r\n\t\treturn (this.genrand_int32() + 0.5) * (1.0 / 4294967296.0);\r\n\t\t/* divided by 2^32 */\r\n\t};\r\n\r\n\t/* generates a random number on [0,1) with 53-bit resolution*/\r\n\tMersenneTwister.prototype.genrand_res53 = function () {\r\n\t\tvar a = this.genrand_int32() >>> 5,\r\n\t\t\tb = this.genrand_int32() >>> 6;\r\n\t\treturn (a * 67108864.0 + b) * (1.0 / 9007199254740992.0);\r\n\t};\r\n\r\n\t// BlueImp MD5 hashing algorithm from https://github.com/blueimp/JavaScript-MD5\r\n\tvar BlueImpMD5 = function () {};\r\n\r\n\tBlueImpMD5.prototype.VERSION = \"1.0.1\";\r\n\r\n\t/*\r\n\t * Add integers, wrapping at 2^32. This uses 16-bit operations internally\r\n\t * to work around bugs in some JS interpreters.\r\n\t */\r\n\tBlueImpMD5.prototype.safe_add = function safe_add(x, y) {\r\n\t\tvar lsw = (x & 0xffff) + (y & 0xffff),\r\n\t\t\tmsw = (x >> 16) + (y >> 16) + (lsw >> 16);\r\n\t\treturn (msw << 16) | (lsw & 0xffff);\r\n\t};\r\n\r\n\t/*\r\n\t * Bitwise rotate a 32-bit number to the left.\r\n\t */\r\n\tBlueImpMD5.prototype.bit_roll = function (num, cnt) {\r\n\t\treturn (num << cnt) | (num >>> (32 - cnt));\r\n\t};\r\n\r\n\t/*\r\n\t * These functions implement the five basic operations the algorithm uses.\r\n\t */\r\n\tBlueImpMD5.prototype.md5_cmn = function (q, a, b, x, s, t) {\r\n\t\treturn this.safe_add(this.bit_roll(this.safe_add(this.safe_add(a, q), this.safe_add(x, t)), s), b);\r\n\t};\r\n\tBlueImpMD5.prototype.md5_ff = function (a, b, c, d, x, s, t) {\r\n\t\treturn this.md5_cmn((b & c) | (~b & d), a, b, x, s, t);\r\n\t};\r\n\tBlueImpMD5.prototype.md5_gg = function (a, b, c, d, x, s, t) {\r\n\t\treturn this.md5_cmn((b & d) | (c & ~d), a, b, x, s, t);\r\n\t};\r\n\tBlueImpMD5.prototype.md5_hh = function (a, b, c, d, x, s, t) {\r\n\t\treturn this.md5_cmn(b ^ c ^ d, a, b, x, s, t);\r\n\t};\r\n\tBlueImpMD5.prototype.md5_ii = function (a, b, c, d, x, s, t) {\r\n\t\treturn this.md5_cmn(c ^ (b | ~d), a, b, x, s, t);\r\n\t};\r\n\r\n\t/*\r\n\t * Calculate the MD5 of an array of little-endian words, and a bit length.\r\n\t */\r\n\tBlueImpMD5.prototype.binl_md5 = function (x, len) {\r\n\t\t/* append padding */\r\n\t\tx[len >> 5] |= 0x80 << len % 32;\r\n\t\tx[(((len + 64) >>> 9) << 4) + 14] = len;\r\n\r\n\t\tvar i,\r\n\t\t\tolda,\r\n\t\t\toldb,\r\n\t\t\toldc,\r\n\t\t\toldd,\r\n\t\t\ta = 1732584193,\r\n\t\t\tb = -271733879,\r\n\t\t\tc = -1732584194,\r\n\t\t\td = 271733878;\r\n\r\n\t\tfor (i = 0; i < x.length; i += 16) {\r\n\t\t\tolda = a;\r\n\t\t\toldb = b;\r\n\t\t\toldc = c;\r\n\t\t\toldd = d;\r\n\r\n\t\t\ta = this.md5_ff(a, b, c, d, x[i], 7, -680876936);\r\n\t\t\td = this.md5_ff(d, a, b, c, x[i + 1], 12, -389564586);\r\n\t\t\tc = this.md5_ff(c, d, a, b, x[i + 2], 17, 606105819);\r\n\t\t\tb = this.md5_ff(b, c, d, a, x[i + 3], 22, -1044525330);\r\n\t\t\ta = this.md5_ff(a, b, c, d, x[i + 4], 7, -176418897);\r\n\t\t\td = this.md5_ff(d, a, b, c, x[i + 5], 12, 1200080426);\r\n\t\t\tc = this.md5_ff(c, d, a, b, x[i + 6], 17, -1473231341);\r\n\t\t\tb = this.md5_ff(b, c, d, a, x[i + 7], 22, -45705983);\r\n\t\t\ta = this.md5_ff(a, b, c, d, x[i + 8], 7, 1770035416);\r\n\t\t\td = this.md5_ff(d, a, b, c, x[i + 9], 12, -1958414417);\r\n\t\t\tc = this.md5_ff(c, d, a, b, x[i + 10], 17, -42063);\r\n\t\t\tb = this.md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);\r\n\t\t\ta = this.md5_ff(a, b, c, d, x[i + 12], 7, 1804603682);\r\n\t\t\td = this.md5_ff(d, a, b, c, x[i + 13], 12, -40341101);\r\n\t\t\tc = this.md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);\r\n\t\t\tb = this.md5_ff(b, c, d, a, x[i + 15], 22, 1236535329);\r\n\r\n\t\t\ta = this.md5_gg(a, b, c, d, x[i + 1], 5, -165796510);\r\n\t\t\td = this.md5_gg(d, a, b, c, x[i + 6], 9, -1069501632);\r\n\t\t\tc = this.md5_gg(c, d, a, b, x[i + 11], 14, 643717713);\r\n\t\t\tb = this.md5_gg(b, c, d, a, x[i], 20, -373897302);\r\n\t\t\ta = this.md5_gg(a, b, c, d, x[i + 5], 5, -701558691);\r\n\t\t\td = this.md5_gg(d, a, b, c, x[i + 10], 9, 38016083);\r\n\t\t\tc = this.md5_gg(c, d, a, b, x[i + 15], 14, -660478335);\r\n\t\t\tb = this.md5_gg(b, c, d, a, x[i + 4], 20, -405537848);\r\n\t\t\ta = this.md5_gg(a, b, c, d, x[i + 9], 5, 568446438);\r\n\t\t\td = this.md5_gg(d, a, b, c, x[i + 14], 9, -1019803690);\r\n\t\t\tc = this.md5_gg(c, d, a, b, x[i + 3], 14, -187363961);\r\n\t\t\tb = this.md5_gg(b, c, d, a, x[i + 8], 20, 1163531501);\r\n\t\t\ta = this.md5_gg(a, b, c, d, x[i + 13], 5, -1444681467);\r\n\t\t\td = this.md5_gg(d, a, b, c, x[i + 2], 9, -51403784);\r\n\t\t\tc = this.md5_gg(c, d, a, b, x[i + 7], 14, 1735328473);\r\n\t\t\tb = this.md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);\r\n\r\n\t\t\ta = this.md5_hh(a, b, c, d, x[i + 5], 4, -378558);\r\n\t\t\td = this.md5_hh(d, a, b, c, x[i + 8], 11, -2022574463);\r\n\t\t\tc = this.md5_hh(c, d, a, b, x[i + 11], 16, 1839030562);\r\n\t\t\tb = this.md5_hh(b, c, d, a, x[i + 14], 23, -35309556);\r\n\t\t\ta = this.md5_hh(a, b, c, d, x[i + 1], 4, -1530992060);\r\n\t\t\td = this.md5_hh(d, a, b, c, x[i + 4], 11, 1272893353);\r\n\t\t\tc = this.md5_hh(c, d, a, b, x[i + 7], 16, -155497632);\r\n\t\t\tb = this.md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);\r\n\t\t\ta = this.md5_hh(a, b, c, d, x[i + 13], 4, 681279174);\r\n\t\t\td = this.md5_hh(d, a, b, c, x[i], 11, -358537222);\r\n\t\t\tc = this.md5_hh(c, d, a, b, x[i + 3], 16, -722521979);\r\n\t\t\tb = this.md5_hh(b, c, d, a, x[i + 6], 23, 76029189);\r\n\t\t\ta = this.md5_hh(a, b, c, d, x[i + 9], 4, -640364487);\r\n\t\t\td = this.md5_hh(d, a, b, c, x[i + 12], 11, -421815835);\r\n\t\t\tc = this.md5_hh(c, d, a, b, x[i + 15], 16, 530742520);\r\n\t\t\tb = this.md5_hh(b, c, d, a, x[i + 2], 23, -995338651);\r\n\r\n\t\t\ta = this.md5_ii(a, b, c, d, x[i], 6, -198630844);\r\n\t\t\td = this.md5_ii(d, a, b, c, x[i + 7], 10, 1126891415);\r\n\t\t\tc = this.md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);\r\n\t\t\tb = this.md5_ii(b, c, d, a, x[i + 5], 21, -57434055);\r\n\t\t\ta = this.md5_ii(a, b, c, d, x[i + 12], 6, 1700485571);\r\n\t\t\td = this.md5_ii(d, a, b, c, x[i + 3], 10, -1894986606);\r\n\t\t\tc = this.md5_ii(c, d, a, b, x[i + 10], 15, -1051523);\r\n\t\t\tb = this.md5_ii(b, c, d, a, x[i + 1], 21, -2054922799);\r\n\t\t\ta = this.md5_ii(a, b, c, d, x[i + 8], 6, 1873313359);\r\n\t\t\td = this.md5_ii(d, a, b, c, x[i + 15], 10, -30611744);\r\n\t\t\tc = this.md5_ii(c, d, a, b, x[i + 6], 15, -1560198380);\r\n\t\t\tb = this.md5_ii(b, c, d, a, x[i + 13], 21, 1309151649);\r\n\t\t\ta = this.md5_ii(a, b, c, d, x[i + 4], 6, -145523070);\r\n\t\t\td = this.md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);\r\n\t\t\tc = this.md5_ii(c, d, a, b, x[i + 2], 15, 718787259);\r\n\t\t\tb = this.md5_ii(b, c, d, a, x[i + 9], 21, -343485551);\r\n\r\n\t\t\ta = this.safe_add(a, olda);\r\n\t\t\tb = this.safe_add(b, oldb);\r\n\t\t\tc = this.safe_add(c, oldc);\r\n\t\t\td = this.safe_add(d, oldd);\r\n\t\t}\r\n\t\treturn [a, b, c, d];\r\n\t};\r\n\r\n\t/*\r\n\t * Convert an array of little-endian words to a string\r\n\t */\r\n\tBlueImpMD5.prototype.binl2rstr = function (input) {\r\n\t\tvar i,\r\n\t\t\toutput = \"\";\r\n\t\tfor (i = 0; i < input.length * 32; i += 8) {\r\n\t\t\toutput += String.fromCharCode((input[i >> 5] >>> i % 32) & 0xff);\r\n\t\t}\r\n\t\treturn output;\r\n\t};\r\n\r\n\t/*\r\n\t * Convert a raw string to an array of little-endian words\r\n\t * Characters >255 have their high-byte silently ignored.\r\n\t */\r\n\tBlueImpMD5.prototype.rstr2binl = function (input) {\r\n\t\tvar i,\r\n\t\t\toutput = [];\r\n\t\toutput[(input.length >> 2) - 1] = undefined;\r\n\t\tfor (i = 0; i < output.length; i += 1) {\r\n\t\t\toutput[i] = 0;\r\n\t\t}\r\n\t\tfor (i = 0; i < input.length * 8; i += 8) {\r\n\t\t\toutput[i >> 5] |= (input.charCodeAt(i / 8) & 0xff) << i % 32;\r\n\t\t}\r\n\t\treturn output;\r\n\t};\r\n\r\n\t/*\r\n\t * Calculate the MD5 of a raw string\r\n\t */\r\n\tBlueImpMD5.prototype.rstr_md5 = function (s) {\r\n\t\treturn this.binl2rstr(this.binl_md5(this.rstr2binl(s), s.length * 8));\r\n\t};\r\n\r\n\t/*\r\n\t * Calculate the HMAC-MD5, of a key and some data (raw strings)\r\n\t */\r\n\tBlueImpMD5.prototype.rstr_hmac_md5 = function (key, data) {\r\n\t\tvar i,\r\n\t\t\tbkey = this.rstr2binl(key),\r\n\t\t\tipad = [],\r\n\t\t\topad = [],\r\n\t\t\thash;\r\n\t\tipad[15] = opad[15] = undefined;\r\n\t\tif (bkey.length > 16) {\r\n\t\t\tbkey = this.binl_md5(bkey, key.length * 8);\r\n\t\t}\r\n\t\tfor (i = 0; i < 16; i += 1) {\r\n\t\t\tipad[i] = bkey[i] ^ 0x36363636;\r\n\t\t\topad[i] = bkey[i] ^ 0x5c5c5c5c;\r\n\t\t}\r\n\t\thash = this.binl_md5(ipad.concat(this.rstr2binl(data)), 512 + data.length * 8);\r\n\t\treturn this.binl2rstr(this.binl_md5(opad.concat(hash), 512 + 128));\r\n\t};\r\n\r\n\t/*\r\n\t * Convert a raw string to a hex string\r\n\t */\r\n\tBlueImpMD5.prototype.rstr2hex = function (input) {\r\n\t\tvar hex_tab = \"**********abcdef\",\r\n\t\t\toutput = \"\",\r\n\t\t\tx,\r\n\t\t\ti;\r\n\t\tfor (i = 0; i < input.length; i += 1) {\r\n\t\t\tx = input.charCodeAt(i);\r\n\t\t\toutput += hex_tab.charAt((x >>> 4) & 0x0f) + hex_tab.charAt(x & 0x0f);\r\n\t\t}\r\n\t\treturn output;\r\n\t};\r\n\r\n\t/*\r\n\t * Encode a string as utf-8\r\n\t */\r\n\tBlueImpMD5.prototype.str2rstr_utf8 = function (input) {\r\n\t\treturn unescape(encodeURIComponent(input));\r\n\t};\r\n\r\n\t/*\r\n\t * Take string arguments and return either raw or hex encoded strings\r\n\t */\r\n\tBlueImpMD5.prototype.raw_md5 = function (s) {\r\n\t\treturn this.rstr_md5(this.str2rstr_utf8(s));\r\n\t};\r\n\tBlueImpMD5.prototype.hex_md5 = function (s) {\r\n\t\treturn this.rstr2hex(this.raw_md5(s));\r\n\t};\r\n\tBlueImpMD5.prototype.raw_hmac_md5 = function (k, d) {\r\n\t\treturn this.rstr_hmac_md5(this.str2rstr_utf8(k), this.str2rstr_utf8(d));\r\n\t};\r\n\tBlueImpMD5.prototype.hex_hmac_md5 = function (k, d) {\r\n\t\treturn this.rstr2hex(this.raw_hmac_md5(k, d));\r\n\t};\r\n\r\n\tBlueImpMD5.prototype.md5 = function (string, key, raw) {\r\n\t\tif (!key) {\r\n\t\t\tif (!raw) {\r\n\t\t\t\treturn this.hex_md5(string);\r\n\t\t\t}\r\n\r\n\t\t\treturn this.raw_md5(string);\r\n\t\t}\r\n\r\n\t\tif (!raw) {\r\n\t\t\treturn this.hex_hmac_md5(key, string);\r\n\t\t}\r\n\r\n\t\treturn this.raw_hmac_md5(key, string);\r\n\t};\r\n\r\n\t// CommonJS module\r\n\tif (typeof exports !== \"undefined\") {\r\n\t\tif (typeof module !== \"undefined\" && module.exports) {\r\n\t\t\texports = module.exports = Chance;\r\n\t\t}\r\n\t\texports.Chance = Chance;\r\n\t}\r\n\r\n\t// Register as an anonymous AMD module\r\n\tif (typeof define === \"function\" && define.amd) {\r\n\t\tdefine([], function () {\r\n\t\t\treturn Chance;\r\n\t\t});\r\n\t}\r\n\r\n\t// if there is a importsScrips object define chance for worker\r\n\t// allows worker to use full Chance functionality with seed\r\n\tif (typeof importScripts !== \"undefined\") {\r\n\t\tchance = new Chance();\r\n\t\tself.Chance = Chance;\r\n\t}\r\n\r\n\t// If there is a window object, that at least has a document property,\r\n\t// instantiate and define chance on the window\r\n\tif (typeof window === \"object\" && typeof window.document === \"object\") {\r\n\t\twindow.Chance = Chance;\r\n\t\twindow.chance = new Chance();\r\n\t}\r\n})();\r\n"], "names": ["MAX_INT", "NUMBERS", "CHARS_LOWER", "CHARS_UPPER", "toUpperCase", "HEX_POOL", "slice", "Array", "prototype", "Chance", "seed", "this", "random", "arguments", "length", "i", "seedling", "Object", "toString", "call", "j", "hash", "k", "charCodeAt", "mt", "mersenne_twister", "bimd5", "blueimp_md5", "initOptions", "options", "defaults", "testRange", "test", "errorMessage", "RangeError", "VERSION", "base64", "Error", "diceFn", "range", "natural", "btoa", "<PERSON><PERSON><PERSON>", "input", "bool", "likelihood", "animal", "type", "get", "toLowerCase", "pick", "animalTypeArray", "character", "alpha", "symbols", "letters", "casing", "pool", "char<PERSON>t", "max", "floating", "fixed", "precision", "Math", "pow", "min", "num_fixed", "integer", "toFixed", "parseFloat", "floor", "numerals", "hex", "letter", "string", "n", "join", "capitalize", "word", "substr", "mixin", "obj", "func_name", "unique", "fn", "num", "comparator", "arr", "val", "indexOf", "count", "MAX_DUPLICATES", "params", "clonedParams", "JSON", "parse", "stringify", "result", "apply", "push", "pad", "number", "width", "shuffle", "pickone", "pickset", "old_array", "new_array", "Number", "splice", "weighted", "weights", "trim", "sum", "weightIndex", "isNaN", "chosenIdx", "selected", "total", "lastGoodIdx", "chosen", "paragraph", "sentences", "sentence", "words", "punctuation", "text", "syllable", "chr", "consonants", "syllables", "substring", "age", "<PERSON><PERSON><PERSON><PERSON>", "birthday", "currentYear", "Date", "getFullYear", "setFullYear", "year", "date", "cpf", "formatted", "d1", "d2", "replace", "cnpj", "first", "gender", "nationality", "profession", "rank", "company", "extraGenders", "concat", "last", "israelId", "x", "y", "thisDigit", "parseInt", "mrz", "checkDigit", "split", "multipliers", "runningTotal", "for<PERSON>ach", "idx", "pos", "that", "opts", "passportNumber", "dob", "getMonth", "getDate", "expiry", "issuer", "name", "middle", "middle_initial", "prefix", "suffix", "name_prefixes", "prefixes", "abbreviation", "name_prefix", "full", "HIDN", "idn", "ssn", "ssn_pool", "dash", "ssnFour", "dashes", "name_suffixes", "name_suffix", "nationalities", "android_id", "apple_token", "wp8_anid2", "wp7_anid", "guid", "bb_pin", "avatar", "protocol", "email", "fileExtension", "size", "fallback", "rating", "constructor", "http", "https", "g", "pg", "r", "404", "mm", "identicon", "monsterid", "wavatar", "retro", "blank", "bmp", "gif", "jpg", "png", "md5", "color", "gray", "value", "delimiter", "rgb", "has<PERSON><PERSON><PERSON>", "rgbValue", "alphaChannel", "min_alpha", "max_alpha", "isGrayscale", "min_rgb", "max_rgb", "min_green", "max_green", "min_blue", "max_blue", "start", "end", "withHash", "symbol", "hexstring", "format", "min_red", "max_red", "undefined", "colorValue", "grayscale", "domain", "tld", "fbid", "google_analytics", "hashtag", "ip", "ipv6", "klout", "semver", "include_prerelease", "prerelease", "rpg", "tlds", "twitter", "url", "extension", "domain_prefix", "path", "extensions", "port", "locale", "region", "locales", "address", "street", "altitude", "areacode", "parens", "city", "coordinates", "latitude", "longitude", "countries", "country", "depth", "geo<PERSON>h", "g<PERSON><PERSON><PERSON>", "phone", "ukNum", "parts", "section", "sections", "self", "area", "mobile", "numPick", "match", "exchange", "subscriber", "postal", "counties", "county", "provinces", "province", "state", "states", "us_states_and_dc", "territories", "armed_forces", "short_suffix", "street_suffix", "street_suffixes", "zip", "plusfour", "ampm", "m", "daysInMonth", "american", "getTime", "month", "raw", "days", "numeric", "day", "hour", "twentyfour", "minute", "second", "millisecond", "date_string", "hammertime", "months", "timestamp", "weekday", "weekdays", "weekday_only", "cc", "cc_type", "to_generate", "luhn_calculate", "cc_types", "types", "short_name", "currency_types", "currency", "timezones", "timezone", "currency_pair", "returnAsString", "currencies", "reduce", "acc", "item", "code", "dollar", "cents", "euro", "toLocaleString", "exp", "exp_year", "exp_month", "future", "cur<PERSON><PERSON><PERSON>", "curYear", "vat", "it_vat", "iban", "cf", "name_generator", "isLast", "temp", "return_value", "map", "c", "range1", "range2", "evens", "digit", "pl_pesel", "controlNumber", "pl_nip", "pl_regon", "note", "notes", "scales", "naturals", "flats", "sharps", "all", "flat<PERSON>ey", "<PERSON><PERSON><PERSON>", "midi_note", "chord_quality", "chord_qualities", "jazz", "chord", "tempo", "coin", "d4", "d6", "d8", "d10", "d12", "d20", "d30", "d100", "thrown", "bits", "rolls", "p", "version", "guid_pool", "luhn_check", "str", "digits", "reverse", "l", "key", "data", "firstNames", "male", "en", "it", "nl", "female", "lastNames", "uk", "ca", "locale_languages", "locale_regions", "country_regions", "us", "colorNames", "raster", "vector", "3d", "document", "abbr", "offset", "isdst", "file", "keys", "fileOptions", "poolCollectionKey", "typeRange", "fileName", "isArray", "extensionObjectCollection", "fileType", "utc", "animals", "ocean", "desert", "grassland", "forest", "farm", "pet", "zoo", "o_hasOwnProperty", "hasOwnProperty", "o_keys", "copyObject", "source", "_target", "target", "_copyArray", "_copyObject", "mac_address", "separator", "networkVersion", "mac_pool", "normal", "mean", "dev", "normal_pool", "s", "u", "v", "sqrt", "log", "performanceCounter", "round", "radio", "fl", "side", "set", "values", "tv", "<PERSON><PERSON><PERSON>", "BlueImpMD5", "N", "M", "MATRIX_A", "UPPER_MASK", "LOWER_MASK", "mti", "init_genrand", "init_by_array", "init_key", "key_length", "genrand_int32", "kk", "mag01", "genrand_int31", "genrand_real1", "genrand_real3", "genrand_res53", "safe_add", "lsw", "bit_roll", "cnt", "md5_cmn", "q", "a", "b", "t", "md5_ff", "d", "md5_gg", "md5_hh", "md5_ii", "binl_md5", "len", "olda", "oldb", "oldc", "oldd", "binl2rstr", "output", "String", "fromCharCode", "rstr2binl", "rstr_md5", "rstr_hmac_md5", "bkey", "ipad", "opad", "rstr2hex", "hex_tab", "str2rstr_utf8", "unescape", "encodeURIComponent", "raw_md5", "hex_md5", "raw_hmac_md5", "hex_hmac_md5", "exports", "module", "define", "amd", "importScripts", "chance", "window"], "mappings": "AAKA,CAAA,WAEC,IAAIA,EAAU,iBAEVC,EAAU,aACVC,EAAc,6BACdC,EAAcD,EAAYE,YAAY,EACtCC,EAAWJ,EAAU,SAGrBK,EAAQC,MAAMC,UAAUF,MAG5B,SAASG,EAAOC,GACf,GAAI,EAAEC,gBAAgBF,GAIrB,OAAgB,QAHXC,EAAAA,GACG,MAEe,IAAID,EAAW,IAAIA,EAAOC,CAAI,EAItD,GAAoB,YAAhB,OAAOA,EACVC,KAAKC,OAASF,MADf,CAKIG,UAAUC,SAEbH,KAAKD,KAAO,GAKb,IAAK,IAAIK,EAAI,EAAGA,EAAIF,UAAUC,OAAQC,CAAC,GAAI,CAC1C,IAAIC,EAAW,EACf,GAAqD,oBAAjDC,OAAOT,UAAUU,SAASC,KAAKN,UAAUE,EAAE,EAC9C,IAAK,IAAIK,EAAI,EAAGA,EAAIP,UAAUE,GAAGD,OAAQM,CAAC,GAAI,CAG7C,IADA,IAAIC,EAAO,EACFC,EAAI,EAAGA,EAAIT,UAAUE,GAAGD,OAAQQ,CAAC,GACzCD,EAAOR,UAAUE,GAAGQ,WAAWD,CAAC,GAAKD,GAAQ,IAAMA,GAAQ,IAAMA,EAElEL,GAAYK,CACb,MAEAL,EAAWH,UAAUE,GAEtBJ,KAAKD,OAASG,UAAUC,OAASC,GAAKC,CACvC,CAGAL,KAAKa,GAAKb,KAAKc,iBAAiBd,KAAKD,IAAI,EACzCC,KAAKe,MAAQf,KAAKgB,YAAY,EAC9BhB,KAAKC,OAAS,WACb,OAAOD,KAAKa,GAAGZ,OAAOD,KAAKD,IAAI,CAChC,CA/BA,CAiCA,OAAOC,IACR,CAKA,SAASiB,EAAYC,EAASC,GAG7B,GAFAD,EAAUA,GAAW,GAEjBC,EACH,IAAK,IAAIf,KAAKe,EACa,KAAA,IAAfD,EAAQd,KAClBc,EAAQd,GAAKe,EAASf,IAKzB,OAAOc,CACR,CAEA,SAASE,EAAUC,EAAMC,GACxB,GAAID,EACH,MAAM,IAAIE,WAAWD,CAAY,CAEnC,CArBAxB,EAAOD,UAAU2B,QAAU,SA0B3B,IAAIC,EAAS,WACZ,MAAM,IAAIC,MAAM,8BAA8B,CAC/C,EA68DA,SAASC,EAAOC,GACf,OAAO,WACN,OAAO5B,KAAK6B,QAAQD,CAAK,CAC1B,CACD,CA78DqB,YAAhB,OAAOE,KACVL,EAASK,KACmB,YAAlB,OAAOC,SACjBN,EAAS,SAAUO,GAClB,OAAO,IAAID,OAAOC,CAAK,EAAEzB,SAAS,QAAQ,CAC3C,GAcFT,EAAOD,UAAUoC,KAAO,SAAUf,GAajC,OAFAE,GATAF,EAAUD,EAAYC,EAAS,CAAEgB,WAAY,EAAG,CAAC,GAS/BA,WAAa,GAA0B,IAArBhB,EAAQgB,WAAkB,kDAAkD,EAEzF,IAAhBlC,KAAKC,OAAO,EAAUiB,EAAQgB,UACtC,EAEApC,EAAOD,UAAUsC,OAAS,SAAUjB,GAInC,OAA4B,KAAA,KAF5BA,EAAUD,EAAYC,CAAO,GAEVkB,MAElBhB,EAAU,CAACpB,KAAKqC,IAAI,SAAS,EAAEnB,EAAQkB,KAAKE,YAAY,GAAI,qEAAqE,EAE1HtC,KAAKuC,KAAKvC,KAAKqC,IAAI,SAAS,EAAEnB,EAAQkB,KAAKE,YAAY,EAAE,IAGjEE,gBAAkB,CAAC,SAAU,SAAU,QAAS,MAAO,OAAQ,MAAO,aAC/DxC,KAAKuC,KAAKvC,KAAKqC,IAAI,SAAS,EAAErC,KAAKuC,KAAKC,eAAe,EAAE,EACjE,EAUA1C,EAAOD,UAAU4C,UAAY,SAAUvB,GAEtCE,GADAF,EAAUD,EAAYC,CAAO,GACXwB,OAASxB,EAAQyB,QAAS,gDAAgD,EAE5F,IAAIA,EAAU,eAKbC,EADsB,UAAnB1B,EAAQ2B,OACDtD,EACmB,UAAnB2B,EAAQ2B,OACRrD,EAEAD,EAAcC,EAIxBsD,EADG5B,EAAQ4B,OAED5B,EAAQwB,MACXE,EACG1B,EAAQyB,QACXA,EAEAC,EAAUtD,EAAUqD,GAG5B,OAAOG,EAAKC,OAAO/C,KAAK6B,QAAQ,CAAEmB,IAAKF,EAAK3C,OAAS,CAAE,CAAC,CAAC,CAC1D,EAgBAL,EAAOD,UAAUoD,SAAW,SAAU/B,GAErCE,GADAF,EAAUD,EAAYC,EAAS,CAAEgC,MAAO,CAAE,CAAC,GACzBA,OAAShC,EAAQiC,UAAW,kDAAkD,EAEhG,IACID,EAAQE,KAAKC,IAAI,GAAInC,EAAQgC,KAAK,EAElCF,EAAM3D,EAAU6D,EAChBI,EAAM,CAACN,EAWPO,GATJnC,EAAUF,EAAQoC,KAAOpC,EAAQgC,OAAShC,EAAQoC,IAAMA,EAAK,8EAAgFA,CAAG,EAChJlC,EAAUF,EAAQ8B,KAAO9B,EAAQgC,OAAShC,EAAQ8B,IAAMA,EAAK,6EAA+EA,CAAG,EAE/I9B,EAAUD,EAAYC,EAAS,CAAEoC,IAAKA,EAAKN,IAAKA,CAAI,CAAC,GAK/ChD,KAAKwD,QAAQ,CAAEF,IAAKpC,EAAQoC,IAAMJ,EAAOF,IAAK9B,EAAQ8B,IAAME,CAAM,CAAC,EAClDA,GAAOO,QAAQvC,EAAQgC,KAAK,GAEnD,OAAOQ,WAAWH,CAAS,CAC5B,EAaAzD,EAAOD,UAAU2D,QAAU,SAAUtC,GAMpC,OAFAE,GADAF,EAAUD,EAAYC,EAAS,CAAEoC,IArOpB,CAAA,iBAqOkCN,IAAK3D,CAAQ,CAAC,GAC3CiE,IAAMpC,EAAQ8B,IAAK,yCAAyC,EAEvEI,KAAKO,MAAM3D,KAAKC,OAAO,GAAKiB,EAAQ8B,IAAM9B,EAAQoC,IAAM,GAAKpC,EAAQoC,GAAG,CAChF,EAaAxD,EAAOD,UAAUgC,QAAU,SAAUX,GAQpC,MANgC,UAA5B,OADJA,EAAUD,EAAYC,EAAS,CAAEoC,IAAK,EAAGN,IAAK3D,CAAQ,CAAC,GACpCuE,WAClBxC,EAAUF,EAAQ0C,SAAW,EAAG,2CAA2C,EAC3E1C,EAAQoC,IAAMF,KAAKC,IAAI,GAAInC,EAAQ0C,SAAW,CAAC,EAC/C1C,EAAQ8B,IAAMI,KAAKC,IAAI,GAAInC,EAAQ0C,QAAQ,EAAI,GAEhDxC,EAAUF,EAAQoC,IAAM,EAAG,uCAAuC,EAC3DtD,KAAKwD,QAAQtC,CAAO,CAC5B,EAaApB,EAAOD,UAAUgE,IAAM,SAAU3C,GAEhCE,GADAF,EAAUD,EAAYC,EAAS,CAAEoC,IAAK,EAAGN,IAAK3D,EAASwD,OAAQ,OAAQ,CAAC,GACtDS,IAAM,EAAG,uCAAuC,EAClE,IAAIE,EAAUxD,KAAK6B,QAAQ,CAAEyB,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,GAAI,CAAC,EACjE,MAAuB,UAAnB9B,EAAQ2B,OACJW,EAAQjD,SAAS,EAAE,EAAEd,YAAY,EAElC+D,EAAQjD,SAAS,EAAE,CAC3B,EAEAT,EAAOD,UAAUiE,OAAS,SAAU5C,GACnCA,EAAUD,EAAYC,EAAS,CAAE2B,OAAQ,OAAQ,CAAC,EAClD,IACIiB,EAAS9D,KAAKyC,UAAU,CAAEK,KADnB,4BAC8B,CAAC,EAI1C,OAFCgB,EADsB,UAAnB5C,EAAQ2B,OACFiB,EAAOrE,YAAY,EAEtBqE,CACR,EASAhE,EAAOD,UAAUkE,OAAS,SAAU7C,GAEnCE,GADAF,EAAUD,EAAYC,EAAS,CAAEf,OAAQH,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,EAAG,CAAC,CAAE,CAAC,GAC1D7C,OAAS,EAAG,0CAA0C,EACxE,IAAIA,EAASe,EAAQf,OAGrB,OAFQH,KAAKgE,EAAEhE,KAAKyC,UAAWtC,EAAQe,CAAO,EAElC+C,KAAK,EAAE,CACpB,EAMAnE,EAAOD,UAAUqE,WAAa,SAAUC,GACvC,OAAOA,EAAKpB,OAAO,CAAC,EAAEtD,YAAY,EAAI0E,EAAKC,OAAO,CAAC,CACpD,EAEAtE,EAAOD,UAAUwE,MAAQ,SAAUC,GAClC,IAAK,IAAIC,KAAaD,EACrBxE,EAAOD,UAAU0E,GAAaD,EAAIC,GAEnC,OAAOvE,IACR,EAaAF,EAAOD,UAAU2E,OAAS,SAAUC,EAAIC,EAAKxD,GAC5CE,EAAwB,YAAd,OAAOqD,EAAmB,gDAAgD,EAgBpF,IAdA,IAAIE,EAAa,SAAUC,EAAKC,GAC/B,MAA4B,CAAC,IAAtBD,EAAIE,QAAQD,CAAG,CACvB,EAMID,GAJA1D,IACHyD,EAAazD,EAAQyD,YAAcA,GAG1B,IACTI,EAAQ,EAERC,EAAuB,GAANN,EACjBO,EAAStF,EAAMa,KAAKN,UAAW,CAAC,EAE1B0E,EAAIzE,OAASuE,GAAK,CACxB,IAAIQ,EAAeC,KAAKC,MAAMD,KAAKE,UAAUJ,CAAM,CAAC,EAQpD,GANKN,EAAWC,EAAKU,EADZb,EAAGc,MAAMvF,KAAMkF,CAAY,CACT,IAC1BN,EAAIY,KAAKF,CAAM,EAEfP,EAAQ,GAGL,EAAEA,EAAQC,EACb,MAAM,IAAIzD,WAAW,gDAAgD,CAEvE,CACA,OAAOqD,CACR,EAWA9E,EAAOD,UAAUmE,EAAI,SAAUS,EAAIT,GAClC5C,EAAwB,YAAd,OAAOqD,EAAmB,gDAAgD,EAYpF,IAPA,IAAIrE,EAFH4D,EADgB,KAAA,IAANA,EACN,EAEGA,EACPY,EAAM,GACNK,EAAStF,EAAMa,KAAKN,UAAW,CAAC,EAGjCE,EAAIgD,KAAKJ,IAAI,EAAG5C,CAAC,EAENA,CAAC,IACXwE,EAAIY,KAAKf,EAAGc,MAAMvF,KAAMiF,CAAM,CAAC,EAGhC,OAAOL,CACR,EAGA9E,EAAOD,UAAU4F,IAAM,SAAUC,EAAQC,EAAOF,GAK/C,OAHAA,EAAMA,GAAO,KAEbC,GAAkB,IACJvF,QAAUwF,EAAQD,EAAS,IAAI9F,MAAM+F,EAAQD,EAAOvF,OAAS,CAAC,EAAE8D,KAAKwB,CAAG,EAAIC,CAC3F,EAGA5F,EAAOD,UAAU0C,KAAO,SAAUqC,EAAKG,GACtC,GAAmB,IAAfH,EAAIzE,OACP,MAAM,IAAIoB,WAAW,2CAA2C,EAEjE,OAAKwD,GAAmB,IAAVA,EAGN/E,KAAK4F,QAAQhB,CAAG,EAAEjF,MAAM,EAAGoF,CAAK,EAFhCH,EAAI5E,KAAK6B,QAAQ,CAAEmB,IAAK4B,EAAIzE,OAAS,CAAE,CAAC,EAIjD,EAGAL,EAAOD,UAAUgG,QAAU,SAAUjB,GACpC,GAAmB,IAAfA,EAAIzE,OACP,MAAM,IAAIoB,WAAW,8CAA8C,EAEpE,OAAOqD,EAAI5E,KAAK6B,QAAQ,CAAEmB,IAAK4B,EAAIzE,OAAS,CAAE,CAAC,EAChD,EAGAL,EAAOD,UAAUiG,QAAU,SAAUlB,EAAKG,GACzC,GAAc,IAAVA,EACH,MAAO,GAER,GAAmB,IAAfH,EAAIzE,OACP,MAAM,IAAIoB,WAAW,8CAA8C,EAEpE,GAAIwD,EAAQ,EACX,MAAM,IAAIxD,WAAW,yCAAyC,EAE/D,OAAKwD,GAAmB,IAAVA,EAGN/E,KAAK4F,QAAQhB,CAAG,EAAEjF,MAAM,EAAGoF,CAAK,EAFhC,CAAC/E,KAAK6F,QAAQjB,CAAG,EAI1B,EAEA9E,EAAOD,UAAU+F,QAAU,SAAUhB,GAMpC,IALA,IAECnE,EAFGsF,EAAYnB,EAAIjF,MAAM,CAAC,EAC1BqG,EAAY,GAEZ7F,EAAS8F,OAAOF,EAAU5F,MAAM,EAExBC,EAAI,EAAGA,EAAID,EAAQC,CAAC,GAE5BK,EAAIT,KAAK6B,QAAQ,CAAEmB,IAAK+C,EAAU5F,OAAS,CAAE,CAAC,EAE9C6F,EAAU5F,GAAK2F,EAAUtF,GAEzBsF,EAAUG,OAAOzF,EAAG,CAAC,EAGtB,OAAOuF,CACR,EAGAlG,EAAOD,UAAUsG,SAAW,SAAUvB,EAAKwB,EAASC,GACnD,GAAIzB,EAAIzE,SAAWiG,EAAQjG,OAC1B,MAAM,IAAIoB,WAAW,gDAAgD,EAMtE,IAFA,IACIsD,EADAyB,EAAM,EAEDC,EAAc,EAAGA,EAAcH,EAAQjG,OAAQ,EAAEoG,EAAa,CAEtE,GADA1B,EAAMuB,EAAQG,GACVC,MAAM3B,CAAG,EACZ,MAAM,IAAItD,WAAW,qCAAqC,EAGjD,EAANsD,IACHyB,GAAOzB,EAET,CAEA,GAAY,IAARyB,EACH,MAAM,IAAI/E,WAAW,2CAA2C,EAUjE,IANA,IAKIkF,EALAC,EAAW1G,KAAKC,OAAO,EAAIqG,EAG3BK,EAAQ,EACRC,EAAc,CAAC,EAEdL,EAAc,EAAGA,EAAcH,EAAQjG,OAAQ,EAAEoG,EAAa,CAGlE,GADAI,GADA9B,EAAMuB,EAAQG,GAEJ,EAAN1B,EAAS,CACZ,GAAI6B,GAAYC,EAAO,CACtBF,EAAYF,EACZ,KACD,CACAK,EAAcL,CACf,CAGIA,IAAgBH,EAAQjG,OAAS,IACpCsG,EAAYG,EAEd,CAEA,IAAIC,EAASjC,EAAI6B,GAOjB,OANAJ,EAAuB,KAAA,IAATA,GAA+BA,KAE5CzB,EAAIsB,OAAOO,EAAW,CAAC,EACvBL,EAAQF,OAAOO,EAAW,CAAC,GAGrBI,CACR,EAMA/G,EAAOD,UAAUiH,UAAY,SAAU5F,GAGlC6F,GAFJ7F,EAAUD,EAAYC,CAAO,GAEL6F,WAAa/G,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAGpE,OAFkBhD,KAAKgE,EAAEhE,KAAKgH,SAAUD,CAAS,EAE3B9C,KAAK,GAAG,CAC/B,EAIAnE,EAAOD,UAAUmH,SAAW,SAAU9F,GAGrC,IAAI+F,GAFJ/F,EAAUD,EAAYC,CAAO,GAET+F,OAASjH,KAAK6B,QAAQ,CAAEyB,IAAK,GAAIN,IAAK,EAAG,CAAC,EAC7DkE,EAAchG,EAAQgG,YAIvBC,EAFcnH,KAAKgE,EAAEhE,KAAKmE,KAAM8C,CAAK,EAEnBhD,KAAK,GAAG,EAe1B,OAZAkD,EAAOnH,KAAKkE,WAAWiD,CAAI,GAI1BD,EADmB,CAAA,IAAhBA,GAA0B,cAAc7F,KAAK6F,CAAW,EAKxDA,EAJW,OAKdC,GAAQD,GAGFC,CACR,EAEArH,EAAOD,UAAUuH,SAAW,SAAUlG,GAYrC,IATA,IAKCmG,EALGlH,GAFJe,EAAUD,EAAYC,CAAO,GAERf,QAAUH,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAC7DsE,EAAa,qBAGbH,EAAO,GAKC/G,EAAI,EAAGA,EAAID,EAAQC,CAAC,GAY5B+G,GATCE,EAFS,IAANjH,EAEGJ,KAAKyC,UAAU,CAAEK,KATlBwE,yBAS4B,CAAC,EACI,CAAC,IAA7BA,EAAWxC,QAAQuC,CAAG,EAE1BrH,KAAKyC,UAAU,CAAEK,KAAMwE,CAAW,CAAC,EAGnCtH,KAAKyC,UAAU,CAAEK,KAhBf,OAgB4B,CAAC,EAUvC,OAHCqE,EADGjG,EAAQgD,WACJlE,KAAKkE,WAAWiD,CAAI,EAGrBA,CACR,EAEArH,EAAOD,UAAUsE,KAAO,SAAUjD,GAGjCE,GAFAF,EAAUD,EAAYC,CAAO,GAEXqG,WAAarG,EAAQf,OAAQ,mDAAmD,EAElG,IAAIoH,EAAYrG,EAAQqG,WAAavH,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EACnEmE,EAAO,GAER,GAAIjG,EAAQf,OAAQ,CAEnB,MACCgH,GAAQnH,KAAKoH,SAAS,GACTjH,OAASe,EAAQf,SAC/BgH,EAAOA,EAAKK,UAAU,EAAGtG,EAAQf,MAAM,CACxC,MAEC,IAAK,IAAIC,EAAI,EAAGA,EAAImH,EAAWnH,CAAC,GAC/B+G,GAAQnH,KAAKoH,SAAS,EAQxB,OAHCD,EADGjG,EAAQgD,WACJlE,KAAKkE,WAAWiD,CAAI,EAGrBA,CACR,EAMArH,EAAOD,UAAU4H,IAAM,SAAUvG,GAEhC,IAAIwG,EAEJ,QAHAxG,EAAUD,EAAYC,CAAO,GAGbkB,MACf,IAAK,QACJsF,EAAW,CAAEpE,IAAK,EAAGN,IAAK,EAAG,EAC7B,MACD,IAAK,OACJ0E,EAAW,CAAEpE,IAAK,GAAIN,IAAK,EAAG,EAC9B,MACD,IAAK,QACJ0E,EAAW,CAAEpE,IAAK,GAAIN,IAAK,EAAG,EAC9B,MACD,IAAK,SACJ0E,EAAW,CAAEpE,IAAK,GAAIN,IAAK,GAAI,EAC/B,MACD,IAAK,MACJ0E,EAAW,CAAEpE,IAAK,EAAGN,IAAK,GAAI,EAC9B,MACD,QACC0E,EAAW,CAAEpE,IAAK,GAAIN,IAAK,EAAG,CAEhC,CAEA,OAAOhD,KAAK6B,QAAQ6F,CAAQ,CAC7B,EAEA5H,EAAOD,UAAU8H,SAAW,SAAUzG,GACrC,IAIKoC,EACAN,EALDyE,EAAMzH,KAAKyH,IAAIvG,CAAO,EACtB0G,GAAc,IAAIC,MAAOC,YAAY,EAkBzC,OAVC5G,EANGA,GAAWA,EAAQkB,MAClBkB,EAAM,IAAIuE,KACV7E,EAAM,IAAI6E,KACdvE,EAAIyE,YAAYH,EAAcH,EAAM,CAAC,EACrCzE,EAAI+E,YAAYH,EAAcH,CAAG,EAEvBxG,EAAYC,EAAS,CAC9BoC,IAAKA,EACLN,IAAKA,CACN,CAAC,GAES/B,EAAYC,EAAS,CAC9B8G,KAAMJ,EAAcH,CACrB,CAAC,EAGKzH,KAAKiI,KAAK/G,CAAO,CACzB,EAGApB,EAAOD,UAAUqI,IAAM,SAAUhH,GAChCA,EAAUD,EAAYC,EAAS,CAC9BiH,UAAW,CAAA,CACZ,CAAC,EAED,IAAInE,EAAIhE,KAAKgE,EAAEhE,KAAK6B,QAAS,EAAG,CAAEmB,IAAK,CAAE,CAAC,EACtCoF,EAAY,EAAPpE,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,GAAPA,EAAE,GAMnGqE,EAAK,IAAMA,EADG,GAFbD,EADS,KAANA,EADC,GAAMA,EAAK,IAEV,EAEGA,GAAgB,EAAPpE,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,GAAPA,EAAE,GAAiB,GAAPA,EAAE,IAC7F,GAIZkE,EAAM,GAAKlE,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMoE,GAF/FC,EADS,IAANA,EACE,EAE+FA,GACrG,OAAOnH,EAAQiH,UAAYD,EAAMA,EAAII,QAAQ,MAAO,EAAE,CACvD,EAGAxI,EAAOD,UAAU0I,KAAO,SAAUrH,GACjCA,EAAUD,EAAYC,EAAS,CAC9BiH,UAAW,CAAA,CACZ,CAAC,EAED,IAAInE,EAAIhE,KAAKgE,EAAEhE,KAAK6B,QAAS,GAAI,CAAEmB,IAAK,EAAG,CAAC,EACxCoF,EAAa,EAARpE,EAAE,IAAkB,EAARA,EAAE,IAAiB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAMtIqE,EAAK,IAAMA,EADG,GAFbD,GADGA,EADC,GAAMA,EAAK,IACP,EACH,EAEGA,GAAiB,EAARpE,EAAE,IAAkB,EAARA,EAAE,IAAiB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,IAC/H,GAIZuE,EAAO,GAAKvE,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,IAAMA,EAAE,IAAM,IAAMoE,GAF7HC,EADGA,EAAK,EACH,EAE6HA,GACnI,OAAOnH,EAAQiH,UAAYI,EAAOA,EAAKD,QAAQ,MAAO,EAAE,CACzD,EAEAxI,EAAOD,UAAU2I,MAAQ,SAAUtH,GAElC,OADAA,EAAUD,EAAYC,EAAS,CAAEuH,OAAQzI,KAAKyI,OAAO,EAAGC,YAAa,IAAK,CAAC,EACpE1I,KAAKuC,KAAKvC,KAAKqC,IAAI,YAAY,EAAEnB,EAAQuH,OAAOnG,YAAY,GAAGpB,EAAQwH,YAAYpG,YAAY,EAAE,CACzG,EAEAxC,EAAOD,UAAU8I,WAAa,SAAUzH,GAEvC,OADAA,EAAUD,EAAYC,CAAO,GACjB0H,KACJ5I,KAAKuC,KAAK,CAAC,cAAe,UAAW,UAAW,QAAQ,EAAIvC,KAAKuC,KAAKvC,KAAKqC,IAAI,YAAY,CAAC,EAE5FrC,KAAKuC,KAAKvC,KAAKqC,IAAI,YAAY,CAAC,CAEzC,EAEAvC,EAAOD,UAAUgJ,QAAU,WAC1B,OAAO7I,KAAKuC,KAAKvC,KAAKqC,IAAI,SAAS,CAAC,CACrC,EAEAvC,EAAOD,UAAU4I,OAAS,SAAUvH,GAEnC,OADAA,EAAUD,EAAYC,EAAS,CAAE4H,aAAc,EAAG,CAAC,EAC5C9I,KAAKuC,KAAK,CAAC,OAAQ,UAAUwG,OAAO7H,EAAQ4H,YAAY,CAAC,CACjE,EAEAhJ,EAAOD,UAAUmJ,KAAO,SAAU9H,GAEjC,OADAA,EAAUD,EAAYC,EAAS,CAAEwH,YAAa,IAAK,CAAC,EAC7C1I,KAAKuC,KAAKvC,KAAKqC,IAAI,WAAW,EAAEnB,EAAQwH,YAAYpG,YAAY,EAAE,CAC1E,EAEAxC,EAAOD,UAAUoJ,SAAW,WAG3B,IAFA,IAAIC,EAAIlJ,KAAK+D,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EACjDgJ,EAAI,EACC/I,EAAI,EAAGA,EAAI8I,EAAE/I,OAAQC,CAAC,GAAI,CAClC,IAAIgJ,EAAYF,EAAE9I,IAAMA,EAAI,IAAMiJ,SAASjJ,EAAI,CAAC,EAAI,EAAI,GACxDgJ,EAAYpJ,KAAKyF,IAAI2D,EAAW,CAAC,EAAE7I,SAAS,EAE5C4I,GADYE,SAASD,EAAU,EAAE,EAAIC,SAASD,EAAU,EAAE,CAE3D,CAEA,OADAF,IAAS,GAAKG,SAASF,EAAE5I,SAAS,EAAEZ,MAAM,CAAC,CAAC,CAAC,GAAGY,SAAS,EAAEZ,MAAM,CAAC,CAAC,CAEpE,EAEAG,EAAOD,UAAUyJ,IAAM,SAAUpI,GAChC,IAsCM+G,EAtCFsB,EAAa,SAAUvH,GAC1B,IAAIU,EAAQ,+BAA+B8G,MAAM,EAAE,EAClDC,EAAc,CAAC,EAAG,EAAG,GACrBC,EAAe,EAiBhB,OAdC1H,EADoB,UAAjB,OAAOA,EACFA,EAAMzB,SAAS,EAGxByB,GAAMwH,MAAM,EAAE,EAAEG,QAAQ,SAAUlH,EAAWmH,GAC5C,IAAIC,EAAMnH,EAAMoC,QAAQrC,CAAS,EAGhCA,EADW,CAAC,IAAToH,EACiB,IAARA,EAAY,EAAIA,EAAM,EAEtBR,SAAS5G,EAAW,EAAE,EAGnCiH,GADAjH,GAAagH,EAAYG,EAAMH,EAAYtJ,OAE5C,CAAC,EACMuJ,EAAe,EACvB,EAUII,EAAO9J,KATc+J,EAWzB7I,EAAUD,EAAYC,EAAS,CAC9BsH,MAAOxI,KAAKwI,MAAM,EAClBQ,KAAMhJ,KAAKgJ,KAAK,EAChBgB,eAAgBhK,KAAKwD,QAAQ,CAAEF,IAAK,IAAWN,IAAK,SAAU,CAAC,EAC/DiH,IAEQ,EADHhC,EAAO6B,EAAKnC,SAAS,CAAEvF,KAAM,OAAQ,CAAC,GAC7B0F,YAAY,EAAEvH,SAAS,EAAE6D,OAAO,CAAC,EAAG0F,EAAKrE,IAAIwC,EAAKiC,SAAS,EAAI,EAAG,CAAC,EAAGJ,EAAKrE,IAAIwC,EAAKkC,QAAQ,EAAG,CAAC,GAAGlG,KAAK,EAAE,EAExHmG,OAEQ,GADHnC,EAAO,IAAIJ,MACDC,YAAY,EAAI,GAAGvH,SAAS,EAAE6D,OAAO,CAAC,EAAG0F,EAAKrE,IAAIwC,EAAKiC,SAAS,EAAI,EAAG,CAAC,EAAGJ,EAAKrE,IAAIwC,EAAKkC,QAAQ,EAAG,CAAC,GAAGlG,KAAK,EAAE,EAE9HwE,OAA0B,WAAlBzI,KAAKyI,OAAO,EAAiB,IAAM,IAC3C4B,OAAQ,MACR3B,YAAa,KACd,CAAC,EAzBU,SAANjD,EAAgBtF,GACnB,OAAO,IAAIP,MAAMO,EAAS,CAAC,EAAE8D,KAAK,GAAG,CACtC,CAGA,OAFIyB,EAAS,CAAC,KAAMqE,EAAKM,OAAQN,EAAKf,KAAKvJ,YAAY,EAAG,KAAMsK,EAAKvB,MAAM/I,YAAY,EAAGgG,EAAI,IAAMsE,EAAKf,KAAK7I,OAAS4J,EAAKvB,MAAMrI,OAAS,EAAE,EAAG4J,EAAKC,eAAgBT,EAAWQ,EAAKC,cAAc,EAAGD,EAAKrB,YAAaqB,EAAKE,IAAKV,EAAWQ,EAAKE,GAAG,EAAGF,EAAKtB,OAAQsB,EAAKK,OAAQb,EAAWQ,EAAKK,MAAM,EAAG3E,EAAI,EAAE,EAAG8D,EAAW9D,EAAI,EAAE,CAAC,GAAGxB,KAAK,EAAE,GAEhUsF,EAAW7D,EAAOtB,OAAO,GAAI,EAAE,EAAIsB,EAAOtB,OAAO,GAAI,CAAC,EAAIsB,EAAOtB,OAAO,GAAI,CAAC,CAAC,CAsBhG,EAEAtE,EAAOD,UAAUyK,KAAO,SAAUpJ,GACjCA,EAAUD,EAAYC,CAAO,EAE7B,IAAIsH,EAAQxI,KAAKwI,MAAMtH,CAAO,EAC7B8H,EAAOhJ,KAAKgJ,KAAK9H,CAAO,EAIxBoJ,EADGpJ,EAAQqJ,OACJ/B,EAAQ,IAAMxI,KAAKwI,MAAMtH,CAAO,EAAI,IAAM8H,EACvC9H,EAAQsJ,eACXhC,EAAQ,IAAMxI,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,EAAI,KAAOmG,EAExER,EAAQ,IAAMQ,EAWtB,OARI9H,EAAQuJ,SACXH,EAAOtK,KAAKyK,OAAOvJ,CAAO,EAAI,IAAMoJ,GAIpCA,EADGpJ,EAAQwJ,OACJJ,EAAO,IAAMtK,KAAK0K,OAAOxJ,CAAO,EAGjCoJ,CACR,EAIAxK,EAAOD,UAAU8K,cAAgB,SAAUlC,GAI1C,IAAImC,EAAW,CAAC,CAAEN,KAAM,SAAUO,aAAc,KAAM,GAWtD,MATe,UAJfpC,GADAA,EAASA,GAAU,OACHnG,YAAY,IAIQ,QAAXmG,GACxBmC,EAASpF,KAAK,CAAE8E,KAAM,SAAUO,aAAc,KAAM,CAAC,EAGvC,WAAXpC,GAAkC,QAAXA,IAC1BmC,EAASpF,KAAK,CAAE8E,KAAM,OAAQO,aAAc,MAAO,CAAC,EACpDD,EAASpF,KAAK,CAAE8E,KAAM,SAAUO,aAAc,MAAO,CAAC,GAGhDD,CACR,EAGA9K,EAAOD,UAAU4K,OAAS,SAAUvJ,GACnC,OAAOlB,KAAK8K,YAAY5J,CAAO,CAChC,EAEApB,EAAOD,UAAUiL,YAAc,SAAU5J,GAExC,OADAA,EAAUD,EAAYC,EAAS,CAAEuH,OAAQ,KAAM,CAAC,GACjCsC,KAAO/K,KAAKuC,KAAKvC,KAAK2K,cAAczJ,EAAQuH,MAAM,CAAC,EAAE6B,KAAOtK,KAAKuC,KAAKvC,KAAK2K,cAAczJ,EAAQuH,MAAM,CAAC,EAAEoC,YAC1H,EAEA/K,EAAOD,UAAUmL,KAAO,WAEvB,IAEIC,EAAM,GAGV,OAFAA,GAAOjL,KAAK+D,OAAO,CAAEjB,KAHN,aAGsB3C,OAAQ,CAAE,CAAC,GACzCH,KAAK+D,OAAO,CAAEjB,KAHN,8BAGsB3C,OAAQ,CAAE,CAAC,CAEjD,EAEAL,EAAOD,UAAUqL,IAAM,SAAUhK,GAEhC,IAAIiK,EAAW,aAEdC,GAHDlK,EAAUD,EAAYC,EAAS,CAAEmK,QAAS,CAAA,EAAOC,OAAQ,CAAA,CAAK,CAAC,GAG/CA,OAAS,IAAM,GAK9BJ,EAHIhK,EAAQmK,QAGNrL,KAAK+D,OAAO,CAAEjB,KAAMqI,EAAUhL,OAAQ,CAAE,CAAC,EAFzCH,KAAK+D,OAAO,CAAEjB,KAAMqI,EAAUhL,OAAQ,CAAE,CAAC,EAAIiL,EAAOpL,KAAK+D,OAAO,CAAEjB,KAAMqI,EAAUhL,OAAQ,CAAE,CAAC,EAAIiL,EAAOpL,KAAK+D,OAAO,CAAEjB,KAAMqI,EAAUhL,OAAQ,CAAE,CAAC,EAIxJ,OAAO+K,CACR,EAIApL,EAAOD,UAAU0L,cAAgB,WAiBhC,MAhBe,CACd,CAAEjB,KAAM,iCAAkCO,aAAc,MAAO,EAC/D,CAAEP,KAAM,uBAAwBO,aAAc,OAAQ,EACtD,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,eAAgBO,aAAc,MAAO,EAC7C,CAAEP,KAAM,iBAAkBO,aAAc,MAAO,EAC/C,CAAEP,KAAM,oCAAqCO,aAAc,QAAS,EACpE,CAAEP,KAAM,oBAAqBO,aAAc,MAAO,EAClD,CAAEP,KAAM,iBAAkBO,aAAc,MAAO,EAC/C,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,YAAaO,aAAc,KAAM,EACzC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,0BAA2BO,aAAc,KAAM,EACvD,CAAEP,KAAM,yBAA0BO,aAAc,QAAS,EAG3D,EAGA/K,EAAOD,UAAU6K,OAAS,SAAUxJ,GACnC,OAAOlB,KAAKwL,YAAYtK,CAAO,CAChC,EAEApB,EAAOD,UAAU2L,YAAc,SAAUtK,GAExC,OADAA,EAAUD,EAAYC,CAAO,GACd6J,KAAO/K,KAAKuC,KAAKvC,KAAKuL,cAAc,CAAC,EAAEjB,KAAOtK,KAAKuC,KAAKvC,KAAKuL,cAAc,CAAC,EAAEV,YAC9F,EAEA/K,EAAOD,UAAU4L,cAAgB,WAChC,OAAOzL,KAAKqC,IAAI,eAAe,CAChC,EAGAvC,EAAOD,UAAU6I,YAAc,WAE9B,OADkB1I,KAAKuC,KAAKvC,KAAKyL,cAAc,CAAC,EAC7BnB,IACpB,EAMAxK,EAAOD,UAAU6L,WAAa,WAC7B,MAAO,QAAU1L,KAAK+D,OAAO,CAAEjB,KAAM,kEAAmE3C,OAAQ,GAAI,CAAC,CACtH,EAGAL,EAAOD,UAAU8L,YAAc,WAC9B,OAAO3L,KAAK+D,OAAO,CAAEjB,KAAM,mBAAoB3C,OAAQ,EAAG,CAAC,CAC5D,EAGAL,EAAOD,UAAU+L,UAAY,WAC5B,OAAOnK,EAAOzB,KAAKU,KAAK,CAAEP,OAAQ,EAAG,CAAC,CAAC,CACxC,EAGAL,EAAOD,UAAUgM,SAAW,WAC3B,MAAO,KAAO7L,KAAK8L,KAAK,EAAExD,QAAQ,KAAM,EAAE,EAAE7I,YAAY,EAAI,MAAQO,KAAKU,KAAK,CAAEP,OAAQ,CAAE,CAAC,EAAI,MAAQH,KAAKwD,QAAQ,CAAEF,IAAK,EAAGN,IAAK,CAAE,CAAC,CACvI,EAGAlD,EAAOD,UAAUkM,OAAS,WACzB,OAAO/L,KAAKU,KAAK,CAAEP,OAAQ,CAAE,CAAC,CAC/B,EAKAL,EAAOD,UAAUmM,OAAS,SAAU9K,GACnC,IA2BI6I,EAAO,CACVkC,SAAU,KACVC,MAAO,KACPC,cAAe,KACfC,KAAM,KACNC,SAAU,KACVC,OAAQ,IACT,EAEA,GAAKpL,EAIE,GAAuB,UAAnB,OAAOA,EACjB6I,EAAKmC,MAAQhL,EACbA,EAAU,OACJ,CAAA,GAAuB,UAAnB,OAAOA,EACjB,OAAO,KACD,GAA4B,UAAxBA,EAAQqL,YAClB,OAAO,IACR,MATCxC,EAAKmC,MAAQlM,KAAKkM,MAAM,EACxBhL,EAAU,GA0BX,OAhBA6I,EAAO9I,EAAYC,EAAS6I,CAAI,GAEtBmC,QAETnC,EAAKmC,MAAQlM,KAAKkM,MAAM,GAIzBnC,EAAKkC,SAvDW,CACfO,KAAM,OACNC,MAAO,OACR,EAoD0B1C,EAAKkC,UAAYlC,EAAKkC,SAAW,IAAM,GACjElC,EAAKqC,KAAO/C,SAASU,EAAKqC,KAAM,CAAC,EAAIrC,EAAKqC,KAAO,GACjDrC,EAAKuC,OAtCS,CACbI,EAAG,IACHC,GAAI,KACJC,EAAG,IACH1D,EAAG,GACJ,EAiCsBa,EAAKuC,QAAUvC,EAAKuC,OAAS,GACnDvC,EAAKsC,SAhDW,CACfQ,IAAK,MACLC,GAAI,KACJC,UAAW,YACXC,UAAW,YACXC,QAAS,UACTC,MAAO,QACPC,MAAO,OACR,EAwC0BpD,EAAKsC,UAAYtC,EAAKsC,SAAW,GAC3DtC,EAAKoC,cAvDY,CAChBiB,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,KACN,EAkDgCxD,EAAKoC,eAAiBpC,EAAKoC,cAAgB,GAErEpC,EAAKkC,SA9DI,6BA8DkBjM,KAAKe,MAAMyM,IAAIzD,EAAKmC,KAAK,GAAKnC,EAAKoC,cAAgB,IAAMpC,EAAKoC,cAAgB,KAAOpC,EAAKqC,MAAQrC,EAAKuC,QAAUvC,EAAKsC,SAAW,IAAM,KAAOtC,EAAKqC,KAAO,MAAQrC,EAAKqC,KAAK7L,SAAS,EAAI,KAAOwJ,EAAKuC,OAAS,MAAQvC,EAAKuC,OAAS,KAAOvC,EAAKsC,SAAW,MAAQtC,EAAKsC,SAAW,GAGrT,EAyCAvM,EAAOD,UAAU4N,MAAQ,SAAUvM,GAClC,SAASwM,EAAKC,EAAOC,GACpB,MAAO,CAACD,EAAOA,EAAOA,GAAO1J,KAAK2J,GAAa,EAAE,CAClD,CAEA,SAASC,EAAIC,GACZ,IAAIC,EAAWD,EAAW,OAAS,MAC/BE,EAAeF,EAAW,IAAM9N,KAAKiD,SAAS,CAAEK,IAAK2K,EAAWjL,IAAKkL,CAAU,CAAC,EAAI,GAExF,OAAOH,EAAW,KADDI,EAAcT,EAAK1N,KAAK6B,QAAQ,CAAEyB,IAAK8K,EAASpL,IAAKqL,CAAQ,CAAC,EAAG,GAAG,EAAIrO,KAAK6B,QAAQ,CAAEyB,IAAKgL,EAAWtL,IAAKuL,CAAU,CAAC,EAAI,IAAMvO,KAAK6B,QAAQ,CAAEyB,IAAKkL,EAAUxL,IAAKyL,CAAS,CAAC,EAAI,IAAMzO,KAAK6B,QAAQ,CAAEmB,IAAK,GAAI,CAAC,GAC7LgL,EAAe,GACrD,CAEA,SAASnK,EAAI6K,EAAOC,EAAKC,GACxB,IAAIC,EAASD,EAAW,IAAM,GAC1BE,EAAY,GAiBhB,OAfIX,GACHW,EAAYpB,EAAK1N,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAK8K,EAASpL,IAAKqL,CAAQ,CAAC,EAAG,CAAC,CAAC,EAC/C,aAAnBnN,EAAQ6N,SACXD,EAAYpB,EAAK1N,KAAK6D,IAAI,CAAEP,IAAK,EAAGN,IAAK,EAAG,CAAC,CAAC,IAI9C8L,EADsB,aAAnB5N,EAAQ6N,OACC/O,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAKF,KAAKO,MAAMqL,EAAU,EAAE,EAAGhM,IAAKI,KAAKO,MAAMsL,EAAU,EAAE,CAAE,CAAC,EAAG,CAAC,EAAIjP,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAKF,KAAKO,MAAM2K,EAAY,EAAE,EAAGtL,IAAKI,KAAKO,MAAM4K,EAAY,EAAE,CAAE,CAAC,EAAG,CAAC,EAAIvO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAKF,KAAKO,MAAM6K,EAAW,EAAE,EAAGxL,IAAKI,KAAKO,MAAM8K,EAAW,EAAE,CAAE,CAAC,EAAG,CAAC,EACtQS,KAAAA,IAAZF,GAAqCE,KAAAA,IAAZD,GAAuCC,KAAAA,IAAdZ,GAAyCY,KAAAA,IAAdX,GAAwCW,KAAAA,IAAbV,GAAuCU,KAAAA,IAAbT,EAChIzO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAK0L,EAAShM,IAAKiM,CAAQ,CAAC,EAAG,CAAC,EAAIjP,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAKgL,EAAWtL,IAAKuL,CAAU,CAAC,EAAG,CAAC,EAAIvO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAKkL,EAAUxL,IAAKyL,CAAS,CAAC,EAAG,CAAC,EAE1KzO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAK8K,EAASpL,IAAKqL,CAAQ,CAAC,EAAG,CAAC,EAAIrO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAK8K,EAASpL,IAAKqL,CAAQ,CAAC,EAAG,CAAC,EAAIrO,KAAKyF,IAAIzF,KAAK6D,IAAI,CAAEP,IAAK8K,EAASpL,IAAKqL,CAAQ,CAAC,EAAG,CAAC,EAI3KQ,EAASC,CACjB,CAkBA,IAuCIK,EAvCAhB,GAhBJjN,EAAUD,EAAYC,EAAS,CAC9B6N,OAAQ/O,KAAKuC,KAAK,CAAC,MAAO,WAAY,MAAO,OAAQ,KAAM,OAAO,EAClE6M,UAAW,CAAA,EACXvM,OAAQ,QACRS,IAAK,EACLN,IAAK,IACLgM,QAASE,KAAAA,EACTD,QAASC,KAAAA,EACTZ,UAAWY,KAAAA,EACXX,UAAWW,KAAAA,EACXV,SAAUU,KAAAA,EACVT,SAAUS,KAAAA,EACVjB,UAAW,EACXC,UAAW,CACZ,CAAC,GAEyBkB,UACtBhB,EAAUlN,EAAQoC,IAClB+K,EAAUnN,EAAQ8B,IAClBgM,EAAU9N,EAAQ8N,QAClBC,EAAU/N,EAAQ+N,QAClBX,EAAYpN,EAAQoN,UACpBC,EAAYrN,EAAQqN,UACpBC,EAAWtN,EAAQsN,SACnBC,EAAWvN,EAAQuN,SACnBR,EAAY/M,EAAQ+M,UACpBC,EAAYhN,EAAQgN,UA+BxB,GA9BwBgB,KAAAA,IAApBhO,EAAQ8N,UACXA,EAAUZ,GAEac,KAAAA,IAApBhO,EAAQ+N,UACXA,EAAUZ,GAEea,KAAAA,IAAtBhO,EAAQoN,YACXA,EAAYF,GAEac,KAAAA,IAAtBhO,EAAQqN,YACXA,EAAYF,GAEYa,KAAAA,IAArBhO,EAAQsN,WACXA,EAAWJ,GAEac,KAAAA,IAArBhO,EAAQuN,WACXA,EAAWJ,GAEca,KAAAA,IAAtBhO,EAAQ+M,YACXA,EAAY,GAEaiB,KAAAA,IAAtBhO,EAAQgN,YACXA,EAAY,GAETC,GAA2B,IAAZC,GAA6B,MAAZC,GAA+Ba,KAAAA,IAAZF,GAAqCE,KAAAA,IAAZD,IAC/Eb,GAAWY,EAAUV,EAAYE,GAAY,EAC7CH,GAAWY,EAAUV,EAAYE,GAAY,GAIvB,QAAnBvN,EAAQ6N,OACXI,EAAatL,EAAIrD,KAAKR,KAAM,EAAG,EAAG,CAAA,CAAI,OAChC,GAAuB,aAAnBkB,EAAQ6N,OAClBI,EAAatL,EAAIrD,KAAKR,KAAM,EAAG,EAAG,CAAA,CAAI,OAChC,GAAuB,QAAnBkB,EAAQ6N,OAClBI,EAAatB,EAAIrN,KAAKR,KAAM,CAAA,CAAK,OAC3B,GAAuB,SAAnBkB,EAAQ6N,OAClBI,EAAatB,EAAIrN,KAAKR,KAAM,CAAA,CAAI,MAC1B,CAAA,GAAuB,OAAnBkB,EAAQ6N,OAEZ,CAAA,GAAuB,SAAnB7N,EAAQ6N,OAClB,OAAO/O,KAAKuC,KAAKvC,KAAKqC,IAAI,YAAY,CAAC,EAEvC,MAAM,IAAId,WAAW,kGAAkG,CACxH,CALC4N,EAAa,KAAOtL,EAAIrD,KAAKR,KAAM,EAAG,CAAC,CAKxC,CAMA,OAHCmP,EADsB,UAAnBjO,EAAQ2B,OACEsM,EAAW1P,YAAY,EAG9B0P,CACR,EAEArP,EAAOD,UAAUwP,OAAS,SAAUnO,GAEnC,OADAA,EAAUD,EAAYC,CAAO,EACtBlB,KAAKmE,KAAK,EAAI,KAAOjD,EAAQoO,KAAOtP,KAAKsP,IAAI,EACrD,EAEAxP,EAAOD,UAAUqM,MAAQ,SAAUhL,GAElC,OADAA,EAAUD,EAAYC,CAAO,EACtBlB,KAAKmE,KAAK,CAAEhE,OAAQe,EAAQf,MAAO,CAAC,EAAI,KAAOe,EAAQmO,QAAUrP,KAAKqP,OAAO,EACrF,EAmBAvP,EAAOD,UAAU0P,KAAO,WACvB,MAAO,QAAUvP,KAAK+D,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,EAAG,CAAC,CAChE,EAEAL,EAAOD,UAAU2P,iBAAmB,WAInC,MAAO,MAHOxP,KAAKyF,IAAIzF,KAAK6B,QAAQ,CAAEmB,IAAK,MAAO,CAAC,EAAG,CAAC,EAG9B,IAFVhD,KAAKyF,IAAIzF,KAAK6B,QAAQ,CAAEmB,IAAK,EAAG,CAAC,EAAG,CAAC,CAGrD,EAEAlD,EAAOD,UAAU4P,QAAU,WAC1B,MAAO,IAAMzP,KAAKmE,KAAK,CACxB,EAEArE,EAAOD,UAAU6P,GAAK,WAGrB,OAAO1P,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,GAAI,CAAC,EAAI,IAAMhD,KAAK6B,QAAQ,CAAEmB,IAAK,GAAI,CAAC,EAAI,IAAMhD,KAAK6B,QAAQ,CAAEmB,IAAK,GAAI,CAAC,EAAI,IAAMhD,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,GAAI,CAAC,CAC1J,EAEAlD,EAAOD,UAAU8P,KAAO,WAGvB,OAFc3P,KAAKgE,EAAEhE,KAAKU,KAAM,EAAG,CAAEP,OAAQ,CAAE,CAAC,EAEjC8D,KAAK,GAAG,CACxB,EAEAnE,EAAOD,UAAU+P,MAAQ,WACxB,OAAO5P,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,EAAG,CAAC,CACxC,EAEAlD,EAAOD,UAAUgQ,OAAS,SAAU3O,GACnCA,EAAUD,EAAYC,EAAS,CAAE4O,mBAAoB,CAAA,CAAK,CAAC,EAE3D,IAAIlO,EAAQ5B,KAAK6F,QAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,IAAI,EAK1DkK,GAJA7O,EAAQU,QACXA,EAAQV,EAAQU,OAGA,IAIjB,OAHIV,EAAQ4O,qBACXC,EAAa/P,KAAKmG,SAAS,CAAC,GAAI,OAAQ,QAAS,UAAW,CAAC,GAAI,GAAI,EAAG,EAAE,GAEpEvE,EAAQ5B,KAAKgQ,IAAI,MAAM,EAAE/L,KAAK,GAAG,EAAI8L,CAC7C,EAEAjQ,EAAOD,UAAUoQ,KAAO,WACvB,MAAO,CAAC,MAAO,MAAO,MAAO,MAAO,QAAS,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAC7hD,EAEAnQ,EAAOD,UAAUyP,IAAM,WACtB,OAAOtP,KAAKuC,KAAKvC,KAAKiQ,KAAK,CAAC,CAC7B,EAEAnQ,EAAOD,UAAUqQ,QAAU,WAC1B,MAAO,IAAMlQ,KAAKmE,KAAK,CACxB,EAEArE,EAAOD,UAAUsQ,IAAM,SAAUjP,GAGhC,IAAIkP,EAAwC,GAF5ClP,EAAUD,EAAYC,EAAS,CAAE+K,SAAU,OAAQoD,OAAQrP,KAAKqP,OAAOnO,CAAO,EAAGmP,cAAe,GAAIC,KAAMtQ,KAAKmE,KAAK,EAAGoM,WAAY,EAAG,CAAC,GAE/GA,WAAWpQ,OAAa,IAAMH,KAAKuC,KAAKrB,EAAQqP,UAAU,EAAI,GAClFlB,EAASnO,EAAQmP,cAAgBnP,EAAQmP,cAAgB,IAAMnP,EAAQmO,OAASnO,EAAQmO,OAE5F,OAAOnO,EAAQ+K,SAAW,MAAQoD,EAAS,IAAMnO,EAAQoP,KAAOF,CACjE,EAEAtQ,EAAOD,UAAU2Q,KAAO,WACvB,OAAOxQ,KAAKwD,QAAQ,CAAEF,IAAK,EAAGN,IAAK,KAAM,CAAC,CAC3C,EAEAlD,EAAOD,UAAU4Q,OAAS,SAAUvP,GAEnC,OADAA,EAAUD,EAAYC,CAAO,GACjBwP,OACJ1Q,KAAKuC,KAAKvC,KAAKqC,IAAI,gBAAgB,CAAC,EAEpCrC,KAAKuC,KAAKvC,KAAKqC,IAAI,kBAAkB,CAAC,CAE/C,EAEAvC,EAAOD,UAAU8Q,QAAU,SAAUzP,GAEpC,OADAA,EAAUD,EAAYC,CAAO,GACjBwP,OACJ1Q,KAAKqC,IAAI,gBAAgB,EAEzBrC,KAAKqC,IAAI,kBAAkB,CAEpC,EAMAvC,EAAOD,UAAU+Q,QAAU,SAAU1P,GAEpC,OADAA,EAAUD,EAAYC,CAAO,EACtBlB,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,GAAK,CAAC,EAAI,IAAMhD,KAAK6Q,OAAO3P,CAAO,CACvE,EAEApB,EAAOD,UAAUiR,SAAW,SAAU5P,GAErC,OADAA,EAAUD,EAAYC,EAAS,CAAEgC,MAAO,EAAGI,IAAK,EAAGN,IAAK,IAAK,CAAC,EACvDhD,KAAKiD,SAAS,CACpBK,IAAKpC,EAAQoC,IACbN,IAAK9B,EAAQ8B,IACbE,MAAOhC,EAAQgC,KAChB,CAAC,CACF,EAEApD,EAAOD,UAAUkR,SAAW,SAAU7P,GACrCA,EAAUD,EAAYC,EAAS,CAAE8P,OAAQ,CAAA,CAAK,CAAC,EAE/C,IAAID,EAAW/Q,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EAAIP,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EAAIP,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EAErJ,OAAOW,EAAQ8P,OAAS,IAAMD,EAAW,IAAMA,CAChD,EAEAjR,EAAOD,UAAUoR,KAAO,WACvB,OAAOjR,KAAKkE,WAAWlE,KAAKmE,KAAK,CAAEoD,UAAW,CAAE,CAAC,CAAC,CACnD,EAEAzH,EAAOD,UAAUqR,YAAc,SAAUhQ,GACxC,OAAOlB,KAAKmR,SAASjQ,CAAO,EAAI,KAAOlB,KAAKoR,UAAUlQ,CAAO,CAC9D,EAEApB,EAAOD,UAAUwR,UAAY,WAC5B,OAAOrR,KAAKqC,IAAI,WAAW,CAC5B,EAEAvC,EAAOD,UAAUyR,QAAU,SAAUpQ,GACpCA,EAAUD,EAAYC,CAAO,EAC7B,IAAIoQ,EAAUtR,KAAKuC,KAAKvC,KAAKqR,UAAU,CAAC,EACxC,OAAOnQ,EAAQ6J,KAAOuG,EAAQhH,KAAOgH,EAAQzG,YAC9C,EAEA/K,EAAOD,UAAU0R,MAAQ,SAAUrQ,GAElC,OADAA,EAAUD,EAAYC,EAAS,CAAEgC,MAAO,EAAGI,IAAK,CAAC,MAAON,IAAK,CAAE,CAAC,EACzDhD,KAAKiD,SAAS,CACpBK,IAAKpC,EAAQoC,IACbN,IAAK9B,EAAQ8B,IACbE,MAAOhC,EAAQgC,KAChB,CAAC,CACF,EAEApD,EAAOD,UAAU2R,QAAU,SAAUtQ,GAEpC,OADAA,EAAUD,EAAYC,EAAS,CAAEf,OAAQ,CAAE,CAAC,EACrCH,KAAK+D,OAAO,CAAE5D,OAAQe,EAAQf,OAAQ2C,KAAM,kCAAmC,CAAC,CACxF,EAEAhD,EAAOD,UAAU4R,QAAU,SAAUvQ,GACpC,OAAOlB,KAAKmR,SAASjQ,CAAO,EAAI,KAAOlB,KAAKoR,UAAUlQ,CAAO,EAAI,KAAOlB,KAAK8Q,SAAS5P,CAAO,CAC9F,EAEApB,EAAOD,UAAUsR,SAAW,SAAUjQ,GAErC,OADAA,EAAUD,EAAYC,EAAS,CAAEgC,MAAO,EAAGI,IAAK,CAAC,GAAIN,IAAK,EAAG,CAAC,EACvDhD,KAAKiD,SAAS,CAAEK,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,IAAKE,MAAOhC,EAAQgC,KAAM,CAAC,CAClF,EAEApD,EAAOD,UAAUuR,UAAY,SAAUlQ,GAEtC,OADAA,EAAUD,EAAYC,EAAS,CAAEgC,MAAO,EAAGI,IAAK,CAAC,IAAKN,IAAK,GAAI,CAAC,EACzDhD,KAAKiD,SAAS,CAAEK,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,IAAKE,MAAOhC,EAAQgC,KAAM,CAAC,CAClF,EAEApD,EAAOD,UAAU6R,MAAQ,SAAUxQ,GAGzB,SAARyQ,EAAkBC,GACjB,IAAIC,EAAU,GAKd,OAHAD,EAAME,SAASnI,QAAQ,SAAU3F,GAChC6N,EAAQrM,KAAKuM,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ6D,CAAE,CAAC,CAAC,CAC5D,CAAC,EACM4N,EAAMI,KAAOH,EAAQ5N,KAAK,GAAG,CACrC,CATD,IAAI8N,EAAO/R,KAmBX,QATAkB,EAAUD,EAAYC,EAAS,CAC9BiH,UAAW,CAAA,EACXmJ,QAAS,KACTW,OAAQ,CAAA,CACT,CAAC,GACY9J,YACZjH,EAAQ8P,OAAS,CAAA,GAGV9P,EAAQoQ,SACf,IAAK,KAcHI,GADAQ,EAZIhR,EAAQ+Q,OAYFjS,KAAKuC,KAAK,CAAC,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAXvEH,KAAKuC,KAAK,CAEnB,KAAOvC,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EACxQ,KAAOH,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAC1U,KAAOH,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EACtV,KAAOH,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EACxW,KAAOH,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAC9Q,KAAO4R,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EACpD,EACOe,EAAQiH,UAAY+J,EAAQC,MAAM,KAAK,EAAElO,KAAK,GAAG,EAAIiO,GAK9D,MACD,IAAK,KAyBHR,EAxBIxQ,EAAQ+Q,QAoBZC,EAAUlS,KAAKuC,KAAK,CACnB,CAAEyP,KAAM,KAAOhS,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAI,EAAGuP,SAAU,CAAC,EAAG,EAAG,EACtE,CAAEE,KAAM,SAAUF,SAAU,CAAC,EAAG,EAChC,EACO5Q,EAAQiH,UAAYwJ,EAAMO,CAAO,EAAIP,EAAMO,CAAO,EAAE5J,QAAQ,IAAK,EAAE,IAvB3E4J,EAAUlS,KAAKuC,KAAK,CAGnB,CAAEyP,KAAM,KAAOhS,KAAKyC,UAAU,CAAEK,KAAM,QAAS,CAAC,EAAI,KAAMgP,SAAU,CAAC,EAAG,EAAG,EAC3E,CAAEE,KAAM,OAAShS,KAAKyC,UAAU,CAAEK,KAAM,KAAM,CAAC,EAAGgP,SAAU,CAAC,EAAG,EAAG,EACnE,CAAEE,KAAM,OAAShS,KAAKyC,UAAU,CAAEK,KAAM,IAAK,CAAC,EAAGgP,SAAU,CAAC,EAAG,EAAG,EAClE,CAAEE,KAAM,QAASF,SAAU,CAAC,EAAG,EAAG,EAClC,CAAEE,KAAM,OAAShS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAGuP,SAAU,CAAC,EAAG,EAAG,EAC/F,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACrF,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACzE,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACrF,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACzE,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACrF,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACzE,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EACzE,CAAEE,KAAM,MAAQhS,KAAKuC,KAAK,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAI,IAAKuP,SAAU,CAAC,EAAG,EAC3F,EACO5Q,EAAQiH,UAAYwJ,EAAMO,CAAO,EAAIP,EAAMO,CAAO,EAAE5J,QAAQ,IAAK,GAAI,GAAG,GAQjF,MACD,IAAK,KAMHoJ,GADAQ,EAJIhR,EAAQ+Q,OAIFjS,KAAKuC,KAAK,CAAC,MAAQvC,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,MAAQH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAO4R,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,MAAQH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAE,EAHtlBH,KAAKuC,KAAK,CAAC,KAAOvC,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAG,KAAOH,KAAKuC,KAAK,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAIwP,EAAKhO,OAAO,CAAEjB,KAAM,aAAc3C,OAAQ,CAAE,CAAC,EAAE,EACzhBe,EAAQiH,WAAa+J,GAM9B,MAED,IAAK,KACJ,IAAInB,EAAW/Q,KAAK+Q,SAAS7P,CAAO,EAAEX,SAAS,EAC3C6R,EAAWpS,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EAAIP,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EAAIP,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,CAAE,CAAC,EAAEzC,SAAS,EACjJ8R,EAAarS,KAAK6B,QAAQ,CAAEyB,IAAK,IAAMN,IAAK,IAAK,CAAC,EAAEzC,SAAS,EACjEmR,EAAQxQ,EAAQiH,UAAY4I,EAAW,IAAMqB,EAAW,IAAMC,EAAatB,EAAWqB,EAAWC,CACnG,CACA,OAAOX,CACR,EAEA5R,EAAOD,UAAUyS,OAAS,WAQzB,OANStS,KAAKyC,UAAU,CAAEK,KAAM,mBAAoB,CAAC,EAEtC9C,KAAK6B,QAAQ,CAAEmB,IAAK,CAAE,CAAC,EAAIhD,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,EAI5E,KAFH7C,KAAK6B,QAAQ,CAAEmB,IAAK,CAAE,CAAC,EAAIhD,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,EAAI7C,KAAK6B,QAAQ,CAAEmB,IAAK,CAAE,CAAC,EAGhH,EAEAlD,EAAOD,UAAU0S,SAAW,SAAUrR,GAErC,OADAA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,IAAK,CAAC,EACzCtR,KAAKqC,IAAI,UAAU,EAAEnB,EAAQoQ,QAAQhP,YAAY,EACzD,EAEAxC,EAAOD,UAAU2S,OAAS,SAAUtR,GACnC,OAAOlB,KAAKuC,KAAKvC,KAAKuS,SAASrR,CAAO,CAAC,EAAEoJ,IAC1C,EAEAxK,EAAOD,UAAU4S,UAAY,SAAUvR,GAEtC,OADAA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,IAAK,CAAC,EACzCtR,KAAKqC,IAAI,WAAW,EAAEnB,EAAQoQ,QAAQhP,YAAY,EAC1D,EAEAxC,EAAOD,UAAU6S,SAAW,SAAUxR,GACrC,OAAOA,GAAWA,EAAQ6J,KAAO/K,KAAKuC,KAAKvC,KAAKyS,UAAUvR,CAAO,CAAC,EAAEoJ,KAAOtK,KAAKuC,KAAKvC,KAAKyS,UAAUvR,CAAO,CAAC,EAAE2J,YAC/G,EAEA/K,EAAOD,UAAU8S,MAAQ,SAAUzR,GAClC,OAAOA,GAAWA,EAAQ6J,KAAO/K,KAAKuC,KAAKvC,KAAK4S,OAAO1R,CAAO,CAAC,EAAEoJ,KAAOtK,KAAKuC,KAAKvC,KAAK4S,OAAO1R,CAAO,CAAC,EAAE2J,YACzG,EAEA/K,EAAOD,UAAU+S,OAAS,SAAU1R,GAKnC,QAJAA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,KAAMuB,iBAAkB,CAAA,CAAK,CAAC,GAIxDvB,QAAQhP,YAAY,GACnC,IAAK,KACJ,IAAIuQ,EAAmB7S,KAAKqC,IAAI,kBAAkB,EACjDyQ,EAAc9S,KAAKqC,IAAI,aAAa,EACpC0Q,EAAe/S,KAAKqC,IAAI,cAAc,EAEvCuQ,EAAS,GAEL1R,EAAQ2R,mBACXD,EAASA,EAAO7J,OAAO8J,CAAgB,GAEpC3R,EAAQ4R,cACXF,EAASA,EAAO7J,OAAO+J,CAAW,GAE/B5R,EAAQ6R,eACXH,EAASA,EAAO7J,OAAOgK,CAAY,GAEpC,MACD,IAAK,KACJH,EAAS5S,KAAKqC,IAAI,iBAAiB,EAAEnB,EAAQoQ,QAAQhP,YAAY,GACjE,MACD,IAAK,KACJsQ,EAAS5S,KAAKqC,IAAI,UAAU,EAAEnB,EAAQoQ,QAAQhP,YAAY,EAE5D,CAEA,OAAOsQ,CACR,EAEA9S,EAAOD,UAAUgR,OAAS,SAAU3P,GAEnC,IAAI2P,EAEJ,QAHA3P,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,KAAM/J,UAAW,CAAE,CAAC,GAG9C+J,QAAQhP,YAAY,GACnC,IAAK,KACJuO,EAAS7Q,KAAKmE,KAAK,CAAEoD,UAAWrG,EAAQqG,SAAU,CAAC,EACnDsJ,EAAS7Q,KAAKkE,WAAW2M,CAAM,EAE/BA,GADAA,GAAU,MACA3P,EAAQ8R,aAAehT,KAAKiT,cAAc/R,CAAO,EAAE2J,aAAe7K,KAAKiT,cAAc/R,CAAO,EAAEoJ,MACxG,MACD,IAAK,KACJuG,EAAS7Q,KAAKmE,KAAK,CAAEoD,UAAWrG,EAAQqG,SAAU,CAAC,EACnDsJ,EAAS7Q,KAAKkE,WAAW2M,CAAM,EAC/BA,GAAU3P,EAAQ8R,aAAehT,KAAKiT,cAAc/R,CAAO,EAAE2J,aAAe7K,KAAKiT,cAAc/R,CAAO,EAAEoJ,MAAQ,IAAMuG,CAExH,CACA,OAAOA,CACR,EAEA/Q,EAAOD,UAAUoT,cAAgB,SAAU/R,GAE1C,OADAA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,IAAK,CAAC,EACzCtR,KAAKuC,KAAKvC,KAAKkT,gBAAgBhS,CAAO,CAAC,CAC/C,EAEApB,EAAOD,UAAUqT,gBAAkB,SAAUhS,GAG5C,OAFAA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,IAAK,CAAC,EAEzCtR,KAAKqC,IAAI,iBAAiB,EAAEnB,EAAQoQ,QAAQhP,YAAY,EAChE,EAIAxC,EAAOD,UAAUsT,IAAM,SAAUjS,GAChC,IAAIiS,EAAMnT,KAAKgE,EAAEhE,KAAK6B,QAAS,EAAG,CAAEmB,IAAK,CAAE,CAAC,EAO5C,OALI9B,GAAgC,CAAA,IAArBA,EAAQkS,WACtBD,EAAI3N,KAAK,GAAG,EACZ2N,EAAMA,EAAIpK,OAAO/I,KAAKgE,EAAEhE,KAAK6B,QAAS,EAAG,CAAEmB,IAAK,CAAE,CAAC,CAAC,GAG9CmQ,EAAIlP,KAAK,EAAE,CACnB,EAMAnE,EAAOD,UAAUwT,KAAO,WACvB,OAAOrT,KAAKiC,KAAK,EAAI,KAAO,IAC7B,EAEAnC,EAAOD,UAAUoI,KAAO,SAAU/G,GACjC,IAcKoS,EACAC,EAgCL,OAnCCtL,EATG/G,IAAYA,EAAQoC,KAAOpC,EAAQ8B,MAKlCM,EAA6B,KAAA,KAJjCpC,EAAUD,EAAYC,EAAS,CAC9BsS,SAAU,CAAA,EACVzP,OAAQ,CAAA,CACT,CAAC,GACwBT,IAAsBpC,EAAQoC,IAAImQ,QAAQ,EAAI,EAEnEzQ,EAA6B,KAAA,IAAhB9B,EAAQ8B,IAAsB9B,EAAQ8B,IAAIyQ,QAAQ,EAAI,OAEhE,IAAI5L,KAAK7H,KAAKwD,QAAQ,CAAEF,IAAKA,EAAKN,IAAKA,CAAI,CAAC,CAAC,IAGhDuQ,GADAD,EAAItT,KAAK0T,MAAM,CAAEC,IAAK,CAAA,CAAK,CAAC,GACZC,KAEhB1S,GAAWA,EAAQwS,QAEtBH,EAAcvT,KAAKqC,IAAI,QAAQ,GAAInB,EAAQwS,MAAQ,GAAM,IAAM,IAAIE,MAGpE1S,EAAUD,EAAYC,EAAS,CAC9B8G,KAAMqB,SAASrJ,KAAKgI,KAAK,EAAG,EAAE,EAG9B0L,MAAOJ,EAAEO,QAAU,EACnBC,IAAK9T,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAKuQ,CAAY,CAAC,EAC9CQ,KAAM/T,KAAK+T,KAAK,CAAEC,WAAY,CAAA,CAAK,CAAC,EACpCC,OAAQjU,KAAKiU,OAAO,EACpBC,OAAQlU,KAAKkU,OAAO,EACpBC,YAAanU,KAAKmU,YAAY,EAC9BX,SAAU,CAAA,EACVzP,OAAQ,CAAA,CACT,CAAC,EAEM,IAAI8D,KAAK3G,EAAQ8G,KAAM9G,EAAQwS,MAAOxS,EAAQ4S,IAAK5S,EAAQ6S,KAAM7S,EAAQ+S,OAAQ/S,EAAQgT,OAAQhT,EAAQiT,WAAW,GAM3HC,EAHGlT,EAAQsS,SAGGvL,EAAKiC,SAAS,EAAI,EAAI,IAAMjC,EAAKkC,QAAQ,EAAI,IAAMlC,EAAKH,YAAY,EAEpEG,EAAKkC,QAAQ,EAAI,KAAOlC,EAAKiC,SAAS,EAAI,GAAK,IAAMjC,EAAKH,YAAY,EAG9E5G,EAAQ6C,OAASqQ,EAAcnM,CACvC,EAEAnI,EAAOD,UAAUwU,WAAa,SAAUnT,GACvC,OAAOlB,KAAKiI,KAAK/G,CAAO,EAAEuS,QAAQ,CACnC,EAEA3T,EAAOD,UAAUkU,KAAO,SAAU7S,GAWjC,OALAE,GALAF,EAAUD,EAAYC,EAAS,CAC9BoC,IAAKpC,GAAWA,EAAQ8S,WAAa,EAAI,EACzChR,IAAK9B,GAAWA,EAAQ8S,WAAa,GAAK,EAC3C,CAAC,GAEiB1Q,IAAM,EAAG,oCAAoC,EAC/DlC,EAAUF,EAAQ8S,YAA4B,GAAd9S,EAAQ8B,IAAU,8DAA8D,EAChH5B,EAAU,CAACF,EAAQ8S,YAA4B,GAAd9S,EAAQ8B,IAAU,wCAAwC,EAC3F5B,EAAUF,EAAQoC,IAAMpC,EAAQ8B,IAAK,yCAAyC,EAEvEhD,KAAK6B,QAAQ,CAAEyB,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,GAAI,CAAC,CAC3D,EAEAlD,EAAOD,UAAUsU,YAAc,WAC9B,OAAOnU,KAAK6B,QAAQ,CAAEmB,IAAK,GAAI,CAAC,CACjC,EAEAlD,EAAOD,UAAUoU,OAASnU,EAAOD,UAAUqU,OAAS,SAAUhT,GAO7D,OAJAE,GAFAF,EAAUD,EAAYC,EAAS,CAAEoC,IAAK,EAAGN,IAAK,EAAG,CAAC,GAEhCM,IAAM,EAAG,oCAAoC,EAC/DlC,EAAwB,GAAdF,EAAQ8B,IAAU,wCAAwC,EACpE5B,EAAUF,EAAQoC,IAAMpC,EAAQ8B,IAAK,yCAAyC,EAEvEhD,KAAK6B,QAAQ,CAAEyB,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,GAAI,CAAC,CAC3D,EAEAlD,EAAOD,UAAU6T,MAAQ,SAAUxS,GAGlCE,GAFAF,EAAUD,EAAYC,EAAS,CAAEoC,IAAK,EAAGN,IAAK,EAAG,CAAC,GAEhCM,IAAM,EAAG,oCAAoC,EAC/DlC,EAAwB,GAAdF,EAAQ8B,IAAU,wCAAwC,EACpE5B,EAAUF,EAAQoC,IAAMpC,EAAQ8B,IAAK,yCAAyC,EAE9E,IAAI0Q,EAAQ1T,KAAKuC,KAAKvC,KAAKsU,OAAO,EAAE3U,MAAMuB,EAAQoC,IAAM,EAAGpC,EAAQ8B,GAAG,CAAC,EACvE,OAAO9B,EAAQyS,IAAMD,EAAQA,EAAMpJ,IACpC,EAEAxK,EAAOD,UAAUyU,OAAS,WACzB,OAAOtU,KAAKqC,IAAI,QAAQ,CACzB,EAEAvC,EAAOD,UAAUqU,OAAS,WACzB,OAAOlU,KAAK6B,QAAQ,CAAEmB,IAAK,EAAG,CAAC,CAChC,EAEAlD,EAAOD,UAAU0U,UAAY,WAC5B,OAAOvU,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAKqG,UAAS,IAAIxB,MAAO4L,QAAQ,EAAI,IAAM,EAAE,CAAE,CAAC,CAC/E,EAEA3T,EAAOD,UAAU2U,QAAU,SAAUtT,GAEpC,IAAIuT,EAAW,CAAC,SAAU,UAAW,YAAa,WAAY,UAK9D,OANAvT,EAAUD,EAAYC,EAAS,CAAEwT,aAAc,CAAA,CAAM,CAAC,GAEzCA,eACZD,EAASjP,KAAK,UAAU,EACxBiP,EAASjP,KAAK,QAAQ,GAEhBxF,KAAK6F,QAAQ4O,CAAQ,CAC7B,EAEA3U,EAAOD,UAAUmI,KAAO,SAAU9G,GAOjC,OALAA,EAAUD,EAAYC,EAAS,CAAEoC,KAAK,IAAIuE,MAAOC,YAAY,CAAE,CAAC,GAGxD9E,IAA6B,KAAA,IAAhB9B,EAAQ8B,IAAsB9B,EAAQ8B,IAAM9B,EAAQoC,IAAM,IAExEtD,KAAK6B,QAAQX,CAAO,EAAEX,SAAS,CACvC,EAMAT,EAAOD,UAAU8U,GAAK,SAAUzT,GAG/B,IAIAwE,GAFAtD,GAJAlB,EAAUD,EAAYC,CAAO,GAIdkB,KAAOpC,KAAK4U,QAAQ,CAAEtK,KAAMpJ,EAAQkB,KAAMuR,IAAK,CAAA,CAAK,CAAC,EAAI3T,KAAK4U,QAAQ,CAAEjB,IAAK,CAAA,CAAK,CAAC,GAEpFlJ,OAAOjB,MAAM,EAAE,EAC7BqL,EAAczS,EAAKjC,OAASiC,EAAKqI,OAAOtK,OAAS,EAQjD,OALAuF,EAASA,EAAOqD,OAAO/I,KAAKgE,EAAEhE,KAAKwD,QAASqR,EAAa,CAAEvR,IAAK,EAAGN,IAAK,CAAE,CAAC,CAAC,GAGrEwC,KAAKxF,KAAK8U,eAAepP,EAAOzB,KAAK,EAAE,CAAC,CAAC,EAEzCyB,EAAOzB,KAAK,EAAE,CACtB,EAEAnE,EAAOD,UAAUkV,SAAW,WAE3B,OAAO/U,KAAKqC,IAAI,UAAU,CAC3B,EAEAvC,EAAOD,UAAU+U,QAAU,SAAU1T,GACpCA,EAAUD,EAAYC,CAAO,EAC7B,IAAI8T,EAAQhV,KAAK+U,SAAS,EACzB3S,EAAO,KAER,GAAIlB,EAAQoJ,KAAM,CACjB,IAAK,IAAIlK,EAAI,EAAGA,EAAI4U,EAAM7U,OAAQC,CAAC,GAElC,GAAI4U,EAAM5U,GAAGkK,OAASpJ,EAAQoJ,MAAQ0K,EAAM5U,GAAG6U,aAAe/T,EAAQoJ,KAAM,CAC3ElI,EAAO4S,EAAM5U,GACb,KACD,CAED,GAAa,OAATgC,EACH,MAAM,IAAIb,WAAW,6BAA+BL,EAAQoJ,KAAO,oBAAoB,CAEzF,MACClI,EAAOpC,KAAKuC,KAAKyS,CAAK,EAGvB,OAAO9T,EAAQyS,IAAMvR,EAAOA,EAAKkI,IAClC,EAGAxK,EAAOD,UAAUqV,eAAiB,WACjC,OAAOlV,KAAKqC,IAAI,gBAAgB,CACjC,EAGAvC,EAAOD,UAAUsV,SAAW,WAC3B,OAAOnV,KAAKuC,KAAKvC,KAAKkV,eAAe,CAAC,CACvC,EAGApV,EAAOD,UAAUuV,UAAY,WAC5B,OAAOpV,KAAKqC,IAAI,WAAW,CAC5B,EAGAvC,EAAOD,UAAUwV,SAAW,WAC3B,OAAOrV,KAAKuC,KAAKvC,KAAKoV,UAAU,CAAC,CAClC,EAGAtV,EAAOD,UAAUyV,cAAgB,SAAUC,GAC1C,IAAIC,EAAaxV,KAAKwE,OAAOxE,KAAKmV,SAAU,EAAG,CAC9CxQ,WAAY,SAAUC,EAAKC,GAC1B,OAAOD,EAAI6Q,OAAO,SAAUC,EAAKC,GAEhC,OAAOD,GAAOC,EAAKC,OAAS/Q,EAAI+Q,IACjC,EAAG,CAAA,CAAK,CACT,CACD,CAAC,EAED,OAAIL,EACIC,EAAW,GAAGI,KAAO,IAAMJ,EAAW,GAAGI,KAEzCJ,CAET,EAEA1V,EAAOD,UAAUgW,OAAS,SAAU3U,GAEnCA,EAAUD,EAAYC,EAAS,CAAE8B,IAAK,IAAOM,IAAK,CAAE,CAAC,EAErD,IAAIuS,EAAS7V,KAAKiD,SAAS,CAAEK,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,IAAKE,MAAO,CAAE,CAAC,EAAE3C,SAAS,EACrFuV,EAAQD,EAAOrM,MAAM,GAAG,EAAE,GAQ3B,OANc0F,KAAAA,IAAV4G,EACHD,GAAU,MACAC,EAAM3V,OAAS,IACzB0V,GAAkB,KAGfA,EAAS,EACL,KAAOA,EAAOvN,QAAQ,IAAK,EAAE,EAE7B,IAAMuN,CAEf,EAEA/V,EAAOD,UAAUkW,KAAO,SAAU7U,GACjC,OAAO+E,OAAOjG,KAAK6V,OAAO3U,CAAO,EAAEoH,QAAQ,IAAK,EAAE,CAAC,EAAE0N,eAAe,EAAI,GACzE,EAEAlW,EAAOD,UAAUoW,IAAM,SAAU/U,GAChCA,EAAUD,EAAYC,CAAO,EAC7B,IAAI+U,EAAM,GAYV,OAVAA,EAAIjO,KAAOhI,KAAKkW,SAAS,EAIrBD,EAAIjO,QAAS,IAAIH,MAAOC,YAAY,EAAEvH,SAAS,EAClD0V,EAAIvC,MAAQ1T,KAAKmW,UAAU,CAAEC,OAAQ,CAAA,CAAK,CAAC,EAE3CH,EAAIvC,MAAQ1T,KAAKmW,UAAU,EAGrBjV,EAAQyS,IAAMsC,EAAMA,EAAIvC,MAAQ,IAAMuC,EAAIjO,IAClD,EAEAlI,EAAOD,UAAUsW,UAAY,SAAUjV,GACtCA,EAAUD,EAAYC,CAAO,EAC7B,IAAIwS,EAGH2C,GAAW,IAAIxO,MAAOqC,SAAS,EAAI,EAEpC,GAAIhJ,EAAQkV,QAAuB,KAAbC,EACrB,KACC3C,EAAQ1T,KAAK0T,MAAM,CAAEC,IAAK,CAAA,CAAK,CAAC,EAAEE,QACtBxK,SAASqK,EAAO,EAAE,GACT2C,SAEtB3C,EAAQ1T,KAAK0T,MAAM,CAAEC,IAAK,CAAA,CAAK,CAAC,EAAEE,QAGnC,OAAOH,CACR,EAEA5T,EAAOD,UAAUqW,SAAW,WAC3B,IAAIG,GAAW,IAAIxO,MAAOqC,SAAS,EAAI,EACtCoM,GAAU,IAAIzO,MAAOC,YAAY,EAElC,OAAO9H,KAAKgI,KAAK,CAAE1E,IAAkB,KAAb+S,EAAkBC,EAAU,EAAIA,EAAStT,IAAKsT,EAAU,EAAG,CAAC,CACrF,EAEAxW,EAAOD,UAAU0W,IAAM,SAAUrV,GAEhC,GACM,QAFNA,EAAUD,EAAYC,EAAS,CAAEoQ,QAAS,IAAK,CAAC,GAChCA,QAAQhP,YAAY,EAElC,OAAOtC,KAAKwW,OAAO,CAEtB,EAMA1W,EAAOD,UAAU4W,KAAO,WACvB,IAAI/T,EAAQ,6BAGZ,OADW1C,KAAK+D,OAAO,CAAE5D,OAAQ,EAAG2C,KAAMJ,CAAM,CAAC,EAAI1C,KAAKyF,IAAIzF,KAAKwD,QAAQ,CAAEF,IAAK,EAAGN,IAAK,EAAG,CAAC,EAAG,CAAC,EAAIhD,KAAK+D,OAAO,CAAE5D,OAAQ,EAAG2C,KADhHJ,sCAC+H,CAAC,EAAI1C,KAAKyF,IAAIzF,KAAK6B,QAAQ,EAAG7B,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,EAAG,CAAC,CAAC,CAE9M,EAMAlD,EAAOD,UAAU2W,OAAS,WACzB,IAAIA,EAASxW,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,IAAQ,CAAC,EAGlD,OAAOwT,EADExW,KAAKyF,IAAI+Q,EAAQ,CAAC,EAAIxW,KAAKyF,IAAIzF,KAAKuC,KAAKvC,KAAKyS,UAAU,CAAEnB,QAAS,IAAK,CAAC,CAAC,EAAEsE,KAAM,CAAC,GAC5E5V,KAAK8U,eAAe0B,CAAM,CAC3C,EAgBA1W,EAAOD,UAAU6W,GAAK,SAAUxV,GAQb,SAAjByV,EAA2BrM,EAAMsM,GAChC,IAAIC,EACHC,EAAe,GAiChB,OA/BIxM,EAAKnK,OAAS,EACjB2W,EAAexM,EAAKd,MAAM,EAAE,EAAET,OAAO,MAAMS,MAAM,EAAE,CAAC,EAAEtD,OAAO,EAAG,CAAC,IAW/D2Q,EAFgB,GAPlBA,EAAOvM,EACL7K,YAAY,EACZ+J,MAAM,EAAE,EACRuN,IAAI,SAAUC,GACd,MAA2C,CAAC,IAArC,qBAAqBlS,QAAQkS,CAAC,EAAWA,EAAI9H,KAAAA,CACrD,CAAC,EACAjL,KAAK,EAAE,GACA9D,OACJyW,EACIC,EAAKzS,OAAO,EAAG,CAAC,EAEhByS,EAAK,GAAKA,EAAKzS,OAAO,EAAG,CAAC,EAG/ByS,GAAK1W,OAAS,IACjB2W,EAAeD,EACfA,EAAOvM,EACL7K,YAAY,EACZ+J,MAAM,EAAE,EACRuN,IAAI,SAAUC,GACd,MAA8B,CAAC,IAAxB,QAAQlS,QAAQkS,CAAC,EAAWA,EAAI9H,KAAAA,CACxC,CAAC,EACAjL,KAAK,EAAE,EACPG,OAAO,EAAG,EAAI0S,EAAa3W,MAAM,GAEpC2W,GAA8BD,GAGxBC,CACR,CA1CD,IAAIrO,GADJvH,EAAUA,GAAW,IACEuH,QAA0BzI,KAAKyI,OAAO,EAC5DD,EAAUtH,EAAQsH,OAAwBxI,KAAKwI,MAAM,CAAEC,OAAQA,EAAQC,YAAa,IAAK,CAAC,EAC1FM,EAAS9H,EAAQ8H,MAAsBhJ,KAAKgJ,KAAK,CAAEN,YAAa,IAAK,CAAC,EACtEf,EAAazG,EAAQyG,UAA8B3H,KAAK2H,SAAS,EACjEsJ,EAAS/P,EAAQ+P,MAAsBjR,KAAK6F,QAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,EAAI7F,KAAKyF,IAAIzF,KAAK6B,QAAQ,CAAEmB,IAAK,GAAI,CAAC,EAAG,CAAC,EA6D3J0T,GAAKA,EA5DC,IA4DE3N,OAAO4N,EAAe3N,EAAM,CAAA,CAAI,EAAG2N,EAAenO,CAAK,GAtBzBC,EAsBqDA,EAtB7CqB,EAsBqD9J,MAtBvE2H,EAsBqDA,GAnB/DG,YAAY,EAAEvH,SAAS,EAAE6D,OAAO,CAAC,EAF9B,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAETuD,EAASuC,SAAS,GAAKJ,EAAKrE,IAAIkC,EAASwC,QAAQ,GAA8B,WAAzB1B,EAAOnG,YAAY,EAAiB,GAAK,GAAI,CAAC,GAmB9D2O,EAAKxR,YAAY,EAAE+J,MAAM,EAAE,CAAC,EAAEvF,KAAK,EAAE,EAG/I,OAFAyS,GAlBwB,SAAUA,GAOhC,IANA,IAAIO,EAAS,uCACZC,EAAS,uCACTC,EAAQ,6BAERC,EAAQ,EAEAhX,EAAI,EAAGA,EAAI,GAAIA,CAAC,GAEvBgX,IADGhX,EAAI,GAAM,EACJ+W,EALH,8BAKSrS,QAAQoS,EAAOD,EAAOnS,QAAQ4R,EAAGtW,EAAE,EAAE,EAKtD,OAAO+W,EAAMC,EAAQ,GACtB,EAG0BV,EAAGjX,YAAY,CAAO,GAEvCA,YAAY,CACvB,EAEAK,EAAOD,UAAUwX,SAAW,WAG3B,IAFA,IAAI3R,EAAS1F,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,UAAW,CAAC,EACjD4B,EAAM5E,KAAKyF,IAAIC,EAAQ,EAAE,EAAE8D,MAAM,EAAE,EAC9BpJ,EAAI,EAAGA,EAAIwE,EAAIzE,OAAQC,CAAC,GAChCwE,EAAIxE,GAAKiJ,SAASzE,EAAIxE,EAAE,EAGrBkX,GAAiB,CAAI1S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAK,CAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAK,CAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAKxJ,OAJsB,IAAlB0S,IACHA,EAAgB,GAAKA,GAGf1S,EAAIX,KAAK,EAAE,EAAIqT,CACvB,EAEAxX,EAAOD,UAAU0X,OAAS,WAGzB,IAFA,IAAI7R,EAAS1F,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,SAAU,CAAC,EAChD4B,EAAM5E,KAAKyF,IAAIC,EAAQ,CAAC,EAAE8D,MAAM,EAAE,EAC7BpJ,EAAI,EAAGA,EAAIwE,EAAIzE,OAAQC,CAAC,GAChCwE,EAAIxE,GAAKiJ,SAASzE,EAAIxE,EAAE,EAGrBkX,GAAiB,EAAI1S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAC3I,OAAsB,IAAlB0S,EACItX,KAAKuX,OAAO,EAGb3S,EAAIX,KAAK,EAAE,EAAIqT,CACvB,EAEAxX,EAAOD,UAAU2X,SAAW,WAG3B,IAFA,IAAI9R,EAAS1F,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAK,QAAS,CAAC,EAC/C4B,EAAM5E,KAAKyF,IAAIC,EAAQ,CAAC,EAAE8D,MAAM,EAAE,EAC7BpJ,EAAI,EAAGA,EAAIwE,EAAIzE,OAAQC,CAAC,GAChCwE,EAAIxE,GAAKiJ,SAASzE,EAAIxE,EAAE,EAGrBkX,GAAiB,EAAI1S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAK9H,OAJsB,KAAlB0S,IACHA,EAAgB,GAGV1S,EAAIX,KAAK,EAAE,EAAIqT,CACvB,EAMAxX,EAAOD,UAAU4X,KAAO,SAAUvW,GAQjCA,EAAUD,EAAYC,EAAS,CAAEwW,MAAO,SAAU,CAAC,EACnD,IAAIC,EAAS,CACZC,SAAU,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACzCC,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,MAChCC,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAClC,EAIA,OAHAH,EAAOI,IAAMJ,EAAOC,SAAS7O,OAAO4O,EAAOE,MAAM9O,OAAO4O,EAAOG,MAAM,CAAC,EACtEH,EAAOK,QAAUL,EAAOC,SAAS7O,OAAO4O,EAAOE,KAAK,EACpDF,EAAOM,SAAWN,EAAOC,SAAS7O,OAAO4O,EAAOG,MAAM,EAC/C9X,KAAK6F,QAAQ8R,EAAOzW,EAAQwW,MAAM,CAC1C,EAEA5X,EAAOD,UAAUqY,UAAY,SAAUhX,GAItC,OADAA,EAAUD,EAAYC,EAAS,CAAEoC,IAFvB,EAEiCN,IADjC,GAC0C,CAAC,EAC9ChD,KAAKwD,QAAQ,CAAEF,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,GAAI,CAAC,CAC3D,EAEAlD,EAAOD,UAAUsY,cAAgB,SAAUjX,GAE1C,IAAIkX,EAAkB,CAAC,MAAO,MAAO,MAAO,OAI5C,OALAlX,EAAUD,EAAYC,EAAS,CAAEmX,KAAM,CAAA,CAAK,CAAC,GAEjCA,OACXD,EAAkB,CAAC,OAAQ,OAAQ,IAAK,MAAO,MAAO,MAEhDpY,KAAK6F,QAAQuS,CAAe,CACpC,EAEAtY,EAAOD,UAAUyY,MAAQ,SAAUpX,GAElC,OADAA,EAAUD,EAAYC,CAAO,EACtBlB,KAAKyX,KAAKvW,CAAO,EAAIlB,KAAKmY,cAAcjX,CAAO,CACvD,EAEApB,EAAOD,UAAU0Y,MAAQ,SAAUrX,GAIlC,OADAA,EAAUD,EAAYC,EAAS,CAAEoC,IAFvB,GAEiCN,IADjC,GAC0C,CAAC,EAC9ChD,KAAKwD,QAAQ,CAAEF,IAAKpC,EAAQoC,IAAKN,IAAK9B,EAAQ8B,GAAI,CAAC,CAC3D,EAOAlD,EAAOD,UAAU2Y,KAAO,SAAUtX,GACjC,OAAOlB,KAAKiC,KAAK,EAAI,QAAU,OAChC,EAQAnC,EAAOD,UAAU4Y,GAAK9W,EAAO,CAAE2B,IAAK,EAAGN,IAAK,CAAE,CAAC,EAC/ClD,EAAOD,UAAU6Y,GAAK/W,EAAO,CAAE2B,IAAK,EAAGN,IAAK,CAAE,CAAC,EAC/ClD,EAAOD,UAAU8Y,GAAKhX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,CAAE,CAAC,EAC/ClD,EAAOD,UAAU+Y,IAAMjX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,EAAG,CAAC,EACjDlD,EAAOD,UAAUgZ,IAAMlX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,EAAG,CAAC,EACjDlD,EAAOD,UAAUiZ,IAAMnX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,EAAG,CAAC,EACjDlD,EAAOD,UAAUkZ,IAAMpX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,EAAG,CAAC,EACjDlD,EAAOD,UAAUmZ,KAAOrX,EAAO,CAAE2B,IAAK,EAAGN,IAAK,GAAI,CAAC,EAEnDlD,EAAOD,UAAUmQ,IAAM,SAAUiJ,EAAQ/X,GAExC,GADAA,EAAUD,EAAYC,CAAO,EACxB+X,EAEE,CACN,IAAIC,EAAOD,EAAO3W,YAAY,EAAEkH,MAAM,GAAG,EACxC2P,EAAQ,GAET,GAAoB,IAAhBD,EAAK/Y,QAAgB,CAACkJ,SAAS6P,EAAK,GAAI,EAAE,GAAK,CAAC7P,SAAS6P,EAAK,GAAI,EAAE,EACvE,MAAM,IAAIxX,MAAM,0IAA0I,EAE3J,IAAK,IAAItB,EAAI8Y,EAAK,GAAQ,EAAJ9Y,EAAOA,CAAC,GAC7B+Y,EAAM/Y,EAAI,GAAKJ,KAAK6B,QAAQ,CAAEyB,IAAK,EAAGN,IAAKkW,EAAK,EAAG,CAAC,EAErD,OAA8B,KAAA,IAAhBhY,EAAQoF,KAAuBpF,EAAQoF,IAClD6S,EAAM1D,OAAO,SAAU2D,EAAGpC,GAC1B,OAAOoC,EAAIpC,CACX,CAAC,EACDmC,CACJ,CAhBC,MAAM,IAAI5X,WAAW,6CAA6C,CAiBpE,EAGAzB,EAAOD,UAAUiM,KAAO,SAAU5K,GACjCA,EAAUD,EAAYC,EAAS,CAAEmY,QAAS,CAAE,CAAC,EAE7C,IAAIC,EAAY,mBAgBhB,OAbEtZ,KAAK+D,OAAO,CAAEjB,KAAMwW,EAAWnZ,OAAQ,CAAE,CAAC,EAC1C,IACAH,KAAK+D,OAAO,CAAEjB,KAAMwW,EAAWnZ,OAAQ,CAAE,CAAC,EAC1C,IAEAe,EAAQmY,QACRrZ,KAAK+D,OAAO,CAAEjB,KAAMwW,EAAWnZ,OAAQ,CAAE,CAAC,EAC1C,IAEAH,KAAK+D,OAAO,CAAEjB,KAXA,OAWoB3C,OAAQ,CAAE,CAAC,EAC7CH,KAAK+D,OAAO,CAAEjB,KAAMwW,EAAWnZ,OAAQ,CAAE,CAAC,EAC1C,IACAH,KAAK+D,OAAO,CAAEjB,KAAMwW,EAAWnZ,OAAQ,EAAG,CAAC,CAE9C,EAGAL,EAAOD,UAAUa,KAAO,SAAUQ,GAEjC,IAAI4B,EAA0B,WAD9B5B,EAAUD,EAAYC,EAAS,CAAEf,OAAQ,GAAI0C,OAAQ,OAAQ,CAAC,GAC3CA,OAAqBnD,EAASD,YAAY,EAAIC,EACjE,OAAOM,KAAK+D,OAAO,CAAEjB,KAAMA,EAAM3C,OAAQe,EAAQf,MAAO,CAAC,CAC1D,EAEAL,EAAOD,UAAU0Z,WAAa,SAAU7U,GACnC8U,EAAM9U,EAAInE,SAAS,EAEvB,MADiB,CAACiZ,EAAIhS,UAAUgS,EAAIrZ,OAAS,CAAC,IACxBH,KAAK8U,eAAe,CAAC0E,EAAIhS,UAAU,EAAGgS,EAAIrZ,OAAS,CAAC,CAAC,CAC5E,EAEAL,EAAOD,UAAUiV,eAAiB,SAAUpQ,GAK3C,IAJA,IAEI0S,EAFAqC,EAAS/U,EAAInE,SAAS,EAAEiJ,MAAM,EAAE,EAAEkQ,QAAQ,EAC1CpT,EAAM,EAGDlG,EAAI,EAAGuZ,EAAIF,EAAOtZ,OAAYC,EAAJuZ,EAAO,EAAEvZ,EAC3CgX,EAAQ,CAACqC,EAAOrZ,GACZA,EAAI,GAAM,GAED,GADZgX,GAAS,KAERA,GAAS,GAGX9Q,GAAO8Q,EAER,OAAc,EAAN9Q,EAAW,EACpB,EAGAxG,EAAOD,UAAU2N,IAAM,SAAUtM,GAChC,IAAI6I,EAAO,CAAEyP,IAAK,GAAII,IAAK,KAAMjG,IAAK,CAAA,CAAM,EAE5C,GAAKzS,EAGE,GAAuB,UAAnB,OAAOA,EACjB6I,EAAKyP,IAAMtY,EACXA,EAAU,OACJ,CAAA,GAAuB,UAAnB,OAAOA,EACjB,OAAO,KACD,GAA4B,UAAxBA,EAAQqL,YAClB,OAAO,IACR,MATCxC,EAAKyP,IAAMxZ,KAAK+D,OAAO,EACvB7C,EAAU,GAYX,IAFA6I,EAAO9I,EAAYC,EAAS6I,CAAI,GAEtByP,IAIV,OAAOxZ,KAAKe,MAAMyM,IAAIzD,EAAKyP,IAAKzP,EAAK6P,IAAK7P,EAAK4J,GAAG,EAHjD,MAAM,IAAIjS,MAAM,gDAAgD,CAIlE,EA+GA,IAAImY,EAAO,CACVC,WAAY,CACXC,KAAM,CACLC,GAAI,CAAC,QAAS,OAAQ,SAAU,UAAW,UAAW,QAAS,UAAW,SAAU,UAAW,SAAU,cAAe,SAAU,UAAW,SAAU,SAAU,UAAW,OAAQ,OAAQ,SAAU,SAAU,UAAW,SAAU,QAAS,SAAU,QAAS,SAAU,UAAW,QAAS,UAAW,QAAS,OAAQ,OAAQ,WAAY,OAAQ,UAAW,QAAS,QAAS,WAAY,QAAS,UAAW,SAAU,UAAW,UAAW,SAAU,WAAY,UAAW,OAAQ,QAAS,SAAU,SAAU,QAAS,YAAa,QAAS,QAAS,UAAW,SAAU,QAAS,OAAQ,OAAQ,SAAU,UAAW,OAAQ,SAAU,SAAU,OAAQ,WAAY,MAAO,SAAU,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,MAAO,QAAS,QAAS,SAAU,QAAS,SAAU,YAAa,QAAS,QAAS,UAAW,SAAU,OAAQ,QAAS,SAAU,SAAU,OAAQ,OAAQ,QAAS,UAAW,QAAS,QAAS,SAAU,UAAW,SAAU,WAAY,SAAU,SAAU,QAAS,UAAW,QAAS,SAAU,UAAW,UAAW,OAAQ,UAAW,QAAS,UAAW,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,SAAU,QAAS,YAAa,QAAS,SAAU,OAAQ,OAAQ,OAAQ,SAAU,SAAU,QAAS,OAAQ,YAAa,SAAU,SAAU,SAAU,QAAS,UAAW,OAAQ,QAAS,QAAS,OAAQ,UAAW,MAAO,UAAW,UAAW,QAAS,QAAS,QAAS,SAAU,WAAY,SAAU,OAAQ,QAAS,QAAS,OAAQ,OAAQ,SAAU,MAAO,SAAU,UAAW,QAAS,QAAS,UAAW,SAAU,MAAO,QAAS,QAAS,SAAU,SAAU,QAAS,WAAY,MAAO,QAAS,SAAU,QAAS,QAAS,QAAS,SAAU,MAAO,QAAS,SAAU,UAAW,SAAU,SAAU,MAAO,OAAQ,MAAO,SAAU,SAAU,OAAQ,QAAS,UAAW,WAAY,OAAQ,SAAU,UAAW,WAAY,QAAS,QAAS,OAAQ,QAAS,MAAO,QAAS,UAAW,SAAU,SAAU,QAAS,OAAQ,QAAS,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,MAAO,UAAW,QAAS,QAAS,OAAQ,SAAU,QAAS,UAAW,UAAW,WAAY,UAAW,MAAO,QAAS,OAAQ,SAAU,UAAW,QAAS,OAAQ,YAAa,OAAQ,OAAQ,UAAW,UAAW,MAAO,QAAS,UAAW,YAAa,OAAQ,SAAU,SAAU,OAAQ,OAAQ,QAAS,QAAS,UAEh1EC,GAAI,CAAC,SAAU,UAAW,OAAQ,aAAc,UAAW,UAAW,SAAU,SAAU,SAAU,UAAW,WAAY,UAAW,UAAW,SAAU,WAAY,QAAS,QAAS,SAAU,YAAa,UAAW,UAAW,SAAU,WAAY,YAAa,UAAW,QAAS,QAAS,SAAU,QAAS,OAAQ,WAAY,SAAU,UAAW,OAAQ,OAAQ,WAAY,WAAY,SAAU,SAAU,OAAQ,SAAU,QAAS,WAAY,WAAY,aAAc,WAAY,UAAW,YAAa,SAAU,WAAY,UAAW,YAAa,YAAa,YAAa,aAAc,WAAY,YAAa,SAAU,OAAQ,UAAW,WAAY,WAAY,SAAU,WAAY,WAAY,WAAY,QAAS,SAAU,SAAU,OAAQ,WAAY,UAAW,OAAQ,UAAW,QAAS,SAAU,WAAY,QAAS,SAAU,QAAS,eAAgB,UAAW,SAAU,SAAU,WAAY,QAAS,UAAW,QAAS,UAAW,QAAS,OAAQ,UAAW,SAAU,UAAW,SAAU,QAAS,aAAc,QAAS,SAAU,WAAY,OAAQ,SAAU,QAAS,WAAY,UAAW,UAAW,SAAU,YAAa,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,SAAU,UAAW,SAAU,MAAO,UAAW,UAAW,SAAU,QAAS,WAAY,YAEjyCC,GAAI,CAAC,QAAS,OAAQ,OAAQ,UAAW,SAAU,YAAa,MAAO,QAAS,OAAQ,OAAQ,MAAO,WAAY,WAAY,MAAO,QAAS,OAAQ,QAAS,MAAO,SAAU,QAAS,aAAc,WAAY,OAAQ,QAAS,SAAU,OAAQ,SAAU,SAAU,QAAS,OAAQ,OAAQ,QAAS,SAAU,SAAU,OAAQ,QAAS,QAAS,OAAQ,SAAU,QAAS,OAAQ,UAAW,SAAU,QAAS,QAAS,WAAY,QAAS,QAAS,SAAU,SAAU,SAAU,OAAQ,OAAQ,OAAQ,UAAW,OAAQ,SAAU,QAAS,OAAQ,OAAQ,WAAY,aAAc,OAAQ,QAAS,MAAO,QAAS,SAAU,SAAU,QAAS,QAAS,OAAQ,SAAU,QAAS,MAAO,MAAO,OAAQ,WAAY,OAAQ,WAAY,QAAS,SAAU,OAAQ,SAAU,OAAQ,QAAS,OAAQ,OAAQ,UAAW,WAAY,UAAW,WAAY,MAAO,OAAQ,QAAS,QAAS,OAAQ,UAAW,SAAU,UAAW,SAAU,WAAY,UAAW,MAAO,OAAQ,QAAS,OAAQ,OAAQ,QAAS,UAAW,WAAY,SAAU,WAAY,SAAU,OAAQ,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,UAAW,QAAS,OAAQ,OAAQ,SAAU,QAAS,SAAU,MAAO,UAAW,UAAW,OAAQ,QAAS,QAAS,MAAO,SAAU,SAAU,aAAc,MAAO,MAAO,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,SAAU,QAAS,OAAQ,OAAQ,UAAW,QAAS,SAAU,OAAQ,MAAO,OAAQ,SAAU,MAAO,SAAU,QAAS,SAAU,MAAO,SAAU,QACx8C,EAEAC,OAAQ,CACPH,GAAI,CAAC,OAAQ,OAAQ,YAAa,SAAU,WAAY,MAAO,QAAS,SAAU,QAAS,QAAS,QAAS,OAAQ,WAAY,OAAQ,SAAU,QAAS,SAAU,QAAS,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,QAAS,SAAU,QAAS,SAAU,OAAQ,YAAa,UAAW,MAAO,SAAU,QAAS,SAAU,SAAU,QAAS,OAAQ,SAAU,MAAO,UAAW,OAAQ,OAAQ,OAAQ,SAAU,QAAS,QAAS,SAAU,YAAa,OAAQ,OAAQ,YAAa,QAAS,QAAS,OAAQ,MAAO,QAAS,UAAW,SAAU,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,SAAU,QAAS,QAAS,OAAQ,MAAO,OAAQ,UAAW,QAAS,WAAY,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,OAAQ,QAAS,UAAW,QAAS,OAAQ,SAAU,SAAU,SAAU,QAAS,YAAa,UAAW,OAAQ,QAAS,QAAS,SAAU,SAAU,OAAQ,WAAY,QAAS,UAAW,QAAS,UAAW,SAAU,SAAU,YAAa,QAAS,MAAO,SAAU,OAAQ,UAAW,UAAW,UAAW,MAAO,UAAW,QAAS,OAAQ,SAAU,QAAS,OAAQ,UAAW,MAAO,SAAU,SAAU,MAAO,QAAS,UAAW,UAAW,MAAO,SAAU,QAAS,QAAS,SAAU,UAAW,OAAQ,QAAS,SAAU,SAAU,UAAW,SAAU,OAAQ,OAAQ,QAAS,QAAS,SAAU,QAAS,WAAY,WAAY,OAAQ,WAAY,OAAQ,QAAS,YAAa,WAAY,SAAU,WAAY,QAAS,OAAQ,SAAU,SAAU,SAAU,SAAU,UAAW,WAAY,QAAS,QAAS,MAAO,QAAS,SAAU,OAAQ,UAAW,YAAa,OAAQ,OAAQ,OAAQ,QAAS,WAAY,QAAS,OAAQ,SAAU,SAAU,SAAU,QAAS,MAAO,SAAU,WAAY,YAAa,QAAS,QAAS,MAAO,UAAW,QAAS,UAAW,UAAW,SAAU,QAAS,UAAW,SAAU,QAAS,QAAS,UAAW,YAAa,OAAQ,WAAY,SAAU,UAAW,WAAY,UAAW,SAAU,OAAQ,OAAQ,OAAQ,SAAU,SAAU,WAAY,OAAQ,SAAU,SAAU,SAAU,OAAQ,OAAQ,SAAU,UAAW,aAAc,OAAQ,UAAW,OAAQ,SAAU,UAAW,QAAS,SAAU,UAAW,OAAQ,SAAU,UAAW,UAAW,SAAU,SAAU,QAAS,OAAQ,SAAU,UAAW,QAAS,QAAS,SAAU,UAAW,SAAU,UAAW,UAAW,WAE71EC,GAAI,CAAC,MAAO,UAAW,aAAc,UAAW,QAAS,SAAU,OAAQ,aAAc,WAAY,SAAU,aAAc,YAAa,UAAW,OAAQ,UAAW,SAAU,UAAW,WAAY,YAAa,SAAU,QAAS,UAAW,QAAS,WAAY,UAAW,WAAY,WAAY,QAAS,UAAW,SAAU,SAAU,QAAS,UAAW,WAAY,WAAY,UAAW,SAAU,UAAW,OAAQ,YAAa,QAAS,WAAY,QAAS,aAAc,WAAY,OAAQ,MAAO,WAAY,WAAY,WAAY,WAAY,QAAS,SAAU,YAAa,YAAa,OAAQ,QAAS,QAAS,SAAU,OAAQ,UAAW,UAAW,WAAY,SAAU,WAAY,WAAY,aAAc,SAAU,YAAa,QAAS,MAAO,SAAU,OAAQ,UAAW,QAAS,OAAQ,WAAY,UAAW,QAAS,MAAO,UAAW,QAAS,QAAS,UAAW,OAAQ,QAAS,OAAQ,QAAS,UAAW,QAAS,QAAS,UAAW,WAAY,QAAS,UAAW,OAAQ,WAAY,aAAc,QAAS,iBAAkB,eAAgB,cAAe,YAAa,eAAgB,SAAU,SAAU,QAAS,UAAW,SAAU,UAAW,UAAW,UAAW,SAAU,UAAW,SAAU,WAAY,QAAS,YAAa,QAAS,OAAQ,QAAS,WAAY,QAAS,UAAW,YAAa,UAAW,SAAU,OAAQ,OAAQ,UAAW,OAAQ,UAAW,UAAW,WAAY,UAAW,SAAU,OAAQ,SAAU,UAAW,SAAU,SAAU,YAAa,QAAS,QAAS,WAAY,UAAW,SAAU,OAAQ,UAAW,QAAS,YAAa,UAAW,QAAS,UAAW,QAAS,OAAQ,WAAY,QAAS,QAAS,WAAY,YAE5pDC,GAAI,CAAC,MAAO,UAAW,OAAQ,SAAU,QAAS,MAAO,QAAS,QAAS,OAAQ,OAAQ,OAAQ,WAAY,YAAa,UAAW,QAAS,SAAU,QAAS,UAAW,SAAU,QAAS,UAAW,WAAY,UAAW,YAAa,UAAW,WAAY,SAAU,QAAS,OAAQ,QAAS,QAAS,OAAQ,QAAS,OAAQ,QAAS,UAAW,SAAU,QAAS,MAAO,QAAS,QAAS,QAAS,UAAW,MAAO,QAAS,QAAS,UAAW,SAAU,QAAS,QAAS,MAAO,OAAQ,SAAU,OAAQ,SAAU,WAAY,UAAW,UAAW,UAAW,WAAY,UAAW,UAAW,OAAQ,QAAS,QAAS,QAAS,QAAS,QAAS,MAAO,OAAQ,QAAS,OAAQ,SAAU,QAAS,SAAU,QAAS,OAAQ,UAAW,QAAS,SAAU,SAAU,QAAS,QAAS,QAAS,UAAW,QAAS,YAAa,UAAW,UAAW,UAAW,QAAS,UAAW,WAAY,SAAU,SAAU,QAAS,UAAW,SAAU,OAAQ,UAAW,SAAU,WAAY,QAAS,UAAW,SAAU,MAAO,SAAU,OAAQ,UAAW,OAAQ,UAAW,SAAU,QAAS,OAAQ,SAAU,SAAU,QAAS,SAAU,QAAS,UAAW,SAAU,OAAQ,QAAS,SAAU,UAAW,UAAW,SAAU,OAAQ,WAAY,QAAS,UAAW,SAAU,MACjwC,CACD,EAEAE,UAAW,CACVJ,GAAI,CAAC,QAAS,UAAW,WAAY,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,SAAU,WAAY,SAAU,UAAW,QAAS,SAAU,SAAU,WAAY,SAAU,WAAY,WAAY,QAAS,YAAa,QAAS,MAAO,SAAU,OAAQ,QAAS,QAAS,YAAa,OAAQ,SAAU,QAAS,OAAQ,QAAS,QAAS,QAAS,QAAS,WAAY,SAAU,SAAU,WAAY,QAAS,UAAW,SAAU,WAAY,WAAY,SAAU,QAAS,UAAW,UAAW,UAAW,UAAW,SAAU,SAAU,OAAQ,OAAQ,SAAU,OAAQ,SAAU,SAAU,SAAU,SAAU,aAAc,MAAO,SAAU,OAAQ,SAAU,WAAY,OAAQ,UAAW,QAAS,SAAU,SAAU,QAAS,UAAW,QAAS,UAAW,OAAQ,SAAU,OAAQ,YAAa,UAAW,UAAW,QAAS,SAAU,OAAQ,YAAa,SAAU,SAAU,aAAc,SAAU,UAAW,SAAU,WAAY,SAAU,YAAa,UAAW,UAAW,OAAQ,QAAS,QAAS,OAAQ,WAAY,SAAU,WAAY,UAAW,QAAS,OAAQ,OAAQ,SAAU,QAAS,WAAY,SAAU,QAAS,WAAY,SAAU,WAAY,OAAQ,WAAY,QAAS,QAAS,SAAU,UAAW,QAAS,OAAQ,UAAW,UAAW,SAAU,SAAU,SAAU,QAAS,WAAY,QAAS,OAAQ,QAAS,UAAW,UAAW,SAAU,QAAS,QAAS,QAAS,QAAS,SAAU,OAAQ,SAAU,OAAQ,YAAa,OAAQ,QAAS,UAAW,SAAU,QAAS,UAAW,QAAS,SAAU,WAAY,OAAQ,QAAS,UAAW,OAAQ,UAAW,SAAU,UAAW,UAAW,WAAY,QAAS,SAAU,QAAS,WAAY,SAAU,SAAU,SAAU,MAAO,UAAW,QAAS,UAAW,SAAU,SAAU,OAAQ,aAAc,UAAW,OAAQ,UAAW,OAAQ,SAAU,MAAO,QAAS,YAAa,YAAa,SAAU,SAAU,WAAY,UAAW,SAAU,OAAQ,SAAU,SAAU,SAAU,WAAY,SAAU,SAAU,YAAa,OAAQ,UAAW,OAAQ,UAAW,WAAY,UAAW,UAAW,SAAU,aAAc,WAAY,aAAc,WAAY,QAAS,QAAS,SAAU,QAAS,SAAU,UAAW,WAAY,SAAU,YAAa,QAAS,SAAU,SAAU,SAAU,UAAW,SAAU,SAAU,SAAU,OAAQ,MAAO,SAAU,QAAS,OAAQ,UAAW,UAAW,SAAU,QAAS,SAAU,UAAW,QAAS,SAAU,MAAO,UAAW,SAAU,SAAU,SAAU,SAAU,SAAU,UAAW,UAAW,QAAS,UAAW,UAAW,UAAW,UAAW,SAAU,SAAU,OAAQ,WAAY,UAAW,MAAO,QAAS,UAAW,OAAQ,OAAQ,UAAW,SAAU,OAAQ,WAAY,OAAQ,WAAY,UAAW,SAAU,UAAW,SAAU,UAAW,UAAW,SAAU,SAAU,SAAU,UAAW,WAAY,QAAS,QAAS,QAAS,YAAa,WAAY,OAAQ,UAAW,WAAY,QAAS,QAAS,OAAQ,SAAU,OAAQ,OAAQ,SAAU,SAAU,WAAY,SAAU,OAAQ,SAAU,QAAS,SAAU,WAAY,SAAU,QAAS,OAAQ,SAAU,QAAS,SAAU,UAAW,SAAU,SAAU,OAAQ,QAAS,OAAQ,SAAU,WAAY,QAAS,UAAW,QAAS,QAAS,SAAU,QAAS,YAAa,UAAW,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,QAAS,UAAW,WAAY,SAAU,UAAW,UAAW,SAAU,SAAU,YAAa,UAAW,SAAU,OAAQ,QAAS,SAAU,OAAQ,OAAQ,OAAQ,WAAY,SAAU,QAAS,SAAU,UAAW,UAAW,OAAQ,SAAU,UAAW,QAAS,SAAU,UAAW,UAAW,SAAU,OAAQ,QAAS,UAAW,SAAU,QAAS,SAAU,aAAc,WAAY,SAAU,UAAW,SAAU,OAAQ,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,UAAW,YAAa,QAAS,SAAU,WAAY,SAAU,OAAQ,SAAU,SAAU,UAAW,SAAU,SAAU,UAAW,UAAW,OAAQ,QAAS,QAAS,QAAS,UAAW,OAAQ,QAAS,UAAW,OAAQ,WAAY,WAAY,UAAW,UAAW,WAAY,QAAS,QAAS,QAAS,aAAc,SAAU,QAAS,UAAW,WAAY,OAAQ,QAAS,OAAQ,WAAY,QAAS,UAAW,QAAS,SAAU,QAAS,UAAW,WAAY,UAAW,UAAW,cAAe,QAAS,QAAS,SAAU,UAAW,aAAc,YAAa,SAAU,WAAY,QAAS,WAAY,MAAO,UAAW,QAAS,YAAa,WAAY,QAAS,QAAS,QAAS,QAAS,OAAQ,OAAQ,QAAS,QAAS,OAAQ,YAAa,OAAQ,SAAU,SAAU,SAAU,UAAW,SAAU,OAAQ,UAAW,SAAU,QAAS,WAAY,SAAU,SAAU,WAAY,SAAU,OAAQ,OAAQ,aAAc,QAAS,QAAS,SAAU,SAAU,SAAU,YAAa,UAAW,OAAQ,QAAS,YAAa,QAAS,WAAY,UAAW,OAAQ,SAAU,UAAW,UAAW,UAAW,YAAa,OAAQ,UAAW,UAE/6JC,GAAI,CAAC,SAAU,WAAY,WAAY,UAAW,QAAS,SAAU,WAAY,UAAW,SAAU,SAAU,UAAW,WAAY,QAAS,YAAa,aAAc,SAAU,WAAY,WAAY,SAAU,WAAY,YAAa,WAAY,QAAS,SAAU,WAAY,UAAW,UAAW,WAAY,QAAS,YAAa,QAAS,WAAY,WAAY,QAAS,UAAW,aAAc,QAAS,UAAW,YAAa,QAAS,UAAW,UAAW,QAAS,SAAU,aAAc,UAAW,OAAQ,WAAY,WAAY,aAAc,WAAY,WAAY,QAAS,WAAY,aAAc,aAAc,QAAS,aAAc,YAAa,SAAU,SAAU,UAAW,YAAa,aAAc,UAAW,YAAa,YAAa,aAAc,UAAW,SAAU,QAAS,WAAY,YAAa,aAAc,QAAS,UAAW,YAAa,SAAU,UAAW,WAAY,UAAW,QAAS,UAAW,WAAY,UAAW,YAAa,UAAW,OAAQ,SAAU,QAAS,UAAW,YAAa,cAAe,eAAgB,WAAY,aAAc,QAAS,UAAW,WAAY,QAAS,UAAW,QAAS,QAAS,UAAW,QAAS,UAAW,WAAY,YAAa,aAAc,UAAW,YAAa,SAAU,UAAW,UAAW,OAAQ,WAAY,QAAS,UAAW,QAAS,OAAQ,SAAU,WAAY,QAAS,SAAU,YAAa,WAAY,UAAW,WAAY,aAAc,QAAS,UAAW,UAAW,OAAQ,SAAU,SAAU,UAAW,SAAU,WAAY,WAAY,QAAS,WAAY,SAAU,SAAU,SAAU,WAAY,SAAU,UAAW,QAAS,WAAY,UAAW,SAAU,SAAU,WAAY,QAAS,WAAY,WAAY,WAAY,QAAS,QAAS,UAAW,UAAW,QAAS,cAAe,WAAY,UAAW,QAAS,UAAW,SAAU,WAAY,UAAW,WAAY,OAAQ,WAAY,SAAU,YAAa,UAAW,cAAe,UAAW,QAAS,WAAY,SAAU,WAAY,QAAS,eAAgB,WAAY,WAAY,WAAY,UAAW,UAAW,WAAY,SAAU,cAAe,WAAY,aAAc,UAAW,WAAY,SAAU,UAAW,UAAW,UAAW,UAAW,WAAY,QAAS,SAAU,UAAW,SAAU,YAAa,QAAS,SAAU,WAAY,SAAU,UAAW,SAAU,aAAc,aAAc,WAAY,cAAe,WAAY,UAAW,WAAY,cAAe,YAAa,YAAa,WAAY,SAAU,aAAc,aAAc,cAAe,aAAc,SAAU,WAAY,UAAW,MAAO,SAAU,QAAS,UAAW,WAAY,OAAQ,QAAS,SAAU,SAAU,SAAU,YAAa,UAAW,YAAa,QAAS,WAAY,OAAQ,QAAS,UAAW,YAAa,cAAe,cAAe,YAAa,SAAU,WAAY,WAAY,YAAa,SAAU,WAAY,QAAS,UAAW,QAAS,WAAY,SAAU,UAAW,cAAe,aAAc,QAAS,WAAY,SAAU,SAAU,SAAU,SAAU,UAAW,UAAW,OAAQ,UAAW,SAAU,QAAS,WAAY,UAAW,QAAS,UAAW,SAAU,SAAU,WAAY,WAAY,SAAU,UAAW,OAAQ,UAAW,UAAW,QAAS,YAAa,WAAY,WAAY,QAAS,QAAS,UAAW,UAAW,UAAW,QAAS,UAAW,QAAS,UAAW,OAAQ,QAAS,aAAc,aAAc,QAAS,SAAU,YAAa,SAAU,QAAS,UAAW,aAAc,eAAgB,UAAW,WAAY,QAAS,WAAY,SAAU,QAAS,UAAW,aAAc,UAAW,WAAY,UAAW,YAAa,YAAa,UAAW,iBAAkB,kBAAmB,MAAO,YAAa,SAAU,WAAY,aAAc,OAAQ,OAAQ,QAAS,QAAS,SAAU,UAAW,OAAQ,UAAW,QAAS,UAAW,SAAU,WAAY,cAAe,SAAU,WAAY,YAAa,WAAY,YAAa,UAAW,WAAY,QAAS,UAAW,SAAU,QAAS,WAAY,UAAW,UAAW,UAAW,UAAW,UAAW,YAAa,WAAY,UAAW,UAAW,UAAW,OAAQ,aAAc,WAAY,QAAS,UAAW,UAAW,SAAU,UAAW,UAAW,OAAQ,MAAO,WAAY,UAAW,UAAW,UAAW,WAAY,QAAS,UAAW,UAAW,UAAW,QAAS,UAAW,UAAW,YAAa,OAAQ,aAAc,QAAS,aAAc,UAAW,QAAS,UAAW,QAAS,UAAW,aAAc,UAAW,UAAW,WAAY,QAAS,QAAS,UAAW,UAAW,QAAS,cAAe,aAAc,eAAgB,UAAW,YAAa,SAAU,WAAY,YAAa,SAAU,aAAc,QAAS,UAAW,SAAU,SAAU,UAAW,UAAW,WAAY,QAAS,OAAQ,aAAc,YAAa,YAAa,UAAW,UAAW,WAAY,WAAY,QAAS,QAAS,UAAW,cAAe,UAAW,WAAY,aAAc,QAAS,YAAa,QAAS,QAAS,UAAW,UAAW,UAAW,OAAQ,SAAU,QAAS,QAAS,YAAa,SAAU,aAAc,WAAY,QAAS,YAAa,YAAa,SAAU,WAAY,WAAY,YAAa,YAAa,QAAS,WAAY,YAAa,SAAU,cAAe,cAAe,cAAe,aAAc,aAAc,WAAY,SAAU,WAAY,SAAU,WAAY,UAAW,OAAQ,QAAS,WAAY,SAAU,SAAU,WAAY,WAAY,QAAS,UAAW,SAAU,WAAY,SAAU,YAAa,YAAa,UAAW,YAAa,SAAU,SAAU,SAAU,WAAY,QAAS,WAAY,KAAM,QAAS,KAAM,QAAS,WAAY,UAAW,YAAa,MAAO,UAAW,MAAO,QAAS,UAAW,SAAU,OAAQ,SAAU,OAAQ,cAAe,YAAa,SAAU,UAAW,YAAa,QAAS,QAAS,WAAY,WAAY,QAAS,QAAS,QAAS,KAAM,OAAQ,MAAO,SAAU,QAAS,OAAQ,OAAQ,WAAY,aAAc,WAAY,QAAS,QAAS,UAAW,YAAa,SAAU,QAAS,KAAM,WAAY,YAAa,SAAU,OAAQ,SAAU,YAAa,SAAU,QAAS,UAAW,YAAa,SAAU,UAAW,WAAY,QAAS,WAAY,UAAW,YAAa,YAAa,QAAS,UAAW,UAAW,WAAY,UAAW,WAAY,QAAS,UAAW,WAAY,WAAY,UAAW,WAAY,WAAY,YAAa,SAAU,YAAa,aAAc,UAAW,WAAY,WAAY,OAAQ,UAAW,UAAW,UAAW,WAAY,YAAa,SAAU,SAAU,WAAY,UAAW,WAAY,aAAc,UAAW,UAAW,QAAS,OAAQ,SAAU,SAAU,SAAU,YAAa,SAAU,WAAY,YAAa,YAAa,WAAY,YAAa,UAAW,QAAS,WAAY,SAAU,WAAY,QAAS,UAAW,YAAa,UAAW,UAAW,WAAY,SAAU,UAAW,QAAS,SAAU,OAAQ,OAAQ,UAAW,aAAc,QAAS,UAAW,UAAW,UAAW,SAAU,YAAa,SAAU,UAAW,YAAa,aAAc,WAAY,aAAc,SAAU,UAAW,SAAU,SAAU,aAAc,WAAY,YAAa,cAAe,QAAS,aAAc,UAAW,UAAW,YAAa,UAAW,UAAW,WAAY,OAAQ,SAAU,SAAU,UAAW,SAAU,WAAY,UAAW,QAAS,UAAW,WAAY,QAAS,UAAW,WAAY,QAAS,UAAW,UAAW,SAAU,SAAU,WAAY,UAAW,WAAY,OAAQ,OAAQ,QAAS,WAAY,UAAW,YAAa,OAAQ,SAAU,YAAa,WAAY,UAAW,QAAS,OAAQ,SAAU,QAAS,WAAY,OAAQ,UAAW,YAAa,UAAW,SAAU,WAAY,YAAa,WAAY,OAAQ,OAAQ,SAAU,SAAU,SAAU,WAAY,UAAW,QAAS,UAAW,WAAY,SAAU,YAAa,UAAW,WAAY,UAAW,YAAa,UAAW,WAAY,aAAc,UAAW,UAAW,WAAY,QAAS,UAAW,OAAQ,SAAU,UAAW,UAAW,SAAU,SAAU,QAAS,UAAW,WAAY,UAAW,YAAa,WAAY,aAAc,OAAQ,SAAU,UAAW,UAAW,QAAS,WAAY,QAAS,UAAW,WAAY,UAAW,UAAW,YAAa,QAAS,SAAU,WAAY,WAAY,WAAY,SAAU,UAAW,WAAY,aAAc,cAAe,WAAY,aAAc,QAAS,UAAW,UAAW,WAAY,OAAQ,QAAS,QAAS,UAAW,WAAY,QAAS,SAAU,YAAa,UAAW,QAAS,WAAY,aAAc,OAAQ,YAAa,WAAY,QAAS,aAAc,UAAW,WAAY,UAAW,QAAS,UAAW,WAAY,OAAQ,WAAY,SAAU,SAAU,YAAa,QAAS,OAAQ,YAAa,YAAa,WAAY,UAAW,YAAa,SAAU,UAAW,QAAS,QAAS,UAAW,QAAS,YAAa,OAAQ,UAAW,UAAW,QAAS,UAAW,UAAW,WAAY,UAAW,QAAS,SAAU,WAAY,QAAS,YAAa,YAAa,SAAU,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,UAAW,SAAU,UAAW,OAAQ,WAAY,QAAS,UAAW,QAAS,UAAW,WAAY,QAAS,WAAY,WAAY,YAAa,SAAU,QAAS,UAAW,YAAa,UAAW,YAAa,cAAe,QAAS,UAAW,SAAU,OAAQ,QAAS,QAAS,UAAW,UAAW,UAAW,WAAY,QAAS,QAAS,QAAS,QAAS,SAAU,QAAS,YAAa,YAAa,YAAa,QAAS,QAAS,WAAY,SAAU,WAAY,SAAU,QAAS,UAAW,UAAW,QAAS,YAAa,YAAa,YAAa,YAAa,SAAU,QAAS,OAAQ,QAAS,UAAW,QAAS,UAAW,aAAc,UAAW,QAAS,WAAY,YAAa,YAAa,aAAc,UAAW,YAAa,SAAU,SAAU,SAAU,UAAW,SAAU,cAAe,YAAa,aAAc,OAAQ,SAAU,YAAa,SAAU,YAAa,WAAY,QAAS,UAAW,WAAY,UAAW,OAAQ,QAAS,QAAS,QAAS,QAAS,aAAc,aAAc,WAAY,SAAU,aAAc,UAAW,SAAU,UAAW,UAAW,OAAQ,OAAQ,QAAS,YAAa,UAAW,QAAS,SAAU,UAAW,QAAS,UAAW,UAAW,YAAa,aAAc,QAAS,UAAW,UAAW,WAAY,YAAa,WAAY,UAAW,UAAW,YAAa,SAAU,UAAW,QAAS,UAAW,QAAS,UAAW,UAAW,YAAa,WAAY,UAAW,QAAS,SAAU,SAAU,SAAU,OAAQ,UAAW,SAAU,QAAS,QAAS,OAAQ,KAAM,KAAM,OAAQ,KAAM,QAAS,OAAQ,UAAW,WAAY,SAAU,QAAS,QAAS,QAAS,OAAQ,MAAO,UAAW,OAAQ,SAErrVC,GAAI,CAAC,SAAU,SAAU,WAAY,QAAS,OAAQ,SAAU,QAAS,UAAW,OAAQ,OAAQ,OAAQ,QAAS,YAAa,OAAQ,OAAQ,MAAO,QAAS,QAAS,SAAU,QAAS,SAAU,UAAW,SAAU,UAAW,SAAU,QAAS,aAAc,QAAS,QAAS,SAAU,UAAW,UAAW,WAAY,WAAY,QAAS,QAAS,QAAS,QAAS,SAAU,YAAa,WAAY,WAAY,QAAS,cAAe,QAAS,OAAQ,OAAQ,YAAa,WAAY,UAAW,WAAY,SAAU,SAAU,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,UAAW,WAAY,QAAS,OAAQ,WAAY,MAAO,OAAQ,UAAW,WAAY,SAAU,SAAU,SAAU,QAAS,WAAY,SAAU,UAAW,OAAQ,SAAU,UAAW,MAAO,UAAW,OAAQ,SAAU,UAAW,SAAU,MAAO,WAAY,SAAU,cAAe,UAAW,SAAU,SAAU,UAAW,OAAQ,OAAQ,SAAU,QAAS,QAAS,YAAa,WAAY,QAAS,OAAQ,UAAW,SAAU,WAAY,SAAU,WAAY,WAAY,UAAW,WAAY,WAAY,QAAS,UAAW,YAAa,SAAU,SAAU,OAAQ,QAAS,OAAQ,WAAY,MAAO,WAAY,aAAc,MAAO,QAAS,SAAU,OAAQ,WAAY,WAAY,UAAW,UAAW,YAAa,UAAW,YAAa,UAAW,OAAQ,SAAU,QAAS,MAAO,WAAY,WAAY,UAAW,YAAa,YAAa,QAAS,UAAW,WAAY,QAAS,UAAW,UAAW,YAAa,WAAY,WAAY,WAAY,UAAW,UAAW,WAAY,UAAW,WAAY,YAAa,WAAY,WAAY,YAAa,WAAY,YAAa,SAAU,WAAY,UAAW,SAAU,WAAY,WAAY,YAAa,UAAW,WAAY,aAAc,YAAa,aAAc,WAAY,SAAU,aAAc,eAAgB,aAAc,YAAa,aAAc,eAAgB,cAAe,WAAY,WAAY,YAAa,YAAa,cAAe,aAAc,eAAgB,WAAY,aAAc,WAAY,YAAa,WAAY,kBAAmB,eAAgB,aAAc,gBAAiB,aAAc,eAAgB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,kBAAmB,gBAAiB,gBAAiB,gBAAiB,eAAgB,iBAAkB,eAAgB,eAAgB,iBAAkB,gBAAiB,gBAAiB,eAAgB,eAAgB,gBAAiB,iBAAkB,gBAAiB,cAClhF,EAGA7I,UAAW,CACV,CAAE/G,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,oBAAqBO,aAAc,IAAK,EAChD,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,iCAAkCO,aAAc,IAAK,EAC7D,CAAEP,KAAM,yBAA0BO,aAAc,IAAK,EACrD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,wBAAyBO,aAAc,IAAK,EACpD,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,2BAA4BO,aAAc,IAAK,EACvD,CAAEP,KAAM,kBAAmBO,aAAc,IAAK,EAC9C,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,0BAA2BO,aAAc,IAAK,EACtD,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,sBAAuBO,aAAc,IAAK,EAClD,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,qBAAsBO,aAAc,IAAK,EACjD,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,oBAAqBO,aAAc,IAAK,EAChD,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,8BAA+BO,aAAc,IAAK,EAC1D,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,sBAAuBO,aAAc,IAAK,EAClD,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,kBAAmBO,aAAc,IAAK,EAC9C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,kBAAmBO,aAAc,IAAK,EAC9C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,2BAA4BO,aAAc,IAAK,EACvD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,0BAA2BO,aAAc,IAAK,EACtD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,wBAAyBO,aAAc,IAAK,EACpD,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,kBAAmBO,aAAc,IAAK,EAC9C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,yCAA0CO,aAAc,IAAK,EACrE,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,oBAAqBO,aAAc,IAAK,EAChD,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,wBAAyBO,aAAc,IAAK,EACpD,CAAEP,KAAM,2BAA4BO,aAAc,IAAK,EACvD,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,oBAAqBO,aAAc,IAAK,EAChD,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,yBAA0BO,aAAc,IAAK,EACrD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,wBAAyBO,aAAc,IAAK,EACpD,CAAEP,KAAM,sBAAuBO,aAAc,IAAK,EAClD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,kBAAmBO,aAAc,IAAK,EAC9C,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,GAGxC0H,SAAU,CAET8H,GAAI,CAAC,CAAE/P,KAAM,8BAA+B,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,uBAAwB,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,sBAAuB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,2BAA4B,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,oBAAqB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,uBAAwB,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,0BAA2B,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,MAAO,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,oBAAqB,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,MAAO,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,MAAO,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,yBAA0B,EAAG,CAAEA,KAAM,oBAAqB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,mBAAoB,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,sBAAuB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,uBAAwB,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,oBAAqB,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,wBAAyB,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,MAAO,EAC/uG,EACAmI,UAAW,CACV6H,GAAI,CACH,CAAEhQ,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,4BAA6BO,aAAc,IAAK,EACxD,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAK3C,CAAEP,KAAM,wBAAyBO,aAAc,IAAK,EACpD,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,GAErCoP,GAAI,CACH,CAAE3P,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,cAAeO,aAAc,KAAM+K,KAAM,CAAE,EACnD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,CAAE,EAC7C,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,CAAE,EAC5C,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,EAAG,EAC7C,CAAEtL,KAAM,wBAAyBO,aAAc,KAAM+K,KAAM,EAAG,EAC9D,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,aAAcO,aAAc,KAAM+K,KAAM,EAAG,EACnD,CAAEtL,KAAM,oBAAqBO,aAAc,KAAM+K,KAAM,EAAG,EAC1D,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,EAAG,EAC7C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,GAAI,EACjD,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,CAAE,EAC7C,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,EAAG,EAC7C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,eAAgBO,aAAc,KAAM+K,KAAM,EAAG,EACrD,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,CAAE,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,EAAG,EAC7C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,kBAAmBO,aAAc,KAAM+K,KAAM,EAAG,EACxD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,CAAE,EAC9C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,eAAgBO,aAAc,KAAM+K,KAAM,EAAG,EACrD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,OAAQO,aAAc,KAAM+K,KAAM,EAAG,EAC7C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,YAAaO,aAAc,KAAM+K,KAAM,EAAG,EAClD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,GAAI,EAC/C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,kBAAmBO,aAAc,KAAM+K,KAAM,EAAG,EACxD,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,EAAG,EACtD,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,OAAQO,aAAc,OAAQ+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,CAAE,EAC9C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,CAAE,EAC9C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,QAASO,aAAc,KAAM+K,KAAM,EAAG,EAC9C,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,EAAG,EACjD,CAAEtL,KAAM,WAAYO,aAAc,KAAM+K,KAAM,CAAE,EAChD,CAAEtL,KAAM,SAAUO,aAAc,KAAM+K,KAAM,EAAG,EAC/C,CAAEtL,KAAM,gBAAiBO,aAAc,KAAM+K,KAAM,GAAI,EACvD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAChD,CAAEtL,KAAM,UAAWO,aAAc,KAAM+K,KAAM,EAAG,EAElD,EAGAnK,cAAe,CAAC,CAAEnB,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,oBAAqB,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,sBAAuB,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,iBAAkB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,mBAAoB,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,gBAAiB,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,kBAAmB,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,eAAgB,EAAG,CAAEA,KAAM,cAAe,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,OAAQ,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,MAAO,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,2BAA4B,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,UAAW,EAAG,CAAEA,KAAM,aAAc,EAAG,CAAEA,KAAM,WAAY,EAAG,CAAEA,KAAM,YAAa,EAAG,CAAEA,KAAM,MAAO,EAAG,CAAEA,KAAM,SAAU,EAAG,CAAEA,KAAM,QAAS,EAAG,CAAEA,KAAM,UAAW,GAE1xIiQ,iBAAkB,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,MAG7lCC,eAAgB,CAAC,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,SAAU,WAAY,cAAe,WAAY,cAAe,SAAU,SAAU,SAAU,SAAU,WAAY,cAAe,WAAY,cAAe,WAAY,cAAe,SAAU,SAAU,SAAU,SAAU,SAAU,QAAS,QAAS,QAAS,QAAS,SAAU,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,UAAW,aAAc,QAAS,QAAS,UAAW,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,UAAW,aAAc,QAAS,QAAS,iBAAkB,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,cAAe,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,aAAc,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,QAAS,UAAW,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,QAAS,UAAW,aAAc,aAAc,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,UAAW,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,aAAc,aAAc,aAAc,UAAW,aAAc,aAAc,aAAc,aAAc,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,UAAW,aAAc,QAAS,QAAS,QAAS,UAAW,aAAc,UAAW,aAAc,UAAW,aAAc,QAAS,SAAU,QAAS,QAAS,UAAW,aAAc,aAAc,aAAc,aAAc,UAAW,aAAc,aAAc,aAAc,SAE1wJ3H,iBAAkB,CACjB,CAAEvI,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,eAAgBO,aAAc,IAAK,EAC3C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,UAAWO,aAAc,IAAK,EACtC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,aAAcO,aAAc,IAAK,EACzC,CAAEP,KAAM,gBAAiBO,aAAc,IAAK,EAC5C,CAAEP,KAAM,YAAaO,aAAc,IAAK,EACxC,CAAEP,KAAM,UAAWO,aAAc,IAAK,GAGvCiI,YAAa,CACZ,CAAExI,KAAM,iBAAkBO,aAAc,IAAK,EAC7C,CAAEP,KAAM,iCAAkCO,aAAc,IAAK,EAC7D,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,mBAAoBO,aAAc,IAAK,EAC/C,CAAEP,KAAM,2BAA4BO,aAAc,IAAK,EACvD,CAAEP,KAAM,cAAeO,aAAc,IAAK,EAC1C,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,GAGpDkI,aAAc,CACb,CAAEzI,KAAM,sBAAuBO,aAAc,IAAK,EAClD,CAAEP,KAAM,uBAAwBO,aAAc,IAAK,EACnD,CAAEP,KAAM,4BAA6BO,aAAc,IAAK,GAGzD4P,gBAAiB,CAChBR,GAAI,CACH,CAAE3P,KAAM,gBAAiBO,aAAc,KAAM,EAC7C,CAAEP,KAAM,WAAYO,aAAc,KAAM,EACxC,CAAEP,KAAM,YAAaO,aAAc,KAAM,EACzC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,sBAAuBO,aAAc,KAAM,EACnD,CAAEP,KAAM,wBAAyBO,aAAc,KAAM,EACrD,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,iBAAkBO,aAAc,KAAM,EAC9C,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,WAAYO,aAAc,KAAM,EACxC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,aAAcO,aAAc,KAAM,EAC1C,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,WAAYO,aAAc,KAAM,EACxC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,WAAYO,aAAc,KAAM,EAE1C,EAEAqI,gBAAiB,CAChBwH,GAAI,CACH,CAAEpQ,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,YAAaO,aAAc,MAAO,EAC1C,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,YAAaO,aAAc,KAAM,EACzC,CAAEP,KAAM,OAAQO,aAAc,KAAM,EACpC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,WAAYO,aAAc,KAAM,EACxC,CAAEP,KAAM,MAAOO,aAAc,KAAM,EACnC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,WAAYO,aAAc,MAAO,EACzC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,MAAOO,aAAc,KAAM,GAEpCoP,GAAI,CACH,CAAE3P,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,SAAUO,aAAc,MAAO,EACvC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,aAAcO,aAAc,MAAO,EAC3C,CAAEP,KAAM,QAASO,aAAc,OAAQ,EACvC,CAAEP,KAAM,SAAUO,aAAc,QAAS,EACzC,CAAEP,KAAM,SAAUO,aAAc,QAAS,EACzC,CAAEP,KAAM,SAAUO,aAAc,QAAS,EACzC,CAAEP,KAAM,WAAYO,aAAc,MAAO,EACzC,CAAEP,KAAM,WAAYO,aAAc,MAAO,EACzC,CAAEP,KAAM,YAAaO,aAAc,OAAQ,EAC3C,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,SAAUO,aAAc,MAAO,EACvC,CAAEP,KAAM,QAASO,aAAc,OAAQ,EACvC,CAAEP,KAAM,YAAaO,aAAc,MAAO,EAC1C,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,SAAUO,aAAc,MAAO,EACvC,CAAEP,KAAM,UAAWO,aAAc,OAAQ,EACzC,CAAEP,KAAM,UAAWO,aAAc,SAAU,EAC3C,CAAEP,KAAM,cAAeO,aAAc,MAAO,EAC5C,CAAEP,KAAM,aAAcO,aAAc,KAAM,EAC1C,CAAEP,KAAM,mBAAoBO,aAAc,KAAM,EAChD,CAAEP,KAAM,aAAcO,aAAc,MAAO,EAC3C,CAAEP,KAAM,WAAYO,aAAc,MAAO,EACzC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,cAAeO,aAAc,MAAO,EAC5C,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,aAAcO,aAAc,MAAO,EAC3C,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,WAAYO,aAAc,KAAM,EACxC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,YAAaO,aAAc,MAAO,EAC1C,CAAEP,KAAM,YAAaO,aAAc,WAAY,EAC/C,CAAEP,KAAM,WAAYO,aAAc,SAAU,EAC5C,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,oBAAqBO,aAAc,aAAc,EACzD,CAAEP,KAAM,cAAeO,aAAc,OAAQ,EAC7C,CAAEP,KAAM,SAAUO,aAAc,OAAQ,EACxC,CAAEP,KAAM,WAAYO,aAAc,MAAO,EACzC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,QAASO,aAAc,OAAQ,EACvC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,MAAOO,aAAc,KAAM,EACnC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,OAAQO,aAAc,MAAO,EACrC,CAAEP,KAAM,QAASO,aAAc,OAAQ,EACvC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,UAAWO,aAAc,OAAQ,EACzC,CAAEP,KAAM,SAAUO,aAAc,MAAO,EACvC,CAAEP,KAAM,YAAaO,aAAc,OAAQ,EAC3C,CAAEP,KAAM,UAAWO,aAAc,OAAQ,EACzC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,eAAgBO,aAAc,OAAQ,EAC9C,CAAEP,KAAM,SAAUO,aAAc,MAAO,EACvC,CAAEP,KAAM,WAAYO,aAAc,QAAS,EAC3C,CAAEP,KAAM,YAAaO,aAAc,SAAU,EAC7C,CAAEP,KAAM,WAAYO,aAAc,OAAQ,EAC1C,CAAEP,KAAM,MAAOO,aAAc,IAAK,EAClC,CAAEP,KAAM,QAASO,aAAc,MAAO,EACtC,CAAEP,KAAM,WAAYO,aAAc,QAAS,EAC3C,CAAEP,KAAM,SAAUO,aAAc,MAAO,GAExCwP,GAAI,CACH,CAAE/P,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,WAAYO,aAAc,IAAK,EACvC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EACtC,CAAEP,KAAM,UAAWO,aAAc,MAAO,EACxC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,QAASO,aAAc,IAAK,EACpC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,QAASO,aAAc,KAAM,EACrC,CAAEP,KAAM,OAAQO,aAAc,IAAK,EACnC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,SAAUO,aAAc,IAAK,EACrC,CAAEP,KAAM,UAAWO,aAAc,KAAM,EACvC,CAAEP,KAAM,SAAUO,aAAc,KAAM,EAExC,EAEAyJ,OAAQ,CACP,CAAEhK,KAAM,UAAW2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAE9D,CAAEtJ,KAAM,WAAY2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC/D,CAAEtJ,KAAM,QAAS2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC5D,CAAEtJ,KAAM,QAAS2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC5D,CAAEtJ,KAAM,MAAO2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC1D,CAAEtJ,KAAM,OAAQ2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC3D,CAAEtJ,KAAM,OAAQ2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC3D,CAAEtJ,KAAM,SAAU2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC7D,CAAEtJ,KAAM,YAAa2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAChE,CAAEtJ,KAAM,UAAW2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC9D,CAAEtJ,KAAM,WAAY2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,EAC/D,CAAEtJ,KAAM,WAAY2K,WAAY,MAAOpB,QAAS,KAAMD,KAAM,EAAG,GAIhEmB,SAAU,CACT,CAAEzK,KAAM,mBAAoB2K,WAAY,OAAQxK,OAAQ,KAAMtK,OAAQ,EAAG,EACzE,CAAEmK,KAAM,WAAY2K,WAAY,WAAYxK,OAAQ,OAAQtK,OAAQ,EAAG,EACvE,CAAEmK,KAAM,iBAAkB2K,WAAY,aAAcxK,OAAQ,KAAMtK,OAAQ,EAAG,EAC7E,CAAEmK,KAAM,4BAA6B2K,WAAY,UAAWxK,OAAQ,MAAOtK,OAAQ,EAAG,EACtF,CAAEmK,KAAM,sBAAuB2K,WAAY,YAAaxK,OAAQ,OAAQtK,OAAQ,EAAG,EACnF,CAAEmK,KAAM,4BAA6B2K,WAAY,SAAUxK,OAAQ,KAAMtK,OAAQ,EAAG,EACpF,CAAEmK,KAAM,qCAAsC2K,WAAY,QAASxK,OAAQ,KAAMtK,OAAQ,EAAG,EAC5F,CAAEmK,KAAM,gBAAiB2K,WAAY,WAAYxK,OAAQ,OAAQtK,OAAQ,EAAG,EAC5E,CAAEmK,KAAM,eAAgB2K,WAAY,WAAYxK,OAAQ,MAAOtK,OAAQ,EAAG,EAC1E,CAAEmK,KAAM,MAAO2K,WAAY,MAAOxK,OAAQ,OAAQtK,OAAQ,EAAG,EAC7D,CAAEmK,KAAM,QAAS2K,WAAY,QAASxK,OAAQ,OAAQtK,OAAQ,EAAG,EACjE,CAAEmK,KAAM,UAAW2K,WAAY,UAAWxK,OAAQ,OAAQtK,OAAQ,EAAG,EACrE,CAAEmK,KAAM,aAAc2K,WAAY,KAAMxK,OAAQ,KAAMtK,OAAQ,EAAG,EACjE,CAAEmK,KAAM,OAAQ2K,WAAY,OAAQxK,OAAQ,OAAQtK,OAAQ,EAAG,EAC/D,CAAEmK,KAAM,SAAU2K,WAAY,SAAUxK,OAAQ,OAAQtK,OAAQ,EAAG,EACnE,CAAEmK,KAAM,OAAQ2K,WAAY,OAAQxK,OAAQ,IAAKtK,OAAQ,EAAG,EAC5D,CAAEmK,KAAM,gBAAiB2K,WAAY,WAAYxK,OAAQ,OAAQtK,OAAQ,EAAG,GAI7E+U,eAAgB,CACf,CAAEU,KAAM,MAAOtL,KAAM,6BAA8B,EACnD,CAAEsL,KAAM,MAAOtL,KAAM,qBAAsB,EAC3C,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,8BAA+B,EACpD,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,sBAAuB,EAC5C,CAAEsL,KAAM,MAAOtL,KAAM,0CAA2C,EAChE,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,0BAA2B,EAChD,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,sBAAuB,EAC5C,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,YAAa,EAClC,CAAEsL,KAAM,MAAOtL,KAAM,qBAAsB,EAC3C,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,WAAY,EACjC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,yBAA0B,EAC/C,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,mCAAoC,EACzD,CAAEsL,KAAM,MAAOtL,KAAM,sBAAuB,EAC5C,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,YAAa,EAClC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,YAAa,EAClC,CAAEsL,KAAM,MAAOtL,KAAM,WAAY,EACjC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,WAAY,EACjC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,UAAW,EAChC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,sBAAuB,EAC5C,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,oCAAqC,EAC1D,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,WAAY,EACjC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,wBAAyB,EAC9C,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,kBAAmB,EACxC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,6BAA8B,EACnD,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,qBAAsB,EAC3C,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,oBAAqB,EAC1C,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,aAAc,EACnC,CAAEsL,KAAM,MAAOtL,KAAM,4BAA6B,EAClD,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,EACvC,CAAEsL,KAAM,MAAOtL,KAAM,sBAAuB,EAC5C,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,gBAAiB,EACtC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,cAAe,EACpC,CAAEsL,KAAM,MAAOtL,KAAM,YAAa,EAClC,CAAEsL,KAAM,MAAOtL,KAAM,uDAAwD,EAC7E,CAAEsL,KAAM,MAAOtL,KAAM,uBAAwB,EAC7C,CAAEsL,KAAM,MAAOtL,KAAM,0DAA2D,EAChF,CAAEsL,KAAM,MAAOtL,KAAM,+CAAgD,EACrE,CAAEsL,KAAM,MAAOtL,KAAM,6CAA8C,EACnE,CAAEsL,KAAM,MAAOtL,KAAM,YAAa,EAClC,CAAEsL,KAAM,MAAOtL,KAAM,mBAAoB,EACzC,CAAEsL,KAAM,MAAOtL,KAAM,eAAgB,EACrC,CAAEsL,KAAM,MAAOtL,KAAM,iBAAkB,GAIxCqQ,WAAY,CAAC,YAAa,QAAS,OAAQ,WAAY,aAAc,OAAQ,YAAa,QAAS,OAAQ,WAAY,cAAe,gBAAiB,oBAAqB,OAAQ,cAAe,OAAQ,OAAQ,eAAgB,aAAc,gBAAiB,cAAe,WAAY,gBAAiB,YAAa,iBAAkB,YAAa,YAAa,YAAa,gBAAiB,kBAAmB,SAAU,iBAAkB,YAAa,iBAAkB,gBAAiB,mBAAoB,UAAW,YAAa,YAAa,YAAa,iBAAkB,kBAAmB,YAAa,aAAc,aAAc,SAAU,SAAU,QAAS,OAAQ,UAAW,eAAgB,aAAc,UAAW,cAAe,cAAe,QAAS,QAAS,eAAgB,aAAc,eAAgB,aAAc,YAAa,aAAc,cAAe,SAAU,QAAS,WAAY,YAAa,cAAe,gBAAiB,iBAAkB,aAAc,YAAa,gBAAiB,eAAgB,YAAa,YAAa,SAAU,kBAAmB,YAAa,OAAQ,YAAa,MAAO,YAAa,UAAW,SAAU,YAAa,gBAAiB,UAAW,YAAa,OAAQ,YAAa,YAAa,WAAY,aAAc,SAAU,gBAAiB,aAAc,QAAS,YAAa,WAAY,QAAS,aAAc,QAAS,QAAS,aAAc,YAAa,aAAc,SAAU,eAAgB,QAAS,uBAAwB,UAAW,MAAO,UAAW,UAAW,WAAY,YAAa,SAAU,UAAW,QAAS,aAAc,cAAe,SAAU,YAAa,OAAQ,OAAQ,YAAa,cAAe,WAAY,SAAU,YAAa,iBAAkB,aAAc,gBAAiB,WAAY,WAAY,eAAgB,cAAe,OAAQ,SAAU,eAGzyD9R,QAAS,CAAC,YAAa,aAAc,oBAAqB,sBAAuB,0BAA2B,8BAA+B,2BAA4B,yBAA0B,gBAAiB,iCAAkC,+BAAgC,sCAAuC,oBAAqB,6BAA8B,uBAAwB,2BAA4B,+BAAgC,mBAAoB,mCAAoC,sBAAuB,aAAc,qCAAsC,qBAAsB,mBAAoB,6BAA8B,aAAc,8CAA+C,mCAAoC,iBAAkB,eAAgB,+BAAgC,yBAA0B,yBAA0B,oBAAqB,aAAc,wBAAyB,yBAA0B,sCAAuC,iBAAkB,eAAgB,6BAA8B,gCAAiC,kCAAmC,2BAA4B,qBAAsB,yBAA0B,mBAAoB,yBAA0B,wCAAyC,2BAA4B,SAAU,qBAAsB,oCAAqC,+CAAgD,kCAAmC,wCAAyC,2BAA4B,iCAAkC,iCAAkC,qCAAsC,mCAAoC,qCAAsC,gCAAiC,+BAAgC,aAAc,yBAA0B,kBAAmB,mBAAoB,eAAgB,iCAAkC,uBAAwB,iCAAkC,6BAA8B,iBAAkB,eAAgB,uBAAwB,kBAAmB,qBAAsB,uBAAwB,sBAAuB,wCAAyC,0BAA2B,eAAgB,sBAAuB,kBAAmB,iCAAkC,4BAA6B,2BAA4B,0BAA2B,qBAAsB,eAAgB,gCAAiC,aAAc,oBAAqB,2BAA4B,uBAAwB,gBAAiB,kCAAmC,mBAAoB,iBAAkB,aAAc,6BAA8B,qBAAsB,cAAe,sBAAuB,4BAA6B,mBAAoB,8BAA+B,qCAAsC,uBAAwB,wBAAyB,oBAAqB,uBAAwB,6BAA8B,4BAA6B,mBAAoB,kCAAmC,yBAA0B,wBAAyB,gCAAiC,yBAA0B,aAAc,0BAA2B,wBAAyB,aAAc,sBAAuB,8BAA+B,0BAA2B,qBAAsB,8BAA+B,4BAA6B,iBAAkB,sBAAuB,4BAA6B,iCAAkC,0BAA2B,qBAAsB,qBAAsB,4BAA6B,sBAAuB,kBAAmB,gCAAiC,uBAAwB,gCAAiC,oBAAqB,8BAA+B,+BAAgC,kBAAmB,2BAA4B,2BAA4B,wBAAyB,qBAAsB,gDAAiD,8BAA+B,2CAA4C,4BAA6B,gCAAiC,2BAA4B,aAAc,+BAAgC,gBAAiB,oBAAqB,8BAA+B,uBAAwB,mBAAoB,qBAAsB,6BAA8B,8BAA+B,mBAAoB,kBAAmB,YAAa,4BAA6B,iBAAkB,eAAgB,oCAAqC,eAAgB,kBAAmB,iBAAkB,sBAAuB,4BAA6B,uBAAwB,wBAAyB,8BAA+B,6BAA8B,sBAAuB,qCAAsC,aAAc,cAAe,aAAc,6BAA8B,gBAAiB,eAAgB,2BAA4B,qBAAsB,iBAAkB,8BAA+B,uBAAwB,oCAAqC,iBAAkB,YAAa,mBAAoB,WAAY,gBAAiB,6BAA8B,wBAAyB,yBAA0B,gBAAiB,gBAAiB,gBAAiB,2BAA4B,wBAAyB,gCAAiC,yBAA0B,yCAA0C,0BAA2B,kBAAmB,2BAA4B,qBAAsB,mBAAoB,gBAAiB,aAAc,eAAgB,iCAAkC,2BAA4B,4BAA6B,kCAAmC,4BAA6B,kBAAmB,uBAAwB,yBAA0B,2BAA4B,mCAAoC,eAAgB,yBAA0B,qCAAsC,4BAA6B,0BAA2B,YAAa,iBAAkB,6BAA8B,iBAAkB,YAAa,eAAgB,YAAa,wBAAyB,iCAAkC,mBAAoB,mBAAoB,sBAAuB,0BAA2B,cAAe,qBAAsB,kBAAmB,qBAAsB,4BAA6B,eAAgB,uBAAwB,qBAAsB,2BAA4B,yBAA0B,mBAAoB,uBAAwB,iBAAkB,qBAAsB,0BAA2B,6BAA8B,2BAA4B,2BAA4B,qBAAsB,0BAA2B,uBAAwB,4BAA6B,WAAY,WAAY,iCAAkC,oBAAqB,iBAAkB,sCAAuC,mBAAoB,wBAAyB,+BAAgC,UAAW,cAAe,sBAAuB,uBAAwB,kBAAmB,2BAA4B,wBAAyB,oBAAqB,sCAAuC,cAAe,uBAAwB,WAAY,sBAAuB,uBAAwB,gCAAiC,wBAAyB,kBAAmB,mBAAoB,uBAAwB,iCAAkC,0BAA2B,0BAA2B,wBAAyB,cAAe,sBAAuB,oCAAqC,sBAAuB,eAAgB,2BAA4B,iCAAkC,sCAAuC,8BAA+B,qBAAsB,qBAAsB,8CAA+C,uBAAwB,yBAA0B,6CAA8C,4BAA6B,2BAA4B,sBAAuB,mCAAoC,wBAAyB,4BAA6B,cAAe,mCAAoC,sBAAuB,iCAAkC,mBAAoB,kCAAmC,iCAAkC,oBAAqB,cAAe,uCAAwC,4BAA6B,6BAA8B,yBAA0B,qBAAsB,gBAAiB,aAAc,WAAY,2BAA4B,kBAAmB,gBAAiB,gBAAiB,2BAA4B,sBAAuB,sBAAuB,iBAAkB,0BAA2B,sCAAuC,oBAAqB,sCAAuC,oBAAqB,WAAY,eAAgB,mBAAoB,sCAAuC,eAAgB,4BAA6B,+BAAgC,2BAA4B,oBAAqB,6BAA8B,+BAAgC,cAAe,+BAAgC,wBAAyB,eAAgB,sBAAuB,2BAA4B,8BAA+B,mBAAoB,iBAAkB,4BAA6B,oCAAqC,2BAA4B,uBAAwB,qCAAsC,oCAAqC,gCAAiC,mCAAoC,2BAA4B,6BAA8B,0BAA2B,yBAA0B,0BAA2B,sBAAuB,iBAAkB,sBAAuB,qBAAsB,kBAAmB,uBAAwB,uCAAwC,8BAA+B,eAAgB,eAAgB,yCAA0C,cAAe,oCAAqC,WAAY,oCAAqC,kBAAmB,mBAAoB,oBAAqB,gBAAiB,qBAAsB,sBAAuB,0BAA2B,iBAAkB,8BAA+B,sBAAuB,gCAAiC,kBAAmB,sBAAuB,+BAAgC,qBAAsB,sBAAuB,gCAAiC,6BAA8B,iBAAkB,eAAgB,qBAAsB,cAAe,6BAA8B,eAAgB,kBAAmB,6BAA8B,2BAA4B,kBAAmB,yBAA0B,kBAAmB,oBAAqB,oBAAqB,2BAA4B,sCAAuC,oBAAqB,0BAA2B,sCAAuC,kCAAmC,wCAAyC,0CAA2C,uCAAwC,cAAe,gBAAiB,sBAAuB,aAAc,oCAAqC,iBAAkB,0BAA2B,qBAAsB,uBAAwB,gCAAiC,qBAAsB,sBAAuB,uCAAwC,oBAAqB,wBAAyB,2BAA4B,UAAW,kBAAmB,mBAAoB,sBAAuB,cAAe,kBAAmB,yBAA0B,UAAW,gBAAiB,6BAA8B,6BAA8B,0BAA2B,yBAA0B,eAAgB,qBAAsB,eAAgB,uBAAwB,aAAc,mCAAoC,6CAA8C,2BAA4B,mCAAoC,kBAAmB,uBAAwB,gBAAiB,mBAAoB,kBAAmB,uBAAwB,gCAAiC,qBAAsB,4BAA6B,8BAA+B,qBAAsB,6BAA8B,eAAgB,+BAAgC,wBAAyB,qBAAsB,qBAAsB,8BAA+B,oBAAqB,gCAAiC,gCAAiC,wBAAyB,wBAAyB,sBAAuB,2BAA4B,2BAA4B,4BAA6B,yBAA0B,gCAAiC,iBAAkB,wBAAyB,kBAAmB,gBAAiB,2BAA4B,2BAA4B,qBAAsB,8BAA+B,kCAAmC,0BAA2B,gCAAiC,iCAAkC,oBAAqB,wBAAyB,cAAe,cAAe,iCAAkC,qBAAsB,cAAe,oCAAqC,qBAAsB,mBAAoB,mCAAoC,yBAA0B,iCAAkC,uBAAwB,yBAA0B,uBAAwB,2BAA4B,2BAA4B,iBAAkB,+BAAgC,2BAA4B,oBAAqB,8BAA+B,2BAA4B,wBAAyB,kBAAmB,eAAgB,uBAAwB,8BAA+B,aAAc,uBAAwB,yBAA0B,wBAAyB,gBAAiB,4BAA6B,qBAAsB,yBAA0B,qBAAsB,sBAAuB,mCAAoC,gBAAiB,iBAAkB,yBAA0B,wBAAyB,uBAAwB,qBAAsB,sBAAuB,0CAA2C,4BAA6B,uBAAwB,2DAA4D,qCAAsC,kCAAmC,qCAAsC,kBAAmB,+BAAgC,mCAAoC,yBAA0B,wBAAyB,6BAA8B,4BAA6B,YAAa,WAAY,eAAgB,mBAAoB,gBAAiB,+BAAgC,aAAc,gCAAiC,6BAA8B,6BAA8B,+BAAgC,2BAA4B,uBAAwB,QAAS,mBAAoB,aAAc,cAAe,UAAW,0BAA2B,4BAA6B,mBAAoB,oBAAqB,gBAAiB,kBAAmB,yBAA0B,sBAAuB,mCAAoC,aAAc,eAAgB,eAAgB,gBAAiB,wBAAyB,YAAa,cAAe,qBAAsB,0BAA2B,qBAAsB,gBAAiB,qBAAsB,0BAA2B,aAAc,gCAAiC,6BAA8B,cAAe,YAAa,aAAc,gCAAiC,wBAAyB,uBAAwB,cAAe,yBAA0B,mBAAoB,gCAAiC,cAAe,uBAAwB,iBAAkB,6BAA8B,6BAA8B,4BAA6B,qBAAsB,eAAgB,6BAA8B,cAAe,kBAAmB,qBAAsB,6BAA8B,2BAA4B,eAAgB,aAAc,+BAAgC,iBAAkB,gBAAiB,qBAAsB,+BAAgC,wBAAyB,4BAA6B,wBAAyB,sBAAuB,8BAA+B,6BAA8B,oCAAqC,oBAAqB,wBAAyB,kCAAmC,oCAAqC,oBAAqB,0BAA2B,yBAA0B,eAAgB,cAAe,gBAAiB,qBAAsB,WAAY,cAAe,2BAA4B,eAAgB,0BAA2B,eAAgB,iCAAkC,uBAAwB,2BAA4B,sBAAuB,0BAA2B,uBAAwB,4BAA6B,4BAA6B,4BAA6B,wBAAyB,uCAAwC,4BAA6B,oBAAqB,kBAAmB,eAAgB,uBAAwB,eAAgB,yBAA0B,eAAgB,0BAA2B,gCAAiC,gCAAiC,gCAAiC,0BAA2B,+BAAgC,mBAAoB,mCAAoC,4BAA6B,0BAA2B,oBAAqB,gCAAiC,sBAAuB,oBAAqB,wBAAyB,aAAc,uBAAwB,gBAAiB,yBAA0B,cAAe,iCAAkC,eAAgB,0BAA2B,uBAAwB,kBAAmB,kBAAmB,WAAY,eAAgB,mBAAoB,mBAAoB,oBAAqB,sBAAuB,cAAe,6BAA8B,cAAe,WAAY,kBAAmB,gBAAiB,yBAA0B,cAAe,uBAAwB,kBAAmB,mBAAoB,kCAAmC,yBAA0B,iBAAkB,gBAAiB,kBAAmB,qBAAsB,gBAAiB,aAAc,8BAA+B,mBAAoB,iBAAkB,2BAA4B,oBAAqB,qBAAsB,6BAA8B,2BAA4B,uBAAwB,uBAAwB,2BAA4B,kBAAmB,0BAA2B,uBAAwB,+BAAgC,cAAe,iBAAkB,cAAe,wBAAyB,sBAAuB,mBAAoB,yBAA0B,mBAAoB,wBAAyB,qBAAsB,yBAA0B,qBAAsB,gBAAiB,uBAAwB,eAAgB,WAAY,uBAAwB,gBAAiB,qBAAsB,+BAAgC,yBAA0B,gBAAiB,cAAe,iBAAkB,0CAA2C,qBAAsB,6BAA8B,gBAAiB,iBAAkB,mCAAoC,oCAAqC,yBAA0B,iCAAkC,eAAgB,4BAA6B,wBAAyB,4BAA6B,cAAe,qBAAsB,gBAAiB,iCAAkC,0BAA2B,0BAA2B,aAAc,gBAAiB,eAAgB,wBAAyB,kBAAmB,4BAA6B,gBAAiB,wBAAyB,+BAAgC,eAAgB,oBAAqB,+BAAgC,0BAA2B,eAAgB,aAAc,yBAA0B,wBAAyB,iCAAkC,cAAe,8BAA+B,6BAA8B,eAAgB,iBAAkB,oBAAqB,oBAAqB,oBAAqB,wBAAyB,eAAgB,wBAAyB,kBAAmB,kCAAmC,qBAAsB,iBAAkB,gCAAiC,sBAAuB,kBAAmB,yBAA0B,yBAA0B,qCAAsC,sBAAuB,UAAW,WAAY,kBAAmB,eAAgB,uBAAwB,kBAAmB,kBAAmB,8BAA+B,4BAA6B,sBAAuB,wBAAyB,qBAAsB,wBAAyB,iCAAkC,4BAA6B,qBAAsB,wBAAyB,kCAAmC,kCAAmC,cAAe,wBAAyB,gCAAiC,gCAAiC,qBAAsB,YAAa,4BAA6B,kBAAmB,uBAAwB,kBAAmB,kBAAmB,2BAA4B,kBAAmB,UAAW,4BAA6B,sBAAuB,mCAAoC,0BAA2B,sBAAuB,+BAAgC,6BAA8B,iBAAkB,aAAc,YAAa,uBAAwB,6BAA8B,sBAAuB,gCAAiC,2BAA4B,2BAA4B,kBAAmB,oBAAqB,uBAAwB,uBAAwB,cAAe,gCAAiC,sBAAuB,iBAAkB,wBAAyB,wBAAyB,sBAAuB,uBAAwB,aAAc,gCAAiC,oBAAqB,gCAAiC,wBAAyB,4BAA6B,yBAA0B,0BAA2B,sBAAuB,4BAA6B,wBAAyB,sBAAuB,mBAAoB,wBAAyB,yBAA0B,6BAA8B,yBAA0B,sBAAuB,wBAAyB,+BAAgC,wBAAyB,kCAAmC,eAAgB,6BAA8B,4BAA6B,QAAS,4BAA6B,kBAAmB,aAAc,aAAc,wBAAyB,qBAAsB,0BAA2B,kBAAmB,mBAAoB,wBAE7stBsD,cAAe,CACdyO,OAAQ,CAAC,MAAO,MAAO,MAAO,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,QACzEC,OAAQ,CAAC,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,OACzGC,KAAM,CAAC,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,QAAS,QAAS,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,IAAK,OAChIC,SAAU,CAAC,MAAO,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAAS,MACpG,EAGA3F,UAAW,CACV,CACC9K,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,CAAC,GACTC,MAAO,EA/5BVpb,EAAOD,UAAUsb,KAAO,SAAUja,GACjC,IAsBMka,EAtBFC,EAAcna,GAAW,GACzBoa,EAAoB,gBACpBC,EAAYjb,OAAO8a,KAAKpb,KAAKqC,IAAI,eAAe,CAAC,EAKrDmZ,EAAWxb,KAAKmE,KAAK,CAAEhE,OAAQkb,EAAYlb,MAAO,CAAC,EAGnD,GAAIkb,EAAYjL,UAEf,OAAOoL,EAAW,IADFH,EAAYjL,UAK7B,GAAIiL,EAAY9K,WAAY,CAC3B,GAAI3Q,MAAM6b,QAAQJ,EAAY9K,UAAU,EAEvC,OAAOiL,EAAW,IADFxb,KAAK6F,QAAQwV,EAAY9K,UAAU,EAE7C,GAAI8K,EAAY9K,WAAWhE,cAAgBjM,OAKjD,OAJIob,EAA4BL,EAAY9K,WACxC6K,EAAO9a,OAAO8a,KAAKM,CAAyB,EAGzCF,EAAW,IADFxb,KAAK6F,QAAQ6V,EAA0B1b,KAAK6F,QAAQuV,CAAI,EAAE,EAI3E,MAAM,IAAI1Z,MAAM,+CAA+C,CAChE,CAGA,GAAI2Z,EAAYM,SAAU,CACzB,IAAIA,EAAWN,EAAYM,SAC3B,GAAoC,CAAC,IAAjCJ,EAAUzW,QAAQ6W,CAAQ,EAE7B,OAAOH,EAAW,IADFxb,KAAK6F,QAAQ7F,KAAKqC,IAAIiZ,CAAiB,EAAEK,EAAS,EAInE,MAAM,IAAIpa,WAAW,6EAA6E,CACnG,CAIA,OAAOia,EAAW,IADFxb,KAAK6F,QAAQ7F,KAAKqC,IAAIiZ,CAAiB,EAAEtb,KAAK6F,QAAQ0V,CAAS,EAAE,CAElF,GAk3BGpU,KAAM,2CACNyU,IAAK,CAAC,aACP,EACA,CACCtR,KAAM,SACN0Q,KAAM,IACNC,OAAQ,CAAC,GACTC,MAAO,CAAA,EACP/T,KAAM,4CACNyU,IAAK,CAAC,aAAc,iBAAkB,eAAgB,oBACvD,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,CAAC,GACTC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,aAAc,mBAAoB,mBAAoB,oBAAqB,iBAClF,EACA,CACCtR,KAAM,wBACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,oBAAqB,iBAAkB,eAAgB,gBAAiB,kBAC/E,EACA,CACCtR,KAAM,iCACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,8BACNyU,IAAK,CAAC,uBACP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,yCACNyU,IAAK,CAAC,iBAAkB,sBAAuB,kBAAmB,oBAAqB,qBAAsB,UAC9G,EACA,CACCtR,KAAM,4BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,kBAAmB,uBAAwB,qBAAsB,kBAAmB,YAC3F,EACA,CACCtR,KAAM,kCACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,0CACNyU,IAAK,CAAC,oBAAqB,mBAC5B,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,0CACNyU,IAAK,CAAC,gBAAiB,wBAAyB,iBAAkB,mBAAoB,iBAAkB,kBAAmB,sBAAuB,UACnJ,EACA,CACCtR,KAAM,gCACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,8BACNyU,IAAK,CAAC,iBAAkB,qBAAsB,sBAAuB,oBAAqB,kBAAmB,sBAAuB,YAAa,oBAClJ,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,yCACNyU,IAAK,CAAC,kBAAmB,uBAAwB,4BAA6B,oBAAqB,oBAAqB,8BAA+B,8BAA+B,iCAAkC,sBAAuB,uBAAwB,mBAAoB,mBAAoB,UAChT,EACA,CACCtR,KAAM,iCACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,kDACNyU,IAAK,CAAC,yBAA0B,iBAAkB,iBAAkB,sBAAuB,oBAC5F,EACA,CACCtR,KAAM,+BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,2BACNyU,IAAK,CAAC,iBAAkB,wBACzB,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,kCACNyU,IAAK,CAAC,iBAAkB,iBAAkB,wBAAyB,mBAAoB,oBAAqB,kBAAmB,eAAgB,iBAAkB,qBAAsB,YACxL,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,yCACNyU,IAAK,CAAC,kBAAmB,iBAAkB,6BAA8B,4BAA6B,0BAA2B,kBAAmB,8BAA+B,qBAAsB,mBAAoB,iBAAkB,mBAAoB,kBAAmB,sBAAuB,yBAA0B,sBAAuB,kBAAmB,UAClX,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,6BACNyU,IAAK,CAAC,0BAA2B,wBAAyB,uBAC3D,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,CAAC,IACTC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,kBACP,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,mBACP,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,qCACNyU,IAAK,CAAC,oBAAqB,oBAAqB,kBAAmB,kBAAmB,gBAAiB,mBACxG,EACA,CACCtR,KAAM,kCACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,uBAAwB,iBAC/B,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,mDACNyU,IAAK,CAAC,mBAAoB,kBAAmB,gBAAiB,mBAAoB,uBAAwB,oBAAqB,kBAAmB,mBAAoB,qBAAsB,kBAAmB,qBAAsB,iBAAkB,qBAAsB,iBAAkB,wBAAyB,iBAAkB,kBAAmB,qBAAsB,qBAAsB,wBAAyB,sBAAuB,sBAAuB,wBAAyB,wBAAyB,mBAAoB,mBAAoB,oBAAqB,qBAAsB,kBAAmB,YACzmB,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,mBAAoB,oBAC3B,EACA,CACCtR,KAAM,6BACN0Q,KAAM,MACNC,OAAQ,CAAC,IACTC,MAAO,CAAA,EACP/T,KAAM,2BACNyU,IAAK,CAAC,mBACP,EACA,CACCtR,KAAM,iCACN0Q,KAAM,QACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,oBACP,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,2BACNyU,IAAK,CAAC,6BAA8B,iCAAkC,0BAA2B,6BAA8B,6BAA8B,4BAA6B,4BAA6B,uBAAwB,oBAAqB,kBAAmB,gBAAiB,kBACzS,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,iCACNyU,IAAK,CAAC,oBAAqB,gBAAiB,kBAAmB,oBAAqB,iBAAkB,qBAAsB,iBAAkB,mBAAoB,qBAAsB,mBAAoB,YAC7M,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,wBACNyU,IAAK,CAAC,kBACP,EACA,CACCtR,KAAM,2BACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,yBACNyU,IAAK,CAAC,qBACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,gBACP,EACA,CACCtR,KAAM,SACN0Q,KAAM,IACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,4CACNyU,IAAK,CAAC,kBAAmB,yBAA0B,YACpD,EACA,CACCtR,KAAM,6BACN0Q,KAAM,MACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,gCACP,EACA,CACCmD,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,uBAAwB,kBAC/B,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,CAAC,EACTC,MAAO,CAAA,EACP/T,KAAM,6BACNyU,IAAK,CAAC,sBAAuB,YAC9B,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,mBACNyU,IAAK,CAAC,oBAAqB,kBAC5B,EACA,CACCtR,KAAM,MACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,mCACNyU,IAAK,CAAC,uBAAwB,UAC/B,EACA,CACCtR,KAAM,oBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,0CACNyU,IAAK,CAAC,kBAAmB,kBAAmB,mBAAoB,gBAAiB,kBAAmB,qBAAsB,gBAAiB,gBAAiB,gBAC7J,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,4BACNyU,IAAK,CAAC,iBAAkB,eAAgB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,eAAgB,kBAAmB,cAAe,kBAAmB,oBAAqB,qBAAsB,kBAAmB,qBAAsB,qBACvQ,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,+DACNyU,IAAK,CAAC,sBAAuB,mBAAoB,iBAAkB,gBAAiB,kBAAmB,mBAAoB,oBAAqB,eAAgB,gBAAiB,cAAe,cAAe,oBAAqB,mBAAoB,eAAgB,iBAAkB,gBAAiB,gBAC5S,EACA,CACCtR,KAAM,+BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,gEACNyU,IAAK,CAAC,kBAAmB,oBAAqB,kBAAmB,mBAAoB,mBAAoB,gBAAiB,gBAC3H,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,kDACNyU,IAAK,CAAC,eAAgB,kBAAmB,oBAAqB,gBAAiB,eAChF,EACA,CACCtR,KAAM,iCACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,+CACNyU,IAAK,CAAC,kBAAmB,gBAAiB,gBAAiB,gBAC5D,EACA,CACCtR,KAAM,kCACN0Q,KAAM,QACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,kCACNyU,IAAK,CAAC,iBAAkB,gBAAiB,qBAAsB,gBAAiB,kBAAmB,eAAgB,oBAAqB,gBAAiB,gBAAiB,kBAAmB,gBAAiB,oBAAqB,eAAgB,YACpP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,kBACP,EACA,CACCtR,KAAM,oBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,gCACNyU,IAAK,CAAC,eAAgB,gBAAiB,mBAAoB,kBAC5D,EACA,CACCtR,KAAM,4BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,cACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,gBACP,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,uBACP,EACA,CACCmD,KAAM,6BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,+BACNyU,IAAK,CAAC,kBAAmB,mBAAoB,kBAAmB,gBAAiB,sBAAuB,gBAAiB,oBAAqB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,YACpN,EACA,CACCtR,KAAM,oBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,4DACNyU,IAAK,CAAC,kBAAmB,cAAe,mBAAoB,cAAe,eAAgB,iBAAkB,kBAAmB,iBAAkB,oBACnJ,EACA,CACCtR,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,kBACP,EACA,CACCtR,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,wBACNyU,IAAK,CAAC,iBACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,iBACP,EACA,CACCtR,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,aACP,EACA,CACCtR,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,4BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,iCACNyU,IAAK,CAAC,qBAAsB,eAC7B,EACA,CACCtR,KAAM,qBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,6BACNyU,IAAK,CAAC,YAAa,eAAgB,cAAe,aAAc,cACjE,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,qBAAsB,gBAAiB,uBAAwB,kBAAmB,cAAe,iBAAkB,kBAAmB,mBAAoB,iBAAkB,mBAAoB,YAAa,sBAAuB,gBAAiB,iBAC5P,EACA,CACCtR,KAAM,qBACN0Q,KAAM,MACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,cACP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,gCACNyU,IAAK,CAAC,aAAc,cAAe,YACpC,EACA,CACCtR,KAAM,2BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,mBACNyU,IAAK,CAAC,YACP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,gDACNyU,IAAK,CAAC,gBAAiB,gBAAiB,oBAAqB,mBAC9D,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,yBACNyU,IAAK,CAAC,cAAe,mBAAoB,iBAC1C,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,4BACN0Q,KAAM,MACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,aACP,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,iCACNyU,IAAK,CAAC,oBAAqB,aAAc,cAAe,gBAAiB,gBAAiB,YAAa,iBAAkB,gBAAiB,YAAa,mBAAoB,kBAC5K,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,iCACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,kDACNyU,IAAK,CAAC,gBACP,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,kCACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,KACRC,MAAO,CAAA,EACP/T,KAAM,wBACNyU,IAAK,CAAC,gBACP,EACA,CACCtR,KAAM,6BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,oBAAqB,cAAe,eAAgB,iBAAkB,cAAe,YAAa,gBACzG,EACA,CACCtR,KAAM,2BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,aAAc,eACrB,EACA,CACCtR,KAAM,6BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,2BACNyU,IAAK,CAAC,qBACP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,+BACNyU,IAAK,CAAC,eAAgB,eACvB,EACA,CACCtR,KAAM,wBACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sCACNyU,IAAK,CAAC,mBAAoB,eAAgB,YAAa,eAAgB,kBAAmB,iBAAkB,cAAe,iBAAkB,YAAa,mBAC3J,EACA,CACCtR,KAAM,gCACN0Q,KAAM,QACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,0BACNyU,IAAK,CAAC,oBAAqB,mBAAoB,YAChD,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oDACNyU,IAAK,CAAC,iBAAkB,aAAc,gBACvC,EACA,CACCtR,KAAM,2BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,0BACNyU,IAAK,CAAC,mBACP,EACA,CACCtR,KAAM,0BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sCACNyU,IAAK,CAAC,cAAe,oBAAqB,eAAgB,gBAAiB,cAAe,iBAAkB,YAC7G,EACA,CACCtR,KAAM,6BACN0Q,KAAM,OACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,mBAAoB,kBAC3B,EACA,CACCtR,KAAM,uBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,cACP,EACA,CACCtR,KAAM,4BACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,0BACNyU,IAAK,CAAC,kBAAmB,mBAC1B,EACA,CACCtR,KAAM,gCACN0Q,KAAM,QACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oCACNyU,IAAK,CAAC,YAAa,gBAAiB,aAAc,YAAa,gBAChE,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,EACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,iBAAkB,aACzB,EACA,CACCtR,KAAM,+BACN0Q,KAAM,OACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,qBAAsB,wBAC7B,EACA,CACCtR,KAAM,4BACN0Q,KAAM,OACNC,OAAQ,IACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,mBACP,EACA,CACCtR,KAAM,6BACN0Q,KAAM,OACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,uBACNyU,IAAK,CAAC,qBAAsB,qBAC7B,EACA,CACCtR,KAAM,4BACN0Q,KAAM,OACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,0CACNyU,IAAK,CAAC,sBAAuB,mBAC9B,EACA,CACCtR,KAAM,6BACN0Q,KAAM,OACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,iCACNyU,IAAK,CAAC,4BAA6B,aAAc,eAAgB,uBAAwB,iBAAkB,eAC5G,EACA,CACCtR,KAAM,yBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,qBACNyU,IAAK,CAAC,mBAAoB,mBAC3B,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,aAAc,gBAAiB,eACtC,EACA,CACCtR,KAAM,gCACN0Q,KAAM,OACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,yCACNyU,IAAK,CAAC,uBAAwB,aAAc,gBAAiB,sBAAuB,iBAAkB,iBAAkB,iBACzH,EACA,CACCtR,KAAM,4BACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,0BACNyU,IAAK,CAAC,gBAAiB,gBAAiB,mBACzC,EACA,CACCtR,KAAM,4BACN0Q,KAAM,OACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,mCACNyU,IAAK,CAAC,qBAAsB,mBAC7B,EACA,CACCtR,KAAM,SACN0Q,KAAM,IACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,4CACNyU,IAAK,CAAC,aAAc,mBAAoB,oBAAqB,iBAAkB,gBAAiB,iBAAkB,eAAgB,iBACnI,EACA,CACCtR,KAAM,qBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,mBACNyU,IAAK,CAAC,eACP,EACA,CACCtR,KAAM,wBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,sBACNyU,IAAK,CAAC,cAAe,iBAAkB,eAAgB,qBACxD,EACA,CACCtR,KAAM,0BACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,4CACP,EACA,CACCmD,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,yBACNyU,IAAK,CAAC,aAAc,oBAAqB,kBAAmB,oBAC7D,EACA,CACCtR,KAAM,sBACN0Q,KAAM,MACNC,OAAQ,GACRC,MAAO,CAAA,EACP/T,KAAM,oBACNyU,IAAK,CAAC,eACP,GAGDjT,WAAY,CAAC,gBAAiB,gBAAiB,aAAc,oBAAqB,QAAS,UAAW,sBAAuB,uBAAwB,yBAA0B,gBAAiB,uBAAwB,qBAAsB,QAAS,yBAA0B,yBAA0B,yBAA0B,iBAAkB,iBAAkB,YAAa,YAAa,eAAgB,SAAU,aAAc,iBAAkB,UAAW,SAAU,QAAS,SAAU,sBAAuB,mBAAoB,YAAa,0BAA2B,sBAAuB,0BAA2B,cAAe,SAAU,mBAAoB,sBAAuB,qBAAsB,mBAAoB,mBAAoB,mBAAoB,QAAS,sBAAuB,mBAAoB,eAAgB,mBAAoB,0BAA2B,0BAA2B,OAAQ,oBAAqB,UAAW,qBAAsB,wBAAyB,eAAgB,kBAAmB,uBAAwB,eAAgB,iBAAkB,iBAAkB,8BAA+B,uBAAwB,qBAAsB,cAAe,mBAAoB,oBAAqB,kCAAmC,0BAA2B,0BAA2B,uBAAwB,sBAAuB,wBAAyB,oBAAqB,oBAAqB,+BAAgC,wBAAyB,uBAAwB,aAAc,6BAA8B,yBAA0B,qBAAsB,aAAc,oBAAqB,sBAAuB,gBAAiB,iBAAkB,kBAAmB,4BAA6B,2BAA4B,2BAA4B,eAAgB,SAAU,wBAAyB,mBAAoB,sBAAuB,UAAW,WAAY,kBAAmB,oBAAqB,YAAa,sBAAuB,mBAAoB,YAAa,kBAAmB,WAAY,uBAAwB,oBAAqB,YAAa,yBAA0B,SAAU,sBAAuB,sBAAuB,2BAA4B,uBAAwB,qBAAsB,mBAAoB,sBAAuB,eAAgB,wBAAyB,yBAA0B,yBAA0B,2BAA4B,iBAAkB,YAAa,sBAAuB,qBAAsB,sBAAuB,qBAAsB,mBAAoB,yBAA0B,uBAAwB,oBAAqB,gBAAiB,4BAA6B,oBAAqB,oBAAqB,YAAa,cAAe,sBAAuB,qBAAsB,mBAAoB,kBAAmB,kBAAmB,2BAA4B,uBAAwB,sBAAuB,uBAAwB,kBAAmB,qBAAsB,mBAAoB,cAAe,kBAAmB,YAAa,kBAAmB,uBAAwB,gBAAiB,uBAAwB,oBAAqB,eAAgB,mBAAoB,iBAAkB,0BAA2B,sBAAuB,mBAAoB,+BAAgC,kBAAmB,iBAAkB,iBAAkB,yBAA0B,gBAAiB,0BAA2B,WAAY,sBAAuB,sBAAuB,uBAAwB,eAAgB,qBAAsB,qBAAsB,6BAA8B,sBAAuB,wBAAyB,uBAAwB,cAAe,eAAgB,oBAAqB,qBAAsB,eAAgB,qBAAsB,qBAAsB,UAAW,wBAAyB,aAAc,mBAAoB,kBAAmB,0BAA2B,wBAAyB,iBAAkB,iBAAkB,sBAAuB,0BAA2B,SAAU,yBAA0B,8BAA+B,kBAAmB,kBAAmB,kBAAmB,sBAAuB,eAAgB,WAAY,oBAAqB,sBAAuB,wBAAyB,wBAAyB,mBAAoB,yBAA0B,mBAAoB,mBAAoB,sBAAuB,qBAAsB,oBAAqB,gBAAiB,sBAAuB,WAAY,sBAAuB,cAAe,mBAAoB,oBAAqB,qBAAsB,0BAA2B,eAAgB,4BAA6B,gBAAiB,iBAAkB,cAAe,0BAA2B,sBAAuB,WAAY,wBAAyB,qBAAsB,mBAAoB,sBAAuB,WAAY,mBAAoB,qBAAsB,eAAgB,wBAAyB,yBAA0B,gBAAiB,iBAAkB,qBAAsB,+BAAgC,qBAAsB,cAAe,qCAAsC,0BAA2B,YAAa,cAAe,kBAAmB,qBAAsB,uBAAwB,qBAAsB,aAAc,eAAgB,qBAAsB,YAAa,sBAAuB,YAAa,oBAAqB,aAAc,oBAAqB,sBAAuB,aAAc,oBAAqB,uBAAwB,oBAAqB,YAAa,iBAAkB,uBAAwB,oBAAqB,mBAAoB,WAAY,kBAAmB,mBAAoB,sBAAuB,qBAAsB,uBAAwB,qBAAsB,YAAa,mBAAoB,kBAAmB,kBAAmB,mBAAoB,uBAAwB,yBAA0B,gBAAiB,YAAa,mBAAoB,sBAAuB,+BAAgC,2BAA4B,4BAA6B,oBAAqB,oBAAqB,qBAAsB,wBAAyB,YAAa,2BAA4B,6BAA8B,mBAAoB,2BAA4B,qBAAsB,WAAY,sBAAuB,qBAAsB,uBAAwB,eAAgB,kBAAmB,iBAAkB,gBAAiB,0BAA2B,uBAAwB,gBAAiB,kBAAmB,sBAAuB,wBAAyB,oBAAqB,qBAAsB,sBAAuB,oBAAqB,oBAAqB,iBAAkB,SAAU,kBAAmB,yBAA0B,kBAAmB,mBAAoB,gBAAiB,cAAe,qBAAsB,oBAAqB,yBAA0B,iBAAkB,yBAA0B,4BAA6B,4BAA6B,qBAAsB,gBAAiB,uBAAwB,eAAgB,gBAAiB,8BAA+B,8BAA+B,8BAA+B,cAAe,WAAY,sBAAuB,iBAAkB,wBAAyB,kBAAmB,kBAAmB,qBAAsB,uBAAwB,iBAAkB,UAAW,+BAAgC,wBAAyB,mBAAoB,sBAAuB,kBAAmB,eAAgB,sBAAuB,iBAAkB,cAAe,sBAAuB,kBAAmB,qBAAsB,yBAA0B,eAAgB,YAAa,gBAAiB,cAAe,uBAAwB,2BAA4B,kBAAmB,gBAAiB,eAAgB,4BAA6B,gBAAiB,qBAC5yPkT,QAAS,CAERC,MAAO,CAAC,aAAc,UAAW,iBAAkB,WAAY,WAAY,kBAAmB,UAAW,gBAAiB,cAAe,wBAAyB,eAAgB,2BAA4B,uBAAwB,oBAAqB,eAAgB,uBAAwB,sBAAuB,mBAAoB,WAAY,iBAAkB,YAAa,gBAAiB,OAAQ,eAAgB,kBAAmB,kBAAmB,WAAY,4BAA6B,cAAe,aAAc,qBAAsB,YAAa,aAAc,uBAAwB,aAAc,qBAAsB,sBAAuB,wBAAyB,iBAAkB,sBAAuB,OAAQ,oBAAqB,oBAAqB,MAAO,aAAc,sBAAuB,mBAAoB,QAAS,qBAAsB,UAAW,QAAS,YAAa,cAAe,OAAQ,2BAA4B,eAAgB,aAAc,wBAAyB,UAAW,cAAe,OAAQ,aAAc,SAAU,gBAAiB,iBAAkB,wBAAyB,sBAAuB,MAAO,gBAAiB,gBAAiB,iBAAkB,sBAAuB,kBAAmB,eAAgB,YAAa,YAAa,wBAAyB,kBAAmB,kBAAmB,WAAY,cAAe,WAAY,QAAS,mBAAoB,gBAAiB,gCAAiC,MAAO,UAAW,sBAAuB,8BAA+B,aAAc,eAAgB,iBAAkB,gBAAiB,wBAAyB,iBAAkB,iBAAkB,cAAe,yBAA0B,kBAAmB,eAAgB,YAAa,oBAAqB,kBAAmB,qBAAsB,UAAW,UAAW,oBAAqB,UAAW,OAAQ,UAAW,mBAAoB,SAAU,kBAAmB,cAAe,cAAe,qBAAsB,mBAAoB,mBAAoB,cAAe,UAAW,OAAQ,aAAc,iBAAkB,sBAAuB,iBAAkB,UAAW,sBAAuB,sBAAuB,SAAU,mBAAoB,uBAAwB,YAAa,YAAa,0BAA2B,eAAgB,eAAgB,QAAS,qBAAsB,UAAW,kBAAmB,eAAgB,SAAU,OAAQ,WAAY,uBAAwB,aAAc,UAAW,oBAAqB,oBAAqB,qBAAsB,iBAAkB,oBAAqB,UAAW,YAAa,SAAU,kBAAmB,mBAAoB,gBAAiB,aAAc,UAAW,WAAY,YAAa,SAAU,SAAU,YAAa,kBAAmB,iBAAkB,UAAW,WAAY,aAAc,oBAAqB,6BAA8B,uBAAwB,iBAAkB,aAAc,cAAe,UAAW,gBAAiB,yBAA0B,UAAW,kBAAmB,gBAAiB,WAAY,QAAS,SAAU,qBAAsB,sBAAuB,kBAAmB,kBAAmB,+BAAgC,8BAA+B,8BAA+B,uBAAwB,wBAAyB,yBAA0B,UAAW,qBAAsB,WAAY,cAAe,WAAY,WAAY,WAAY,aAAc,wBAAyB,wBAAyB,SAAU,kBAAmB,cAAe,mBAAoB,qBAAsB,UAAW,UAAW,eAAgB,MAAO,WAAY,gBAAiB,cAAe,kBAAmB,aAAc,YAAa,SAAU,cAAe,gBAAiB,UAAW,uBAAwB,6BAA8B,WAAY,eAAgB,WAAY,aAAc,OAAQ,QAAS,sBAAuB,wBAAyB,SAAU,kBAAmB,gBAAiB,oBAAqB,qBAAsB,SAAU,iBAAkB,oBAAqB,SAAU,wBAAyB,kBAAmB,oBAAqB,gBAAiB,QAAS,YAAa,WAAY,cAAe,YAAa,qBAAsB,WAAY,YAAa,mBAAoB,qBAAsB,wBAAyB,uBAAwB,cAAe,cAAe,WAAY,WAAY,4BAA6B,OAAQ,iBAAkB,cAAe,4BAA6B,oBAAqB,YAAa,gBAAiB,UAAW,QAAS,SAAU,sBAAuB,QAAS,cAAe,kBAAmB,uBAAwB,wBAAyB,eAAgB,YAAa,SAAU,YAAa,YAAa,wBAAyB,qBAAsB,yBAA0B,qBAAsB,iBAAkB,aAAc,eAEj0JC,OAAQ,CAAC,WAAY,QAAS,mBAAoB,MAAO,WAAY,YAAa,SAAU,SAAU,MAAO,iBAAkB,SAAU,OAAQ,mBAAoB,MAAO,aAAc,UAAW,YAAa,QAAS,UAAW,WAAY,cAAe,YAAa,UAAW,WAAY,aAAc,iBAAkB,QAAS,QAAS,aAAc,SAAU,UAAW,YAAa,OAAQ,aAAc,OAAQ,UAAW,QAAS,UAAW,WAAY,MAAO,SAAU,MAAO,MAAO,YAAa,QAAS,iBAAkB,SAAU,cAAe,UAAW,QAAS,UAAW,OAAQ,WAAY,QAAS,QAAS,SAAU,WAAY,eAAgB,UAAW,SAAU,UAAW,UAAW,OAAQ,QAAS,UAAW,QAAS,OAAQ,UAAW,MAAO,YAAa,SAAU,SAAU,UAAW,cAAe,aAAc,WAAY,kBAAmB,cAAe,WAAY,YAAa,YAAa,MAAO,OAAQ,WAAY,oBAAqB,QAAS,UAAW,UAAW,QAAS,SAC1/BC,UAAW,CAAC,WAAY,WAAY,WAAY,kBAAmB,mBAAoB,SAAU,WAAY,MAAO,WAAY,WAAY,YAAa,SAAU,SAAU,YAAa,SAAU,MAAO,MAAO,YAAa,SAAU,OAAQ,QAAS,mBAAoB,sBAAuB,WAAY,MAAO,YAAa,aAAc,WAAY,aAAc,UAAW,YAAa,UAAW,UAAW,WAAY,WAAY,cAAe,UAAW,WAAY,QAAS,iBAAkB,kBAAmB,QAAS,QAAS,WAAY,YAAa,eAAgB,aAAc,SAAU,UAAW,SAAU,QAAS,YAAa,UAAW,OAAQ,SAAU,cAAe,OAAQ,aAAc,QAAS,WAAY,OAAQ,SAAU,OAAQ,SAAU,UAAW,QAAS,UAAW,WAAY,MAAO,MAAO,SAAU,QAAS,OAAQ,MAAO,cAAe,MAAO,OAAQ,YAAa,eAAgB,UAAW,QAAS,iBAAkB,SAAU,iBAAkB,UAAW,cAAe,SAAU,YAAa,SAAU,UAAW,aAAc,UAAW,UAAW,aAAc,OAAQ,WAAY,eAAgB,eAAgB,WAAY,QAAS,QAAS,SAAU,SAAU,SAAU,aAAc,WAAY,eAAgB,UAAW,UAAW,UAAW,UAAW,OAAQ,QAAS,UAAW,QAAS,OAAQ,OAAQ,UAAW,MAAO,WAAY,WAAY,cAAe,YAAa,qBAAsB,SAAU,QAAS,SAAU,QAAS,aAAc,WAAY,aAAc,kBAAmB,WAAY,WAAY,YAAa,WAAY,kBAAmB,YAAa,SAAU,OAAQ,YAC3mDC,OAAQ,CAAC,SAAU,WAAY,OAAQ,MAAO,WAAY,WAAY,YAAa,mBAAoB,UAAW,WAAY,SAAU,SAAU,YAAa,UAAW,SAAU,WAAY,MAAO,iBAAkB,MAAO,YAAa,SAAU,UAAW,YAAa,mBAAoB,QAAS,YAAa,SAAU,WAAY,WAAY,aAAc,YAAa,UAAW,YAAa,WAAY,YAAa,cAAe,YAAa,YAAa,aAAc,SAAU,QAAS,kBAAmB,QAAS,QAAS,WAAY,YAAa,SAAU,UAAW,aAAc,SAAU,YAAa,UAAW,YAAa,OAAQ,SAAU,SAAU,cAAe,OAAQ,QAAS,QAAS,WAAY,SAAU,OAAQ,SAAU,QAAS,UAAW,WAAY,QAAS,kBAAmB,OAAQ,eAAgB,MAAO,cAAe,QAAS,OAAQ,YAAa,OAAQ,QAAS,UAAW,SAAU,wBAAyB,OAAQ,WAAY,eAAgB,WAAY,QAAS,SAAU,SAAU,SAAU,aAAc,UAAW,UAAW,QAAS,UAAW,OAAQ,QAAS,WAAY,SAAU,SAAU,QAAS,aAAc,OAAQ,QAAS,uBAAwB,YAAa,MAAO,WAAY,UAAW,WAAY,SAAU,SAAU,SAAU,SAAU,UAAW,YAAa,gBAAiB,aAAc,aAAc,kBAAmB,WAAY,WAAY,YAAa,WAAY,WAAY,UAAW,QAAS,YAAa,SAAU,QAAS,MAAO,OAAQ,WAAY,SAAU,SAAU,YAAa,SAAU,SAAU,oBAAqB,QAAS,UAAW,UAAW,UAAW,OAAQ,UAAW,SAAU,eAAgB,WAAY,YAAa,SAAU,cAAe,OAAQ,OAAQ,SAAU,eAAgB,WAAY,kBAAmB,sBAAuB,QAAS,OAAQ,QAAS,MAAO,aAAc,WAAY,eAAgB,aAAc,SAAU,aAAc,WAAY,MAAO,MAAO,SAAU,MAAO,eAAgB,cAAe,iBAAkB,YAAa,SAAU,UAAW,iBAAkB,WAAY,QAAS,SAAU,QAAS,cAAe,kBAAmB,YAAa,SAAU,OAAQ,SAAU,UAAW,OAAQ,SAAU,YAAa,cAEzsEC,KAAM,CAAC,SAAU,UAAW,UAAW,MAAO,MAAO,UAAW,OAAQ,QAAS,SAAU,MAAO,OAAQ,MAAO,OAAQ,QAAS,SAAU,QAAS,QAAS,QAAS,QAAS,MAAO,SAAU,OAAQ,SAAU,QAAS,WAAY,SAAU,MAAO,QAEzPC,IAAK,CAAC,iBAAkB,QAAS,QAAS,OAAQ,aAAc,WAAY,cAAe,uBAAwB,OAAQ,OAAQ,SAAU,QAAS,UAAW,OAAQ,SAAU,QAAS,UAAW,QAAS,cAAe,cAAe,WAAY,YAAa,SAAU,UAAW,SAAU,UAAW,OAAQ,OAAQ,UAAW,gBAAiB,UAAW,SAAU,kBAAmB,UAAW,OAAQ,QAAS,SAAU,SAAU,gBAAiB,gBAAiB,YAAa,UAAW,WAE/eC,IAAK,CAAC,WAAY,mBAAoB,mBAAoB,qBAAsB,iBAAkB,aAAc,WAAY,gBAAiB,iBAAkB,kBAAmB,aAAc,aAAc,cAAe,YAAa,sBAAuB,qBAAsB,aAAc,iBAAkB,oBAAqB,sBAAuB,eAAgB,aAAc,QAAS,SAAU,iBAAkB,UAAW,UAAW,eAAgB,6BAA8B,iBAAkB,oBAAqB,aAAc,eAAgB,gBAAiB,yBAA0B,OAAQ,iBAAkB,sBAAuB,qBAAsB,gBAAiB,gBAAiB,0BAA2B,cAAe,oBAAqB,iBAAkB,2BAA4B,cAAe,QAAS,mBAAoB,aAAc,iBAAkB,oBAAqB,YAAa,mBAAoB,oBAAqB,uBAAwB,aAAc,2BAA4B,eAAgB,YAAa,2BAA4B,6BAA8B,kBAAmB,gBAAiB,uBAAwB,2BAA4B,QACzrC,CACD,EAEIC,EAAmB/b,OAAOT,UAAUyc,eACpCC,EACHjc,OAAO8a,MACP,SAAU9W,GACT,IACSsV,EADLtU,EAAS,GACb,IAASsU,KAAOtV,EACX+X,EAAiB7b,KAAK8D,EAAKsV,CAAG,GACjCtU,EAAOE,KAAKoU,CAAG,EAIjB,OAAOtU,CACR,EAkBD,SAASkX,EAAWC,EAAQC,GAC3B,IAAIjB,EAAU7b,MAAM6b,QAAQgB,CAAM,EAC9BE,EAASD,IAAYjB,EAAU,IAAI7b,MAAM6c,EAAOtc,MAAM,EAAI,IAE9D,GAAIsb,EATJ,IAUCmB,IAXkBH,EAWPA,EAXeE,EAWPA,EAVXvc,EAAI,EAAGuZ,EAAI8C,EAAOtc,OAAQC,EAAIuZ,EAAGvZ,CAAC,GAC1Cuc,EAAOvc,GAAKqc,EAAOrc,QARpB,IAmBCyc,IArBGjD,EAFgB6C,EAuBPA,EAvBeE,EAuBPA,EAtBjBvB,EAAOmB,EAAOE,CAAM,EAGfrc,EAAI,EAAGuZ,EAAIyB,EAAKjb,OAAQC,EAAIuZ,EAAGvZ,CAAC,GAExCuc,EADA/C,EAAMwB,EAAKhb,IACGqc,EAAO7C,IAAQ+C,EAAO/C,GAoBrC,OAAO+C,CACR,CAGA7c,EAAOD,UAAUwC,IAAM,SAAUiI,GAChC,OAAOkS,EAAW3C,EAAKvP,EAAK,CAC7B,EAGAxK,EAAOD,UAAUid,YAAc,SAAU5b,IAKxCA,EAAUD,EAAYC,CAAO,GAChB6b,YACZ7b,EAAQ6b,UAAY7b,EAAQ8b,eAAiB,IAAM,KAGpD,IAAIC,EAAW,mBAQf,OANK/b,EAAQ8b,eAGNhd,KAAKgE,EAAEhE,KAAK+D,OAAQ,EAAG,CAAEjB,KAAMma,EAAU9c,OAAQ,CAAE,CAAC,EAFpDH,KAAKgE,EAAEhE,KAAK+D,OAAQ,EAAG,CAAEjB,KAAMma,EAAU9c,OAAQ,CAAE,CAAC,GAEE8D,KAAK/C,EAAQ6b,SAAS,CAIpF,EAEAjd,EAAOD,UAAUqd,OAAS,SAAUhc,GASnC,GANAE,GAFAF,EAAUD,EAAYC,EAAS,CAAEic,KAAM,EAAGC,IAAK,EAAGta,KAAM,EAAG,CAAC,GAE1CA,KAAKyJ,cAAgB3M,MAAO,gDAAgD,EAC9FwB,EAAkC,UAAxB,OAAOF,EAAQic,KAAmB,sCAAsC,EAClF/b,EAAiC,UAAvB,OAAOF,EAAQkc,IAAkB,mDAAmD,EAIpE,EAAtBlc,EAAQ4B,KAAK3C,OAChB,OAAOH,KAAKqd,YAAYnc,CAAO,EAWhC,IAPA,IAAIoc,EACHC,EACAC,EAEAL,EAAOjc,EAAQic,KACfC,EAAMlc,EAAQkc,IAQD,IADbE,GAHAC,EAAoB,EAAhBvd,KAAKC,OAAO,EAAQ,GAGhBsd,GAFRC,EAAoB,EAAhBxd,KAAKC,OAAO,EAAQ,GAERud,KAOjB,OAAOJ,GAHAG,EAAIna,KAAKqa,KAAM,CAAC,EAAIra,KAAKsa,IAAIJ,CAAC,EAAKA,CAAC,GAGvBH,CACrB,EAEArd,EAAOD,UAAUwd,YAAc,SAAUnc,GACxC,IAAIyc,EAAqB,EACzB,EAAG,CACF,IAAI/T,EAAMxG,KAAKwa,MAAM5d,KAAKkd,OAAO,CAAEC,KAAMjc,EAAQic,KAAMC,IAAKlc,EAAQkc,GAAI,CAAC,CAAC,EAC1E,GAAIxT,EAAM1I,EAAQ4B,KAAK3C,QAAiB,GAAPyJ,EAChC,OAAO1I,EAAQ4B,KAAK8G,EAItB,OAFE+T,EAAAA,EAE4B,KAE9B,MAAM,IAAIpc,WAAW,0FAA0F,CAChH,EAEAzB,EAAOD,UAAUge,MAAQ,SAAU3c,GAGlC,IAAI4c,EAAK,GACT,QAFA5c,EAAUD,EAAYC,EAAS,CAAE6c,KAAM,GAAI,CAAC,GAE5BA,KAAKzb,YAAY,GAChC,IAAK,OACL,IAAK,IACJwb,EAAK,IACL,MACD,IAAK,OACL,IAAK,IACJA,EAAK,IACL,MACD,QACCA,EAAK9d,KAAKyC,UAAU,CAAEK,KAAM,IAAK,CAAC,CAEpC,CAEA,OAAOgb,EAAK9d,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,EAAI7C,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,EAAI7C,KAAKyC,UAAU,CAAEC,MAAO,CAAA,EAAMG,OAAQ,OAAQ,CAAC,CAClK,EAGA/C,EAAOD,UAAUme,IAAM,SAAU1T,EAAM2T,GAClB,UAAhB,OAAO3T,EACVuP,EAAKvP,GAAQ2T,EAEbpE,EAAO2C,EAAWlS,EAAMuP,CAAI,CAE9B,EAEA/Z,EAAOD,UAAUqe,GAAK,SAAUhd,GAC/B,OAAOlB,KAAK6d,MAAM3c,CAAO,CAC1B,EAGApB,EAAOD,UAAU0I,KAAO,WACvB,IAAIvE,EAAIhE,KAAKgE,EAAEhE,KAAK6B,QAAS,EAAG,CAAEmB,IAAK,CAAE,CAAC,EACtCoF,EAAK,EAAW,EAAPpE,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAM5FqE,EAAK,IAAMA,EADG,GAFbD,EADS,KAANA,EADC,GAAMA,EAAK,IAEV,EAEGA,GAAS,EAAW,EAAPpE,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,GAAgB,EAAPA,EAAE,IACrF,GAIhB,MAAO,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,IAAMA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAK,SAAWoE,GAF1FC,EADS,IAANA,EACE,EAE0FA,EACjG,EAIAvI,EAAOD,UAAUiB,iBAAmB,SAAUf,GAC7C,OAAO,IAAIoe,EAAgBpe,CAAI,CAChC,EAEAD,EAAOD,UAAUmB,YAAc,WAC9B,OAAO,IAAIod,CACZ,EA6CA,IAAID,EAAkB,SAAUpe,GAClBmP,KAAAA,IAATnP,IAEHA,EAAOqD,KAAKO,MAAMP,KAAKnD,OAAO,EAAImD,KAAKC,IAAI,GAAI,EAAE,CAAC,GAGnDrD,KAAKqe,EAAI,IACTre,KAAKse,EAAI,IACTte,KAAKue,SAAW,WAChBve,KAAKwe,WAAa,WAClBxe,KAAKye,WAAa,WAElBze,KAAKa,GAAK,IAAIjB,MAAMI,KAAKqe,CAAC,EAC1Bre,KAAK0e,IAAM1e,KAAKqe,EAAI,EAEpBre,KAAK2e,aAAa5e,CAAI,CACvB,EA8HIqe,GA3HJD,EAAgBte,UAAU8e,aAAe,SAAUrB,GAElD,IADAtd,KAAKa,GAAG,GAAKyc,IAAM,EACdtd,KAAK0e,IAAM,EAAG1e,KAAK0e,IAAM1e,KAAKqe,EAAGre,KAAK0e,GAAG,GAC7CpB,EAAItd,KAAKa,GAAGb,KAAK0e,IAAM,GAAM1e,KAAKa,GAAGb,KAAK0e,IAAM,KAAO,GACvD1e,KAAKa,GAAGb,KAAK0e,MAAqC,aAAtB,WAAJpB,KAAoB,KAAqB,IAAyB,YAAd,MAAJA,GAA+Btd,KAAK0e,IAK5G1e,KAAKa,GAAGb,KAAK0e,QAAU,CAGzB,EAMAP,EAAgBte,UAAU+e,cAAgB,SAAUC,EAAUC,GAC7D,IAECne,EACA2c,EAHGld,EAAI,EACPK,EAAI,EAKL,IAFAT,KAAK2e,aAAa,QAAQ,EAC1Bhe,EAAIX,KAAKqe,EAAIS,EAAa9e,KAAKqe,EAAIS,EAC5Bne,EAAGA,CAAC,GACV2c,EAAItd,KAAKa,GAAGT,EAAI,GAAMJ,KAAKa,GAAGT,EAAI,KAAO,GACzCJ,KAAKa,GAAGT,IAAMJ,KAAKa,GAAGT,IAAoC,UAAtB,WAAJkd,KAAoB,KAAkB,IAAyB,SAAd,MAAJA,IAA8BuB,EAASpe,GAAKA,EACzHT,KAAKa,GAAGT,MAAQ,EAEhBK,CAAC,GADDL,EAAAA,GAESJ,KAAKqe,IACbre,KAAKa,GAAG,GAAKb,KAAKa,GAAGb,KAAKqe,EAAI,GAC9Bje,EAAI,GAEI0e,GAALre,IACHA,EAAI,GAGN,IAAKE,EAAIX,KAAKqe,EAAI,EAAG1d,EAAGA,CAAC,GACxB2c,EAAItd,KAAKa,GAAGT,EAAI,GAAMJ,KAAKa,GAAGT,EAAI,KAAO,GACzCJ,KAAKa,GAAGT,IAAMJ,KAAKa,GAAGT,IAAoC,aAAtB,WAAJkd,KAAoB,KAAqB,IAAyB,YAAd,MAAJA,IAAiCld,EACjHJ,KAAKa,GAAGT,MAAQ,EAChBA,EAAAA,GACSJ,KAAKqe,IACbre,KAAKa,GAAG,GAAKb,KAAKa,GAAGb,KAAKqe,EAAI,GAC9Bje,EAAI,GAINJ,KAAKa,GAAG,GAAK,UACd,EAGAsd,EAAgBte,UAAUkf,cAAgB,WACzC,IAAI5V,EAMC6V,EALDC,EAAQ,IAAIrf,MAAM,EAAKI,KAAKue,QAAQ,EAGxC,GAAIve,KAAK0e,KAAO1e,KAAKqe,EAAG,CAQvB,IAJIre,KAAK0e,MAAQ1e,KAAKqe,EAAI,GAEzBre,KAAK2e,aAAa,IAAI,EAElBK,EAAK,EAAGA,EAAKhf,KAAKqe,EAAIre,KAAKse,EAAGU,CAAE,GACpC7V,EAAKnJ,KAAKa,GAAGme,GAAMhf,KAAKwe,WAAexe,KAAKa,GAAGme,EAAK,GAAKhf,KAAKye,WAC9Dze,KAAKa,GAAGme,GAAMhf,KAAKa,GAAGme,EAAKhf,KAAKse,GAAMnV,IAAM,EAAK8V,EAAU,EAAJ9V,GAExD,KAAO6V,EAAKhf,KAAKqe,EAAI,EAAGW,CAAE,GACzB7V,EAAKnJ,KAAKa,GAAGme,GAAMhf,KAAKwe,WAAexe,KAAKa,GAAGme,EAAK,GAAKhf,KAAKye,WAC9Dze,KAAKa,GAAGme,GAAMhf,KAAKa,GAAGme,GAAMhf,KAAKse,EAAIte,KAAKqe,IAAOlV,IAAM,EAAK8V,EAAU,EAAJ9V,GAEnEA,EAAKnJ,KAAKa,GAAGb,KAAKqe,EAAI,GAAKre,KAAKwe,WAAexe,KAAKa,GAAG,GAAKb,KAAKye,WACjEze,KAAKa,GAAGb,KAAKqe,EAAI,GAAKre,KAAKa,GAAGb,KAAKse,EAAI,GAAMnV,IAAM,EAAK8V,EAAU,EAAJ9V,GAE9DnJ,KAAK0e,IAAM,CACZ,CAUA,OARAvV,EAAInJ,KAAKa,GAAGb,KAAK0e,GAAG,KAMpBvV,GADAA,GADAA,GADAA,GAAKA,IAAM,IACLA,GAAK,EAAK,YACVA,GAAK,GAAM,YACZA,IAAM,MAEE,CACd,EAGAgV,EAAgBte,UAAUqf,cAAgB,WACzC,OAAOlf,KAAK+e,cAAc,IAAM,CACjC,EAGAZ,EAAgBte,UAAUsf,cAAgB,WACzC,OAAOnf,KAAK+e,cAAc,GAAK,EAAM,WAEtC,EAGAZ,EAAgBte,UAAUI,OAAS,WAClC,OAAOD,KAAK+e,cAAc,GAAK,EAAM,WAEtC,EAGAZ,EAAgBte,UAAUuf,cAAgB,WACzC,OAAQpf,KAAK+e,cAAc,EAAI,KAAQ,EAAM,WAE9C,EAGAZ,EAAgBte,UAAUwf,cAAgB,WAGzC,OAA+B,EAAM,kBAAzB,UAFJrf,KAAK+e,cAAc,IAAM,IAC5B/e,KAAK+e,cAAc,IAAM,GAE/B,EAGiB,cAEjBX,EAAWve,UAAU2B,QAAU,QAM/B4c,EAAWve,UAAUyf,SAAW,SAAkBpW,EAAGC,GACpD,IAAIoW,GAAW,MAAJrW,IAAmB,MAAJC,GAE1B,OADQD,GAAK,KAAOC,GAAK,KAAOoW,GAAO,KACxB,GAAa,MAANA,CACvB,EAKAnB,EAAWve,UAAU2f,SAAW,SAAU9a,EAAK+a,GAC9C,OAAQ/a,GAAO+a,EAAQ/a,IAAS,GAAK+a,CACtC,EAKArB,EAAWve,UAAU6f,QAAU,SAAUC,EAAGC,EAAGC,EAAG3W,EAAGoU,EAAGwC,GACvD,OAAO9f,KAAKsf,SAAStf,KAAKwf,SAASxf,KAAKsf,SAAStf,KAAKsf,SAASM,EAAGD,CAAC,EAAG3f,KAAKsf,SAASpW,EAAG4W,CAAC,CAAC,EAAGxC,CAAC,EAAGuC,CAAC,CAClG,EACAzB,EAAWve,UAAUkgB,OAAS,SAAUH,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAGoU,EAAGwC,GACzD,OAAO9f,KAAK0f,QAASG,EAAI7I,EAAM,CAAC6I,EAAIG,EAAIJ,EAAGC,EAAG3W,EAAGoU,EAAGwC,CAAC,CACtD,EACA1B,EAAWve,UAAUogB,OAAS,SAAUL,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAGoU,EAAGwC,GACzD,OAAO9f,KAAK0f,QAASG,EAAIG,EAAMhJ,EAAI,CAACgJ,EAAIJ,EAAGC,EAAG3W,EAAGoU,EAAGwC,CAAC,CACtD,EACA1B,EAAWve,UAAUqgB,OAAS,SAAUN,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAGoU,EAAGwC,GACzD,OAAO9f,KAAK0f,QAAQG,EAAI7I,EAAIgJ,EAAGJ,EAAGC,EAAG3W,EAAGoU,EAAGwC,CAAC,CAC7C,EACA1B,EAAWve,UAAUsgB,OAAS,SAAUP,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAGoU,EAAGwC,GACzD,OAAO9f,KAAK0f,QAAQ1I,GAAK6I,EAAI,CAACG,GAAIJ,EAAGC,EAAG3W,EAAGoU,EAAGwC,CAAC,CAChD,EAKA1B,EAAWve,UAAUugB,SAAW,SAAUlX,EAAGmX,GAE5CnX,EAAEmX,GAAO,IAAM,KAAQA,EAAM,GAC7BnX,EAA8B,IAAzBmX,EAAM,KAAQ,GAAM,IAAWA,EAYpC,IAVA,IACCC,EACAC,EACAC,EACAC,EACAb,EAAI,WACJC,EAAI,CAAC,UACL7I,EAAI,CAAC,WACLgJ,EAAI,UAEA5f,EAAI,EAAGA,EAAI8I,EAAE/I,OAAQC,GAAK,GAC9BkgB,EAAOV,EACPW,EAAOV,EACPW,EAAOxJ,EACPyJ,EAAOT,EAEPJ,EAAI5f,KAAK+f,OAAOH,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,GAAI,EAAG,CAAC,SAAS,EAC/C4f,EAAIhgB,KAAK+f,OAAOC,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EACpD4W,EAAIhX,KAAK+f,OAAO/I,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,SAAS,EACnDyf,EAAI7f,KAAK+f,OAAOF,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrDwf,EAAI5f,KAAK+f,OAAOH,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,SAAS,EACnD4f,EAAIhgB,KAAK+f,OAAOC,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,UAAU,EACpD4W,EAAIhX,KAAK+f,OAAO/I,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrDyf,EAAI7f,KAAK+f,OAAOF,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,QAAQ,EACnDwf,EAAI5f,KAAK+f,OAAOH,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,UAAU,EACnD4f,EAAIhgB,KAAK+f,OAAOC,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrD4W,EAAIhX,KAAK+f,OAAO/I,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,CAAC,KAAK,EACjDyf,EAAI7f,KAAK+f,OAAOF,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EACtDwf,EAAI5f,KAAK+f,OAAOH,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,IAAK,EAAG,UAAU,EACpD4f,EAAIhgB,KAAK+f,OAAOC,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,GAAI,CAAC,QAAQ,EACpD4W,EAAIhX,KAAK+f,OAAO/I,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EACtDyf,EAAI7f,KAAK+f,OAAOF,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,UAAU,EAErDwf,EAAI5f,KAAKigB,OAAOL,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,SAAS,EACnD4f,EAAIhgB,KAAKigB,OAAOD,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,EAAG,CAAC,UAAU,EACpD4W,EAAIhX,KAAKigB,OAAOjJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,SAAS,EACpDyf,EAAI7f,KAAKigB,OAAOJ,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,GAAI,GAAI,CAAC,SAAS,EAChDwf,EAAI5f,KAAKigB,OAAOL,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,SAAS,EACnD4f,EAAIhgB,KAAKigB,OAAOD,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,EAAG,QAAQ,EAClD4W,EAAIhX,KAAKigB,OAAOjJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,CAAC,SAAS,EACrDyf,EAAI7f,KAAKigB,OAAOJ,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EACpDwf,EAAI5f,KAAKigB,OAAOL,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,SAAS,EAClD4f,EAAIhgB,KAAKigB,OAAOD,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,EAAG,CAAC,UAAU,EACrD4W,EAAIhX,KAAKigB,OAAOjJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EACpDyf,EAAI7f,KAAKigB,OAAOJ,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,UAAU,EACpDwf,EAAI5f,KAAKigB,OAAOL,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,IAAK,EAAG,CAAC,UAAU,EACrD4f,EAAIhgB,KAAKigB,OAAOD,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,EAAG,CAAC,QAAQ,EAClD4W,EAAIhX,KAAKigB,OAAOjJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,UAAU,EACpDyf,EAAI7f,KAAKigB,OAAOJ,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EAEtDwf,EAAI5f,KAAKkgB,OAAON,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,MAAM,EAChD4f,EAAIhgB,KAAKkgB,OAAOF,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrD4W,EAAIhX,KAAKkgB,OAAOlJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,UAAU,EACrDyf,EAAI7f,KAAKkgB,OAAOL,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,CAAC,QAAQ,EACpDwf,EAAI5f,KAAKkgB,OAAON,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,UAAU,EACpD4f,EAAIhgB,KAAKkgB,OAAOF,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,UAAU,EACpD4W,EAAIhX,KAAKkgB,OAAOlJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EACpDyf,EAAI7f,KAAKkgB,OAAOL,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EACtDwf,EAAI5f,KAAKkgB,OAAON,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,IAAK,EAAG,SAAS,EACnD4f,EAAIhgB,KAAKkgB,OAAOF,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,GAAI,GAAI,CAAC,SAAS,EAChD4W,EAAIhX,KAAKkgB,OAAOlJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EACpDyf,EAAI7f,KAAKkgB,OAAOL,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,QAAQ,EAClDwf,EAAI5f,KAAKkgB,OAAON,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,SAAS,EACnD4f,EAAIhgB,KAAKkgB,OAAOF,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,GAAI,CAAC,SAAS,EACrD4W,EAAIhX,KAAKkgB,OAAOlJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,SAAS,EACpDyf,EAAI7f,KAAKkgB,OAAOL,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EAEpDwf,EAAI5f,KAAKmgB,OAAOP,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,GAAI,EAAG,CAAC,SAAS,EAC/C4f,EAAIhgB,KAAKmgB,OAAOH,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,UAAU,EACpD4W,EAAIhX,KAAKmgB,OAAOnJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EACtDyf,EAAI7f,KAAKmgB,OAAON,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,QAAQ,EACnDwf,EAAI5f,KAAKmgB,OAAOP,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,IAAK,EAAG,UAAU,EACpD4f,EAAIhgB,KAAKmgB,OAAOH,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrD4W,EAAIhX,KAAKmgB,OAAOnJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,IAAK,GAAI,CAAC,OAAO,EACnDyf,EAAI7f,KAAKmgB,OAAON,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrDwf,EAAI5f,KAAKmgB,OAAOP,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,UAAU,EACnD4f,EAAIhgB,KAAKmgB,OAAOH,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,GAAI,CAAC,QAAQ,EACpD4W,EAAIhX,KAAKmgB,OAAOnJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,CAAC,UAAU,EACrDyf,EAAI7f,KAAKmgB,OAAON,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,IAAK,GAAI,UAAU,EACrDwf,EAAI5f,KAAKmgB,OAAOP,EAAGC,EAAG7I,EAAGgJ,EAAG9W,EAAE9I,EAAI,GAAI,EAAG,CAAC,SAAS,EACnD4f,EAAIhgB,KAAKmgB,OAAOH,EAAGJ,EAAGC,EAAG7I,EAAG9N,EAAE9I,EAAI,IAAK,GAAI,CAAC,UAAU,EACtD4W,EAAIhX,KAAKmgB,OAAOnJ,EAAGgJ,EAAGJ,EAAGC,EAAG3W,EAAE9I,EAAI,GAAI,GAAI,SAAS,EACnDyf,EAAI7f,KAAKmgB,OAAON,EAAG7I,EAAGgJ,EAAGJ,EAAG1W,EAAE9I,EAAI,GAAI,GAAI,CAAC,SAAS,EAEpDwf,EAAI5f,KAAKsf,SAASM,EAAGU,CAAI,EACzBT,EAAI7f,KAAKsf,SAASO,EAAGU,CAAI,EACzBvJ,EAAIhX,KAAKsf,SAAStI,EAAGwJ,CAAI,EACzBR,EAAIhgB,KAAKsf,SAASU,EAAGS,CAAI,EAE1B,MAAO,CAACb,EAAGC,EAAG7I,EAAGgJ,EAClB,EAKA5B,EAAWve,UAAU6gB,UAAY,SAAU1e,GAG1C,IAFA,IACC2e,EAAS,GACLvgB,EAAI,EAAGA,EAAmB,GAAf4B,EAAM7B,OAAaC,GAAK,EACvCugB,GAAUC,OAAOC,aAAc7e,EAAM5B,GAAK,KAAOA,EAAI,GAAM,GAAI,EAEhE,OAAOugB,CACR,EAMAvC,EAAWve,UAAUihB,UAAY,SAAU9e,GAC1C,IAAI5B,EACHugB,EAAS,GAEV,IADAA,GAAQ3e,EAAM7B,QAAU,GAAK,GAAK+O,KAAAA,EAC7B9O,EAAI,EAAGA,EAAIugB,EAAOxgB,OAAQC,GAAK,EACnCugB,EAAOvgB,GAAK,EAEb,IAAKA,EAAI,EAAGA,EAAmB,EAAf4B,EAAM7B,OAAYC,GAAK,EACtCugB,EAAOvgB,GAAK,KAAiC,IAA1B4B,EAAMpB,WAAWR,EAAI,CAAC,IAAaA,EAAI,GAE3D,OAAOugB,CACR,EAKAvC,EAAWve,UAAUkhB,SAAW,SAAUzD,GACzC,OAAOtd,KAAK0gB,UAAU1gB,KAAKogB,SAASpgB,KAAK8gB,UAAUxD,CAAC,EAAc,EAAXA,EAAEnd,MAAU,CAAC,CACrE,EAKAie,EAAWve,UAAUmhB,cAAgB,SAAUpH,EAAKC,GACnD,IAAIzZ,EACH6gB,EAAOjhB,KAAK8gB,UAAUlH,CAAG,EACzBsH,EAAO,GACPC,EAAO,GAMR,IAJAD,EAAK,IAAMC,EAAK,IAAMjS,KAAAA,EACJ,GAAd+R,EAAK9gB,SACR8gB,EAAOjhB,KAAKogB,SAASa,EAAmB,EAAbrH,EAAIzZ,MAAU,GAErCC,EAAI,EAAGA,EAAI,GAAIA,GAAK,EACxB8gB,EAAK9gB,GAAe,UAAV6gB,EAAK7gB,GACf+gB,EAAK/gB,GAAe,WAAV6gB,EAAK7gB,GAGhB,OADAM,EAAOV,KAAKogB,SAASc,EAAKnY,OAAO/I,KAAK8gB,UAAUjH,CAAI,CAAC,EAAG,IAAoB,EAAdA,EAAK1Z,MAAU,EACtEH,KAAK0gB,UAAU1gB,KAAKogB,SAASe,EAAKpY,OAAOrI,CAAI,EAAG,GAAS,CAAC,CAClE,EAKA0d,EAAWve,UAAUuhB,SAAW,SAAUpf,GAKzC,IAJA,IAECkH,EAFGmY,EAAU,mBACbV,EAAS,GAGLvgB,EAAI,EAAGA,EAAI4B,EAAM7B,OAAQC,GAAK,EAClC8I,EAAIlH,EAAMpB,WAAWR,CAAC,EACtBugB,GAAUU,EAAQte,OAAQmG,IAAM,EAAK,EAAI,EAAImY,EAAQte,OAAW,GAAJmG,CAAQ,EAErE,OAAOyX,CACR,EAKAvC,EAAWve,UAAUyhB,cAAgB,SAAUtf,GAC9C,OAAOuf,SAASC,mBAAmBxf,CAAK,CAAC,CAC1C,EAKAoc,EAAWve,UAAU4hB,QAAU,SAAUnE,GACxC,OAAOtd,KAAK+gB,SAAS/gB,KAAKshB,cAAchE,CAAC,CAAC,CAC3C,EACAc,EAAWve,UAAU6hB,QAAU,SAAUpE,GACxC,OAAOtd,KAAKohB,SAASphB,KAAKyhB,QAAQnE,CAAC,CAAC,CACrC,EACAc,EAAWve,UAAU8hB,aAAe,SAAUhhB,EAAGqf,GAChD,OAAOhgB,KAAKghB,cAAchhB,KAAKshB,cAAc3gB,CAAC,EAAGX,KAAKshB,cAActB,CAAC,CAAC,CACvE,EACA5B,EAAWve,UAAU+hB,aAAe,SAAUjhB,EAAGqf,GAChD,OAAOhgB,KAAKohB,SAASphB,KAAK2hB,aAAahhB,EAAGqf,CAAC,CAAC,CAC7C,EAEA5B,EAAWve,UAAU2N,IAAM,SAAUzJ,EAAQ6V,EAAKjG,GACjD,OAAKiG,EAQAjG,EAIE3T,KAAK2hB,aAAa/H,EAAK7V,CAAM,EAH5B/D,KAAK4hB,aAAahI,EAAK7V,CAAM,EAR/B4P,EAIE3T,KAAKyhB,QAAQ1d,CAAM,EAHlB/D,KAAK0hB,QAAQ3d,CAAM,CAW7B,EAGuB,aAAnB,OAAO8d,WAETA,QADqB,aAAlB,OAAOC,QAA0BA,OAAOD,QACjCC,OAAOD,QAAU/hB,EAE5B+hB,SAAQ/hB,OAASA,GAII,YAAlB,OAAOiiB,QAAyBA,OAAOC,KAC1CD,OAAO,GAAI,WACV,OAAOjiB,CACR,CAAC,EAK2B,aAAzB,OAAOmiB,gBACVC,OAAS,IAAIpiB,EACbiS,KAAKjS,OAASA,GAKO,UAAlB,OAAOqiB,QAAkD,UAA3B,OAAOA,OAAOpH,WAC/CoH,OAAOriB,OAASA,EAChBqiB,OAAOD,OAAS,IAAIpiB,EAErB,EAAE"}
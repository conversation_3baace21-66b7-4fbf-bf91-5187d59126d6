!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.PerfectScrollbar=e()}(this,function(){"use strict";function f(t){return getComputedStyle(t)}function a(t,e){for(var i in e){var l=e[i];"number"==typeof l&&(l+="px"),t.style[i]=l}}function c(t){var e=document.createElement("div");return e.className=t,e}var i="undefined"!=typeof Element&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector);function s(t,e){if(i)return i.call(t,e);throw new Error("No element matching method supported")}function o(t){t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)}function h(t,e){return Array.prototype.filter.call(t.children,function(t){return s(t,e)})}var m={main:"ps",element:{thumb:function(t){return"ps__thumb-"+t},rail:function(t){return"ps__rail-"+t},consuming:"ps__child--consume"},state:{focus:"ps--focus",clicking:"ps--clicking",active:function(t){return"ps--active-"+t},scrolling:function(t){return"ps--scrolling-"+t}}},l={x:null,y:null};function v(t,e){var t=t.element.classList,i=m.state.scrolling(e);t.contains(i)?clearTimeout(l[e]):t.add(i)}function Y(t,e){l[e]=setTimeout(function(){return t.isAlive&&t.element.classList.remove(m.state.scrolling(e))},t.settings.scrollingThreshold)}function r(t){this.element=t,this.handlers={}}function d(){this.eventElements=[]}var t={isEmpty:{configurable:!0}};r.prototype.bind=function(t,e){void 0===this.handlers[t]&&(this.handlers[t]=[]),this.handlers[t].push(e),this.element.addEventListener(t,e,!1)},r.prototype.unbind=function(e,i){var l=this;this.handlers[e]=this.handlers[e].filter(function(t){return!(!i||t===i)||(l.element.removeEventListener(e,t,!1),!1)})},r.prototype.unbindAll=function(){for(var t in this.handlers)this.unbind(t)},t.isEmpty.get=function(){var e=this;return Object.keys(this.handlers).every(function(t){return 0===e.handlers[t].length})},Object.defineProperties(r.prototype,t);function u(t){var e;return"function"==typeof window.CustomEvent?new CustomEvent(t):((e=document.createEvent("CustomEvent")).initCustomEvent(t,!1,!1,void 0),e)}d.prototype.eventElement=function(e){var t=this.eventElements.filter(function(t){return t.element===e})[0];return t||(t=new r(e),this.eventElements.push(t)),t},d.prototype.bind=function(t,e,i){this.eventElement(t).bind(e,i)},d.prototype.unbind=function(t,e,i){t=this.eventElement(t);t.unbind(e,i),t.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(t),1)},d.prototype.unbindAll=function(){this.eventElements.forEach(function(t){return t.unbindAll()}),this.eventElements=[]},d.prototype.once=function(t,e,i){var l=this.eventElement(t),r=function(t){l.unbind(e,r),i(t)};l.bind(e,r)};function e(t,e,i,l,r){var n,o,s,a,c,h,d;if(void 0===l&&(l=!0),void 0===r&&(r=!1),"top"===e)n=["contentHeight","containerHeight","scrollTop","y","up","down"];else{if("left"!==e)throw new Error("A proper axis should be provided");n=["contentWidth","containerWidth","scrollLeft","x","left","right"]}e=t,t=i,i=l,l=r,o=(r=n)[0],s=r[1],a=r[2],c=r[3],h=r[4],r=r[5],void 0===i&&(i=!0),void 0===l&&(l=!1),d=e.element,e.reach[c]=null,d[a]<1&&(e.reach[c]="start"),d[a]>e[o]-e[s]-1&&(e.reach[c]="end"),t&&(d.dispatchEvent(u("ps-scroll-"+c)),t<0?d.dispatchEvent(u("ps-scroll-"+h)):0<t&&d.dispatchEvent(u("ps-scroll-"+r)),i)&&(v(a=e,o=c),Y(a,o)),e.reach[c]&&(t||l)&&d.dispatchEvent(u("ps-"+c+"-reach-"+e.reach[c]))}function p(t){return parseInt(t,10)||0}function X(t){var e,i,l=t.element,r=Math.floor(l.scrollTop),n=l.getBoundingClientRect();t.containerWidth=Math.ceil(n.width),t.containerHeight=Math.ceil(n.height),t.contentWidth=l.scrollWidth,t.contentHeight=l.scrollHeight,l.contains(t.scrollbarXRail)||(h(l,m.element.rail("x")).forEach(o),l.appendChild(t.scrollbarXRail)),l.contains(t.scrollbarYRail)||(h(l,m.element.rail("y")).forEach(o),l.appendChild(t.scrollbarYRail)),!t.settings.suppressScrollX&&t.containerWidth+t.settings.scrollXMarginOffset<t.contentWidth?(t.scrollbarXActive=!0,t.railXWidth=t.containerWidth-t.railXMarginWidth,t.railXRatio=t.containerWidth/t.railXWidth,t.scrollbarXWidth=g(t,p(t.railXWidth*t.containerWidth/t.contentWidth)),t.scrollbarXLeft=p((t.negativeScrollAdjustment+l.scrollLeft)*(t.railXWidth-t.scrollbarXWidth)/(t.contentWidth-t.containerWidth))):t.scrollbarXActive=!1,!t.settings.suppressScrollY&&t.containerHeight+t.settings.scrollYMarginOffset<t.contentHeight?(t.scrollbarYActive=!0,t.railYHeight=t.containerHeight-t.railYMarginHeight,t.railYRatio=t.containerHeight/t.railYHeight,t.scrollbarYHeight=g(t,p(t.railYHeight*t.containerHeight/t.contentHeight)),t.scrollbarYTop=p(r*(t.railYHeight-t.scrollbarYHeight)/(t.contentHeight-t.containerHeight))):t.scrollbarYActive=!1,t.scrollbarXLeft>=t.railXWidth-t.scrollbarXWidth&&(t.scrollbarXLeft=t.railXWidth-t.scrollbarXWidth),t.scrollbarYTop>=t.railYHeight-t.scrollbarYHeight&&(t.scrollbarYTop=t.railYHeight-t.scrollbarYHeight),n=l,e={width:(r=t).railXWidth},i=Math.floor(n.scrollTop),r.isRtl?e.left=r.negativeScrollAdjustment+n.scrollLeft+r.containerWidth-r.contentWidth:e.left=n.scrollLeft,r.isScrollbarXUsingBottom?e.bottom=r.scrollbarXBottom-i:e.top=r.scrollbarXTop+i,a(r.scrollbarXRail,e),e={top:i,height:r.railYHeight},r.isScrollbarYUsingRight?r.isRtl?e.right=r.contentWidth-(r.negativeScrollAdjustment+n.scrollLeft)-r.scrollbarYRight-r.scrollbarYOuterWidth:e.right=r.scrollbarYRight-n.scrollLeft:r.isRtl?e.left=r.negativeScrollAdjustment+n.scrollLeft+2*r.containerWidth-r.contentWidth-r.scrollbarYLeft-r.scrollbarYOuterWidth:e.left=r.scrollbarYLeft+n.scrollLeft,a(r.scrollbarYRail,e),a(r.scrollbarX,{left:r.scrollbarXLeft,width:r.scrollbarXWidth-r.railBorderXWidth}),a(r.scrollbarY,{top:r.scrollbarYTop,height:r.scrollbarYHeight-r.railBorderYWidth}),t.scrollbarXActive?l.classList.add(m.state.active("x")):(l.classList.remove(m.state.active("x")),t.scrollbarXWidth=0,t.scrollbarXLeft=0,l.scrollLeft=0),t.scrollbarYActive?l.classList.add(m.state.active("y")):(l.classList.remove(m.state.active("y")),t.scrollbarYHeight=0,t.scrollbarYTop=0,l.scrollTop=0)}var b={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof navigator&&navigator.msMaxTouchPoints,isChrome:"undefined"!=typeof navigator&&/Chrome/i.test(navigator&&navigator.userAgent)};function g(t,e){return t.settings.minScrollbarLength&&(e=Math.max(e,t.settings.minScrollbarLength)),e=t.settings.maxScrollbarLength?Math.min(e,t.settings.maxScrollbarLength):e}function n(e,t){var i,l,r,n=t[0],o=t[1],s=t[2],a=t[3],c=t[4],h=t[5],d=t[6],u=t[7],f=t[8],p=e.element;function b(t){p[d]=i+r*(t[s]-l),v(e,u),X(e),t.stopPropagation(),t.preventDefault()}function g(){Y(e,u),e[f].classList.remove(m.state.clicking),e.event.unbind(e.ownerDocument,"mousemove",b)}e.event.bind(e[c],"mousedown",function(t){i=p[d],l=t[s],r=(e[o]-e[n])/(e[a]-e[h]),e.event.bind(e.ownerDocument,"mousemove",b),e.event.once(e.ownerDocument,"mouseup",g),e[f].classList.add(m.state.clicking),t.stopPropagation(),t.preventDefault()})}function w(t,e){var i,l=this;if(void 0===e&&(e={}),!(t="string"==typeof t?document.querySelector(t):t)||!t.nodeName)throw new Error("no element is specified to initialize PerfectScrollbar");for(i in(this.element=t).classList.add(m.main),this.settings={handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1},e)l.settings[i]=e[i];function r(){return t.classList.add(m.state.focus)}function n(){return t.classList.remove(m.state.focus)}this.containerWidth=null,this.containerHeight=null,this.contentWidth=null,this.contentHeight=null,this.isRtl="rtl"===f(t).direction,this.isNegativeScroll=(o=t.scrollLeft,t.scrollLeft=-1,s=t.scrollLeft<0,t.scrollLeft=o,s),this.negativeScrollAdjustment=this.isNegativeScroll?t.scrollWidth-t.clientWidth:0,this.event=new d,this.ownerDocument=t.ownerDocument||document,this.scrollbarXRail=c(m.element.rail("x")),t.appendChild(this.scrollbarXRail),this.scrollbarX=c(m.element.thumb("x")),this.scrollbarXRail.appendChild(this.scrollbarX),this.scrollbarX.setAttribute("tabindex",0),this.event.bind(this.scrollbarX,"focus",r),this.event.bind(this.scrollbarX,"blur",n),this.scrollbarXActive=null,this.scrollbarXWidth=null,this.scrollbarXLeft=null;var o=f(this.scrollbarXRail),s=(this.scrollbarXBottom=parseInt(o.bottom,10),isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=p(o.top)):this.isScrollbarXUsingBottom=!0,this.railBorderXWidth=p(o.borderLeftWidth)+p(o.borderRightWidth),a(this.scrollbarXRail,{display:"block"}),this.railXMarginWidth=p(o.marginLeft)+p(o.marginRight),a(this.scrollbarXRail,{display:""}),this.railXWidth=null,this.railXRatio=null,this.scrollbarYRail=c(m.element.rail("y")),t.appendChild(this.scrollbarYRail),this.scrollbarY=c(m.element.thumb("y")),this.scrollbarYRail.appendChild(this.scrollbarY),this.scrollbarY.setAttribute("tabindex",0),this.event.bind(this.scrollbarY,"focus",r),this.event.bind(this.scrollbarY,"blur",n),this.scrollbarYActive=null,this.scrollbarYHeight=null,this.scrollbarYTop=null,f(this.scrollbarYRail));this.scrollbarYRight=parseInt(s.right,10),isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=p(s.left)):this.isScrollbarYUsingRight=!0,this.scrollbarYOuterWidth=this.isRtl?p((o=f(this.scrollbarY)).width)+p(o.paddingLeft)+p(o.paddingRight)+p(o.borderLeftWidth)+p(o.borderRightWidth):null,this.railBorderYWidth=p(s.borderTopWidth)+p(s.borderBottomWidth),a(this.scrollbarYRail,{display:"block"}),this.railYMarginHeight=p(s.marginTop)+p(s.marginBottom),a(this.scrollbarYRail,{display:""}),this.railYHeight=null,this.railYRatio=null,this.reach={x:t.scrollLeft<=0?"start":t.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:t.scrollTop<=0?"start":t.scrollTop>=this.contentHeight-this.containerHeight?"end":null},this.isAlive=!0,this.settings.handlers.forEach(function(t){return y[t](l)}),this.lastScrollTop=Math.floor(t.scrollTop),this.lastScrollLeft=t.scrollLeft,this.event.bind(this.element,"scroll",function(t){return l.onScroll(t)}),X(this)}var y={"click-rail":function(i){i.event.bind(i.scrollbarY,"mousedown",function(t){return t.stopPropagation()}),i.event.bind(i.scrollbarYRail,"mousedown",function(t){var e=t.pageY-window.pageYOffset-i.scrollbarYRail.getBoundingClientRect().top>i.scrollbarYTop?1:-1;i.element.scrollTop+=e*i.containerHeight,X(i),t.stopPropagation()}),i.event.bind(i.scrollbarX,"mousedown",function(t){return t.stopPropagation()}),i.event.bind(i.scrollbarXRail,"mousedown",function(t){var e=t.pageX-window.pageXOffset-i.scrollbarXRail.getBoundingClientRect().left>i.scrollbarXLeft?1:-1;i.element.scrollLeft+=e*i.containerWidth,X(i),t.stopPropagation()})},"drag-thumb":function(t){n(t,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"]),n(t,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"])},keyboard:function(n){var o=n.element;n.event.bind(n.ownerDocument,"keydown",function(t){if(!(t.isDefaultPrevented&&t.isDefaultPrevented()||t.defaultPrevented)&&(s(o,":hover")||s(n.scrollbarX,":focus")||s(n.scrollbarY,":focus"))){var e,i=document.activeElement||n.ownerDocument.activeElement;if(i){if("IFRAME"===i.tagName)i=i.contentDocument.activeElement;else for(;i.shadowRoot;)i=i.shadowRoot.activeElement;if(s(e=i,"input,[contenteditable]")||s(e,"select,[contenteditable]")||s(e,"textarea,[contenteditable]")||s(e,"button,[contenteditable]"))return}var l=0,r=0;switch(t.which){case 37:l=t.metaKey?-n.contentWidth:t.altKey?-n.containerWidth:-30;break;case 38:r=t.metaKey?n.contentHeight:t.altKey?n.containerHeight:30;break;case 39:l=t.metaKey?n.contentWidth:t.altKey?n.containerWidth:30;break;case 40:r=t.metaKey?-n.contentHeight:t.altKey?-n.containerHeight:-30;break;case 32:r=t.shiftKey?n.containerHeight:-n.containerHeight;break;case 33:r=n.containerHeight;break;case 34:r=-n.containerHeight;break;case 36:r=n.contentHeight;break;case 35:r=-n.contentHeight;break;default:return}n.settings.suppressScrollX&&0!==l||n.settings.suppressScrollY&&0!==r||(o.scrollTop-=r,o.scrollLeft+=l,X(n),function(t,e){var i=Math.floor(o.scrollTop);if(0===t){if(!n.scrollbarYActive)return;if(0===i&&0<e||i>=n.contentHeight-n.containerHeight&&e<0)return!n.settings.wheelPropagation}i=o.scrollLeft;if(0===e){if(!n.scrollbarXActive)return;if(0===i&&t<0||i>=n.contentWidth-n.containerWidth&&0<t)return!n.settings.wheelPropagation}return 1}(l,r)&&t.preventDefault())}})},wheel:function(a){var c=a.element;function t(t){o=t.deltaX,s=-1*t.deltaY,void 0!==o&&void 0!==s||(o=-1*t.wheelDeltaX/6,s=t.wheelDeltaY/6),t.deltaMode&&1===t.deltaMode&&(o*=10,s*=10),o!=o&&s!=s&&(o=0,s=t.wheelDelta);var e,i,l,r,n,o=t.shiftKey?[-s,-o]:[o,s],s=o[0],o=o[1];!function(t,e,i){if(!b.isWebKit&&c.querySelector("select:focus"))return 1;if(c.contains(t))for(var l=t;l&&l!==c;){if(l.classList.contains(m.element.consuming))return 1;var r=f(l);if([r.overflow,r.overflowX,r.overflowY].join("").match(/(scroll|auto)/)){r=l.scrollHeight-l.clientHeight;if(0<r&&!(0===l.scrollTop&&0<i||l.scrollTop===r&&i<0))return 1;r=l.scrollWidth-l.clientWidth;if(0<r&&!(0===l.scrollLeft&&e<0||l.scrollLeft===r&&0<e))return 1}l=l.parentNode}}(t.target,s,o)&&(n=!1,a.settings.useBothWheelAxes?a.scrollbarYActive&&!a.scrollbarXActive?(o?c.scrollTop-=o*a.settings.wheelSpeed:c.scrollTop+=s*a.settings.wheelSpeed,n=!0):a.scrollbarXActive&&!a.scrollbarYActive&&(s?c.scrollLeft+=s*a.settings.wheelSpeed:c.scrollLeft-=o*a.settings.wheelSpeed,n=!0):(c.scrollTop-=o*a.settings.wheelSpeed,c.scrollLeft+=s*a.settings.wheelSpeed),X(a),n=n||(s=s,o=o,i=Math.floor(c.scrollTop),e=0===c.scrollTop,i=i+c.offsetHeight===c.scrollHeight,l=0===c.scrollLeft,r=c.scrollLeft+c.offsetWidth===c.scrollWidth,!(Math.abs(o)>Math.abs(s)?e||i:l||r))||!a.settings.wheelPropagation)&&!t.ctrlKey&&(t.stopPropagation(),t.preventDefault())}void 0!==window.onwheel?a.event.bind(c,"wheel",t):void 0!==window.onmousewheel&&a.event.bind(c,"mousewheel",t)},touch:function(o){var s,n,a,c,e;function h(t,e){s.scrollTop-=e,s.scrollLeft-=t,X(o)}function d(t){return t.targetTouches?t.targetTouches[0]:t}function u(t){return(!t.pointerType||"pen"!==t.pointerType||0!==t.buttons)&&(t.targetTouches&&1===t.targetTouches.length||t.pointerType&&"mouse"!==t.pointerType&&t.pointerType!==t.MSPOINTER_TYPE_MOUSE)}function t(t){u(t)&&(t=d(t),n.pageX=t.pageX,n.pageY=t.pageY,a=(new Date).getTime(),null!==e)&&clearInterval(e)}function i(t){var e,i,l,r;u(t)&&(e=(l={pageX:(l=d(t)).pageX,pageY:l.pageY}).pageX-n.pageX,i=l.pageY-n.pageY,!function(t,e,i){if(s.contains(t))for(var l=t;l&&l!==s;){if(l.classList.contains(m.element.consuming))return 1;var r=f(l);if([r.overflow,r.overflowX,r.overflowY].join("").match(/(scroll|auto)/)){r=l.scrollHeight-l.clientHeight;if(0<r&&!(0===l.scrollTop&&0<i||l.scrollTop===r&&i<0))return 1;r=l.scrollLeft-l.clientWidth;if(0<r&&!(0===l.scrollLeft&&e<0||l.scrollLeft===r&&0<e))return 1}l=l.parentNode}}(t.target,e,i))&&(h(e,i),n=l,0<(r=(l=(new Date).getTime())-a)&&(c.x=e/r,c.y=i/r,a=l),function(t,e){var i=Math.floor(s.scrollTop),l=s.scrollLeft,r=Math.abs(t),n=Math.abs(e);if(r<n){if(e<0&&i===o.contentHeight-o.containerHeight||0<e&&0===i)return 0===window.scrollY&&0<e&&b.isChrome}else if(n<r&&(t<0&&l===o.contentWidth-o.containerWidth||0<t&&0===l));return 1}(e,i))&&t.preventDefault()}function l(){o.settings.swipeEasing&&(clearInterval(e),e=setInterval(function(){o.isInitialized||!c.x&&!c.y||Math.abs(c.x)<.01&&Math.abs(c.y)<.01?clearInterval(e):(h(30*c.x,30*c.y),c.x*=.8,c.y*=.8)},10))}(b.supportsTouch||b.supportsIePointer)&&(s=o.element,n={},a=0,c={},e=null,b.supportsTouch?(o.event.bind(s,"touchstart",t),o.event.bind(s,"touchmove",i),o.event.bind(s,"touchend",l)):b.supportsIePointer&&(window.PointerEvent?(o.event.bind(s,"pointerdown",t),o.event.bind(s,"pointermove",i),o.event.bind(s,"pointerup",l)):window.MSPointerEvent&&(o.event.bind(s,"MSPointerDown",t),o.event.bind(s,"MSPointerMove",i),o.event.bind(s,"MSPointerUp",l))))}};return w.prototype.update=function(){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,a(this.scrollbarXRail,{display:"block"}),a(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=p(f(this.scrollbarXRail).marginLeft)+p(f(this.scrollbarXRail).marginRight),this.railYMarginHeight=p(f(this.scrollbarYRail).marginTop)+p(f(this.scrollbarYRail).marginBottom),a(this.scrollbarXRail,{display:"none"}),a(this.scrollbarYRail,{display:"none"}),X(this),e(this,"top",0,!1,!0),e(this,"left",0,!1,!0),a(this.scrollbarXRail,{display:""}),a(this.scrollbarYRail,{display:""}))},w.prototype.onScroll=function(t){this.isAlive&&(X(this),e(this,"top",this.element.scrollTop-this.lastScrollTop),e(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=Math.floor(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},w.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),o(this.scrollbarX),o(this.scrollbarY),o(this.scrollbarXRail),o(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},w.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(t){return!t.match(/^ps([-_].+|)$/)}).join(" ")},w});
//# sourceMappingURL=perfect-scrollbar.min.js.map

{"version": 3, "file": "jquery-jvectormap.min.js", "sources": ["jquery-jvectormap.min.js"], "sourcesContent": ["!(function (t) {\r\n\tvar s = { set: { colors: 1, values: 1, backgroundColor: 1, scaleColors: 1, normalizeFunction: 1, focus: 1 }, get: { selectedRegions: 1, selectedMarkers: 1, mapObject: 1, regionName: 1 } };\r\n\tt.fn.vectorMap = function (t) {\r\n\t\tvar e,\r\n\t\t\ta = this.children(\".jvectormap-container\").data(\"mapObject\");\r\n\t\tif (\"addMap\" === t) jvm.Map.maps[arguments[1]] = arguments[2];\r\n\t\telse {\r\n\t\t\tif ((\"set\" === t || \"get\" === t) && s[t][arguments[1]]) return (e = arguments[1].charAt(0).toUpperCase() + arguments[1].substr(1)), a[t + e].apply(a, Array.prototype.slice.call(arguments, 2));\r\n\t\t\t((t = t || {}).container = this), (a = new jvm.Map(t));\r\n\t\t}\r\n\t\treturn this;\r\n\t};\r\n})(jQuery),\r\n\t(function (t) {\r\n\t\t\"function\" == typeof define && define.amd ? define([\"jquery\"], t) : \"object\" == typeof exports ? (module.exports = t) : t(jQuery);\r\n\t})(function (h) {\r\n\t\tvar o,\r\n\t\t\tl,\r\n\t\t\tt = [\"wheel\", \"mousewheel\", \"DOMMouseScroll\", \"MozMousePixelScroll\"],\r\n\t\t\te = \"onwheel\" in document || 9 <= document.documentMode ? [\"wheel\"] : [\"mousewheel\", \"DomMouseScroll\", \"MozMousePixelScroll\"],\r\n\t\t\tm = Array.prototype.slice;\r\n\t\tif (h.event.fixHooks) for (var a = t.length; a; ) h.event.fixHooks[t[--a]] = h.event.mouseHooks;\r\n\t\tvar s = (h.event.special.mousewheel = {\r\n\t\t\tversion: \"3.1.9\",\r\n\t\t\tsetup: function () {\r\n\t\t\t\tif (this.addEventListener) for (var t = e.length; t; ) this.addEventListener(e[--t], i, !1);\r\n\t\t\t\telse this.onmousewheel = i;\r\n\t\t\t\th.data(this, \"mousewheel-line-height\", s.getLineHeight(this)), h.data(this, \"mousewheel-page-height\", s.getPageHeight(this));\r\n\t\t\t},\r\n\t\t\tteardown: function () {\r\n\t\t\t\tif (this.removeEventListener) for (var t = e.length; t; ) this.removeEventListener(e[--t], i, !1);\r\n\t\t\t\telse this.onmousewheel = null;\r\n\t\t\t},\r\n\t\t\tgetLineHeight: function (t) {\r\n\t\t\t\treturn parseInt(h(t)[\"offsetParent\" in h.fn ? \"offsetParent\" : \"parent\"]().css(\"fontSize\"), 10);\r\n\t\t\t},\r\n\t\t\tgetPageHeight: function (t) {\r\n\t\t\t\treturn h(t).height();\r\n\t\t\t},\r\n\t\t\tsettings: { adjustOldDeltas: !0 },\r\n\t\t});\r\n\t\tfunction i(t) {\r\n\t\t\tvar e,\r\n\t\t\t\ta = t || window.event,\r\n\t\t\t\ts = m.call(arguments, 1),\r\n\t\t\t\ti = 0,\r\n\t\t\t\tn = 0,\r\n\t\t\t\tr = 0;\r\n\t\t\tif ((((t = h.event.fix(a)).type = \"mousewheel\"), \"detail\" in a && (r = -1 * a.detail), \"wheelDelta\" in a && (r = a.wheelDelta), \"wheelDeltaY\" in a && (r = a.wheelDeltaY), \"wheelDeltaX\" in a && (n = -1 * a.wheelDeltaX), \"axis\" in a && a.axis === a.HORIZONTAL_AXIS && ((n = -1 * r), (r = 0)), (i = 0 === r ? n : r), \"deltaY\" in a && (i = r = -1 * a.deltaY), \"deltaX\" in a && ((n = a.deltaX), 0 === r && (i = -1 * n)), 0 !== r || 0 !== n)) return 1 === a.deltaMode ? ((i *= e = h.data(this, \"mousewheel-line-height\")), (r *= e), (n *= e)) : 2 === a.deltaMode && ((i *= e = h.data(this, \"mousewheel-page-height\")), (r *= e), (n *= e)), (e = Math.max(Math.abs(r), Math.abs(n))), (!l || e < l) && p(a, (l = e)) && (l /= 40), p(a, e) && ((i /= 40), (n /= 40), (r /= 40)), (i = Math[1 <= i ? \"floor\" : \"ceil\"](i / l)), (n = Math[1 <= n ? \"floor\" : \"ceil\"](n / l)), (r = Math[1 <= r ? \"floor\" : \"ceil\"](r / l)), (t.deltaX = n), (t.deltaY = r), (t.deltaFactor = l), (t.deltaMode = 0), s.unshift(t, i, n, r), o && clearTimeout(o), (o = setTimeout(c, 200)), (h.event.dispatch || h.event.handle).apply(this, s);\r\n\t\t}\r\n\t\tfunction c() {\r\n\t\t\tl = null;\r\n\t\t}\r\n\t\tfunction p(t, e) {\r\n\t\t\treturn s.settings.adjustOldDeltas && \"mousewheel\" === t.type && e % 120 == 0;\r\n\t\t}\r\n\t\th.fn.extend({\r\n\t\t\tmousewheel: function (t) {\r\n\t\t\t\treturn t ? this.bind(\"mousewheel\", t) : this.trigger(\"mousewheel\");\r\n\t\t\t},\r\n\t\t\tunmousewheel: function (t) {\r\n\t\t\t\treturn this.unbind(\"mousewheel\", t);\r\n\t\t\t},\r\n\t\t});\r\n\t});\r\nvar jvm = {\r\n\tinherits: function (t, e) {\r\n\t\tfunction a() {}\r\n\t\t(a.prototype = e.prototype), (t.prototype = new a()), ((t.prototype.constructor = t).parentClass = e);\r\n\t},\r\n\tmixin: function (t, e) {\r\n\t\tfor (var a in e.prototype) e.prototype.hasOwnProperty(a) && (t.prototype[a] = e.prototype[a]);\r\n\t},\r\n\tmin: function (t) {\r\n\t\tvar e,\r\n\t\t\ta = Number.MAX_VALUE;\r\n\t\tif (t instanceof Array) for (e = 0; e < t.length; e++) t[e] < a && (a = t[e]);\r\n\t\telse for (e in t) t[e] < a && (a = t[e]);\r\n\t\treturn a;\r\n\t},\r\n\tmax: function (t) {\r\n\t\tvar e,\r\n\t\t\ta = Number.MIN_VALUE;\r\n\t\tif (t instanceof Array) for (e = 0; e < t.length; e++) t[e] > a && (a = t[e]);\r\n\t\telse for (e in t) t[e] > a && (a = t[e]);\r\n\t\treturn a;\r\n\t},\r\n\tkeys: function (t) {\r\n\t\tvar e,\r\n\t\t\ta = [];\r\n\t\tfor (e in t) a.push(e);\r\n\t\treturn a;\r\n\t},\r\n\tvalues: function (t) {\r\n\t\tfor (var e, a = [], s = 0; s < arguments.length; s++) for (e in (t = arguments[s])) a.push(t[e]);\r\n\t\treturn a;\r\n\t},\r\n\twhenImageLoaded: function (t) {\r\n\t\tvar e = new jvm.$.Deferred(),\r\n\t\t\ta = jvm.$(\"<img/>\");\r\n\t\treturn (\r\n\t\t\ta\r\n\t\t\t\t.error(function () {\r\n\t\t\t\t\te.reject();\r\n\t\t\t\t})\r\n\t\t\t\t.load(function () {\r\n\t\t\t\t\te.resolve(a);\r\n\t\t\t\t}),\r\n\t\t\ta.attr(\"src\", t),\r\n\t\t\te\r\n\t\t);\r\n\t},\r\n\tisImageUrl: function (t) {\r\n\t\treturn /\\.\\w{3,4}$/.test(t);\r\n\t},\r\n};\r\n(jvm.$ = jQuery),\r\n\tArray.prototype.indexOf ||\r\n\t\t(Array.prototype.indexOf = function (t, e) {\r\n\t\t\tvar a;\r\n\t\t\tif (null == this) throw new TypeError('\"this\" is null or not defined');\r\n\t\t\tvar s = Object(this),\r\n\t\t\t\ti = s.length >>> 0;\r\n\t\t\tif (0 == i) return -1;\r\n\t\t\tif (((e = +e || 0), i <= (e = Math.abs(e) === 1 / 0 ? 0 : e))) return -1;\r\n\t\t\tfor (a = Math.max(0 <= e ? e : i - Math.abs(e), 0); a < i; ) {\r\n\t\t\t\tif (a in s && s[a] === t) return a;\r\n\t\t\t\ta++;\r\n\t\t\t}\r\n\t\t\treturn -1;\r\n\t\t}),\r\n\t(jvm.AbstractElement = function (t, e) {\r\n\t\t(this.node = this.createElement(t)), (this.name = t), (this.properties = {}), e && this.set(e);\r\n\t}),\r\n\t(jvm.AbstractElement.prototype.set = function (t, e) {\r\n\t\tif (\"object\" == typeof t) for (var a in t) (this.properties[a] = t[a]), this.applyAttr(a, t[a]);\r\n\t\telse (this.properties[t] = e), this.applyAttr(t, e);\r\n\t}),\r\n\t(jvm.AbstractElement.prototype.get = function (t) {\r\n\t\treturn this.properties[t];\r\n\t}),\r\n\t(jvm.AbstractElement.prototype.applyAttr = function (t, e) {\r\n\t\tthis.node.setAttribute(t, e);\r\n\t}),\r\n\t(jvm.AbstractElement.prototype.remove = function () {\r\n\t\tjvm.$(this.node).remove();\r\n\t}),\r\n\t(jvm.AbstractCanvasElement = function (t, e, a) {\r\n\t\t(this.container = t), this.setSize(e, a), (this.rootElement = new jvm[this.classPrefix + \"GroupElement\"]()), this.node.appendChild(this.rootElement.node), this.container.appendChild(this.node);\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.add = function (t, e) {\r\n\t\t(e = e || this.rootElement).add(t), (t.canvas = this);\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.addPath = function (t, e, a) {\r\n\t\treturn (e = new jvm[this.classPrefix + \"PathElement\"](t, e)), this.add(e, a), e;\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.addCircle = function (t, e, a) {\r\n\t\treturn (e = new jvm[this.classPrefix + \"CircleElement\"](t, e)), this.add(e, a), e;\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.addImage = function (t, e, a) {\r\n\t\treturn (e = new jvm[this.classPrefix + \"ImageElement\"](t, e)), this.add(e, a), e;\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.addText = function (t, e, a) {\r\n\t\treturn (e = new jvm[this.classPrefix + \"TextElement\"](t, e)), this.add(e, a), e;\r\n\t}),\r\n\t(jvm.AbstractCanvasElement.prototype.addGroup = function (t) {\r\n\t\tvar e = new jvm[this.classPrefix + \"GroupElement\"]();\r\n\t\treturn (t || this).node.appendChild(e.node), (e.canvas = this), e;\r\n\t}),\r\n\t(jvm.AbstractShapeElement = function (t, e, a) {\r\n\t\t(this.style = a || {}), (this.style.current = this.style.current || {}), (this.isHovered = !1), (this.isSelected = !1), this.updateStyle();\r\n\t}),\r\n\t(jvm.AbstractShapeElement.prototype.setStyle = function (t, e) {\r\n\t\tvar a = {};\r\n\t\t\"object\" == typeof t ? (a = t) : (a[t] = e), jvm.$.extend(this.style.current, a), this.updateStyle();\r\n\t}),\r\n\t(jvm.AbstractShapeElement.prototype.updateStyle = function () {\r\n\t\tvar t = {};\r\n\t\tjvm.AbstractShapeElement.mergeStyles(t, this.style.initial), jvm.AbstractShapeElement.mergeStyles(t, this.style.current), this.isHovered && jvm.AbstractShapeElement.mergeStyles(t, this.style.hover), this.isSelected && (jvm.AbstractShapeElement.mergeStyles(t, this.style.selected), this.isHovered && jvm.AbstractShapeElement.mergeStyles(t, this.style.selectedHover)), this.set(t);\r\n\t}),\r\n\t(jvm.AbstractShapeElement.mergeStyles = function (t, e) {\r\n\t\tfor (var a in (e = e || {})) null === e[a] ? delete t[a] : (t[a] = e[a]);\r\n\t}),\r\n\t(jvm.SVGElement = function (t, e) {\r\n\t\tjvm.SVGElement.parentClass.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGElement, jvm.AbstractElement),\r\n\t(jvm.SVGElement.svgns = \"http://www.w3.org/2000/svg\"),\r\n\t(jvm.SVGElement.prototype.createElement = function (t) {\r\n\t\treturn document.createElementNS(jvm.SVGElement.svgns, t);\r\n\t}),\r\n\t(jvm.SVGElement.prototype.addClass = function (t) {\r\n\t\tthis.node.setAttribute(\"class\", t);\r\n\t}),\r\n\t(jvm.SVGElement.prototype.getElementCtr = function (t) {\r\n\t\treturn jvm[\"SVG\" + t];\r\n\t}),\r\n\t(jvm.SVGElement.prototype.getBBox = function () {\r\n\t\treturn this.node.getBBox();\r\n\t}),\r\n\t(jvm.SVGGroupElement = function () {\r\n\t\tjvm.SVGGroupElement.parentClass.call(this, \"g\");\r\n\t}),\r\n\tjvm.inherits(jvm.SVGGroupElement, jvm.SVGElement),\r\n\t(jvm.SVGGroupElement.prototype.add = function (t) {\r\n\t\tthis.node.appendChild(t.node);\r\n\t}),\r\n\t(jvm.SVGCanvasElement = function (t, e, a) {\r\n\t\t(this.classPrefix = \"SVG\"), jvm.SVGCanvasElement.parentClass.call(this, \"svg\"), (this.defsElement = new jvm.SVGElement(\"defs\")), this.node.appendChild(this.defsElement.node), jvm.AbstractCanvasElement.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGCanvasElement, jvm.SVGElement),\r\n\tjvm.mixin(jvm.SVGCanvasElement, jvm.AbstractCanvasElement),\r\n\t(jvm.SVGCanvasElement.prototype.setSize = function (t, e) {\r\n\t\t(this.width = t), (this.height = e), this.node.setAttribute(\"width\", t), this.node.setAttribute(\"height\", e);\r\n\t}),\r\n\t(jvm.SVGCanvasElement.prototype.applyTransformParams = function (t, e, a) {\r\n\t\t(this.scale = t), (this.transX = e), (this.transY = a), this.rootElement.node.setAttribute(\"transform\", \"scale(\" + t + \") translate(\" + e + \", \" + a + \")\");\r\n\t}),\r\n\t(jvm.SVGShapeElement = function (t, e, a) {\r\n\t\tjvm.SVGShapeElement.parentClass.call(this, t, e), jvm.AbstractShapeElement.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGShapeElement, jvm.SVGElement),\r\n\tjvm.mixin(jvm.SVGShapeElement, jvm.AbstractShapeElement),\r\n\t(jvm.SVGShapeElement.prototype.applyAttr = function (t, e) {\r\n\t\tvar a,\r\n\t\t\ts,\r\n\t\t\ti = this;\r\n\t\t\"fill\" === t && jvm.isImageUrl(e)\r\n\t\t\t? jvm.SVGShapeElement.images[e]\r\n\t\t\t\t? this.applyAttr(\"fill\", \"url(#image\" + jvm.SVGShapeElement.images[e] + \")\")\r\n\t\t\t\t: jvm.whenImageLoaded(e).then(function (t) {\r\n\t\t\t\t\t\t(s = new jvm.SVGElement(\"image\")).node.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", e), s.applyAttr(\"x\", \"0\"), s.applyAttr(\"y\", \"0\"), s.applyAttr(\"width\", t[0].width), s.applyAttr(\"height\", t[0].height), (a = new jvm.SVGElement(\"pattern\")).applyAttr(\"id\", \"image\" + jvm.SVGShapeElement.imageCounter), a.applyAttr(\"x\", 0), a.applyAttr(\"y\", 0), a.applyAttr(\"width\", t[0].width / 2), a.applyAttr(\"height\", t[0].height / 2), a.applyAttr(\"viewBox\", \"0 0 \" + t[0].width + \" \" + t[0].height), a.applyAttr(\"patternUnits\", \"userSpaceOnUse\"), a.node.appendChild(s.node), i.canvas.defsElement.node.appendChild(a.node), (jvm.SVGShapeElement.images[e] = jvm.SVGShapeElement.imageCounter++), i.applyAttr(\"fill\", \"url(#image\" + jvm.SVGShapeElement.images[e] + \")\");\r\n\t\t\t\t  })\r\n\t\t\t: jvm.SVGShapeElement.parentClass.prototype.applyAttr.apply(this, arguments);\r\n\t}),\r\n\t(jvm.SVGShapeElement.imageCounter = 1),\r\n\t(jvm.SVGShapeElement.images = {}),\r\n\t(jvm.SVGPathElement = function (t, e) {\r\n\t\tjvm.SVGPathElement.parentClass.call(this, \"path\", t, e), this.node.setAttribute(\"fill-rule\", \"evenodd\");\r\n\t}),\r\n\tjvm.inherits(jvm.SVGPathElement, jvm.SVGShapeElement),\r\n\t(jvm.SVGCircleElement = function (t, e) {\r\n\t\tjvm.SVGCircleElement.parentClass.call(this, \"circle\", t, e);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGCircleElement, jvm.SVGShapeElement),\r\n\t(jvm.SVGImageElement = function (t, e) {\r\n\t\tjvm.SVGImageElement.parentClass.call(this, \"image\", t, e);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGImageElement, jvm.SVGShapeElement),\r\n\t(jvm.SVGImageElement.prototype.applyAttr = function (t, e) {\r\n\t\tvar a = this;\r\n\t\t\"image\" == t\r\n\t\t\t? jvm.whenImageLoaded(e).then(function (t) {\r\n\t\t\t\t\ta.node.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", e), (a.width = t[0].width), (a.height = t[0].height), a.applyAttr(\"width\", a.width), a.applyAttr(\"height\", a.height), a.applyAttr(\"x\", a.cx - a.width / 2), a.applyAttr(\"y\", a.cy - a.height / 2), jvm.$(a.node).trigger(\"imageloaded\", [t]);\r\n\t\t\t  })\r\n\t\t\t: \"cx\" == t\r\n\t\t\t? ((this.cx = e), this.width && this.applyAttr(\"x\", e - this.width / 2))\r\n\t\t\t: \"cy\" == t\r\n\t\t\t? ((this.cy = e), this.height && this.applyAttr(\"y\", e - this.height / 2))\r\n\t\t\t: jvm.SVGImageElement.parentClass.prototype.applyAttr.apply(this, arguments);\r\n\t}),\r\n\t(jvm.SVGTextElement = function (t, e) {\r\n\t\tjvm.SVGTextElement.parentClass.call(this, \"text\", t, e);\r\n\t}),\r\n\tjvm.inherits(jvm.SVGTextElement, jvm.SVGShapeElement),\r\n\t(jvm.SVGTextElement.prototype.applyAttr = function (t, e) {\r\n\t\t\"text\" === t ? (this.node.textContent = e) : jvm.SVGTextElement.parentClass.prototype.applyAttr.apply(this, arguments);\r\n\t}),\r\n\t(jvm.VMLElement = function (t, e) {\r\n\t\tjvm.VMLElement.VMLInitialized || jvm.VMLElement.initializeVML(), jvm.VMLElement.parentClass.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.VMLElement, jvm.AbstractElement),\r\n\t(jvm.VMLElement.VMLInitialized = !1),\r\n\t(jvm.VMLElement.initializeVML = function () {\r\n\t\ttry {\r\n\t\t\tdocument.namespaces.rvml || document.namespaces.add(\"rvml\", \"urn:schemas-microsoft-com:vml\"),\r\n\t\t\t\t(jvm.VMLElement.prototype.createElement = function (t) {\r\n\t\t\t\t\treturn document.createElement(\"<rvml:\" + t + ' class=\"rvml\">');\r\n\t\t\t\t});\r\n\t\t} catch (t) {\r\n\t\t\tjvm.VMLElement.prototype.createElement = function (t) {\r\n\t\t\t\treturn document.createElement(\"<\" + t + ' xmlns=\"urn:schemas-microsoft.com:vml\" class=\"rvml\">');\r\n\t\t\t};\r\n\t\t}\r\n\t\tdocument.createStyleSheet().addRule(\".rvml\", \"behavior:url(#default#VML)\"), (jvm.VMLElement.VMLInitialized = !0);\r\n\t}),\r\n\t(jvm.VMLElement.prototype.getElementCtr = function (t) {\r\n\t\treturn jvm[\"VML\" + t];\r\n\t}),\r\n\t(jvm.VMLElement.prototype.addClass = function (t) {\r\n\t\tjvm.$(this.node).addClass(t);\r\n\t}),\r\n\t(jvm.VMLElement.prototype.applyAttr = function (t, e) {\r\n\t\tthis.node[t] = e;\r\n\t}),\r\n\t(jvm.VMLElement.prototype.getBBox = function () {\r\n\t\tvar t = jvm.$(this.node);\r\n\t\treturn { x: t.position().left / this.canvas.scale, y: t.position().top / this.canvas.scale, width: t.width() / this.canvas.scale, height: t.height() / this.canvas.scale };\r\n\t}),\r\n\t(jvm.VMLGroupElement = function () {\r\n\t\tjvm.VMLGroupElement.parentClass.call(this, \"group\"), (this.node.style.left = \"0px\"), (this.node.style.top = \"0px\"), (this.node.coordorigin = \"0 0\");\r\n\t}),\r\n\tjvm.inherits(jvm.VMLGroupElement, jvm.VMLElement),\r\n\t(jvm.VMLGroupElement.prototype.add = function (t) {\r\n\t\tthis.node.appendChild(t.node);\r\n\t}),\r\n\t(jvm.VMLCanvasElement = function (t, e, a) {\r\n\t\t(this.classPrefix = \"VML\"), jvm.VMLCanvasElement.parentClass.call(this, \"group\"), jvm.AbstractCanvasElement.apply(this, arguments), (this.node.style.position = \"absolute\");\r\n\t}),\r\n\tjvm.inherits(jvm.VMLCanvasElement, jvm.VMLElement),\r\n\tjvm.mixin(jvm.VMLCanvasElement, jvm.AbstractCanvasElement),\r\n\t(jvm.VMLCanvasElement.prototype.setSize = function (t, e) {\r\n\t\tvar a, s, i, n;\r\n\t\tif (((this.width = t), (this.height = e), (this.node.style.width = t + \"px\"), (this.node.style.height = e + \"px\"), (this.node.coordsize = t + \" \" + e), (this.node.coordorigin = \"0 0\"), this.rootElement)) {\r\n\t\t\tfor (i = 0, n = (a = this.rootElement.node.getElementsByTagName(\"shape\")).length; i < n; i++) (a[i].coordsize = t + \" \" + e), (a[i].style.width = t + \"px\"), (a[i].style.height = e + \"px\");\r\n\t\t\tfor (i = 0, n = (s = this.node.getElementsByTagName(\"group\")).length; i < n; i++) (s[i].coordsize = t + \" \" + e), (s[i].style.width = t + \"px\"), (s[i].style.height = e + \"px\");\r\n\t\t}\r\n\t}),\r\n\t(jvm.VMLCanvasElement.prototype.applyTransformParams = function (t, e, a) {\r\n\t\t(this.scale = t), (this.transX = e), (this.transY = a), (this.rootElement.node.coordorigin = this.width - e - this.width / 100 + \",\" + (this.height - a - this.height / 100)), (this.rootElement.node.coordsize = this.width / t + \",\" + this.height / t);\r\n\t}),\r\n\t(jvm.VMLShapeElement = function (t, e) {\r\n\t\tjvm.VMLShapeElement.parentClass.call(this, t, e), (this.fillElement = new jvm.VMLElement(\"fill\")), (this.strokeElement = new jvm.VMLElement(\"stroke\")), this.node.appendChild(this.fillElement.node), this.node.appendChild(this.strokeElement.node), (this.node.stroked = !1), jvm.AbstractShapeElement.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.VMLShapeElement, jvm.VMLElement),\r\n\tjvm.mixin(jvm.VMLShapeElement, jvm.AbstractShapeElement),\r\n\t(jvm.VMLShapeElement.prototype.applyAttr = function (t, e) {\r\n\t\tswitch (t) {\r\n\t\t\tcase \"fill\":\r\n\t\t\t\tthis.node.fillcolor = e;\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"fill-opacity\":\r\n\t\t\t\tthis.fillElement.node.opacity = Math.round(100 * e) + \"%\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"stroke\":\r\n\t\t\t\t(this.node.stroked = \"none\" !== e), (this.node.strokecolor = e);\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"stroke-opacity\":\r\n\t\t\t\tthis.strokeElement.node.opacity = Math.round(100 * e) + \"%\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"stroke-width\":\r\n\t\t\t\t0 === parseInt(e, 10) ? (this.node.stroked = !1) : (this.node.stroked = !0), (this.node.strokeweight = e);\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"d\":\r\n\t\t\t\tthis.node.path = jvm.VMLPathElement.pathSvgToVml(e);\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tjvm.VMLShapeElement.parentClass.prototype.applyAttr.apply(this, arguments);\r\n\t\t}\r\n\t}),\r\n\t(jvm.VMLPathElement = function (t, e) {\r\n\t\tvar a = new jvm.VMLElement(\"skew\");\r\n\t\tjvm.VMLPathElement.parentClass.call(this, \"shape\", t, e), (this.node.coordorigin = \"0 0\"), (a.node.on = !0), (a.node.matrix = \"0.01,0,0,0.01,0,0\"), (a.node.offset = \"0,0\"), this.node.appendChild(a.node);\r\n\t}),\r\n\tjvm.inherits(jvm.VMLPathElement, jvm.VMLShapeElement),\r\n\t(jvm.VMLPathElement.prototype.applyAttr = function (t, e) {\r\n\t\t\"d\" === t ? (this.node.path = jvm.VMLPathElement.pathSvgToVml(e)) : jvm.VMLShapeElement.prototype.applyAttr.call(this, t, e);\r\n\t}),\r\n\t(jvm.VMLPathElement.pathSvgToVml = function (t) {\r\n\t\tvar r,\r\n\t\t\th,\r\n\t\t\to = 0,\r\n\t\t\tl = 0;\r\n\t\treturn (t = t.replace(/(-?\\d+)e(-?\\d+)/g, \"0\"))\r\n\t\t\t.replace(/([MmLlHhVvCcSs])\\s*((?:-?\\d*(?:\\.\\d+)?\\s*,?\\s*)+)/g, function (t, e, a, s) {\r\n\t\t\t\t(a = a.replace(/(\\d)-/g, \"$1,-\").replace(/^\\s+/g, \"\").replace(/\\s+$/g, \"\").replace(/\\s+/g, \",\").split(\",\"))[0] || a.shift();\r\n\t\t\t\tfor (var i = 0, n = a.length; i < n; i++) a[i] = Math.round(100 * a[i]);\r\n\t\t\t\tswitch (e) {\r\n\t\t\t\t\tcase \"m\":\r\n\t\t\t\t\t\treturn (o += a[0]), (l += a[1]), \"t\" + a.join(\",\");\r\n\t\t\t\t\tcase \"M\":\r\n\t\t\t\t\t\treturn (o = a[0]), (l = a[1]), \"m\" + a.join(\",\");\r\n\t\t\t\t\tcase \"l\":\r\n\t\t\t\t\t\treturn (o += a[0]), (l += a[1]), \"r\" + a.join(\",\");\r\n\t\t\t\t\tcase \"L\":\r\n\t\t\t\t\t\treturn (o = a[0]), (l = a[1]), \"l\" + a.join(\",\");\r\n\t\t\t\t\tcase \"h\":\r\n\t\t\t\t\t\treturn (o += a[0]), \"r\" + a[0] + \",0\";\r\n\t\t\t\t\tcase \"H\":\r\n\t\t\t\t\t\treturn \"l\" + (o = a[0]) + \",\" + l;\r\n\t\t\t\t\tcase \"v\":\r\n\t\t\t\t\t\treturn (l += a[0]), \"r0,\" + a[0];\r\n\t\t\t\t\tcase \"V\":\r\n\t\t\t\t\t\treturn (l = a[0]), \"l\" + o + \",\" + l;\r\n\t\t\t\t\tcase \"c\":\r\n\t\t\t\t\t\treturn (r = o + a[a.length - 4]), (h = l + a[a.length - 3]), (o += a[a.length - 2]), (l += a[a.length - 1]), \"v\" + a.join(\",\");\r\n\t\t\t\t\tcase \"C\":\r\n\t\t\t\t\t\treturn (r = a[a.length - 4]), (h = a[a.length - 3]), (o = a[a.length - 2]), (l = a[a.length - 1]), \"c\" + a.join(\",\");\r\n\t\t\t\t\tcase \"s\":\r\n\t\t\t\t\t\treturn a.unshift(l - h), a.unshift(o - r), (r = o + a[a.length - 4]), (h = l + a[a.length - 3]), (o += a[a.length - 2]), (l += a[a.length - 1]), \"v\" + a.join(\",\");\r\n\t\t\t\t\tcase \"S\":\r\n\t\t\t\t\t\treturn a.unshift(l + l - h), a.unshift(o + o - r), (r = a[a.length - 4]), (h = a[a.length - 3]), (o = a[a.length - 2]), (l = a[a.length - 1]), \"c\" + a.join(\",\");\r\n\t\t\t\t}\r\n\t\t\t\treturn \"\";\r\n\t\t\t})\r\n\t\t\t.replace(/z/g, \"e\");\r\n\t}),\r\n\t(jvm.VMLCircleElement = function (t, e) {\r\n\t\tjvm.VMLCircleElement.parentClass.call(this, \"oval\", t, e);\r\n\t}),\r\n\tjvm.inherits(jvm.VMLCircleElement, jvm.VMLShapeElement),\r\n\t(jvm.VMLCircleElement.prototype.applyAttr = function (t, e) {\r\n\t\tswitch (t) {\r\n\t\t\tcase \"r\":\r\n\t\t\t\t(this.node.style.width = 2 * e + \"px\"), (this.node.style.height = 2 * e + \"px\"), this.applyAttr(\"cx\", this.get(\"cx\") || 0), this.applyAttr(\"cy\", this.get(\"cy\") || 0);\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"cx\":\r\n\t\t\t\tif (!e) return;\r\n\t\t\t\tthis.node.style.left = e - (this.get(\"r\") || 0) + \"px\";\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"cy\":\r\n\t\t\t\tif (!e) return;\r\n\t\t\t\tthis.node.style.top = e - (this.get(\"r\") || 0) + \"px\";\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tjvm.VMLCircleElement.parentClass.prototype.applyAttr.call(this, t, e);\r\n\t\t}\r\n\t}),\r\n\t(jvm.VectorCanvas = function (t, e, a) {\r\n\t\treturn (this.mode = window.SVGAngle ? \"svg\" : \"vml\"), \"svg\" == this.mode ? (this.impl = new jvm.SVGCanvasElement(t, e, a)) : (this.impl = new jvm.VMLCanvasElement(t, e, a)), (this.impl.mode = this.mode), this.impl;\r\n\t}),\r\n\t(jvm.SimpleScale = function (t) {\r\n\t\tthis.scale = t;\r\n\t}),\r\n\t(jvm.SimpleScale.prototype.getValue = function (t) {\r\n\t\treturn t;\r\n\t}),\r\n\t(jvm.OrdinalScale = function (t) {\r\n\t\tthis.scale = t;\r\n\t}),\r\n\t(jvm.OrdinalScale.prototype.getValue = function (t) {\r\n\t\treturn this.scale[t];\r\n\t}),\r\n\t(jvm.OrdinalScale.prototype.getTicks = function () {\r\n\t\tvar t,\r\n\t\t\te = [];\r\n\t\tfor (t in this.scale) e.push({ label: t, value: this.scale[t] });\r\n\t\treturn e;\r\n\t}),\r\n\t(jvm.NumericScale = function (t, e, a, s) {\r\n\t\t(this.scale = []), (e = e || \"linear\"), t && this.setScale(t), e && this.setNormalizeFunction(e), void 0 !== a && this.setMin(a), void 0 !== s && this.setMax(s);\r\n\t}),\r\n\t(jvm.NumericScale.prototype = {\r\n\t\tsetMin: function (t) {\r\n\t\t\t(this.clearMinValue = t), \"function\" == typeof this.normalize ? (this.minValue = this.normalize(t)) : (this.minValue = t);\r\n\t\t},\r\n\t\tsetMax: function (t) {\r\n\t\t\t(this.clearMaxValue = t), \"function\" == typeof this.normalize ? (this.maxValue = this.normalize(t)) : (this.maxValue = t);\r\n\t\t},\r\n\t\tsetScale: function (t) {\r\n\t\t\tvar e;\r\n\t\t\tfor (this.scale = [], e = 0; e < t.length; e++) this.scale[e] = [t[e]];\r\n\t\t},\r\n\t\tsetNormalizeFunction: function (t) {\r\n\t\t\t\"polynomial\" === t\r\n\t\t\t\t? (this.normalize = function (t) {\r\n\t\t\t\t\t\treturn Math.pow(t, 0.2);\r\n\t\t\t\t  })\r\n\t\t\t\t: \"linear\" === t\r\n\t\t\t\t? delete this.normalize\r\n\t\t\t\t: (this.normalize = t),\r\n\t\t\t\tthis.setMin(this.clearMinValue),\r\n\t\t\t\tthis.setMax(this.clearMaxValue);\r\n\t\t},\r\n\t\tgetValue: function (t) {\r\n\t\t\tvar e,\r\n\t\t\t\ta,\r\n\t\t\t\ts = [],\r\n\t\t\t\ti = 0,\r\n\t\t\t\tn = 0;\r\n\t\t\tfor (\"function\" == typeof this.normalize && (t = this.normalize(t)), n = 0; n < this.scale.length - 1; n++) (e = this.vectorLength(this.vectorSubtract(this.scale[n + 1], this.scale[n]))), s.push(e), (i += e);\r\n\t\t\tfor (a = (this.maxValue - this.minValue) / i, n = 0; n < s.length; n++) s[n] *= a;\r\n\t\t\tfor (n = 0, t -= this.minValue; 0 <= t - s[n]; ) (t -= s[n]), n++;\r\n\t\t\treturn n == this.scale.length - 1 ? this.vectorToNum(this.scale[n]) : this.vectorToNum(this.vectorAdd(this.scale[n], this.vectorMult(this.vectorSubtract(this.scale[n + 1], this.scale[n]), t / s[n])));\r\n\t\t},\r\n\t\tvectorToNum: function (t) {\r\n\t\t\tfor (var e = 0, a = 0; a < t.length; a++) e += Math.round(t[a]) * Math.pow(256, t.length - a - 1);\r\n\t\t\treturn e;\r\n\t\t},\r\n\t\tvectorSubtract: function (t, e) {\r\n\t\t\tfor (var a = [], s = 0; s < t.length; s++) a[s] = t[s] - e[s];\r\n\t\t\treturn a;\r\n\t\t},\r\n\t\tvectorAdd: function (t, e) {\r\n\t\t\tfor (var a = [], s = 0; s < t.length; s++) a[s] = t[s] + e[s];\r\n\t\t\treturn a;\r\n\t\t},\r\n\t\tvectorMult: function (t, e) {\r\n\t\t\tfor (var a = [], s = 0; s < t.length; s++) a[s] = t[s] * e;\r\n\t\t\treturn a;\r\n\t\t},\r\n\t\tvectorLength: function (t) {\r\n\t\t\tfor (var e = 0, a = 0; a < t.length; a++) e += t[a] * t[a];\r\n\t\t\treturn Math.sqrt(e);\r\n\t\t},\r\n\t\tgetTicks: function () {\r\n\t\t\tvar t,\r\n\t\t\t\te,\r\n\t\t\t\ta = [this.clearMinValue, this.clearMaxValue],\r\n\t\t\t\ts = a[1] - a[0],\r\n\t\t\t\ti = Math.pow(10, Math.floor(Math.log(s / 5) / Math.LN10)),\r\n\t\t\t\tn = [];\r\n\t\t\tfor ((s = (5 / s) * i) <= 0.15 ? (i *= 10) : s <= 0.35 ? (i *= 5) : s <= 0.75 && (i *= 2), a[0] = Math.floor(a[0] / i) * i, a[1] = Math.ceil(a[1] / i) * i, t = a[0]; t <= a[1]; ) (e = t == a[0] ? this.clearMinValue : t == a[1] ? this.clearMaxValue : t), n.push({ label: t, value: this.getValue(e) }), (t += i);\r\n\t\t\treturn n;\r\n\t\t},\r\n\t}),\r\n\t(jvm.ColorScale = function (t, e, a, s) {\r\n\t\tjvm.ColorScale.parentClass.apply(this, arguments);\r\n\t}),\r\n\tjvm.inherits(jvm.ColorScale, jvm.NumericScale),\r\n\t(jvm.ColorScale.prototype.setScale = function (t) {\r\n\t\tfor (var e = 0; e < t.length; e++) this.scale[e] = jvm.ColorScale.rgbToArray(t[e]);\r\n\t}),\r\n\t(jvm.ColorScale.prototype.getValue = function (t) {\r\n\t\treturn jvm.ColorScale.numToRgb(jvm.ColorScale.parentClass.prototype.getValue.call(this, t));\r\n\t}),\r\n\t(jvm.ColorScale.arrayToRgb = function (t) {\r\n\t\tfor (var e, a = \"#\", s = 0; s < t.length; s++) a += 1 == (e = t[s].toString(16)).length ? \"0\" + e : e;\r\n\t\treturn a;\r\n\t}),\r\n\t(jvm.ColorScale.numToRgb = function (t) {\r\n\t\tfor (t = t.toString(16); t.length < 6; ) t = \"0\" + t;\r\n\t\treturn \"#\" + t;\r\n\t}),\r\n\t(jvm.ColorScale.rgbToArray = function (t) {\r\n\t\treturn (t = t.substr(1)), [parseInt(t.substr(0, 2), 16), parseInt(t.substr(2, 2), 16), parseInt(t.substr(4, 2), 16)];\r\n\t}),\r\n\t(jvm.Legend = function (t) {\r\n\t\t(this.params = t || {}), (this.map = this.params.map), (this.series = this.params.series), (this.body = jvm.$(\"<div/>\")), this.body.addClass(\"jvectormap-legend\"), this.params.cssClass && this.body.addClass(this.params.cssClass), (t.vertical ? this.map.legendCntVertical : this.map.legendCntHorizontal).append(this.body), this.render();\r\n\t}),\r\n\t(jvm.Legend.prototype.render = function () {\r\n\t\tvar t,\r\n\t\t\te,\r\n\t\t\ta,\r\n\t\t\ts,\r\n\t\t\ti = this.series.scale.getTicks(),\r\n\t\t\tn = jvm.$(\"<div/>\").addClass(\"jvectormap-legend-inner\");\r\n\t\tfor (this.body.html(\"\"), this.params.title && this.body.append(jvm.$(\"<div/>\").addClass(\"jvectormap-legend-title\").html(this.params.title)), this.body.append(n), t = 0; t < i.length; t++) {\r\n\t\t\tswitch (((e = jvm.$(\"<div/>\").addClass(\"jvectormap-legend-tick\")), (a = jvm.$(\"<div/>\").addClass(\"jvectormap-legend-tick-sample\")), this.series.params.attribute)) {\r\n\t\t\t\tcase \"fill\":\r\n\t\t\t\t\tjvm.isImageUrl(i[t].value) ? a.css(\"background\", \"url(\" + i[t].value + \")\") : a.css(\"background\", i[t].value);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"stroke\":\r\n\t\t\t\t\ta.css(\"background\", i[t].value);\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"image\":\r\n\t\t\t\t\ta.css(\"background\", \"url(\" + i[t].value + \") no-repeat center center\");\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase \"r\":\r\n\t\t\t\t\tjvm.$(\"<div/>\")\r\n\t\t\t\t\t\t.css({ \"border-radius\": i[t].value, border: this.map.params.markerStyle.initial[\"stroke-width\"] + \"px \" + this.map.params.markerStyle.initial.stroke + \" solid\", width: 2 * i[t].value + \"px\", height: 2 * i[t].value + \"px\", background: this.map.params.markerStyle.initial.fill })\r\n\t\t\t\t\t\t.appendTo(a);\r\n\t\t\t}\r\n\t\t\te.append(a), (s = i[t].label), this.params.labelRender && (s = this.params.labelRender(s)), e.append(jvm.$(\"<div>\" + s + \" </div>\").addClass(\"jvectormap-legend-tick-text\")), n.append(e);\r\n\t\t}\r\n\t\tn.append(jvm.$(\"<div/>\").css(\"clear\", \"both\"));\r\n\t}),\r\n\t(jvm.DataSeries = function (t, e, a) {\r\n\t\t((t = t || {}).attribute = t.attribute || \"fill\"), (this.elements = e), (this.params = t), (this.map = a), t.attributes && this.setAttributes(t.attributes), jvm.$.isArray(t.scale) ? ((a = \"fill\" === t.attribute || \"stroke\" === t.attribute ? jvm.ColorScale : jvm.NumericScale), (this.scale = new a(t.scale, t.normalizeFunction, t.min, t.max))) : t.scale ? (this.scale = new jvm.OrdinalScale(t.scale)) : (this.scale = new jvm.SimpleScale(t.scale)), (this.values = t.values || {}), this.setValues(this.values), this.params.legend && (this.legend = new jvm.Legend($.extend({ map: this.map, series: this }, this.params.legend)));\r\n\t}),\r\n\t(jvm.DataSeries.prototype = {\r\n\t\tsetAttributes: function (t, e) {\r\n\t\t\tvar a,\r\n\t\t\t\ts = t;\r\n\t\t\tif (\"string\" == typeof t) this.elements[t] && this.elements[t].setStyle(this.params.attribute, e);\r\n\t\t\telse for (a in s) this.elements[a] && this.elements[a].element.setStyle(this.params.attribute, s[a]);\r\n\t\t},\r\n\t\tsetValues: function (t) {\r\n\t\t\tvar e,\r\n\t\t\t\ta,\r\n\t\t\t\ts = -Number.MAX_VALUE,\r\n\t\t\t\ti = Number.MAX_VALUE,\r\n\t\t\t\tn = {};\r\n\t\t\tif (this.scale instanceof jvm.OrdinalScale || this.scale instanceof jvm.SimpleScale) for (a in t) t[a] ? (n[a] = this.scale.getValue(t[a])) : (n[a] = this.elements[a].element.style.initial[this.params.attribute]);\r\n\t\t\telse {\r\n\t\t\t\tif (void 0 === this.params.min || void 0 === this.params.max) for (a in t) s < (e = parseFloat(t[a])) && (s = e), e < i && (i = e);\r\n\t\t\t\tfor (a in (void 0 === this.params.min ? (this.scale.setMin(i), (this.params.min = i)) : this.scale.setMin(this.params.min), void 0 === this.params.max ? (this.scale.setMax(s), (this.params.max = s)) : this.scale.setMax(this.params.max), t)) \"indexOf\" != a && ((e = parseFloat(t[a])), isNaN(e) ? (n[a] = this.elements[a].element.style.initial[this.params.attribute]) : (n[a] = this.scale.getValue(e)));\r\n\t\t\t}\r\n\t\t\tthis.setAttributes(n), jvm.$.extend(this.values, t);\r\n\t\t},\r\n\t\tclear: function () {\r\n\t\t\tvar t,\r\n\t\t\t\te = {};\r\n\t\t\tfor (t in this.values) this.elements[t] && (e[t] = this.elements[t].element.shape.style.initial[this.params.attribute]);\r\n\t\t\tthis.setAttributes(e), (this.values = {});\r\n\t\t},\r\n\t\tsetScale: function (t) {\r\n\t\t\tthis.scale.setScale(t), this.values && this.setValues(this.values);\r\n\t\t},\r\n\t\tsetNormalizeFunction: function (t) {\r\n\t\t\tthis.scale.setNormalizeFunction(t), this.values && this.setValues(this.values);\r\n\t\t},\r\n\t}),\r\n\t(jvm.Proj = {\r\n\t\tdegRad: 180 / Math.PI,\r\n\t\tradDeg: Math.PI / 180,\r\n\t\tradius: 6381372,\r\n\t\tsgn: function (t) {\r\n\t\t\treturn 0 < t ? 1 : t < 0 ? -1 : t;\r\n\t\t},\r\n\t\tmill: function (t, e, a) {\r\n\t\t\treturn { x: this.radius * (e - a) * this.radDeg, y: (-this.radius * Math.log(Math.tan((45 + 0.4 * t) * this.radDeg))) / 0.8 };\r\n\t\t},\r\n\t\tmill_inv: function (t, e, a) {\r\n\t\t\treturn { lat: (2.5 * Math.atan(Math.exp((0.8 * e) / this.radius)) - (5 * Math.PI) / 8) * this.degRad, lng: (a * this.radDeg + t / this.radius) * this.degRad };\r\n\t\t},\r\n\t\tmerc: function (t, e, a) {\r\n\t\t\treturn { x: this.radius * (e - a) * this.radDeg, y: -this.radius * Math.log(Math.tan(Math.PI / 4 + (t * Math.PI) / 360)) };\r\n\t\t},\r\n\t\tmerc_inv: function (t, e, a) {\r\n\t\t\treturn { lat: (2 * Math.atan(Math.exp(e / this.radius)) - Math.PI / 2) * this.degRad, lng: (a * this.radDeg + t / this.radius) * this.degRad };\r\n\t\t},\r\n\t\taea: function (t, e, a) {\r\n\t\t\tvar s = a * this.radDeg,\r\n\t\t\t\ti = 29.5 * this.radDeg,\r\n\t\t\t\ta = 45.5 * this.radDeg,\r\n\t\t\t\tt = t * this.radDeg,\r\n\t\t\t\te = e * this.radDeg,\r\n\t\t\t\ta = (Math.sin(i) + Math.sin(a)) / 2,\r\n\t\t\t\ti = Math.cos(i) * Math.cos(i) + 2 * a * Math.sin(i),\r\n\t\t\t\ts = a * (e - s),\r\n\t\t\t\tt = Math.sqrt(i - 2 * a * Math.sin(t)) / a,\r\n\t\t\t\ta = Math.sqrt(i - 2 * a * Math.sin(0)) / a;\r\n\t\t\treturn { x: t * Math.sin(s) * this.radius, y: -(a - t * Math.cos(s)) * this.radius };\r\n\t\t},\r\n\t\taea_inv: function (t, e, a) {\r\n\t\t\tvar s = t / this.radius,\r\n\t\t\t\ti = e / this.radius,\r\n\t\t\t\tn = a * this.radDeg,\r\n\t\t\t\tr = 29.5 * this.radDeg,\r\n\t\t\t\tt = 45.5 * this.radDeg,\r\n\t\t\t\te = (Math.sin(r) + Math.sin(t)) / 2,\r\n\t\t\t\ta = Math.cos(r) * Math.cos(r) + 2 * e * Math.sin(r),\r\n\t\t\t\tt = Math.sqrt(a - 2 * e * Math.sin(0)) / e,\r\n\t\t\t\tr = Math.sqrt(s * s + (t - i) * (t - i)),\r\n\t\t\t\ti = Math.atan(s / (t - i));\r\n\t\t\treturn { lat: Math.asin((a - r * r * e * e) / (2 * e)) * this.degRad, lng: (n + i / e) * this.degRad };\r\n\t\t},\r\n\t\tlcc: function (t, e, a) {\r\n\t\t\tvar s = a * this.radDeg,\r\n\t\t\t\ti = e * this.radDeg,\r\n\t\t\t\ta = 33 * this.radDeg,\r\n\t\t\t\te = 45 * this.radDeg,\r\n\t\t\t\tt = t * this.radDeg,\r\n\t\t\t\te = Math.log(Math.cos(a) * (1 / Math.cos(e))) / Math.log(Math.tan(Math.PI / 4 + e / 2) * (1 / Math.tan(Math.PI / 4 + a / 2))),\r\n\t\t\t\tt = (a = (Math.cos(a) * Math.pow(Math.tan(Math.PI / 4 + a / 2), e)) / e) * Math.pow(1 / Math.tan(Math.PI / 4 + t / 2), e),\r\n\t\t\t\ta = a * Math.pow(1 / Math.tan(Math.PI / 4 + 0), e);\r\n\t\t\treturn { x: t * Math.sin(e * (i - s)) * this.radius, y: -(a - t * Math.cos(e * (i - s))) * this.radius };\r\n\t\t},\r\n\t\tlcc_inv: function (t, e, a) {\r\n\t\t\tvar s = t / this.radius,\r\n\t\t\t\ti = e / this.radius,\r\n\t\t\t\tn = a * this.radDeg,\r\n\t\t\t\tr = 33 * this.radDeg,\r\n\t\t\t\tt = 45 * this.radDeg,\r\n\t\t\t\te = Math.log(Math.cos(r) * (1 / Math.cos(t))) / Math.log(Math.tan(Math.PI / 4 + t / 2) * (1 / Math.tan(Math.PI / 4 + r / 2))),\r\n\t\t\t\tt = (a = (Math.cos(r) * Math.pow(Math.tan(Math.PI / 4 + r / 2), e)) / e) * Math.pow(1 / Math.tan(Math.PI / 4 + 0), e),\r\n\t\t\t\tr = this.sgn(e) * Math.sqrt(s * s + (t - i) * (t - i)),\r\n\t\t\t\ti = Math.atan(s / (t - i));\r\n\t\t\treturn { lat: (2 * Math.atan(Math.pow(a / r, 1 / e)) - Math.PI / 2) * this.degRad, lng: (n + i / e) * this.degRad };\r\n\t\t},\r\n\t}),\r\n\t(jvm.MapObject = function (t) {}),\r\n\t(jvm.MapObject.prototype.getLabelText = function (t) {\r\n\t\treturn this.config.label ? (\"function\" == typeof this.config.label.render ? this.config.label.render(t) : t) : null;\r\n\t}),\r\n\t(jvm.MapObject.prototype.getLabelOffsets = function (t) {\r\n\t\tvar e;\r\n\t\treturn this.config.label && (\"function\" == typeof this.config.label.offsets ? (e = this.config.label.offsets(t)) : \"object\" == typeof this.config.label.offsets && (e = this.config.label.offsets[t])), e || [0, 0];\r\n\t}),\r\n\t(jvm.MapObject.prototype.setHovered = function (t) {\r\n\t\tthis.isHovered !== t && ((this.isHovered = t), (this.shape.isHovered = t), this.shape.updateStyle(), this.label && ((this.label.isHovered = t), this.label.updateStyle()));\r\n\t}),\r\n\t(jvm.MapObject.prototype.setSelected = function (t) {\r\n\t\tthis.isSelected !== t && ((this.isSelected = t), (this.shape.isSelected = t), this.shape.updateStyle(), this.label && ((this.label.isSelected = t), this.label.updateStyle()), jvm.$(this.shape).trigger(\"selected\", [t]));\r\n\t}),\r\n\t(jvm.MapObject.prototype.setStyle = function () {\r\n\t\tthis.shape.setStyle.apply(this.shape, arguments);\r\n\t}),\r\n\t(jvm.MapObject.prototype.remove = function () {\r\n\t\tthis.shape.remove(), this.label && this.label.remove();\r\n\t}),\r\n\t(jvm.Region = function (t) {\r\n\t\tvar e, a, s;\r\n\t\t(this.config = t), (this.map = this.config.map), (this.shape = t.canvas.addPath({ d: t.path, \"data-code\": t.code }, t.style, t.canvas.rootElement)), this.shape.addClass(\"jvectormap-region jvectormap-element\"), (e = this.shape.getBBox()), (a = this.getLabelText(t.code)), this.config.label && a && ((s = this.getLabelOffsets(t.code)), (this.labelX = e.x + e.width / 2 + s[0]), (this.labelY = e.y + e.height / 2 + s[1]), (this.label = t.canvas.addText({ text: a, \"text-anchor\": \"middle\", \"alignment-baseline\": \"central\", x: this.labelX, y: this.labelY, \"data-code\": t.code }, t.labelStyle, t.labelsGroup)), this.label.addClass(\"jvectormap-region jvectormap-element\"));\r\n\t}),\r\n\tjvm.inherits(jvm.Region, jvm.MapObject),\r\n\t(jvm.Region.prototype.updateLabelPosition = function () {\r\n\t\tthis.label && this.label.set({ x: this.labelX * this.map.scale + this.map.transX * this.map.scale, y: this.labelY * this.map.scale + this.map.transY * this.map.scale });\r\n\t}),\r\n\t(jvm.Marker = function (t) {\r\n\t\tvar e;\r\n\t\t(this.config = t), (this.map = this.config.map), (this.isImage = !!this.config.style.initial.image), this.createShape(), (e = this.getLabelText(t.index)), this.config.label && e && ((this.offsets = this.getLabelOffsets(t.index)), (this.labelX = t.cx / this.map.scale - this.map.transX), (this.labelY = t.cy / this.map.scale - this.map.transY), (this.label = t.canvas.addText({ text: e, \"data-index\": t.index, dy: \"0.6ex\", x: this.labelX, y: this.labelY }, t.labelStyle, t.labelsGroup)), this.label.addClass(\"jvectormap-marker jvectormap-element\"));\r\n\t}),\r\n\tjvm.inherits(jvm.Marker, jvm.MapObject),\r\n\t(jvm.Marker.prototype.createShape = function () {\r\n\t\tvar t = this;\r\n\t\tthis.shape && this.shape.remove(),\r\n\t\t\t(this.shape = this.config.canvas[this.isImage ? \"addImage\" : \"addCircle\"]({ \"data-index\": this.config.index, cx: this.config.cx, cy: this.config.cy }, this.config.style, this.config.group)),\r\n\t\t\tthis.shape.addClass(\"jvectormap-marker jvectormap-element\"),\r\n\t\t\tthis.isImage &&\r\n\t\t\t\tjvm.$(this.shape.node).on(\"imageloaded\", function () {\r\n\t\t\t\t\tt.updateLabelPosition();\r\n\t\t\t\t});\r\n\t}),\r\n\t(jvm.Marker.prototype.updateLabelPosition = function () {\r\n\t\tthis.label && this.label.set({ x: this.labelX * this.map.scale + this.offsets[0] + this.map.transX * this.map.scale + 5 + (this.isImage ? (this.shape.width || 0) / 2 : this.shape.properties.r), y: this.labelY * this.map.scale + this.map.transY * this.map.scale + this.offsets[1] });\r\n\t}),\r\n\t(jvm.Marker.prototype.setStyle = function (t, e) {\r\n\t\tvar a;\r\n\t\tjvm.Marker.parentClass.prototype.setStyle.apply(this, arguments), \"r\" === t && this.updateLabelPosition(), (a = !!this.shape.get(\"image\")) != this.isImage && ((this.isImage = a), (this.config.style = jvm.$.extend(!0, {}, this.shape.style)), this.createShape());\r\n\t}),\r\n\t(jvm.Map = function (t) {\r\n\t\tvar e,\r\n\t\t\ta = this;\r\n\t\tif (((this.params = jvm.$.extend(!0, {}, jvm.Map.defaultParams, t)), !jvm.Map.maps[this.params.map])) throw new Error(\"Attempt to use map which was not loaded: \" + this.params.map);\r\n\t\tfor (e in ((this.mapData = jvm.Map.maps[this.params.map]),\r\n\t\t(this.markers = {}),\r\n\t\t(this.regions = {}),\r\n\t\t(this.regionsColors = {}),\r\n\t\t(this.regionsData = {}),\r\n\t\t(this.container = jvm.$(\"<div>\").addClass(\"jvectormap-container\")),\r\n\t\tthis.params.container && this.params.container.append(this.container),\r\n\t\tthis.container.data(\"mapObject\", this),\r\n\t\t(this.defaultWidth = this.mapData.width),\r\n\t\t(this.defaultHeight = this.mapData.height),\r\n\t\tthis.setBackgroundColor(this.params.backgroundColor),\r\n\t\t(this.onResize = function () {\r\n\t\t\ta.updateSize();\r\n\t\t}),\r\n\t\tjvm.$(window).resize(this.onResize),\r\n\t\tjvm.Map.apiEvents))\r\n\t\t\tthis.params[e] && this.container.bind(jvm.Map.apiEvents[e] + \".jvectormap\", this.params[e]);\r\n\t\t(this.canvas = new jvm.VectorCanvas(this.container[0], this.width, this.height)), (\"ontouchstart\" in window || (window.DocumentTouch && document instanceof DocumentTouch)) && this.params.bindTouchEvents && this.bindContainerTouchEvents(), this.bindContainerEvents(), this.bindElementEvents(), this.createTip(), this.params.zoomButtons && this.bindZoomButtons(), this.createRegions(), this.createMarkers(this.params.markers || {}), this.updateSize(), this.params.focusOn && (\"string\" == typeof this.params.focusOn ? (this.params.focusOn = { region: this.params.focusOn }) : jvm.$.isArray(this.params.focusOn) && (this.params.focusOn = { regions: this.params.focusOn }), this.setFocus(this.params.focusOn)), this.params.selectedRegions && this.setSelectedRegions(this.params.selectedRegions), this.params.selectedMarkers && this.setSelectedMarkers(this.params.selectedMarkers), (this.legendCntHorizontal = jvm.$(\"<div/>\").addClass(\"jvectormap-legend-cnt jvectormap-legend-cnt-h\")), (this.legendCntVertical = jvm.$(\"<div/>\").addClass(\"jvectormap-legend-cnt jvectormap-legend-cnt-v\")), this.container.append(this.legendCntHorizontal), this.container.append(this.legendCntVertical), this.params.series && this.createSeries();\r\n\t}),\r\n\t(jvm.Map.prototype = {\r\n\t\ttransX: 0,\r\n\t\ttransY: 0,\r\n\t\tscale: 1,\r\n\t\tbaseTransX: 0,\r\n\t\tbaseTransY: 0,\r\n\t\tbaseScale: 1,\r\n\t\twidth: 0,\r\n\t\theight: 0,\r\n\t\tsetBackgroundColor: function (t) {\r\n\t\t\tthis.container.css(\"background-color\", t);\r\n\t\t},\r\n\t\tresize: function () {\r\n\t\t\tvar t = this.baseScale;\r\n\t\t\tthis.width / this.height > this.defaultWidth / this.defaultHeight ? ((this.baseScale = this.height / this.defaultHeight), (this.baseTransX = Math.abs(this.width - this.defaultWidth * this.baseScale) / (2 * this.baseScale))) : ((this.baseScale = this.width / this.defaultWidth), (this.baseTransY = Math.abs(this.height - this.defaultHeight * this.baseScale) / (2 * this.baseScale))), (this.scale *= this.baseScale / t), (this.transX *= this.baseScale / t), (this.transY *= this.baseScale / t);\r\n\t\t},\r\n\t\tupdateSize: function () {\r\n\t\t\t(this.width = this.container.width()), (this.height = this.container.height()), this.resize(), this.canvas.setSize(this.width, this.height), this.applyTransform();\r\n\t\t},\r\n\t\treset: function () {\r\n\t\t\tvar t, e;\r\n\t\t\tfor (t in this.series) for (e = 0; e < this.series[t].length; e++) this.series[t][e].clear();\r\n\t\t\t(this.scale = this.baseScale), (this.transX = this.baseTransX), (this.transY = this.baseTransY), this.applyTransform();\r\n\t\t},\r\n\t\tapplyTransform: function () {\r\n\t\t\tvar t,\r\n\t\t\t\te,\r\n\t\t\t\ta = this.defaultWidth * this.scale <= this.width ? ((t = (this.width - this.defaultWidth * this.scale) / (2 * this.scale)), (this.width - this.defaultWidth * this.scale) / (2 * this.scale)) : ((t = 0), (this.width - this.defaultWidth * this.scale) / this.scale),\r\n\t\t\t\ts = this.defaultHeight * this.scale <= this.height ? ((e = (this.height - this.defaultHeight * this.scale) / (2 * this.scale)), (this.height - this.defaultHeight * this.scale) / (2 * this.scale)) : ((e = 0), (this.height - this.defaultHeight * this.scale) / this.scale);\r\n\t\t\tthis.transY > e ? (this.transY = e) : this.transY < s && (this.transY = s), this.transX > t ? (this.transX = t) : this.transX < a && (this.transX = a), this.canvas.applyTransformParams(this.scale, this.transX, this.transY), this.markers && this.repositionMarkers(), this.repositionLabels(), this.container.trigger(\"viewportChange\", [this.scale / this.baseScale, this.transX, this.transY]);\r\n\t\t},\r\n\t\tbindContainerEvents: function () {\r\n\t\t\tvar e,\r\n\t\t\t\ta,\r\n\t\t\t\ts = !1,\r\n\t\t\t\th = this;\r\n\t\t\tthis.params.panOnDrag &&\r\n\t\t\t\t(this.container\r\n\t\t\t\t\t.mousemove(function (t) {\r\n\t\t\t\t\t\treturn s && ((h.transX -= (e - t.pageX) / h.scale), (h.transY -= (a - t.pageY) / h.scale), h.applyTransform(), (e = t.pageX), (a = t.pageY)), !1;\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.mousedown(function (t) {\r\n\t\t\t\t\t\treturn (s = !0), (e = t.pageX), (a = t.pageY), !1;\r\n\t\t\t\t\t}),\r\n\t\t\t\t(this.onContainerMouseUp = function () {\r\n\t\t\t\t\ts = !1;\r\n\t\t\t\t}),\r\n\t\t\t\tjvm.$(\"body\").mouseup(this.onContainerMouseUp)),\r\n\t\t\t\tthis.params.zoomOnScroll &&\r\n\t\t\t\t\tthis.container.mousewheel(function (t, e, a, s) {\r\n\t\t\t\t\t\tvar i = jvm.$(h.container).offset(),\r\n\t\t\t\t\t\t\tn = t.pageX - i.left,\r\n\t\t\t\t\t\t\tr = t.pageY - i.top,\r\n\t\t\t\t\t\t\ti = Math.pow(1 + h.params.zoomOnScrollSpeed / 1e3, t.deltaFactor * t.deltaY);\r\n\t\t\t\t\t\th.tip.hide(), h.setScale(h.scale * i, n, r), t.preventDefault();\r\n\t\t\t\t\t});\r\n\t\t},\r\n\t\tbindContainerTouchEvents: function () {\r\n\t\t\tfunction t(t) {\r\n\t\t\t\tvar e,\r\n\t\t\t\t\ta,\r\n\t\t\t\t\ts = t.originalEvent.touches;\r\n\t\t\t\t\"touchstart\" == t.type && (m = 0), 1 == s.length ? (1 == m && ((e = c.transX), (a = c.transY), (c.transX -= (r - s[0].pageX) / c.scale), (c.transY -= (h - s[0].pageY) / c.scale), c.applyTransform(), c.tip.hide(), (e == c.transX && a == c.transY) || t.preventDefault()), (r = s[0].pageX), (h = s[0].pageY)) : 2 == s.length && (2 == m ? ((a = Math.sqrt(Math.pow(s[0].pageX - s[1].pageX, 2) + Math.pow(s[0].pageY - s[1].pageY, 2)) / n), c.setScale(i * a, o, l), c.tip.hide(), t.preventDefault()) : ((t = jvm.$(c.container).offset()), (o = s[0].pageX > s[1].pageX ? s[1].pageX + (s[0].pageX - s[1].pageX) / 2 : s[0].pageX + (s[1].pageX - s[0].pageX) / 2), (l = s[0].pageY > s[1].pageY ? s[1].pageY + (s[0].pageY - s[1].pageY) / 2 : s[0].pageY + (s[1].pageY - s[0].pageY) / 2), (o -= t.left), (l -= t.top), (i = c.scale), (n = Math.sqrt(Math.pow(s[0].pageX - s[1].pageX, 2) + Math.pow(s[0].pageY - s[1].pageY, 2))))), (m = s.length);\r\n\t\t\t}\r\n\t\t\tvar i,\r\n\t\t\t\tn,\r\n\t\t\t\tr,\r\n\t\t\t\th,\r\n\t\t\t\to,\r\n\t\t\t\tl,\r\n\t\t\t\tm,\r\n\t\t\t\tc = this;\r\n\t\t\tjvm.$(this.container).bind(\"touchstart\", t), jvm.$(this.container).bind(\"touchmove\", t);\r\n\t\t},\r\n\t\tbindElementEvents: function () {\r\n\t\t\tvar i,\r\n\t\t\t\th = this;\r\n\t\t\tthis.container.mousemove(function () {\r\n\t\t\t\ti = !0;\r\n\t\t\t}),\r\n\t\t\t\tthis.container.delegate(\"[class~='jvectormap-element']\", \"mouseover mouseout\", function (t) {\r\n\t\t\t\t\tvar e = -1 === (jvm.$(this).attr(\"class\").baseVal || jvm.$(this).attr(\"class\")).indexOf(\"jvectormap-region\") ? \"marker\" : \"region\",\r\n\t\t\t\t\t\ta = \"region\" == e ? jvm.$(this).attr(\"data-code\") : jvm.$(this).attr(\"data-index\"),\r\n\t\t\t\t\t\ts = (\"region\" == e ? h.regions : h.markers)[a].element,\r\n\t\t\t\t\t\ti = \"region\" == e ? h.mapData.paths[a].name : h.markers[a].config.name || \"\",\r\n\t\t\t\t\t\tn = jvm.$.Event(e + \"TipShow.jvectormap\"),\r\n\t\t\t\t\t\tr = jvm.$.Event(e + \"Over.jvectormap\");\r\n\t\t\t\t\t\"mouseover\" == t.type ? (h.container.trigger(r, [a]), r.isDefaultPrevented() || s.setHovered(!0), h.tip.text(i), h.container.trigger(n, [h.tip, a]), n.isDefaultPrevented() || (h.tip.show(), (h.tipWidth = h.tip.width()), (h.tipHeight = h.tip.height()))) : (s.setHovered(!1), h.tip.hide(), h.container.trigger(e + \"Out.jvectormap\", [a]));\r\n\t\t\t\t}),\r\n\t\t\t\tthis.container.delegate(\"[class~='jvectormap-element']\", \"mousedown\", function () {\r\n\t\t\t\t\ti = !1;\r\n\t\t\t\t}),\r\n\t\t\t\tthis.container.delegate(\"[class~='jvectormap-element']\", \"mouseup\", function () {\r\n\t\t\t\t\tvar t = -1 === (jvm.$(this).attr(\"class\").baseVal ? jvm.$(this).attr(\"class\").baseVal : jvm.$(this).attr(\"class\")).indexOf(\"jvectormap-region\") ? \"marker\" : \"region\",\r\n\t\t\t\t\t\te = \"region\" == t ? jvm.$(this).attr(\"data-code\") : jvm.$(this).attr(\"data-index\"),\r\n\t\t\t\t\t\ta = jvm.$.Event(t + \"Click.jvectormap\"),\r\n\t\t\t\t\t\ts = (\"region\" == t ? h.regions : h.markers)[e].element;\r\n\t\t\t\t\ti || (h.container.trigger(a, [e]), ((\"region\" == t && h.params.regionsSelectable) || (\"marker\" == t && h.params.markersSelectable)) && (a.isDefaultPrevented() || (h.params[t + \"sSelectableOne\"] && h.clearSelected(t + \"s\"), s.setSelected(!s.isSelected))));\r\n\t\t\t\t});\r\n\t\t},\r\n\t\tbindZoomButtons: function () {\r\n\t\t\tvar t = this;\r\n\t\t\tjvm.$(\"<div/>\").addClass(\"jvectormap-zoomin\").text(\"+\").appendTo(this.container),\r\n\t\t\t\tjvm.$(\"<div/>\").addClass(\"jvectormap-zoomout\").html(\"&#x2212;\").appendTo(this.container),\r\n\t\t\t\tthis.container.find(\".jvectormap-zoomin\").click(function () {\r\n\t\t\t\t\tt.setScale(t.scale * t.params.zoomStep, t.width / 2, t.height / 2, !1, t.params.zoomAnimate);\r\n\t\t\t\t}),\r\n\t\t\t\tthis.container.find(\".jvectormap-zoomout\").click(function () {\r\n\t\t\t\t\tt.setScale(t.scale / t.params.zoomStep, t.width / 2, t.height / 2, !1, t.params.zoomAnimate);\r\n\t\t\t\t});\r\n\t\t},\r\n\t\tcreateTip: function () {\r\n\t\t\tvar s = this;\r\n\t\t\t(this.tip = jvm.$(\"<div/>\").addClass(\"jvectormap-tip\").appendTo(jvm.$(\"body\"))),\r\n\t\t\t\tthis.container.mousemove(function (t) {\r\n\t\t\t\t\tvar e = t.pageX - 15 - s.tipWidth,\r\n\t\t\t\t\t\ta = t.pageY - 15 - s.tipHeight;\r\n\t\t\t\t\te < 5 && (e = t.pageX + 15), a < 5 && (a = t.pageY + 15), s.tip.css({ left: e, top: a });\r\n\t\t\t\t});\r\n\t\t},\r\n\t\tsetScale: function (t, e, a, s, i) {\r\n\t\t\tvar n,\r\n\t\t\t\tr,\r\n\t\t\t\th,\r\n\t\t\t\to,\r\n\t\t\t\tl,\r\n\t\t\t\tm,\r\n\t\t\t\tc,\r\n\t\t\t\tp,\r\n\t\t\t\td,\r\n\t\t\t\tv = jvm.$.Event(\"zoom.jvectormap\"),\r\n\t\t\t\tu = this,\r\n\t\t\t\tg = 0,\r\n\t\t\t\tf = Math.abs(Math.round((60 * (t - this.scale)) / Math.max(t, this.scale))),\r\n\t\t\t\tj = new jvm.$.Deferred();\r\n\t\t\treturn (\r\n\t\t\t\tt > this.params.zoomMax * this.baseScale ? (t = this.params.zoomMax * this.baseScale) : t < this.params.zoomMin * this.baseScale && (t = this.params.zoomMin * this.baseScale),\r\n\t\t\t\tvoid 0 !== e && void 0 !== a && ((zoomStep = t / this.scale), (d = s ? ((p = e + (this.defaultWidth * (this.width / (this.defaultWidth * t))) / 2), a + (this.defaultHeight * (this.height / (this.defaultHeight * t))) / 2) : ((p = this.transX - ((zoomStep - 1) / t) * e), this.transY - ((zoomStep - 1) / t) * a))),\r\n\t\t\t\ti && 0 < f\r\n\t\t\t\t\t? ((r = this.scale),\r\n\t\t\t\t\t  (h = (t - r) / f),\r\n\t\t\t\t\t  (o = this.transX * this.scale),\r\n\t\t\t\t\t  (m = this.transY * this.scale),\r\n\t\t\t\t\t  (l = (p * t - o) / f),\r\n\t\t\t\t\t  (c = (d * t - m) / f),\r\n\t\t\t\t\t  (n = setInterval(function () {\r\n\t\t\t\t\t\t\t(g += 1), (u.scale = r + h * g), (u.transX = (o + l * g) / u.scale), (u.transY = (m + c * g) / u.scale), u.applyTransform(), g == f && (clearInterval(n), u.container.trigger(v, [t / u.baseScale]), j.resolve());\r\n\t\t\t\t\t  }, 10)))\r\n\t\t\t\t\t: ((this.transX = p), (this.transY = d), (this.scale = t), this.applyTransform(), this.container.trigger(v, [t / this.baseScale]), j.resolve()),\r\n\t\t\t\tj\r\n\t\t\t);\r\n\t\t},\r\n\t\tsetFocus: function (t) {\r\n\t\t\tvar e, a, s, i, n;\r\n\t\t\tif (((t = t || {}).region ? (s = [t.region]) : t.regions && (s = t.regions), s)) {\r\n\t\t\t\tfor (i = 0; i < s.length; i++) this.regions[s[i]] && (a = this.regions[s[i]].element.shape.getBBox()) && (e = void 0 === e ? a : { x: Math.min(e.x, a.x), y: Math.min(e.y, a.y), width: Math.max(e.x + e.width, a.x + a.width) - Math.min(e.x, a.x), height: Math.max(e.y + e.height, a.y + a.height) - Math.min(e.y, a.y) });\r\n\t\t\t\treturn this.setScale(Math.min(this.width / e.width, this.height / e.height), -(e.x + e.width / 2), -(e.y + e.height / 2), !0, t.animate);\r\n\t\t\t}\r\n\t\t\treturn t.lat && t.lng ? ((n = this.latLngToPoint(t.lat, t.lng)), (t.x = this.transX - n.x / this.scale), (t.y = this.transY - n.y / this.scale)) : t.x && t.y && ((t.x *= -this.defaultWidth), (t.y *= -this.defaultHeight)), this.setScale(t.scale * this.baseScale, t.x, t.y, !0, t.animate);\r\n\t\t},\r\n\t\tgetSelected: function (t) {\r\n\t\t\tvar e,\r\n\t\t\t\ta = [];\r\n\t\t\tfor (e in this[t]) this[t][e].element.isSelected && a.push(e);\r\n\t\t\treturn a;\r\n\t\t},\r\n\t\tgetSelectedRegions: function () {\r\n\t\t\treturn this.getSelected(\"regions\");\r\n\t\t},\r\n\t\tgetSelectedMarkers: function () {\r\n\t\t\treturn this.getSelected(\"markers\");\r\n\t\t},\r\n\t\tsetSelected: function (t, e) {\r\n\t\t\tif ((\"object\" != typeof e && (e = [e]), jvm.$.isArray(e))) for (a = 0; a < e.length; a++) this[t][e[a]].element.setSelected(!0);\r\n\t\t\telse for (var a in e) this[t][a].element.setSelected(!!e[a]);\r\n\t\t},\r\n\t\tsetSelectedRegions: function (t) {\r\n\t\t\tthis.setSelected(\"regions\", t);\r\n\t\t},\r\n\t\tsetSelectedMarkers: function (t) {\r\n\t\t\tthis.setSelected(\"markers\", t);\r\n\t\t},\r\n\t\tclearSelected: function (t) {\r\n\t\t\tfor (var e = {}, a = this.getSelected(t), s = 0; s < a.length; s++) e[a[s]] = !1;\r\n\t\t\tthis.setSelected(t, e);\r\n\t\t},\r\n\t\tclearSelectedRegions: function () {\r\n\t\t\tthis.clearSelected(\"regions\");\r\n\t\t},\r\n\t\tclearSelectedMarkers: function () {\r\n\t\t\tthis.clearSelected(\"markers\");\r\n\t\t},\r\n\t\tgetMapObject: function () {\r\n\t\t\treturn this;\r\n\t\t},\r\n\t\tgetRegionName: function (t) {\r\n\t\t\treturn this.mapData.paths[t].name;\r\n\t\t},\r\n\t\tcreateRegions: function () {\r\n\t\t\tvar t,\r\n\t\t\t\te,\r\n\t\t\t\ta = this;\r\n\t\t\tfor (t in ((this.regionLabelsGroup = this.regionLabelsGroup || this.canvas.addGroup()), this.mapData.paths))\r\n\t\t\t\t(e = new jvm.Region({ map: this, path: this.mapData.paths[t].path, code: t, style: jvm.$.extend(!0, {}, this.params.regionStyle), labelStyle: jvm.$.extend(!0, {}, this.params.regionLabelStyle), canvas: this.canvas, labelsGroup: this.regionLabelsGroup, label: \"vml\" != this.canvas.mode ? this.params.labels && this.params.labels.regions : null })),\r\n\t\t\t\t\tjvm.$(e.shape).bind(\"selected\", function (t, e) {\r\n\t\t\t\t\t\ta.container.trigger(\"regionSelected.jvectormap\", [jvm.$(this.node).attr(\"data-code\"), e, a.getSelectedRegions()]);\r\n\t\t\t\t\t}),\r\n\t\t\t\t\t(this.regions[t] = { element: e, config: this.mapData.paths[t] });\r\n\t\t},\r\n\t\tcreateMarkers: function (t) {\r\n\t\t\tvar e,\r\n\t\t\t\ta,\r\n\t\t\t\ts,\r\n\t\t\t\ti,\r\n\t\t\t\tn = this;\r\n\t\t\tif (((this.markersGroup = this.markersGroup || this.canvas.addGroup()), (this.markerLabelsGroup = this.markerLabelsGroup || this.canvas.addGroup()), jvm.$.isArray(t))) for (i = t.slice(), t = {}, e = 0; e < i.length; e++) t[e] = i[e];\r\n\t\t\tfor (e in t)\r\n\t\t\t\t(s = t[e] instanceof Array ? { latLng: t[e] } : t[e]),\r\n\t\t\t\t\t!1 !== (a = this.getMarkerPosition(s)) &&\r\n\t\t\t\t\t\t((a = new jvm.Marker({ map: this, style: jvm.$.extend(!0, {}, this.params.markerStyle, { initial: s.style || {} }), labelStyle: jvm.$.extend(!0, {}, this.params.markerLabelStyle), index: e, cx: a.x, cy: a.y, group: this.markersGroup, canvas: this.canvas, labelsGroup: this.markerLabelsGroup, label: \"vml\" != this.canvas.mode ? this.params.labels && this.params.labels.markers : null })),\r\n\t\t\t\t\t\tjvm.$(a.shape).bind(\"selected\", function (t, e) {\r\n\t\t\t\t\t\t\tn.container.trigger(\"markerSelected.jvectormap\", [jvm.$(this.node).attr(\"data-index\"), e, n.getSelectedMarkers()]);\r\n\t\t\t\t\t\t}),\r\n\t\t\t\t\t\tthis.markers[e] && this.removeMarkers([e]),\r\n\t\t\t\t\t\t(this.markers[e] = { element: a, config: s }));\r\n\t\t},\r\n\t\trepositionMarkers: function () {\r\n\t\t\tvar t, e;\r\n\t\t\tfor (t in this.markers) !1 !== (e = this.getMarkerPosition(this.markers[t].config)) && this.markers[t].element.setStyle({ cx: e.x, cy: e.y });\r\n\t\t},\r\n\t\trepositionLabels: function () {\r\n\t\t\tfor (var t in this.regions) this.regions[t].element.updateLabelPosition();\r\n\t\t\tfor (t in this.markers) this.markers[t].element.updateLabelPosition();\r\n\t\t},\r\n\t\tgetMarkerPosition: function (t) {\r\n\t\t\treturn jvm.Map.maps[this.params.map].projection ? this.latLngToPoint.apply(this, t.latLng || [0, 0]) : { x: t.coords[0] * this.scale + this.transX * this.scale, y: t.coords[1] * this.scale + this.transY * this.scale };\r\n\t\t},\r\n\t\taddMarker: function (t, e, a) {\r\n\t\t\tvar s,\r\n\t\t\t\ti,\r\n\t\t\t\tn = {},\r\n\t\t\t\tr = [],\r\n\t\t\t\ta = a || [];\r\n\t\t\tfor (n[t] = e, i = 0; i < a.length; i++) (s = {}), void 0 !== a[i] && (s[t] = a[i]), r.push(s);\r\n\t\t\tthis.addMarkers(n, r);\r\n\t\t},\r\n\t\taddMarkers: function (t, e) {\r\n\t\t\tvar a;\r\n\t\t\tfor (e = e || [], this.createMarkers(t), a = 0; a < e.length; a++) this.series.markers[a].setValues(e[a] || {});\r\n\t\t},\r\n\t\tremoveMarkers: function (t) {\r\n\t\t\tfor (var e = 0; e < t.length; e++) this.markers[t[e]].element.remove(), delete this.markers[t[e]];\r\n\t\t},\r\n\t\tremoveAllMarkers: function () {\r\n\t\t\tvar t,\r\n\t\t\t\te = [];\r\n\t\t\tfor (t in this.markers) e.push(t);\r\n\t\t\tthis.removeMarkers(e);\r\n\t\t},\r\n\t\tlatLngToPoint: function (t, e) {\r\n\t\t\tvar a = jvm.Map.maps[this.params.map].projection,\r\n\t\t\t\ts = a.centralMeridian;\r\n\t\t\treturn e < -180 + s && (e += 360), (t = jvm.Proj[a.type](t, e, s)), !!(e = this.getInsetForPoint(t.x, t.y)) && ((s = e.bbox), (t.x = ((t.x - s[0].x) / (s[1].x - s[0].x)) * e.width * this.scale), (t.y = ((t.y - s[0].y) / (s[1].y - s[0].y)) * e.height * this.scale), { x: t.x + this.transX * this.scale + e.left * this.scale, y: t.y + this.transY * this.scale + e.top * this.scale });\r\n\t\t},\r\n\t\tpointToLatLng: function (t, e) {\r\n\t\t\tfor (var a, s, i, n, r = jvm.Map.maps[this.params.map].projection, h = r.centralMeridian, o = jvm.Map.maps[this.params.map].insets, l = 0; l < o.length; l++) if (((s = (a = o[l]).bbox), (i = t - (this.transX * this.scale + a.left * this.scale)), (n = e - (this.transY * this.scale + a.top * this.scale)), (i = (i / (a.width * this.scale)) * (s[1].x - s[0].x) + s[0].x), (n = (n / (a.height * this.scale)) * (s[1].y - s[0].y) + s[0].y), i > s[0].x && i < s[1].x && n > s[0].y && n < s[1].y)) return jvm.Proj[r.type + \"_inv\"](i, -n, h);\r\n\t\t\treturn !1;\r\n\t\t},\r\n\t\tgetInsetForPoint: function (t, e) {\r\n\t\t\tfor (var a, s = jvm.Map.maps[this.params.map].insets, i = 0; i < s.length; i++) if (t > (a = s[i].bbox)[0].x && t < a[1].x && e > a[0].y && e < a[1].y) return s[i];\r\n\t\t},\r\n\t\tcreateSeries: function () {\r\n\t\t\tvar t, e;\r\n\t\t\tfor (e in ((this.series = { markers: [], regions: [] }), this.params.series)) for (t = 0; t < this.params.series[e].length; t++) this.series[e][t] = new jvm.DataSeries(this.params.series[e][t], this[e], this);\r\n\t\t},\r\n\t\tremove: function () {\r\n\t\t\tthis.tip.remove(), this.container.remove(), jvm.$(window).unbind(\"resize\", this.onResize), jvm.$(\"body\").unbind(\"mouseup\", this.onContainerMouseUp);\r\n\t\t},\r\n\t}),\r\n\t(jvm.Map.maps = {}),\r\n\t(jvm.Map.defaultParams = { map: \"world_mill_en\", backgroundColor: \"#505050\", zoomButtons: !0, zoomOnScroll: !0, zoomOnScrollSpeed: 3, panOnDrag: !0, zoomMax: 8, zoomMin: 1, zoomStep: 1.6, zoomAnimate: !0, regionsSelectable: !1, markersSelectable: !1, bindTouchEvents: !0, regionStyle: { initial: { fill: \"white\", \"fill-opacity\": 1, stroke: \"none\", \"stroke-width\": 0, \"stroke-opacity\": 1 }, hover: { \"fill-opacity\": 0.8, cursor: \"pointer\" }, selected: { fill: \"yellow\" }, selectedHover: {} }, regionLabelStyle: { initial: { \"font-family\": \"Verdana\", \"font-size\": \"12\", \"font-weight\": \"bold\", cursor: \"default\", fill: \"black\" }, hover: { cursor: \"pointer\" } }, markerStyle: { initial: { fill: \"grey\", stroke: \"#505050\", \"fill-opacity\": 1, \"stroke-width\": 1, \"stroke-opacity\": 1, r: 5 }, hover: { stroke: \"black\", \"stroke-width\": 2, cursor: \"pointer\" }, selected: { fill: \"blue\" }, selectedHover: {} }, markerLabelStyle: { initial: { \"font-family\": \"Verdana\", \"font-size\": \"12\", \"font-weight\": \"bold\", cursor: \"default\", fill: \"black\" }, hover: { cursor: \"pointer\" } } }),\r\n\t(jvm.Map.apiEvents = { onRegionTipShow: \"regionTipShow\", onRegionOver: \"regionOver\", onRegionOut: \"regionOut\", onRegionClick: \"regionClick\", onRegionSelected: \"regionSelected\", onMarkerTipShow: \"markerTipShow\", onMarkerOver: \"markerOver\", onMarkerOut: \"markerOut\", onMarkerClick: \"markerClick\", onMarkerSelected: \"markerSelected\", onViewportChange: \"viewportChange\" }),\r\n\t(jvm.MultiMap = function (t) {\r\n\t\tvar e = this;\r\n\t\t(this.maps = {}),\r\n\t\t\t(this.params = jvm.$.extend(!0, {}, jvm.MultiMap.defaultParams, t)),\r\n\t\t\t(this.params.maxLevel = this.params.maxLevel || Number.MAX_VALUE),\r\n\t\t\t(this.params.main = this.params.main || {}),\r\n\t\t\t(this.params.main.multiMapLevel = 0),\r\n\t\t\t(this.history = [this.addMap(this.params.main.map, this.params.main)]),\r\n\t\t\t(this.defaultProjection = this.history[0].mapData.projection.type),\r\n\t\t\t(this.mapsLoaded = {}),\r\n\t\t\tthis.params.container.css({ position: \"relative\" }),\r\n\t\t\t(this.backButton = jvm.$(\"<div/>\").addClass(\"jvectormap-goback\").text(\"Back\").appendTo(this.params.container)),\r\n\t\t\tthis.backButton.hide(),\r\n\t\t\tthis.backButton.click(function () {\r\n\t\t\t\te.goBack();\r\n\t\t\t}),\r\n\t\t\t(this.spinner = jvm.$(\"<div/>\").addClass(\"jvectormap-spinner\").appendTo(this.params.container)),\r\n\t\t\tthis.spinner.hide();\r\n\t}),\r\n\t(jvm.MultiMap.prototype = {\r\n\t\taddMap: function (t, e) {\r\n\t\t\tvar a = jvm.$(\"<div/>\").css({ width: \"100%\", height: \"100%\" });\r\n\t\t\treturn (\r\n\t\t\t\tthis.params.container.append(a),\r\n\t\t\t\t(this.maps[t] = new jvm.Map(jvm.$.extend(e, { container: a }))),\r\n\t\t\t\tthis.params.maxLevel > e.multiMapLevel &&\r\n\t\t\t\t\tthis.maps[t].container.on(\"regionClick.jvectormap\", { scope: this }, function (t, e) {\r\n\t\t\t\t\t\tvar a = t.data.scope,\r\n\t\t\t\t\t\t\tt = a.params.mapNameByCode(e, a);\r\n\t\t\t\t\t\t(a.drillDownPromise && \"pending\" === a.drillDownPromise.state()) || a.drillDown(t, e);\r\n\t\t\t\t\t}),\r\n\t\t\t\tthis.maps[t]\r\n\t\t\t);\r\n\t\t},\r\n\t\tdownloadMap: function (t) {\r\n\t\t\tvar e = this,\r\n\t\t\t\ta = jvm.$.Deferred();\r\n\t\t\treturn (\r\n\t\t\t\tthis.mapsLoaded[t]\r\n\t\t\t\t\t? a.resolve()\r\n\t\t\t\t\t: jvm.$.get(this.params.mapUrlByCode(t, this)).then(\r\n\t\t\t\t\t\t\tfunction () {\r\n\t\t\t\t\t\t\t\t(e.mapsLoaded[t] = !0), a.resolve();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfunction () {\r\n\t\t\t\t\t\t\t\ta.reject();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t  ),\r\n\t\t\t\ta\r\n\t\t\t);\r\n\t\t},\r\n\t\tdrillDown: function (t, e) {\r\n\t\t\tvar a = this.history[this.history.length - 1],\r\n\t\t\t\ts = this,\r\n\t\t\t\ti = a.setFocus({ region: e, animate: !0 }),\r\n\t\t\t\tn = this.downloadMap(e);\r\n\t\t\ti.then(function () {\r\n\t\t\t\t\"pending\" === n.state() && s.spinner.show();\r\n\t\t\t}),\r\n\t\t\t\tn.always(function () {\r\n\t\t\t\t\ts.spinner.hide();\r\n\t\t\t\t}),\r\n\t\t\t\t(this.drillDownPromise = jvm.$.when(n, i)),\r\n\t\t\t\tthis.drillDownPromise.then(function () {\r\n\t\t\t\t\ta.params.container.hide(), s.maps[t] ? s.maps[t].params.container.show() : s.addMap(t, { map: t, multiMapLevel: a.params.multiMapLevel + 1 }), s.history.push(s.maps[t]), s.backButton.show();\r\n\t\t\t\t});\r\n\t\t},\r\n\t\tgoBack: function () {\r\n\t\t\tvar t = this.history.pop(),\r\n\t\t\t\te = this.history[this.history.length - 1],\r\n\t\t\t\ta = this;\r\n\t\t\tt.setFocus({ scale: 1, x: 0.5, y: 0.5, animate: !0 }).then(function () {\r\n\t\t\t\tt.params.container.hide(), e.params.container.show(), e.updateSize(), 1 === a.history.length && a.backButton.hide(), e.setFocus({ scale: 1, x: 0.5, y: 0.5, animate: !0 });\r\n\t\t\t});\r\n\t\t},\r\n\t}),\r\n\t(jvm.MultiMap.defaultParams = {\r\n\t\tmapNameByCode: function (t, e) {\r\n\t\t\treturn t.toLowerCase() + \"_\" + e.defaultProjection + \"_en\";\r\n\t\t},\r\n\t\tmapUrlByCode: function (t, e) {\r\n\t\t\treturn \"jquery-jvectormap-data-\" + t.toLowerCase() + \"-\" + e.defaultProjection + \"-en.js\";\r\n\t\t},\r\n\t});\r\n"], "names": ["t", "s", "set", "colors", "values", "backgroundColor", "scaleColors", "normalizeFunction", "focus", "get", "selectedRegions", "selected<PERSON><PERSON><PERSON>", "mapObject", "regionName", "fn", "vectorMap", "e", "a", "this", "children", "data", "jvm", "Map", "maps", "arguments", "char<PERSON>t", "toUpperCase", "substr", "apply", "Array", "prototype", "slice", "call", "container", "j<PERSON><PERSON><PERSON>", "define", "amd", "exports", "module", "h", "o", "l", "document", "documentMode", "m", "event", "fix<PERSON>ooks", "length", "mouseHooks", "special", "mousewheel", "version", "setup", "addEventListener", "i", "onmousew<PERSON><PERSON>", "getLineHeight", "getPageHeight", "teardown", "removeEventListener", "parseInt", "css", "height", "settings", "adjustOldDeltas", "window", "n", "r", "fix", "type", "detail", "wheelDelta", "wheelDeltaY", "wheelDeltaX", "axis", "HORIZONTAL_AXIS", "deltaY", "deltaX", "deltaMode", "Math", "max", "abs", "p", "deltaFactor", "unshift", "clearTimeout", "setTimeout", "c", "dispatch", "handle", "extend", "bind", "trigger", "unmousewheel", "unbind", "inherits", "constructor", "parentClass", "mixin", "hasOwnProperty", "min", "Number", "MAX_VALUE", "MIN_VALUE", "keys", "push", "whenImageLoaded", "$", "Deferred", "error", "reject", "load", "resolve", "attr", "isImageUrl", "test", "indexOf", "TypeError", "Object", "AbstractElement", "node", "createElement", "name", "properties", "applyAttr", "setAttribute", "remove", "AbstractCanvasElement", "setSize", "rootElement", "classPrefix", "append<PERSON><PERSON><PERSON>", "add", "canvas", "addPath", "addCircle", "addImage", "addText", "addGroup", "AbstractShapeElement", "style", "current", "isHovered", "isSelected", "updateStyle", "setStyle", "mergeStyles", "initial", "hover", "selected", "selectedHover", "SVGElement", "svgns", "createElementNS", "addClass", "getElementCtr", "getBBox", "SVGGroupElement", "SVGCanvasElement", "defsElement", "width", "applyTransformParams", "scale", "transX", "transY", "SVGShapeElement", "images", "then", "setAttributeNS", "imageCounter", "SVGPathElement", "SVGCircleElement", "SVGImageElement", "cx", "cy", "SVGTextElement", "textContent", "VMLElement", "VMLInitialized", "initializeVML", "namespaces", "rvml", "createStyleSheet", "addRule", "x", "position", "left", "y", "top", "VMLGroupElement", "coordorigin", "VMLCanvasElement", "coordsize", "getElementsByTagName", "VMLShapeElement", "fillElement", "strokeElement", "stroked", "fillcolor", "opacity", "round", "strokecolor", "strokeweight", "path", "VMLPathElement", "pathSvgToVml", "on", "matrix", "offset", "replace", "split", "shift", "join", "VMLCircleElement", "VectorCanvas", "mode", "SVGAngle", "impl", "SimpleScale", "getValue", "OrdinalScale", "getTicks", "label", "value", "NumericScale", "setScale", "setNormalizeFunction", "setMin", "setMax", "clearMinValue", "normalize", "minValue", "clearMaxValue", "maxValue", "pow", "vectorLength", "vectorSubtract", "vectorToNum", "vectorAdd", "vectorMult", "sqrt", "floor", "log", "LN10", "ceil", "ColorScale", "rgbToArray", "numToRgb", "arrayToRgb", "toString", "Legend", "params", "map", "series", "body", "cssClass", "vertical", "legendCntVertical", "legendCntHorizontal", "append", "render", "html", "title", "attribute", "border-radius", "border", "markerStyle", "stroke", "background", "fill", "appendTo", "labelRender", "DataSeries", "elements", "attributes", "setAttributes", "isArray", "set<PERSON><PERSON><PERSON>", "legend", "element", "parseFloat", "isNaN", "clear", "shape", "Proj", "degRad", "PI", "radDeg", "radius", "sgn", "mill", "tan", "mill_inv", "lat", "atan", "exp", "lng", "merc", "merc_inv", "aea", "sin", "cos", "aea_inv", "asin", "lcc", "lcc_inv", "MapObject", "getLabelText", "config", "getLabelOffsets", "offsets", "setHovered", "setSelected", "Region", "d", "data-code", "code", "labelX", "labelY", "text", "text-anchor", "alignment-baseline", "labelStyle", "labelsGroup", "updateLabelPosition", "<PERSON><PERSON>", "isImage", "image", "createShape", "index", "data-index", "dy", "group", "defaultParams", "Error", "mapData", "markers", "regions", "regionsColors", "regionsData", "defaultWidth", "defaultHeight", "setBackgroundColor", "onResize", "updateSize", "resize", "apiEvents", "DocumentTouch", "bindTouchEvents", "bindContainerTouchEvents", "bindContainerEvents", "bindElementEvents", "createTip", "zoomButtons", "bindZoomButtons", "createRegions", "createMarkers", "focusOn", "region", "setFocus", "setSelectedRegions", "setSelectedMarkers", "createSeries", "baseTransX", "baseTransY", "baseScale", "applyTransform", "reset", "repositionM<PERSON><PERSON>", "reposition<PERSON><PERSON><PERSON>", "panOnDrag", "mousemove", "pageX", "pageY", "mousedown", "onContainerMouseUp", "mouseup", "zoomOnScroll", "zoomOnScrollSpeed", "tip", "hide", "preventDefault", "originalEvent", "touches", "delegate", "baseVal", "paths", "Event", "isDefaultPrevented", "show", "tipWidth", "tipHeight", "regionsSelectable", "markersSelectable", "clearSelected", "find", "click", "zoomStep", "zoomAnimate", "v", "u", "g", "f", "j", "zoomMax", "zoomMin", "setInterval", "clearInterval", "animate", "latLngToPoint", "getSelected", "getSelectedRegions", "getSelectedMarkers", "clearSelectedRegions", "clearSelectedMarkers", "getMapObject", "getRegionName", "regionLabelsGroup", "regionStyle", "regionLabelStyle", "labels", "markersGroup", "markerLabelsGroup", "latLng", "getMarkerPosition", "markerLabelStyle", "removeMarkers", "projection", "coords", "add<PERSON><PERSON><PERSON>", "addMarkers", "removeAllMarkers", "centralMeridian", "getInsetForPoint", "bbox", "pointToLatLng", "insets", "fill-opacity", "stroke-width", "stroke-opacity", "cursor", "font-family", "font-size", "font-weight", "onRegionTipShow", "onRegionOver", "onRegionOut", "onRegionClick", "onRegionSelected", "onMarkerTipShow", "onMarkerOver", "onMarkerOut", "onMarkerClick", "onMarkerSelected", "onViewportChange", "MultiMap", "maxLevel", "main", "multiMapLevel", "history", "addMap", "defaultProjection", "mapsLoaded", "backButton", "goBack", "spinner", "scope", "mapNameByCode", "drillDownPromise", "state", "drillDown", "downloadMap", "mapUrlByCode", "always", "when", "pop", "toLowerCase"], "mappings": "AAAA,CAAC,SAAWA,GACX,IAAIC,EAAI,CAAEC,IAAK,CAAEC,OAAQ,EAAGC,OAAQ,EAAGC,gBAAiB,EAAGC,YAAa,EAAGC,kBAAmB,EAAGC,MAAO,CAAE,EAAGC,IAAK,CAAEC,gBAAiB,EAAGC,gBAAiB,EAAGC,UAAW,EAAGC,WAAY,CAAE,CAAE,EAC1Lb,EAAEc,GAAGC,UAAY,SAAUf,GAC1B,IAAIgB,EACHC,EAAIC,KAAKC,SAAS,uBAAuB,EAAEC,KAAK,WAAW,EAC5D,GAAI,WAAapB,EAAGqB,IAAIC,IAAIC,KAAKC,UAAU,IAAMA,UAAU,OACtD,CACJ,IAAK,QAAUxB,GAAK,QAAUA,IAAMC,EAAED,GAAGwB,UAAU,IAAK,OAAQR,EAAIQ,UAAU,GAAGC,OAAO,CAAC,EAAEC,YAAY,EAAIF,UAAU,GAAGG,OAAO,CAAC,EAAIV,EAAEjB,EAAIgB,GAAGY,MAAMX,EAAGY,MAAMC,UAAUC,MAAMC,KAAKR,UAAW,CAAC,CAAC,GAC5LxB,EAAIA,GAAK,IAAIiC,UAAYf,KAAQD,EAAI,IAAII,IAAIC,IAAItB,CAAC,CACrD,CACA,OAAOkB,IACR,CACA,EAAEgB,MAAM,EACR,SAAWlC,GACV,YAAc,OAAOmC,QAAUA,OAAOC,IAAMD,OAAO,CAAC,UAAWnC,CAAC,EAAI,UAAY,OAAOqC,QAAWC,OAAOD,QAAUrC,EAAKA,EAAEkC,MAAM,CAChI,EAAE,SAAUK,GACZ,IAAIC,EACHC,EACAzC,EAAI,CAAC,QAAS,aAAc,iBAAkB,uBAC9CgB,EAAI,YAAa0B,UAAY,GAAKA,SAASC,aAAe,CAAC,SAAW,CAAC,aAAc,iBAAkB,uBACvGC,EAAIf,MAAMC,UAAUC,MACrB,GAAIQ,EAAEM,MAAMC,SAAU,IAAK,IAAI7B,EAAIjB,EAAE+C,OAAQ9B,GAAKsB,EAAEM,MAAMC,SAAS9C,EAAE,EAAEiB,IAAMsB,EAAEM,MAAMG,WACrF,IAAI/C,EAAKsC,EAAEM,MAAMI,QAAQC,WAAa,CACrCC,QAAS,QACTC,MAAO,WACN,GAAIlC,KAAKmC,iBAAkB,IAAK,IAAIrD,EAAIgB,EAAE+B,OAAQ/C,GAAKkB,KAAKmC,iBAAiBrC,EAAE,EAAEhB,GAAIsD,EAAG,CAAA,CAAE,OACrFpC,KAAKqC,aAAeD,EACzBf,EAAEnB,KAAKF,KAAM,yBAA0BjB,EAAEuD,cAActC,IAAI,CAAC,EAAGqB,EAAEnB,KAAKF,KAAM,yBAA0BjB,EAAEwD,cAAcvC,IAAI,CAAC,CAC5H,EACAwC,SAAU,WACT,GAAIxC,KAAKyC,oBAAqB,IAAK,IAAI3D,EAAIgB,EAAE+B,OAAQ/C,GAAKkB,KAAKyC,oBAAoB3C,EAAE,EAAEhB,GAAIsD,EAAG,CAAA,CAAE,OAC3FpC,KAAKqC,aAAe,IAC1B,EACAC,cAAe,SAAUxD,GACxB,OAAO4D,SAASrB,EAAEvC,CAAC,EAAE,iBAAkBuC,EAAEzB,GAAK,eAAiB,UAAU,EAAE+C,IAAI,UAAU,EAAG,EAAE,CAC/F,EACAJ,cAAe,SAAUzD,GACxB,OAAOuC,EAAEvC,CAAC,EAAE8D,OAAO,CACpB,EACAC,SAAU,CAAEC,gBAAiB,CAAA,CAAG,CACjC,EACA,SAASV,EAAEtD,GACV,IAAIgB,EACHC,EAAIjB,GAAKiE,OAAOpB,MAChB5C,EAAI2C,EAAEZ,KAAKR,UAAW,CAAC,EACvB8B,EAAI,EACJY,EAAI,EACJC,EAAI,EACL,IAAOnE,EAAIuC,EAAEM,MAAMuB,IAAInD,CAAC,GAAGoD,KAAO,aAAe,WAAYpD,IAAMkD,EAAI,CAAC,EAAIlD,EAAEqD,QAAS,eAAgBrD,IAAMkD,EAAIlD,EAAEsD,YAAa,gBAAiBtD,IAAMkD,EAAIlD,EAAEuD,aAAc,gBAAiBvD,IAAMiD,EAAI,CAAC,EAAIjD,EAAEwD,aAAc,SAAUxD,GAAKA,EAAEyD,OAASzD,EAAE0D,kBAAqBT,EAAI,CAAC,EAAIC,EAAKA,EAAI,GAAMb,EAAI,IAAMa,EAAID,EAAIC,EAAI,WAAYlD,IAAMqC,EAAIa,EAAI,CAAC,EAAIlD,EAAE2D,QAAS,WAAY3D,IAAOiD,EAAIjD,EAAE4D,OAAS,IAAMV,KAAMb,EAAI,CAAC,EAAIY,GAAK,IAAMC,GAAK,IAAMD,EAAI,OAAO,IAAMjD,EAAE6D,WAAcxB,GAAKtC,EAAIuB,EAAEnB,KAAKF,KAAM,wBAAwB,EAAKiD,GAAKnD,EAAKkD,GAAKlD,GAAM,IAAMC,EAAE6D,YAAexB,GAAKtC,EAAIuB,EAAEnB,KAAKF,KAAM,wBAAwB,EAAKiD,GAAKnD,EAAKkD,GAAKlD,GAAMA,EAAI+D,KAAKC,IAAID,KAAKE,IAAId,CAAC,EAAGY,KAAKE,IAAIf,CAAC,CAAC,GAAK,CAACzB,GAAKzB,EAAIyB,IAAMyC,EAAEjE,EAAIwB,EAAIzB,CAAE,IAAMyB,GAAK,IAAKyC,EAAEjE,EAAGD,CAAC,IAAOsC,GAAK,GAAMY,GAAK,GAAMC,GAAK,IAAOb,EAAIyB,KAAK,GAAKzB,EAAI,QAAU,QAAQA,EAAIb,CAAC,EAAKyB,EAAIa,KAAK,GAAKb,EAAI,QAAU,QAAQA,EAAIzB,CAAC,EAAK0B,EAAIY,KAAK,GAAKZ,EAAI,QAAU,QAAQA,EAAI1B,CAAC,EAAKzC,EAAE6E,OAASX,EAAKlE,EAAE4E,OAAST,EAAKnE,EAAEmF,YAAc1C,EAAKzC,EAAE8E,UAAY,EAAI7E,EAAEmF,QAAQpF,EAAGsD,EAAGY,EAAGC,CAAC,EAAG3B,GAAK6C,aAAa7C,CAAC,EAAIA,EAAI8C,WAAWC,EAAG,GAAG,GAAKhD,EAAEM,MAAM2C,UAAYjD,EAAEM,MAAM4C,QAAQ7D,MAAMV,KAAMjB,CAAC,CACzkC,CACA,SAASsF,IACR9C,EAAI,IACL,CACA,SAASyC,EAAElF,EAAGgB,GACb,OAAOf,EAAE8D,SAASC,iBAAmB,eAAiBhE,EAAEqE,MAAQrD,EAAI,KAAO,CAC5E,CACAuB,EAAEzB,GAAG4E,OAAO,CACXxC,WAAY,SAAUlD,GACrB,OAAOA,EAAIkB,KAAKyE,KAAK,aAAc3F,CAAC,EAAIkB,KAAK0E,QAAQ,YAAY,CAClE,EACAC,aAAc,SAAU7F,GACvB,OAAOkB,KAAK4E,OAAO,aAAc9F,CAAC,CACnC,CACD,CAAC,CACF,CAAC,EACF,IAAIqB,IAAM,CACT0E,SAAU,SAAU/F,EAAGgB,GACtB,SAASC,KACRA,EAAEa,UAAYd,EAAEc,UAAa9B,EAAE8B,UAAY,IAAIb,GAAQjB,EAAE8B,UAAUkE,YAAchG,GAAGiG,YAAcjF,CACpG,EACAkF,MAAO,SAAUlG,EAAGgB,GACnB,IAAK,IAAIC,KAAKD,EAAEc,UAAWd,EAAEc,UAAUqE,eAAelF,CAAC,IAAMjB,EAAE8B,UAAUb,GAAKD,EAAEc,UAAUb,GAC3F,EACAmF,IAAK,SAAUpG,GACd,IAAIgB,EACHC,EAAIoF,OAAOC,UACZ,GAAItG,aAAa6B,MAAO,IAAKb,EAAI,EAAGA,EAAIhB,EAAE+C,OAAQ/B,CAAC,GAAIhB,EAAEgB,GAAKC,IAAMA,EAAIjB,EAAEgB,SACrE,IAAKA,KAAKhB,EAAGA,EAAEgB,GAAKC,IAAMA,EAAIjB,EAAEgB,IACrC,OAAOC,CACR,EACA+D,IAAK,SAAUhF,GACd,IAAIgB,EACHC,EAAIoF,OAAOE,UACZ,GAAIvG,aAAa6B,MAAO,IAAKb,EAAI,EAAGA,EAAIhB,EAAE+C,OAAQ/B,CAAC,GAAIhB,EAAEgB,GAAKC,IAAMA,EAAIjB,EAAEgB,SACrE,IAAKA,KAAKhB,EAAGA,EAAEgB,GAAKC,IAAMA,EAAIjB,EAAEgB,IACrC,OAAOC,CACR,EACAuF,KAAM,SAAUxG,GACf,IAAIgB,EACHC,EAAI,GACL,IAAKD,KAAKhB,EAAGiB,EAAEwF,KAAKzF,CAAC,EACrB,OAAOC,CACR,EACAb,OAAQ,SAAUJ,GACjB,IAAK,IAAIgB,EAAGC,EAAI,GAAIhB,EAAI,EAAGA,EAAIuB,UAAUuB,OAAQ9C,CAAC,GAAI,IAAKe,KAAMhB,EAAIwB,UAAUvB,GAAKgB,EAAEwF,KAAKzG,EAAEgB,EAAE,EAC/F,OAAOC,CACR,EACAyF,gBAAiB,SAAU1G,GAC1B,IAAIgB,EAAI,IAAIK,IAAIsF,EAAEC,SACjB3F,EAAII,IAAIsF,EAAE,QAAQ,EACnB,OACC1F,EACE4F,MAAM,WACN7F,EAAE8F,OAAO,CACV,CAAC,EACAC,KAAK,WACL/F,EAAEgG,QAAQ/F,CAAC,CACZ,CAAC,EACFA,EAAEgG,KAAK,MAAOjH,CAAC,EACfgB,CAEF,EACAkG,WAAY,SAAUlH,GACrB,MAAO,aAAamH,KAAKnH,CAAC,CAC3B,CACD,EACCqB,IAAIsF,EAAIzE,OACRL,MAAMC,UAAUsF,UACdvF,MAAMC,UAAUsF,QAAU,SAAUpH,EAAGgB,GACvC,IAAIC,EACJ,GAAI,MAAQC,KAAM,MAAM,IAAImG,UAAU,+BAA+B,EACrE,IAAIpH,EAAIqH,OAAOpG,IAAI,EAClBoC,EAAIrD,EAAE8C,SAAW,EAClB,GAAI,GAAKO,IACHtC,EAAI,CAACA,GAAK,EAAIsC,EAAAA,IAAMtC,EAAI+D,KAAKE,IAAIjE,CAAC,IAAM,EAAA,EAAQ,EAAIA,KAC1D,IAAKC,EAAI8D,KAAKC,IAAI,GAAKhE,EAAIA,EAAIsC,EAAIyB,KAAKE,IAAIjE,CAAC,EAAG,CAAC,EAAGC,EAAIqC,GAAK,CAC5D,GAAIrC,KAAKhB,GAAKA,EAAEgB,KAAOjB,EAAG,OAAOiB,EACjCA,CAAC,EACF,CACA,MAAO,CAAC,CACT,GACAI,IAAIkG,gBAAkB,SAAUvH,EAAGgB,GAClCE,KAAKsG,KAAOtG,KAAKuG,cAAczH,CAAC,EAAKkB,KAAKwG,KAAO1H,EAAKkB,KAAKyG,WAAa,GAAK3G,GAAKE,KAAKhB,IAAIc,CAAC,CAC9F,EACCK,IAAIkG,gBAAgBzF,UAAU5B,IAAM,SAAUF,EAAGgB,GACjD,GAAI,UAAY,OAAOhB,EAAG,IAAK,IAAIiB,KAAKjB,EAAIkB,KAAKyG,WAAW1G,GAAKjB,EAAEiB,GAAKC,KAAK0G,UAAU3G,EAAGjB,EAAEiB,EAAE,OACxFC,KAAKyG,WAAW3H,GAAKgB,EAAIE,KAAK0G,UAAU5H,EAAGgB,CAAC,CACnD,EACCK,IAAIkG,gBAAgBzF,UAAUrB,IAAM,SAAUT,GAC9C,OAAOkB,KAAKyG,WAAW3H,EACxB,EACCqB,IAAIkG,gBAAgBzF,UAAU8F,UAAY,SAAU5H,EAAGgB,GACvDE,KAAKsG,KAAKK,aAAa7H,EAAGgB,CAAC,CAC5B,EACCK,IAAIkG,gBAAgBzF,UAAUgG,OAAS,WACvCzG,IAAIsF,EAAEzF,KAAKsG,IAAI,EAAEM,OAAO,CACzB,EACCzG,IAAI0G,sBAAwB,SAAU/H,EAAGgB,EAAGC,GAC3CC,KAAKe,UAAYjC,EAAIkB,KAAK8G,QAAQhH,EAAGC,CAAC,EAAIC,KAAK+G,YAAc,IAAI5G,IAAIH,KAAKgH,YAAc,gBAAoBhH,KAAKsG,KAAKW,YAAYjH,KAAK+G,YAAYT,IAAI,EAAGtG,KAAKe,UAAUkG,YAAYjH,KAAKsG,IAAI,CAChM,EACCnG,IAAI0G,sBAAsBjG,UAAUsG,IAAM,SAAUpI,EAAGgB,IACtDA,EAAIA,GAAKE,KAAK+G,aAAaG,IAAIpI,CAAC,EAAIA,EAAEqI,OAASnH,IACjD,EACCG,IAAI0G,sBAAsBjG,UAAUwG,QAAU,SAAUtI,EAAGgB,EAAGC,GAC9D,OAAQD,EAAI,IAAIK,IAAIH,KAAKgH,YAAc,eAAelI,EAAGgB,CAAC,EAAIE,KAAKkH,IAAIpH,EAAGC,CAAC,EAAGD,CAC/E,EACCK,IAAI0G,sBAAsBjG,UAAUyG,UAAY,SAAUvI,EAAGgB,EAAGC,GAChE,OAAQD,EAAI,IAAIK,IAAIH,KAAKgH,YAAc,iBAAiBlI,EAAGgB,CAAC,EAAIE,KAAKkH,IAAIpH,EAAGC,CAAC,EAAGD,CACjF,EACCK,IAAI0G,sBAAsBjG,UAAU0G,SAAW,SAAUxI,EAAGgB,EAAGC,GAC/D,OAAQD,EAAI,IAAIK,IAAIH,KAAKgH,YAAc,gBAAgBlI,EAAGgB,CAAC,EAAIE,KAAKkH,IAAIpH,EAAGC,CAAC,EAAGD,CAChF,EACCK,IAAI0G,sBAAsBjG,UAAU2G,QAAU,SAAUzI,EAAGgB,EAAGC,GAC9D,OAAQD,EAAI,IAAIK,IAAIH,KAAKgH,YAAc,eAAelI,EAAGgB,CAAC,EAAIE,KAAKkH,IAAIpH,EAAGC,CAAC,EAAGD,CAC/E,EACCK,IAAI0G,sBAAsBjG,UAAU4G,SAAW,SAAU1I,GACzD,IAAIgB,EAAI,IAAIK,IAAIH,KAAKgH,YAAc,gBACnC,OAAQlI,GAAKkB,MAAMsG,KAAKW,YAAYnH,EAAEwG,IAAI,EAAIxG,EAAEqH,OAASnH,KAAOF,CACjE,EACCK,IAAIsH,qBAAuB,SAAU3I,EAAGgB,EAAGC,GAC1CC,KAAK0H,MAAQ3H,GAAK,GAAMC,KAAK0H,MAAMC,QAAU3H,KAAK0H,MAAMC,SAAW,GAAM3H,KAAK4H,UAAY,CAAA,EAAM5H,KAAK6H,WAAa,CAAA,EAAK7H,KAAK8H,YAAY,CAC1I,EACC3H,IAAIsH,qBAAqB7G,UAAUmH,SAAW,SAAUjJ,EAAGgB,GAC3D,IAAIC,EAAI,GACR,UAAY,OAAOjB,EAAKiB,EAAIjB,EAAMiB,EAAEjB,GAAKgB,EAAIK,IAAIsF,EAAEjB,OAAOxE,KAAK0H,MAAMC,QAAS5H,CAAC,EAAGC,KAAK8H,YAAY,CACpG,EACC3H,IAAIsH,qBAAqB7G,UAAUkH,YAAc,WACjD,IAAIhJ,EAAI,GACRqB,IAAIsH,qBAAqBO,YAAYlJ,EAAGkB,KAAK0H,MAAMO,OAAO,EAAG9H,IAAIsH,qBAAqBO,YAAYlJ,EAAGkB,KAAK0H,MAAMC,OAAO,EAAG3H,KAAK4H,WAAazH,IAAIsH,qBAAqBO,YAAYlJ,EAAGkB,KAAK0H,MAAMQ,KAAK,EAAGlI,KAAK6H,aAAe1H,IAAIsH,qBAAqBO,YAAYlJ,EAAGkB,KAAK0H,MAAMS,QAAQ,EAAGnI,KAAK4H,YAAazH,IAAIsH,qBAAqBO,YAAYlJ,EAAGkB,KAAK0H,MAAMU,aAAa,EAAIpI,KAAKhB,IAAIF,CAAC,CAC1X,EACCqB,IAAIsH,qBAAqBO,YAAc,SAAUlJ,EAAGgB,GACpD,IAAK,IAAIC,KAAMD,EAAIA,GAAK,GAAK,OAASA,EAAEC,GAAK,OAAOjB,EAAEiB,GAAMjB,EAAEiB,GAAKD,EAAEC,EACtE,EACCI,IAAIkI,WAAa,SAAUvJ,EAAGgB,GAC9BK,IAAIkI,WAAWtD,YAAYrE,MAAMV,KAAMM,SAAS,CACjD,EACAH,IAAI0E,SAAS1E,IAAIkI,WAAYlI,IAAIkG,eAAe,EAC/ClG,IAAIkI,WAAWC,MAAQ,6BACvBnI,IAAIkI,WAAWzH,UAAU2F,cAAgB,SAAUzH,GACnD,OAAO0C,SAAS+G,gBAAgBpI,IAAIkI,WAAWC,MAAOxJ,CAAC,CACxD,EACCqB,IAAIkI,WAAWzH,UAAU4H,SAAW,SAAU1J,GAC9CkB,KAAKsG,KAAKK,aAAa,QAAS7H,CAAC,CAClC,EACCqB,IAAIkI,WAAWzH,UAAU6H,cAAgB,SAAU3J,GACnD,OAAOqB,IAAI,MAAQrB,EACpB,EACCqB,IAAIkI,WAAWzH,UAAU8H,QAAU,WACnC,OAAO1I,KAAKsG,KAAKoC,QAAQ,CAC1B,EACCvI,IAAIwI,gBAAkB,WACtBxI,IAAIwI,gBAAgB5D,YAAYjE,KAAKd,KAAM,GAAG,CAC/C,EACAG,IAAI0E,SAAS1E,IAAIwI,gBAAiBxI,IAAIkI,UAAU,EAC/ClI,IAAIwI,gBAAgB/H,UAAUsG,IAAM,SAAUpI,GAC9CkB,KAAKsG,KAAKW,YAAYnI,EAAEwH,IAAI,CAC7B,EACCnG,IAAIyI,iBAAmB,SAAU9J,EAAGgB,EAAGC,GACtCC,KAAKgH,YAAc,MAAQ7G,IAAIyI,iBAAiB7D,YAAYjE,KAAKd,KAAM,KAAK,EAAIA,KAAK6I,YAAc,IAAI1I,IAAIkI,WAAW,MAAM,EAAIrI,KAAKsG,KAAKW,YAAYjH,KAAK6I,YAAYvC,IAAI,EAAGnG,IAAI0G,sBAAsBnG,MAAMV,KAAMM,SAAS,CAC/N,EACAH,IAAI0E,SAAS1E,IAAIyI,iBAAkBzI,IAAIkI,UAAU,EACjDlI,IAAI6E,MAAM7E,IAAIyI,iBAAkBzI,IAAI0G,qBAAqB,EACxD1G,IAAIyI,iBAAiBhI,UAAUkG,QAAU,SAAUhI,EAAGgB,GACrDE,KAAK8I,MAAQhK,EAAKkB,KAAK4C,OAAS9C,EAAIE,KAAKsG,KAAKK,aAAa,QAAS7H,CAAC,EAAGkB,KAAKsG,KAAKK,aAAa,SAAU7G,CAAC,CAC5G,EACCK,IAAIyI,iBAAiBhI,UAAUmI,qBAAuB,SAAUjK,EAAGgB,EAAGC,GACrEC,KAAKgJ,MAAQlK,EAAKkB,KAAKiJ,OAASnJ,EAAKE,KAAKkJ,OAASnJ,EAAIC,KAAK+G,YAAYT,KAAKK,aAAa,YAAa,SAAW7H,EAAI,eAAiBgB,EAAI,KAAOC,EAAI,GAAG,CAC3J,EACCI,IAAIgJ,gBAAkB,SAAUrK,EAAGgB,EAAGC,GACtCI,IAAIgJ,gBAAgBpE,YAAYjE,KAAKd,KAAMlB,EAAGgB,CAAC,EAAGK,IAAIsH,qBAAqB/G,MAAMV,KAAMM,SAAS,CACjG,EACAH,IAAI0E,SAAS1E,IAAIgJ,gBAAiBhJ,IAAIkI,UAAU,EAChDlI,IAAI6E,MAAM7E,IAAIgJ,gBAAiBhJ,IAAIsH,oBAAoB,EACtDtH,IAAIgJ,gBAAgBvI,UAAU8F,UAAY,SAAU5H,EAAGgB,GACvD,IAAIC,EACHhB,EACAqD,EAAIpC,KACL,SAAWlB,GAAKqB,IAAI6F,WAAWlG,CAAC,EAC7BK,IAAIgJ,gBAAgBC,OAAOtJ,GAC1BE,KAAK0G,UAAU,OAAQ,aAAevG,IAAIgJ,gBAAgBC,OAAOtJ,GAAK,GAAG,EACzEK,IAAIqF,gBAAgB1F,CAAC,EAAEuJ,KAAK,SAAUvK,IACrCC,EAAI,IAAIoB,IAAIkI,WAAW,OAAO,GAAG/B,KAAKgD,eAAe,+BAAgC,OAAQxJ,CAAC,EAAGf,EAAE2H,UAAU,IAAK,GAAG,EAAG3H,EAAE2H,UAAU,IAAK,GAAG,EAAG3H,EAAE2H,UAAU,QAAS5H,EAAE,GAAGgK,KAAK,EAAG/J,EAAE2H,UAAU,SAAU5H,EAAE,GAAG8D,MAAM,GAAI7C,EAAI,IAAII,IAAIkI,WAAW,SAAS,GAAG3B,UAAU,KAAM,QAAUvG,IAAIgJ,gBAAgBI,YAAY,EAAGxJ,EAAE2G,UAAU,IAAK,CAAC,EAAG3G,EAAE2G,UAAU,IAAK,CAAC,EAAG3G,EAAE2G,UAAU,QAAS5H,EAAE,GAAGgK,MAAQ,CAAC,EAAG/I,EAAE2G,UAAU,SAAU5H,EAAE,GAAG8D,OAAS,CAAC,EAAG7C,EAAE2G,UAAU,UAAW,OAAS5H,EAAE,GAAGgK,MAAQ,IAAMhK,EAAE,GAAG8D,MAAM,EAAG7C,EAAE2G,UAAU,eAAgB,gBAAgB,EAAG3G,EAAEuG,KAAKW,YAAYlI,EAAEuH,IAAI,EAAGlE,EAAE+E,OAAO0B,YAAYvC,KAAKW,YAAYlH,EAAEuG,IAAI,EAAInG,IAAIgJ,gBAAgBC,OAAOtJ,GAAKK,IAAIgJ,gBAAgBI,YAAY,GAAKnH,EAAEsE,UAAU,OAAQ,aAAevG,IAAIgJ,gBAAgBC,OAAOtJ,GAAK,GAAG,CACtvB,CAAC,EACFK,IAAIgJ,gBAAgBpE,YAAYnE,UAAU8F,UAAUhG,MAAMV,KAAMM,SAAS,CAC7E,EACCH,IAAIgJ,gBAAgBI,aAAe,EACnCpJ,IAAIgJ,gBAAgBC,OAAS,GAC7BjJ,IAAIqJ,eAAiB,SAAU1K,EAAGgB,GAClCK,IAAIqJ,eAAezE,YAAYjE,KAAKd,KAAM,OAAQlB,EAAGgB,CAAC,EAAGE,KAAKsG,KAAKK,aAAa,YAAa,SAAS,CACvG,EACAxG,IAAI0E,SAAS1E,IAAIqJ,eAAgBrJ,IAAIgJ,eAAe,EACnDhJ,IAAIsJ,iBAAmB,SAAU3K,EAAGgB,GACpCK,IAAIsJ,iBAAiB1E,YAAYjE,KAAKd,KAAM,SAAUlB,EAAGgB,CAAC,CAC3D,EACAK,IAAI0E,SAAS1E,IAAIsJ,iBAAkBtJ,IAAIgJ,eAAe,EACrDhJ,IAAIuJ,gBAAkB,SAAU5K,EAAGgB,GACnCK,IAAIuJ,gBAAgB3E,YAAYjE,KAAKd,KAAM,QAASlB,EAAGgB,CAAC,CACzD,EACAK,IAAI0E,SAAS1E,IAAIuJ,gBAAiBvJ,IAAIgJ,eAAe,EACpDhJ,IAAIuJ,gBAAgB9I,UAAU8F,UAAY,SAAU5H,EAAGgB,GACvD,IAAIC,EAAIC,KACR,SAAWlB,EACRqB,IAAIqF,gBAAgB1F,CAAC,EAAEuJ,KAAK,SAAUvK,GACtCiB,EAAEuG,KAAKgD,eAAe,+BAAgC,OAAQxJ,CAAC,EAAIC,EAAE+I,MAAQhK,EAAE,GAAGgK,MAAS/I,EAAE6C,OAAS9D,EAAE,GAAG8D,OAAS7C,EAAE2G,UAAU,QAAS3G,EAAE+I,KAAK,EAAG/I,EAAE2G,UAAU,SAAU3G,EAAE6C,MAAM,EAAG7C,EAAE2G,UAAU,IAAK3G,EAAE4J,GAAK5J,EAAE+I,MAAQ,CAAC,EAAG/I,EAAE2G,UAAU,IAAK3G,EAAE6J,GAAK7J,EAAE6C,OAAS,CAAC,EAAGzC,IAAIsF,EAAE1F,EAAEuG,IAAI,EAAE5B,QAAQ,cAAe,CAAC5F,EAAE,CACzS,CAAC,EACD,MAAQA,GACNkB,KAAK2J,GAAK7J,EAAIE,KAAK8I,OAAS9I,KAAK0G,UAAU,IAAK5G,EAAIE,KAAK8I,MAAQ,CAAC,GACpE,MAAQhK,GACNkB,KAAK4J,GAAK9J,EAAIE,KAAK4C,QAAU5C,KAAK0G,UAAU,IAAK5G,EAAIE,KAAK4C,OAAS,CAAC,GACtEzC,IAAIuJ,gBAAgB3E,YAAYnE,UAAU8F,UAAUhG,MAAMV,KAAMM,SAAS,CAC7E,EACCH,IAAI0J,eAAiB,SAAU/K,EAAGgB,GAClCK,IAAI0J,eAAe9E,YAAYjE,KAAKd,KAAM,OAAQlB,EAAGgB,CAAC,CACvD,EACAK,IAAI0E,SAAS1E,IAAI0J,eAAgB1J,IAAIgJ,eAAe,EACnDhJ,IAAI0J,eAAejJ,UAAU8F,UAAY,SAAU5H,EAAGgB,GACtD,SAAWhB,EAAKkB,KAAKsG,KAAKwD,YAAchK,EAAKK,IAAI0J,eAAe9E,YAAYnE,UAAU8F,UAAUhG,MAAMV,KAAMM,SAAS,CACtH,EACCH,IAAI4J,WAAa,SAAUjL,EAAGgB,GAC9BK,IAAI4J,WAAWC,gBAAkB7J,IAAI4J,WAAWE,cAAc,EAAG9J,IAAI4J,WAAWhF,YAAYrE,MAAMV,KAAMM,SAAS,CAClH,EACAH,IAAI0E,SAAS1E,IAAI4J,WAAY5J,IAAIkG,eAAe,EAC/ClG,IAAI4J,WAAWC,eAAiB,CAAA,EAChC7J,IAAI4J,WAAWE,cAAgB,WAC/B,IACCzI,SAAS0I,WAAWC,MAAQ3I,SAAS0I,WAAWhD,IAAI,OAAQ,+BAA+B,EACzF/G,IAAI4J,WAAWnJ,UAAU2F,cAAgB,SAAUzH,GACnD,OAAO0C,SAAS+E,cAAc,SAAWzH,EAAI,gBAAgB,CAC9D,CAKF,CAJE,MAAOA,GACRqB,IAAI4J,WAAWnJ,UAAU2F,cAAgB,SAAUzH,GAClD,OAAO0C,SAAS+E,cAAc,IAAMzH,EAAI,sDAAsD,CAC/F,CACD,CACA0C,SAAS4I,iBAAiB,EAAEC,QAAQ,QAAS,4BAA4B,EAAIlK,IAAI4J,WAAWC,eAAiB,CAAA,CAC9G,EACC7J,IAAI4J,WAAWnJ,UAAU6H,cAAgB,SAAU3J,GACnD,OAAOqB,IAAI,MAAQrB,EACpB,EACCqB,IAAI4J,WAAWnJ,UAAU4H,SAAW,SAAU1J,GAC9CqB,IAAIsF,EAAEzF,KAAKsG,IAAI,EAAEkC,SAAS1J,CAAC,CAC5B,EACCqB,IAAI4J,WAAWnJ,UAAU8F,UAAY,SAAU5H,EAAGgB,GAClDE,KAAKsG,KAAKxH,GAAKgB,CAChB,EACCK,IAAI4J,WAAWnJ,UAAU8H,QAAU,WACnC,IAAI5J,EAAIqB,IAAIsF,EAAEzF,KAAKsG,IAAI,EACvB,MAAO,CAAEgE,EAAGxL,EAAEyL,SAAS,EAAEC,KAAOxK,KAAKmH,OAAO6B,MAAOyB,EAAG3L,EAAEyL,SAAS,EAAEG,IAAM1K,KAAKmH,OAAO6B,MAAOF,MAAOhK,EAAEgK,MAAM,EAAI9I,KAAKmH,OAAO6B,MAAOpG,OAAQ9D,EAAE8D,OAAO,EAAI5C,KAAKmH,OAAO6B,KAAM,CAC1K,EACC7I,IAAIwK,gBAAkB,WACtBxK,IAAIwK,gBAAgB5F,YAAYjE,KAAKd,KAAM,OAAO,EAAIA,KAAKsG,KAAKoB,MAAM8C,KAAO,MAASxK,KAAKsG,KAAKoB,MAAMgD,IAAM,MAAS1K,KAAKsG,KAAKsE,YAAc,KAC9I,EACAzK,IAAI0E,SAAS1E,IAAIwK,gBAAiBxK,IAAI4J,UAAU,EAC/C5J,IAAIwK,gBAAgB/J,UAAUsG,IAAM,SAAUpI,GAC9CkB,KAAKsG,KAAKW,YAAYnI,EAAEwH,IAAI,CAC7B,EACCnG,IAAI0K,iBAAmB,SAAU/L,EAAGgB,EAAGC,GACtCC,KAAKgH,YAAc,MAAQ7G,IAAI0K,iBAAiB9F,YAAYjE,KAAKd,KAAM,OAAO,EAAGG,IAAI0G,sBAAsBnG,MAAMV,KAAMM,SAAS,EAAIN,KAAKsG,KAAKoB,MAAM6C,SAAW,UACjK,EACApK,IAAI0E,SAAS1E,IAAI0K,iBAAkB1K,IAAI4J,UAAU,EACjD5J,IAAI6E,MAAM7E,IAAI0K,iBAAkB1K,IAAI0G,qBAAqB,EACxD1G,IAAI0K,iBAAiBjK,UAAUkG,QAAU,SAAUhI,EAAGgB,GACtD,IAAIC,EAAGhB,EAAGqD,EAAGY,EACb,GAAMhD,KAAK8I,MAAQhK,EAAKkB,KAAK4C,OAAS9C,EAAKE,KAAKsG,KAAKoB,MAAMoB,MAAQhK,EAAI,KAAQkB,KAAKsG,KAAKoB,MAAM9E,OAAS9C,EAAI,KAAQE,KAAKsG,KAAKwE,UAAYhM,EAAI,IAAMgB,EAAKE,KAAKsG,KAAKsE,YAAc,MAAQ5K,KAAK+G,YAAc,CAC3M,IAAK3E,EAAI,EAAGY,GAAKjD,EAAIC,KAAK+G,YAAYT,KAAKyE,qBAAqB,OAAO,GAAGlJ,OAAQO,EAAIY,EAAGZ,CAAC,GAAKrC,EAAEqC,GAAG0I,UAAYhM,EAAI,IAAMgB,EAAKC,EAAEqC,GAAGsF,MAAMoB,MAAQhK,EAAI,KAAQiB,EAAEqC,GAAGsF,MAAM9E,OAAS9C,EAAI,KACtL,IAAKsC,EAAI,EAAGY,GAAKjE,EAAIiB,KAAKsG,KAAKyE,qBAAqB,OAAO,GAAGlJ,OAAQO,EAAIY,EAAGZ,CAAC,GAAKrD,EAAEqD,GAAG0I,UAAYhM,EAAI,IAAMgB,EAAKf,EAAEqD,GAAGsF,MAAMoB,MAAQhK,EAAI,KAAQC,EAAEqD,GAAGsF,MAAM9E,OAAS9C,EAAI,IAC3K,CACD,EACCK,IAAI0K,iBAAiBjK,UAAUmI,qBAAuB,SAAUjK,EAAGgB,EAAGC,GACrEC,KAAKgJ,MAAQlK,EAAKkB,KAAKiJ,OAASnJ,EAAKE,KAAKkJ,OAASnJ,EAAKC,KAAK+G,YAAYT,KAAKsE,YAAc5K,KAAK8I,MAAQhJ,EAAIE,KAAK8I,MAAQ,IAAM,KAAO9I,KAAK4C,OAAS7C,EAAIC,KAAK4C,OAAS,KAAQ5C,KAAK+G,YAAYT,KAAKwE,UAAY9K,KAAK8I,MAAQhK,EAAI,IAAMkB,KAAK4C,OAAS9D,CACxP,EACCqB,IAAI6K,gBAAkB,SAAUlM,EAAGgB,GACnCK,IAAI6K,gBAAgBjG,YAAYjE,KAAKd,KAAMlB,EAAGgB,CAAC,EAAIE,KAAKiL,YAAc,IAAI9K,IAAI4J,WAAW,MAAM,EAAK/J,KAAKkL,cAAgB,IAAI/K,IAAI4J,WAAW,QAAQ,EAAI/J,KAAKsG,KAAKW,YAAYjH,KAAKiL,YAAY3E,IAAI,EAAGtG,KAAKsG,KAAKW,YAAYjH,KAAKkL,cAAc5E,IAAI,EAAItG,KAAKsG,KAAK6E,QAAU,CAAA,EAAKhL,IAAIsH,qBAAqB/G,MAAMV,KAAMM,SAAS,CAC/T,EACAH,IAAI0E,SAAS1E,IAAI6K,gBAAiB7K,IAAI4J,UAAU,EAChD5J,IAAI6E,MAAM7E,IAAI6K,gBAAiB7K,IAAIsH,oBAAoB,EACtDtH,IAAI6K,gBAAgBpK,UAAU8F,UAAY,SAAU5H,EAAGgB,GACvD,OAAQhB,GACP,IAAK,OACJkB,KAAKsG,KAAK8E,UAAYtL,EACtB,MACD,IAAK,eACJE,KAAKiL,YAAY3E,KAAK+E,QAAUxH,KAAKyH,MAAM,IAAMxL,CAAC,EAAI,IACtD,MACD,IAAK,SACHE,KAAKsG,KAAK6E,QAAU,SAAWrL,EAAKE,KAAKsG,KAAKiF,YAAczL,EAC7D,MACD,IAAK,iBACJE,KAAKkL,cAAc5E,KAAK+E,QAAUxH,KAAKyH,MAAM,IAAMxL,CAAC,EAAI,IACxD,MACD,IAAK,eACJ,IAAM4C,SAAS5C,EAAG,EAAE,EAAKE,KAAKsG,KAAK6E,QAAU,CAAA,EAAOnL,KAAKsG,KAAK6E,QAAU,CAAA,EAAMnL,KAAKsG,KAAKkF,aAAe1L,EACvG,MACD,IAAK,IACJE,KAAKsG,KAAKmF,KAAOtL,IAAIuL,eAAeC,aAAa7L,CAAC,EAClD,MACD,QACCK,IAAI6K,gBAAgBjG,YAAYnE,UAAU8F,UAAUhG,MAAMV,KAAMM,SAAS,CAC3E,CACD,EACCH,IAAIuL,eAAiB,SAAU5M,EAAGgB,GAClC,IAAIC,EAAI,IAAII,IAAI4J,WAAW,MAAM,EACjC5J,IAAIuL,eAAe3G,YAAYjE,KAAKd,KAAM,QAASlB,EAAGgB,CAAC,EAAIE,KAAKsG,KAAKsE,YAAc,MAAS7K,EAAEuG,KAAKsF,GAAK,CAAA,EAAM7L,EAAEuG,KAAKuF,OAAS,oBAAuB9L,EAAEuG,KAAKwF,OAAS,MAAQ9L,KAAKsG,KAAKW,YAAYlH,EAAEuG,IAAI,CAC1M,EACAnG,IAAI0E,SAAS1E,IAAIuL,eAAgBvL,IAAI6K,eAAe,EACnD7K,IAAIuL,eAAe9K,UAAU8F,UAAY,SAAU5H,EAAGgB,GACtD,MAAQhB,EAAKkB,KAAKsG,KAAKmF,KAAOtL,IAAIuL,eAAeC,aAAa7L,CAAC,EAAKK,IAAI6K,gBAAgBpK,UAAU8F,UAAU5F,KAAKd,KAAMlB,EAAGgB,CAAC,CAC5H,EACCK,IAAIuL,eAAeC,aAAe,SAAU7M,GAC5C,IAAImE,EACH5B,EACAC,EAAI,EACJC,EAAI,EACL,OAAQzC,EAAIA,EAAEiN,QAAQ,mBAAoB,GAAG,GAC3CA,QAAQ,qDAAsD,SAAUjN,EAAGgB,EAAGC,EAAGhB,IAChFgB,EAAIA,EAAEgM,QAAQ,SAAU,MAAM,EAAEA,QAAQ,QAAS,EAAE,EAAEA,QAAQ,QAAS,EAAE,EAAEA,QAAQ,OAAQ,GAAG,EAAEC,MAAM,GAAG,GAAG,IAAMjM,EAAEkM,MAAM,EAC1H,IAAK,IAAI7J,EAAI,EAAGY,EAAIjD,EAAE8B,OAAQO,EAAIY,EAAGZ,CAAC,GAAIrC,EAAEqC,GAAKyB,KAAKyH,MAAM,IAAMvL,EAAEqC,EAAE,EACtE,OAAQtC,GACP,IAAK,IACJ,OAAQwB,GAAKvB,EAAE,GAAMwB,GAAKxB,EAAE,GAAK,IAAMA,EAAEmM,KAAK,GAAG,EAClD,IAAK,IACJ,OAAQ5K,EAAIvB,EAAE,GAAMwB,EAAIxB,EAAE,GAAK,IAAMA,EAAEmM,KAAK,GAAG,EAChD,IAAK,IACJ,OAAQ5K,GAAKvB,EAAE,GAAMwB,GAAKxB,EAAE,GAAK,IAAMA,EAAEmM,KAAK,GAAG,EAClD,IAAK,IACJ,OAAQ5K,EAAIvB,EAAE,GAAMwB,EAAIxB,EAAE,GAAK,IAAMA,EAAEmM,KAAK,GAAG,EAChD,IAAK,IACJ,OAAQ5K,GAAKvB,EAAE,GAAK,IAAMA,EAAE,GAAK,KAClC,IAAK,IACJ,MAAO,KAAOuB,EAAIvB,EAAE,IAAM,IAAMwB,EACjC,IAAK,IACJ,OAAQA,GAAKxB,EAAE,GAAK,MAAQA,EAAE,GAC/B,IAAK,IACJ,OAAQwB,EAAIxB,EAAE,GAAK,IAAMuB,EAAI,IAAMC,EACpC,IAAK,IACJ,OAAQ0B,EAAI3B,EAAIvB,EAAEA,EAAE8B,OAAS,GAAMR,EAAIE,EAAIxB,EAAEA,EAAE8B,OAAS,GAAMP,GAAKvB,EAAEA,EAAE8B,OAAS,GAAMN,GAAKxB,EAAEA,EAAE8B,OAAS,GAAK,IAAM9B,EAAEmM,KAAK,GAAG,EAC9H,IAAK,IACJ,OAAQjJ,EAAIlD,EAAEA,EAAE8B,OAAS,GAAMR,EAAItB,EAAEA,EAAE8B,OAAS,GAAMP,EAAIvB,EAAEA,EAAE8B,OAAS,GAAMN,EAAIxB,EAAEA,EAAE8B,OAAS,GAAK,IAAM9B,EAAEmM,KAAK,GAAG,EACpH,IAAK,IACJ,OAAOnM,EAAEmE,QAAQ3C,EAAIF,CAAC,EAAGtB,EAAEmE,QAAQ5C,EAAI2B,CAAC,EAAIA,EAAI3B,EAAIvB,EAAEA,EAAE8B,OAAS,GAAMR,EAAIE,EAAIxB,EAAEA,EAAE8B,OAAS,GAAMP,GAAKvB,EAAEA,EAAE8B,OAAS,GAAMN,GAAKxB,EAAEA,EAAE8B,OAAS,GAAK,IAAM9B,EAAEmM,KAAK,GAAG,EAClK,IAAK,IACJ,OAAOnM,EAAEmE,QAAQ3C,EAAIA,EAAIF,CAAC,EAAGtB,EAAEmE,QAAQ5C,EAAIA,EAAI2B,CAAC,EAAIA,EAAIlD,EAAEA,EAAE8B,OAAS,GAAMR,EAAItB,EAAEA,EAAE8B,OAAS,GAAMP,EAAIvB,EAAEA,EAAE8B,OAAS,GAAMN,EAAIxB,EAAEA,EAAE8B,OAAS,GAAK,IAAM9B,EAAEmM,KAAK,GAAG,CACjK,CACA,MAAO,EACR,CAAC,EACAH,QAAQ,KAAM,GAAG,CACpB,EACC5L,IAAIgM,iBAAmB,SAAUrN,EAAGgB,GACpCK,IAAIgM,iBAAiBpH,YAAYjE,KAAKd,KAAM,OAAQlB,EAAGgB,CAAC,CACzD,EACAK,IAAI0E,SAAS1E,IAAIgM,iBAAkBhM,IAAI6K,eAAe,EACrD7K,IAAIgM,iBAAiBvL,UAAU8F,UAAY,SAAU5H,EAAGgB,GACxD,OAAQhB,GACP,IAAK,IACHkB,KAAKsG,KAAKoB,MAAMoB,MAAQ,EAAIhJ,EAAI,KAAQE,KAAKsG,KAAKoB,MAAM9E,OAAS,EAAI9C,EAAI,KAAOE,KAAK0G,UAAU,KAAM1G,KAAKT,IAAI,IAAI,GAAK,CAAC,EAAGS,KAAK0G,UAAU,KAAM1G,KAAKT,IAAI,IAAI,GAAK,CAAC,EACpK,MACD,IAAK,KACCO,IACLE,KAAKsG,KAAKoB,MAAM8C,KAAO1K,GAAKE,KAAKT,IAAI,GAAG,GAAK,GAAK,MAClD,MACD,IAAK,KACCO,IACLE,KAAKsG,KAAKoB,MAAMgD,IAAM5K,GAAKE,KAAKT,IAAI,GAAG,GAAK,GAAK,MACjD,MACD,QACCY,IAAIgM,iBAAiBpH,YAAYnE,UAAU8F,UAAU5F,KAAKd,KAAMlB,EAAGgB,CAAC,CACtE,CACD,EACCK,IAAIiM,aAAe,SAAUtN,EAAGgB,EAAGC,GACnC,OAAQC,KAAKqM,KAAOtJ,OAAOuJ,SAAW,MAAQ,MAAQ,OAAStM,KAAKqM,KAAQrM,KAAKuM,KAAO,IAAIpM,IAAIyI,iBAAiB9J,EAAGgB,EAAGC,CAAC,EAAMC,KAAKuM,KAAO,IAAIpM,IAAI0K,iBAAiB/L,EAAGgB,EAAGC,CAAC,EAAKC,KAAKuM,KAAKF,KAAOrM,KAAKqM,KAAOrM,KAAKuM,IAClN,EACCpM,IAAIqM,YAAc,SAAU1N,GAC5BkB,KAAKgJ,MAAQlK,CACd,EACCqB,IAAIqM,YAAY5L,UAAU6L,SAAW,SAAU3N,GAC/C,OAAOA,CACR,EACCqB,IAAIuM,aAAe,SAAU5N,GAC7BkB,KAAKgJ,MAAQlK,CACd,EACCqB,IAAIuM,aAAa9L,UAAU6L,SAAW,SAAU3N,GAChD,OAAOkB,KAAKgJ,MAAMlK,EACnB,EACCqB,IAAIuM,aAAa9L,UAAU+L,SAAW,WACtC,IAAI7N,EACHgB,EAAI,GACL,IAAKhB,KAAKkB,KAAKgJ,MAAOlJ,EAAEyF,KAAK,CAAEqH,MAAO9N,EAAG+N,MAAO7M,KAAKgJ,MAAMlK,EAAG,CAAC,EAC/D,OAAOgB,CACR,EACCK,IAAI2M,aAAe,SAAUhO,EAAGgB,EAAGC,EAAGhB,GACrCiB,KAAKgJ,MAAQ,GAAMlJ,EAAIA,GAAK,SAAWhB,GAAKkB,KAAK+M,SAASjO,CAAC,EAAGgB,GAAKE,KAAKgN,qBAAqBlN,CAAC,EAAG,KAAA,IAAWC,GAAKC,KAAKiN,OAAOlN,CAAC,EAAG,KAAA,IAAWhB,GAAKiB,KAAKkN,OAAOnO,CAAC,CAChK,EACCoB,IAAI2M,aAAalM,UAAY,CAC7BqM,OAAQ,SAAUnO,GAChBkB,KAAKmN,cAAgBrO,EAAI,YAAc,OAAOkB,KAAKoN,UAAapN,KAAKqN,SAAWrN,KAAKoN,UAAUtO,CAAC,EAAMkB,KAAKqN,SAAWvO,CACxH,EACAoO,OAAQ,SAAUpO,GAChBkB,KAAKsN,cAAgBxO,EAAI,YAAc,OAAOkB,KAAKoN,UAAapN,KAAKuN,SAAWvN,KAAKoN,UAAUtO,CAAC,EAAMkB,KAAKuN,SAAWzO,CACxH,EACAiO,SAAU,SAAUjO,GACnB,IAAIgB,EACJ,IAAKE,KAAKgJ,MAAQ,GAAIlJ,EAAI,EAAGA,EAAIhB,EAAE+C,OAAQ/B,CAAC,GAAIE,KAAKgJ,MAAMlJ,GAAK,CAAChB,EAAEgB,GACpE,EACAkN,qBAAsB,SAAUlO,GAC/B,eAAiBA,EACbkB,KAAKoN,UAAY,SAAUtO,GAC5B,OAAO+E,KAAK2J,IAAI1O,EAAG,EAAG,CACtB,EACA,WAAaA,EACb,OAAOkB,KAAKoN,UACXpN,KAAKoN,UAAYtO,EACpBkB,KAAKiN,OAAOjN,KAAKmN,aAAa,EAC9BnN,KAAKkN,OAAOlN,KAAKsN,aAAa,CAChC,EACAb,SAAU,SAAU3N,GACnB,IAAIgB,EACHC,EACAhB,EAAI,GACJqD,EAAI,EACJY,EAAI,EACL,IAAK,YAAc,OAAOhD,KAAKoN,YAActO,EAAIkB,KAAKoN,UAAUtO,CAAC,GAAIkE,EAAI,EAAGA,EAAIhD,KAAKgJ,MAAMnH,OAAS,EAAGmB,CAAC,GAAKlD,EAAIE,KAAKyN,aAAazN,KAAK0N,eAAe1N,KAAKgJ,MAAMhG,EAAI,GAAIhD,KAAKgJ,MAAMhG,EAAE,CAAC,EAAIjE,EAAEwG,KAAKzF,CAAC,EAAIsC,GAAKtC,EAC7M,IAAKC,GAAKC,KAAKuN,SAAWvN,KAAKqN,UAAYjL,EAAGY,EAAI,EAAGA,EAAIjE,EAAE8C,OAAQmB,CAAC,GAAIjE,EAAEiE,IAAMjD,EAChF,IAAKiD,EAAI,EAAGlE,GAAKkB,KAAKqN,SAAU,GAAKvO,EAAIC,EAAEiE,IAAOlE,GAAKC,EAAEiE,GAAKA,CAAC,GAC/D,OAAOA,GAAKhD,KAAKgJ,MAAMnH,OAAS,EAAI7B,KAAK2N,YAAY3N,KAAKgJ,MAAMhG,EAAE,EAAIhD,KAAK2N,YAAY3N,KAAK4N,UAAU5N,KAAKgJ,MAAMhG,GAAIhD,KAAK6N,WAAW7N,KAAK0N,eAAe1N,KAAKgJ,MAAMhG,EAAI,GAAIhD,KAAKgJ,MAAMhG,EAAE,EAAGlE,EAAIC,EAAEiE,EAAE,CAAC,CAAC,CACvM,EACA2K,YAAa,SAAU7O,GACtB,IAAK,IAAIgB,EAAI,EAAGC,EAAI,EAAGA,EAAIjB,EAAE+C,OAAQ9B,CAAC,GAAID,GAAK+D,KAAKyH,MAAMxM,EAAEiB,EAAE,EAAI8D,KAAK2J,IAAI,IAAK1O,EAAE+C,OAAS9B,EAAI,CAAC,EAChG,OAAOD,CACR,EACA4N,eAAgB,SAAU5O,EAAGgB,GAC5B,IAAK,IAAIC,EAAI,GAAIhB,EAAI,EAAGA,EAAID,EAAE+C,OAAQ9C,CAAC,GAAIgB,EAAEhB,GAAKD,EAAEC,GAAKe,EAAEf,GAC3D,OAAOgB,CACR,EACA6N,UAAW,SAAU9O,EAAGgB,GACvB,IAAK,IAAIC,EAAI,GAAIhB,EAAI,EAAGA,EAAID,EAAE+C,OAAQ9C,CAAC,GAAIgB,EAAEhB,GAAKD,EAAEC,GAAKe,EAAEf,GAC3D,OAAOgB,CACR,EACA8N,WAAY,SAAU/O,EAAGgB,GACxB,IAAK,IAAIC,EAAI,GAAIhB,EAAI,EAAGA,EAAID,EAAE+C,OAAQ9C,CAAC,GAAIgB,EAAEhB,GAAKD,EAAEC,GAAKe,EACzD,OAAOC,CACR,EACA0N,aAAc,SAAU3O,GACvB,IAAK,IAAIgB,EAAI,EAAGC,EAAI,EAAGA,EAAIjB,EAAE+C,OAAQ9B,CAAC,GAAID,GAAKhB,EAAEiB,GAAKjB,EAAEiB,GACxD,OAAO8D,KAAKiK,KAAKhO,CAAC,CACnB,EACA6M,SAAU,WACT,IAAI7N,EACHgB,EACAC,EAAI,CAACC,KAAKmN,cAAenN,KAAKsN,eAC9BvO,EAAIgB,EAAE,GAAKA,EAAE,GACbqC,EAAIyB,KAAK2J,IAAI,GAAI3J,KAAKkK,MAAMlK,KAAKmK,IAAIjP,EAAI,CAAC,EAAI8E,KAAKoK,IAAI,CAAC,EACxDjL,EAAI,GACL,KAAMjE,EAAK,EAAIA,EAAKqD,IAAM,IAAQA,GAAK,GAAMrD,GAAK,IAAQqD,GAAK,EAAKrD,GAAK,MAASqD,GAAK,GAAIrC,EAAE,GAAK8D,KAAKkK,MAAMhO,EAAE,GAAKqC,CAAC,EAAIA,EAAGrC,EAAE,GAAK8D,KAAKqK,KAAKnO,EAAE,GAAKqC,CAAC,EAAIA,EAAGtD,EAAIiB,EAAE,GAAIjB,GAAKiB,EAAE,IAAOD,EAAIhB,GAAKiB,EAAE,GAAKC,KAAKmN,cAAgBrO,GAAKiB,EAAE,GAAKC,KAAKsN,cAAgBxO,EAAIkE,EAAEuC,KAAK,CAAEqH,MAAO9N,EAAG+N,MAAO7M,KAAKyM,SAAS3M,CAAC,CAAE,CAAC,EAAIhB,GAAKsD,EACnT,OAAOY,CACR,CACD,EACC7C,IAAIgO,WAAa,SAAUrP,EAAGgB,EAAGC,EAAGhB,GACpCoB,IAAIgO,WAAWpJ,YAAYrE,MAAMV,KAAMM,SAAS,CACjD,EACAH,IAAI0E,SAAS1E,IAAIgO,WAAYhO,IAAI2M,YAAY,EAC5C3M,IAAIgO,WAAWvN,UAAUmM,SAAW,SAAUjO,GAC9C,IAAK,IAAIgB,EAAI,EAAGA,EAAIhB,EAAE+C,OAAQ/B,CAAC,GAAIE,KAAKgJ,MAAMlJ,GAAKK,IAAIgO,WAAWC,WAAWtP,EAAEgB,EAAE,CAClF,EACCK,IAAIgO,WAAWvN,UAAU6L,SAAW,SAAU3N,GAC9C,OAAOqB,IAAIgO,WAAWE,SAASlO,IAAIgO,WAAWpJ,YAAYnE,UAAU6L,SAAS3L,KAAKd,KAAMlB,CAAC,CAAC,CAC3F,EACCqB,IAAIgO,WAAWG,WAAa,SAAUxP,GACtC,IAAK,IAAIgB,EAAGC,EAAI,IAAKhB,EAAI,EAAGA,EAAID,EAAE+C,OAAQ9C,CAAC,GAAIgB,GAAK,IAAMD,EAAIhB,EAAEC,GAAGwP,SAAS,EAAE,GAAG1M,OAAS,IAAM/B,EAAIA,EACpG,OAAOC,CACR,EACCI,IAAIgO,WAAWE,SAAW,SAAUvP,GACpC,IAAKA,EAAIA,EAAEyP,SAAS,EAAE,EAAGzP,EAAE+C,OAAS,GAAK/C,EAAI,IAAMA,EACnD,MAAO,IAAMA,CACd,EACCqB,IAAIgO,WAAWC,WAAa,SAAUtP,GACtC,OAAQA,EAAIA,EAAE2B,OAAO,CAAC,EAAI,CAACiC,SAAS5D,EAAE2B,OAAO,EAAG,CAAC,EAAG,EAAE,EAAGiC,SAAS5D,EAAE2B,OAAO,EAAG,CAAC,EAAG,EAAE,EAAGiC,SAAS5D,EAAE2B,OAAO,EAAG,CAAC,EAAG,EAAE,EACnH,EACCN,IAAIqO,OAAS,SAAU1P,GACtBkB,KAAKyO,OAAS3P,GAAK,GAAMkB,KAAK0O,IAAM1O,KAAKyO,OAAOC,IAAO1O,KAAK2O,OAAS3O,KAAKyO,OAAOE,OAAU3O,KAAK4O,KAAOzO,IAAIsF,EAAE,QAAQ,EAAIzF,KAAK4O,KAAKpG,SAAS,mBAAmB,EAAGxI,KAAKyO,OAAOI,UAAY7O,KAAK4O,KAAKpG,SAASxI,KAAKyO,OAAOI,QAAQ,GAAI/P,EAAEgQ,SAAW9O,KAAK0O,IAAIK,kBAAoB/O,KAAK0O,IAAIM,qBAAqBC,OAAOjP,KAAK4O,IAAI,EAAG5O,KAAKkP,OAAO,CAC9U,EACC/O,IAAIqO,OAAO5N,UAAUsO,OAAS,WAC9B,IAAIpQ,EACHgB,EACAC,EACAhB,EACAqD,EAAIpC,KAAK2O,OAAO3F,MAAM2D,SAAS,EAC/B3J,EAAI7C,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,yBAAyB,EACvD,IAAKxI,KAAK4O,KAAKO,KAAK,EAAE,EAAGnP,KAAKyO,OAAOW,OAASpP,KAAK4O,KAAKK,OAAO9O,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,yBAAyB,EAAE2G,KAAKnP,KAAKyO,OAAOW,KAAK,CAAC,EAAGpP,KAAK4O,KAAKK,OAAOjM,CAAC,EAAGlE,EAAI,EAAGA,EAAIsD,EAAEP,OAAQ/C,CAAC,GAAI,CAC3L,OAAUgB,EAAIK,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,wBAAwB,EAAKzI,EAAII,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,+BAA+B,EAAIxI,KAAK2O,OAAOF,OAAOY,WACtJ,IAAK,OACJlP,IAAI6F,WAAW5D,EAAEtD,GAAG+N,KAAK,EAAI9M,EAAE4C,IAAI,aAAc,OAASP,EAAEtD,GAAG+N,MAAQ,GAAG,EAAI9M,EAAE4C,IAAI,aAAcP,EAAEtD,GAAG+N,KAAK,EAC5G,MACD,IAAK,SACJ9M,EAAE4C,IAAI,aAAcP,EAAEtD,GAAG+N,KAAK,EAC9B,MACD,IAAK,QACJ9M,EAAE4C,IAAI,aAAc,OAASP,EAAEtD,GAAG+N,MAAQ,2BAA2B,EACrE,MACD,IAAK,IACJ1M,IAAIsF,EAAE,QAAQ,EACZ9C,IAAI,CAAE2M,gBAAiBlN,EAAEtD,GAAG+N,MAAO0C,OAAQvP,KAAK0O,IAAID,OAAOe,YAAYvH,QAAQ,gBAAkB,MAAQjI,KAAK0O,IAAID,OAAOe,YAAYvH,QAAQwH,OAAS,SAAU3G,MAAO,EAAI1G,EAAEtD,GAAG+N,MAAQ,KAAMjK,OAAQ,EAAIR,EAAEtD,GAAG+N,MAAQ,KAAM6C,WAAY1P,KAAK0O,IAAID,OAAOe,YAAYvH,QAAQ0H,IAAK,CAAC,EACnRC,SAAS7P,CAAC,CACd,CACAD,EAAEmP,OAAOlP,CAAC,EAAIhB,EAAIqD,EAAEtD,GAAG8N,MAAQ5M,KAAKyO,OAAOoB,cAAgB9Q,EAAIiB,KAAKyO,OAAOoB,YAAY9Q,CAAC,GAAIe,EAAEmP,OAAO9O,IAAIsF,EAAE,QAAU1G,EAAI,SAAS,EAAEyJ,SAAS,6BAA6B,CAAC,EAAGxF,EAAEiM,OAAOnP,CAAC,CACzL,CACAkD,EAAEiM,OAAO9O,IAAIsF,EAAE,QAAQ,EAAE9C,IAAI,QAAS,MAAM,CAAC,CAC9C,EACCxC,IAAI2P,WAAa,SAAUhR,EAAGgB,EAAGC,IAC/BjB,EAAIA,GAAK,IAAIuQ,UAAYvQ,EAAEuQ,WAAa,OAAUrP,KAAK+P,SAAWjQ,EAAKE,KAAKyO,OAAS3P,EAAKkB,KAAK0O,IAAM3O,EAAIjB,EAAEkR,YAAchQ,KAAKiQ,cAAcnR,EAAEkR,UAAU,EAAG7P,IAAIsF,EAAEyK,QAAQpR,EAAEkK,KAAK,GAAMjJ,EAAI,SAAWjB,EAAEuQ,WAAa,WAAavQ,EAAEuQ,UAAYlP,IAAIgO,WAAahO,IAAI2M,aAAgB9M,KAAKgJ,MAAQ,IAAIjJ,EAAEjB,EAAEkK,MAAOlK,EAAEO,kBAAmBP,EAAEoG,IAAKpG,EAAEgF,GAAG,GAAMhF,EAAEkK,MAAShJ,KAAKgJ,MAAQ,IAAI7I,IAAIuM,aAAa5N,EAAEkK,KAAK,EAAMhJ,KAAKgJ,MAAQ,IAAI7I,IAAIqM,YAAY1N,EAAEkK,KAAK,EAAKhJ,KAAKd,OAASJ,EAAEI,QAAU,GAAKc,KAAKmQ,UAAUnQ,KAAKd,MAAM,EAAGc,KAAKyO,OAAO2B,SAAWpQ,KAAKoQ,OAAS,IAAIjQ,IAAIqO,OAAO/I,EAAEjB,OAAO,CAAEkK,IAAK1O,KAAK0O,IAAKC,OAAQ3O,IAAK,EAAGA,KAAKyO,OAAO2B,MAAM,CAAC,EAC9mB,EACCjQ,IAAI2P,WAAWlP,UAAY,CAC3BqP,cAAe,SAAUnR,EAAGgB,GAC3B,IAAIC,EACHhB,EAAID,EACL,GAAI,UAAY,OAAOA,EAAGkB,KAAK+P,SAASjR,IAAMkB,KAAK+P,SAASjR,GAAGiJ,SAAS/H,KAAKyO,OAAOY,UAAWvP,CAAC,OAC3F,IAAKC,KAAKhB,EAAGiB,KAAK+P,SAAShQ,IAAMC,KAAK+P,SAAShQ,GAAGsQ,QAAQtI,SAAS/H,KAAKyO,OAAOY,UAAWtQ,EAAEgB,EAAE,CACpG,EACAoQ,UAAW,SAAUrR,GACpB,IAAIgB,EACHC,EACAhB,EAAI,CAACoG,OAAOC,UACZhD,EAAI+C,OAAOC,UACXpC,EAAI,GACL,GAAIhD,KAAKgJ,iBAAiB7I,IAAIuM,cAAgB1M,KAAKgJ,iBAAiB7I,IAAIqM,YAAa,IAAKzM,KAAKjB,EAAGA,EAAEiB,GAAMiD,EAAEjD,GAAKC,KAAKgJ,MAAMyD,SAAS3N,EAAEiB,EAAE,EAAMiD,EAAEjD,GAAKC,KAAK+P,SAAShQ,GAAGsQ,QAAQ3I,MAAMO,QAAQjI,KAAKyO,OAAOY,eACpM,CACJ,GAAI,KAAA,IAAWrP,KAAKyO,OAAOvJ,KAAO,KAAA,IAAWlF,KAAKyO,OAAO3K,IAAK,IAAK/D,KAAKjB,EAAGC,GAAKe,EAAIwQ,WAAWxR,EAAEiB,EAAE,KAAOhB,EAAIe,GAAIA,EAAIsC,IAAMA,EAAItC,GAChI,IAAKC,KAAM,KAAA,IAAWC,KAAKyO,OAAOvJ,KAAOlF,KAAKgJ,MAAMiE,OAAO7K,CAAC,EAAIpC,KAAKyO,OAAOvJ,IAAM9C,GAAMpC,KAAKgJ,MAAMiE,OAAOjN,KAAKyO,OAAOvJ,GAAG,EAAG,KAAA,IAAWlF,KAAKyO,OAAO3K,KAAO9D,KAAKgJ,MAAMkE,OAAOnO,CAAC,EAAIiB,KAAKyO,OAAO3K,IAAM/E,GAAMiB,KAAKgJ,MAAMkE,OAAOlN,KAAKyO,OAAO3K,GAAG,EAAGhF,EAAI,WAAaiB,IAAOD,EAAIwQ,WAAWxR,EAAEiB,EAAE,EAAIwQ,MAAMzQ,CAAC,EAAKkD,EAAEjD,GAAKC,KAAK+P,SAAShQ,GAAGsQ,QAAQ3I,MAAMO,QAAQjI,KAAKyO,OAAOY,WAAerM,EAAEjD,GAAKC,KAAKgJ,MAAMyD,SAAS3M,CAAC,EAC9Y,CACAE,KAAKiQ,cAAcjN,CAAC,EAAG7C,IAAIsF,EAAEjB,OAAOxE,KAAKd,OAAQJ,CAAC,CACnD,EACA0R,MAAO,WACN,IAAI1R,EACHgB,EAAI,GACL,IAAKhB,KAAKkB,KAAKd,OAAQc,KAAK+P,SAASjR,KAAOgB,EAAEhB,GAAKkB,KAAK+P,SAASjR,GAAGuR,QAAQI,MAAM/I,MAAMO,QAAQjI,KAAKyO,OAAOY,YAC5GrP,KAAKiQ,cAAcnQ,CAAC,EAAIE,KAAKd,OAAS,EACvC,EACA6N,SAAU,SAAUjO,GACnBkB,KAAKgJ,MAAM+D,SAASjO,CAAC,EAAGkB,KAAKd,QAAUc,KAAKmQ,UAAUnQ,KAAKd,MAAM,CAClE,EACA8N,qBAAsB,SAAUlO,GAC/BkB,KAAKgJ,MAAMgE,qBAAqBlO,CAAC,EAAGkB,KAAKd,QAAUc,KAAKmQ,UAAUnQ,KAAKd,MAAM,CAC9E,CACD,EACCiB,IAAIuQ,KAAO,CACXC,OAAQ,IAAM9M,KAAK+M,GACnBC,OAAQhN,KAAK+M,GAAK,IAClBE,OAAQ,QACRC,IAAK,SAAUjS,GACd,OAAO,EAAIA,EAAI,EAAIA,EAAI,EAAI,CAAC,EAAIA,CACjC,EACAkS,KAAM,SAAUlS,EAAGgB,EAAGC,GACrB,MAAO,CAAEuK,EAAGtK,KAAK8Q,QAAUhR,EAAIC,GAAKC,KAAK6Q,OAAQpG,EAAI,CAACzK,KAAK8Q,OAASjN,KAAKmK,IAAInK,KAAKoN,KAAK,GAAK,GAAMnS,GAAKkB,KAAK6Q,MAAM,CAAC,EAAK,EAAI,CAC7H,EACAK,SAAU,SAAUpS,EAAGgB,EAAGC,GACzB,MAAO,CAAEoR,KAAM,IAAMtN,KAAKuN,KAAKvN,KAAKwN,IAAK,GAAMvR,EAAKE,KAAK8Q,MAAM,CAAC,EAAK,EAAIjN,KAAK+M,GAAM,GAAK5Q,KAAK2Q,OAAQW,KAAMvR,EAAIC,KAAK6Q,OAAS/R,EAAIkB,KAAK8Q,QAAU9Q,KAAK2Q,MAAO,CAC9J,EACAY,KAAM,SAAUzS,EAAGgB,EAAGC,GACrB,MAAO,CAAEuK,EAAGtK,KAAK8Q,QAAUhR,EAAIC,GAAKC,KAAK6Q,OAAQpG,EAAG,CAACzK,KAAK8Q,OAASjN,KAAKmK,IAAInK,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAK9R,EAAI+E,KAAK+M,GAAM,GAAG,CAAC,CAAE,CAC1H,EACAY,SAAU,SAAU1S,EAAGgB,EAAGC,GACzB,MAAO,CAAEoR,KAAM,EAAItN,KAAKuN,KAAKvN,KAAKwN,IAAIvR,EAAIE,KAAK8Q,MAAM,CAAC,EAAIjN,KAAK+M,GAAK,GAAK5Q,KAAK2Q,OAAQW,KAAMvR,EAAIC,KAAK6Q,OAAS/R,EAAIkB,KAAK8Q,QAAU9Q,KAAK2Q,MAAO,CAC9I,EACAc,IAAK,SAAU3S,EAAGgB,EAAGC,GACpB,IAAIhB,EAAIgB,EAAIC,KAAK6Q,OAChBzO,EAAI,KAAOpC,KAAK6Q,OAChB9Q,EAAI,KAAOC,KAAK6Q,OAChB/R,EAAIA,EAAIkB,KAAK6Q,OACb/Q,EAAIA,EAAIE,KAAK6Q,OACb9Q,GAAK8D,KAAK6N,IAAItP,CAAC,EAAIyB,KAAK6N,IAAI3R,CAAC,GAAK,EAClCqC,EAAIyB,KAAK8N,IAAIvP,CAAC,EAAIyB,KAAK8N,IAAIvP,CAAC,EAAI,EAAIrC,EAAI8D,KAAK6N,IAAItP,CAAC,EAClDrD,EAAIgB,GAAKD,EAAIf,GACbD,EAAI+E,KAAKiK,KAAK1L,EAAI,EAAIrC,EAAI8D,KAAK6N,IAAI5S,CAAC,CAAC,EAAIiB,EACzCA,EAAI8D,KAAKiK,KAAK1L,EAAI,EAAIrC,EAAI8D,KAAK6N,IAAI,CAAC,CAAC,EAAI3R,EAC1C,MAAO,CAAEuK,EAAGxL,EAAI+E,KAAK6N,IAAI3S,CAAC,EAAIiB,KAAK8Q,OAAQrG,EAAG,EAAE1K,EAAIjB,EAAI+E,KAAK8N,IAAI5S,CAAC,GAAKiB,KAAK8Q,MAAO,CACpF,EACAc,QAAS,SAAU9S,EAAGgB,EAAGC,GACxB,IAAIhB,EAAID,EAAIkB,KAAK8Q,OAChB1O,EAAItC,EAAIE,KAAK8Q,OACb9N,EAAIjD,EAAIC,KAAK6Q,OACb5N,EAAI,KAAOjD,KAAK6Q,OAChB/R,EAAI,KAAOkB,KAAK6Q,OAChB/Q,GAAK+D,KAAK6N,IAAIzO,CAAC,EAAIY,KAAK6N,IAAI5S,CAAC,GAAK,EAClCiB,EAAI8D,KAAK8N,IAAI1O,CAAC,EAAIY,KAAK8N,IAAI1O,CAAC,EAAI,EAAInD,EAAI+D,KAAK6N,IAAIzO,CAAC,EAClDnE,EAAI+E,KAAKiK,KAAK/N,EAAI,EAAID,EAAI+D,KAAK6N,IAAI,CAAC,CAAC,EAAI5R,EACzCmD,EAAIY,KAAKiK,KAAK/O,EAAIA,GAAKD,EAAIsD,IAAMtD,EAAIsD,EAAE,EACvCA,EAAIyB,KAAKuN,KAAKrS,GAAKD,EAAIsD,EAAE,EAC1B,MAAO,CAAE+O,IAAKtN,KAAKgO,MAAM9R,EAAIkD,EAAIA,EAAInD,EAAIA,IAAM,EAAIA,EAAE,EAAIE,KAAK2Q,OAAQW,KAAMtO,EAAIZ,EAAItC,GAAKE,KAAK2Q,MAAO,CACtG,EACAmB,IAAK,SAAUhT,EAAGgB,EAAGC,GACpB,IAAIhB,EAAIgB,EAAIC,KAAK6Q,OAChBzO,EAAItC,EAAIE,KAAK6Q,OACb9Q,EAAI,GAAKC,KAAK6Q,OACd/Q,EAAI,GAAKE,KAAK6Q,OACd/R,EAAIA,EAAIkB,KAAK6Q,OACb/Q,EAAI+D,KAAKmK,IAAInK,KAAK8N,IAAI5R,CAAC,GAAK,EAAI8D,KAAK8N,IAAI7R,CAAC,EAAE,EAAI+D,KAAKmK,IAAInK,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI9Q,EAAI,CAAC,GAAK,EAAI+D,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI7Q,EAAI,CAAC,EAAE,EAC5HjB,GAAKiB,EAAK8D,KAAK8N,IAAI5R,CAAC,EAAI8D,KAAK2J,IAAI3J,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI7Q,EAAI,CAAC,EAAGD,CAAC,EAAKA,GAAK+D,KAAK2J,IAAI,EAAI3J,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI9R,EAAI,CAAC,EAAGgB,CAAC,EACxHC,EAAIA,EAAI8D,KAAK2J,IAAI,EAAI3J,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI,CAAC,EAAG9Q,CAAC,EAClD,MAAO,CAAEwK,EAAGxL,EAAI+E,KAAK6N,IAAI5R,GAAKsC,EAAIrD,EAAE,EAAIiB,KAAK8Q,OAAQrG,EAAG,EAAE1K,EAAIjB,EAAI+E,KAAK8N,IAAI7R,GAAKsC,EAAIrD,EAAE,GAAKiB,KAAK8Q,MAAO,CACxG,EACAiB,QAAS,SAAUjT,EAAGgB,EAAGC,GACxB,IAAIhB,EAAID,EAAIkB,KAAK8Q,OAChB1O,EAAItC,EAAIE,KAAK8Q,OACb9N,EAAIjD,EAAIC,KAAK6Q,OACb5N,EAAI,GAAKjD,KAAK6Q,OACd/R,EAAI,GAAKkB,KAAK6Q,OACd/Q,EAAI+D,KAAKmK,IAAInK,KAAK8N,IAAI1O,CAAC,GAAK,EAAIY,KAAK8N,IAAI7S,CAAC,EAAE,EAAI+E,KAAKmK,IAAInK,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI9R,EAAI,CAAC,GAAK,EAAI+E,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI3N,EAAI,CAAC,EAAE,EAC5HnE,GAAKiB,EAAK8D,KAAK8N,IAAI1O,CAAC,EAAIY,KAAK2J,IAAI3J,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI3N,EAAI,CAAC,EAAGnD,CAAC,EAAKA,GAAK+D,KAAK2J,IAAI,EAAI3J,KAAKoN,IAAIpN,KAAK+M,GAAK,EAAI,CAAC,EAAG9Q,CAAC,EACpHmD,EAAIjD,KAAK+Q,IAAIjR,CAAC,EAAI+D,KAAKiK,KAAK/O,EAAIA,GAAKD,EAAIsD,IAAMtD,EAAIsD,EAAE,EACrDA,EAAIyB,KAAKuN,KAAKrS,GAAKD,EAAIsD,EAAE,EAC1B,MAAO,CAAE+O,KAAM,EAAItN,KAAKuN,KAAKvN,KAAK2J,IAAIzN,EAAIkD,EAAG,EAAInD,CAAC,CAAC,EAAI+D,KAAK+M,GAAK,GAAK5Q,KAAK2Q,OAAQW,KAAMtO,EAAIZ,EAAItC,GAAKE,KAAK2Q,MAAO,CACnH,CACD,EACCxQ,IAAI6R,UAAY,SAAUlT,KAC1BqB,IAAI6R,UAAUpR,UAAUqR,aAAe,SAAUnT,GACjD,OAAOkB,KAAKkS,OAAOtF,MAAS,YAAc,OAAO5M,KAAKkS,OAAOtF,MAAMsC,OAASlP,KAAKkS,OAAOtF,MAAMsC,OAAOpQ,CAAC,EAAIA,EAAK,IAChH,EACCqB,IAAI6R,UAAUpR,UAAUuR,gBAAkB,SAAUrT,GACpD,IAAIgB,EACJ,OAAOE,KAAKkS,OAAOtF,QAAU,YAAc,OAAO5M,KAAKkS,OAAOtF,MAAMwF,QAAWtS,EAAIE,KAAKkS,OAAOtF,MAAMwF,QAAQtT,CAAC,EAAK,UAAY,OAAOkB,KAAKkS,OAAOtF,MAAMwF,UAAYtS,EAAIE,KAAKkS,OAAOtF,MAAMwF,QAAQtT,KAAMgB,GAAK,CAAC,EAAG,EAClN,EACCK,IAAI6R,UAAUpR,UAAUyR,WAAa,SAAUvT,GAC/CkB,KAAK4H,YAAc9I,IAAOkB,KAAK4H,UAAY9I,EAAKkB,KAAKyQ,MAAM7I,UAAY9I,EAAIkB,KAAKyQ,MAAM3I,YAAY,EAAG9H,KAAK4M,SAAW5M,KAAK4M,MAAMhF,UAAY9I,EAAIkB,KAAK4M,MAAM9E,YAAY,EACxK,EACC3H,IAAI6R,UAAUpR,UAAU0R,YAAc,SAAUxT,GAChDkB,KAAK6H,aAAe/I,IAAOkB,KAAK6H,WAAa/I,EAAKkB,KAAKyQ,MAAM5I,WAAa/I,EAAIkB,KAAKyQ,MAAM3I,YAAY,EAAG9H,KAAK4M,QAAW5M,KAAK4M,MAAM/E,WAAa/I,EAAIkB,KAAK4M,MAAM9E,YAAY,GAAI3H,IAAIsF,EAAEzF,KAAKyQ,KAAK,EAAE/L,QAAQ,WAAY,CAAC5F,EAAE,EACzN,EACCqB,IAAI6R,UAAUpR,UAAUmH,SAAW,WACnC/H,KAAKyQ,MAAM1I,SAASrH,MAAMV,KAAKyQ,MAAOnQ,SAAS,CAChD,EACCH,IAAI6R,UAAUpR,UAAUgG,OAAS,WACjC5G,KAAKyQ,MAAM7J,OAAO,EAAG5G,KAAK4M,OAAS5M,KAAK4M,MAAMhG,OAAO,CACtD,EACCzG,IAAIoS,OAAS,SAAUzT,GACvB,IAAIgB,EAAGC,EAAGhB,EACTiB,KAAKkS,OAASpT,EAAKkB,KAAK0O,IAAM1O,KAAKkS,OAAOxD,IAAO1O,KAAKyQ,MAAQ3R,EAAEqI,OAAOC,QAAQ,CAAEoL,EAAG1T,EAAE2M,KAAMgH,YAAa3T,EAAE4T,IAAK,EAAG5T,EAAE4I,MAAO5I,EAAEqI,OAAOJ,WAAW,EAAI/G,KAAKyQ,MAAMjI,SAAS,sCAAsC,EAAI1I,EAAIE,KAAKyQ,MAAM/H,QAAQ,EAAK3I,EAAIC,KAAKiS,aAAanT,EAAE4T,IAAI,EAAI1S,KAAKkS,OAAOtF,OAAS7M,IAAOhB,EAAIiB,KAAKmS,gBAAgBrT,EAAE4T,IAAI,EAAK1S,KAAK2S,OAAS7S,EAAEwK,EAAIxK,EAAEgJ,MAAQ,EAAI/J,EAAE,GAAMiB,KAAK4S,OAAS9S,EAAE2K,EAAI3K,EAAE8C,OAAS,EAAI7D,EAAE,GAAMiB,KAAK4M,MAAQ9N,EAAEqI,OAAOI,QAAQ,CAAEsL,KAAM9S,EAAG+S,cAAe,SAAUC,qBAAsB,UAAWzI,EAAGtK,KAAK2S,OAAQlI,EAAGzK,KAAK4S,OAAQH,YAAa3T,EAAE4T,IAAK,EAAG5T,EAAEkU,WAAYlU,EAAEmU,WAAW,EAAIjT,KAAK4M,MAAMpE,SAAS,sCAAsC,EACxpB,EACArI,IAAI0E,SAAS1E,IAAIoS,OAAQpS,IAAI6R,SAAS,EACrC7R,IAAIoS,OAAO3R,UAAUsS,oBAAsB,WAC3ClT,KAAK4M,OAAS5M,KAAK4M,MAAM5N,IAAI,CAAEsL,EAAGtK,KAAK2S,OAAS3S,KAAK0O,IAAI1F,MAAQhJ,KAAK0O,IAAIzF,OAASjJ,KAAK0O,IAAI1F,MAAOyB,EAAGzK,KAAK4S,OAAS5S,KAAK0O,IAAI1F,MAAQhJ,KAAK0O,IAAIxF,OAASlJ,KAAK0O,IAAI1F,KAAM,CAAC,CACxK,EACC7I,IAAIgT,OAAS,SAAUrU,GACvB,IAAIgB,EACHE,KAAKkS,OAASpT,EAAKkB,KAAK0O,IAAM1O,KAAKkS,OAAOxD,IAAO1O,KAAKoT,QAAU,CAAC,CAACpT,KAAKkS,OAAOxK,MAAMO,QAAQoL,MAAQrT,KAAKsT,YAAY,EAAIxT,EAAIE,KAAKiS,aAAanT,EAAEyU,KAAK,EAAIvT,KAAKkS,OAAOtF,OAAS9M,IAAOE,KAAKoS,QAAUpS,KAAKmS,gBAAgBrT,EAAEyU,KAAK,EAAKvT,KAAK2S,OAAS7T,EAAE6K,GAAK3J,KAAK0O,IAAI1F,MAAQhJ,KAAK0O,IAAIzF,OAAUjJ,KAAK4S,OAAS9T,EAAE8K,GAAK5J,KAAK0O,IAAI1F,MAAQhJ,KAAK0O,IAAIxF,OAAUlJ,KAAK4M,MAAQ9N,EAAEqI,OAAOI,QAAQ,CAAEsL,KAAM/S,EAAG0T,aAAc1U,EAAEyU,MAAOE,GAAI,QAASnJ,EAAGtK,KAAK2S,OAAQlI,EAAGzK,KAAK4S,MAAO,EAAG9T,EAAEkU,WAAYlU,EAAEmU,WAAW,EAAIjT,KAAK4M,MAAMpE,SAAS,sCAAsC,EACliB,EACArI,IAAI0E,SAAS1E,IAAIgT,OAAQhT,IAAI6R,SAAS,EACrC7R,IAAIgT,OAAOvS,UAAU0S,YAAc,WACnC,IAAIxU,EAAIkB,KACRA,KAAKyQ,OAASzQ,KAAKyQ,MAAM7J,OAAO,EAC9B5G,KAAKyQ,MAAQzQ,KAAKkS,OAAO/K,OAAOnH,KAAKoT,QAAU,WAAa,aAAa,CAAEI,aAAcxT,KAAKkS,OAAOqB,MAAO5J,GAAI3J,KAAKkS,OAAOvI,GAAIC,GAAI5J,KAAKkS,OAAOtI,EAAG,EAAG5J,KAAKkS,OAAOxK,MAAO1H,KAAKkS,OAAOwB,KAAK,EAC3L1T,KAAKyQ,MAAMjI,SAAS,sCAAsC,EAC1DxI,KAAKoT,SACJjT,IAAIsF,EAAEzF,KAAKyQ,MAAMnK,IAAI,EAAEsF,GAAG,cAAe,WACxC9M,EAAEoU,oBAAoB,CACvB,CAAC,CACJ,EACC/S,IAAIgT,OAAOvS,UAAUsS,oBAAsB,WAC3ClT,KAAK4M,OAAS5M,KAAK4M,MAAM5N,IAAI,CAAEsL,EAAGtK,KAAK2S,OAAS3S,KAAK0O,IAAI1F,MAAQhJ,KAAKoS,QAAQ,GAAKpS,KAAK0O,IAAIzF,OAASjJ,KAAK0O,IAAI1F,MAAQ,GAAKhJ,KAAKoT,SAAWpT,KAAKyQ,MAAM3H,OAAS,GAAK,EAAI9I,KAAKyQ,MAAMhK,WAAWxD,GAAIwH,EAAGzK,KAAK4S,OAAS5S,KAAK0O,IAAI1F,MAAQhJ,KAAK0O,IAAIxF,OAASlJ,KAAK0O,IAAI1F,MAAQhJ,KAAKoS,QAAQ,EAAG,CAAC,CACzR,EACCjS,IAAIgT,OAAOvS,UAAUmH,SAAW,SAAUjJ,EAAGgB,GAC7C,IAAIC,EACJI,IAAIgT,OAAOpO,YAAYnE,UAAUmH,SAASrH,MAAMV,KAAMM,SAAS,EAAG,MAAQxB,GAAKkB,KAAKkT,oBAAoB,GAAInT,EAAI,CAAC,CAACC,KAAKyQ,MAAMlR,IAAI,OAAO,IAAMS,KAAKoT,UAAapT,KAAKoT,QAAUrT,EAAKC,KAAKkS,OAAOxK,MAAQvH,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIxE,KAAKyQ,MAAM/I,KAAK,EAAI1H,KAAKsT,YAAY,EACnQ,EACCnT,IAAIC,IAAM,SAAUtB,GACpB,IAAIgB,EACHC,EAAIC,KACL,GAAMA,KAAKyO,OAAStO,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIrE,IAAIC,IAAIuT,cAAe7U,CAAC,EAAI,CAACqB,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAO,MAAM,IAAIkF,MAAM,4CAA8C5T,KAAKyO,OAAOC,GAAG,EACnL,IAAK5O,KAAOE,KAAK6T,QAAU1T,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KACnD1O,KAAK8T,QAAU,GACf9T,KAAK+T,QAAU,GACf/T,KAAKgU,cAAgB,GACrBhU,KAAKiU,YAAc,GACnBjU,KAAKe,UAAYZ,IAAIsF,EAAE,OAAO,EAAE+C,SAAS,sBAAsB,EAChExI,KAAKyO,OAAO1N,WAAaf,KAAKyO,OAAO1N,UAAUkO,OAAOjP,KAAKe,SAAS,EACpEf,KAAKe,UAAUb,KAAK,YAAaF,IAAI,EACpCA,KAAKkU,aAAelU,KAAK6T,QAAQ/K,MACjC9I,KAAKmU,cAAgBnU,KAAK6T,QAAQjR,OACnC5C,KAAKoU,mBAAmBpU,KAAKyO,OAAOtP,eAAe,EAClDa,KAAKqU,SAAW,WAChBtU,EAAEuU,WAAW,CACd,EACAnU,IAAIsF,EAAE1C,MAAM,EAAEwR,OAAOvU,KAAKqU,QAAQ,EAClClU,IAAIC,IAAIoU,UACPxU,KAAKyO,OAAO3O,IAAME,KAAKe,UAAU0D,KAAKtE,IAAIC,IAAIoU,UAAU1U,GAAK,cAAeE,KAAKyO,OAAO3O,EAAE,EAC1FE,KAAKmH,OAAS,IAAIhH,IAAIiM,aAAapM,KAAKe,UAAU,GAAIf,KAAK8I,MAAO9I,KAAK4C,MAAM,GAAK,iBAAkBG,QAAWA,OAAO0R,eAAiBjT,oBAAoBiT,gBAAmBzU,KAAKyO,OAAOiG,iBAAmB1U,KAAK2U,yBAAyB,EAAG3U,KAAK4U,oBAAoB,EAAG5U,KAAK6U,kBAAkB,EAAG7U,KAAK8U,UAAU,EAAG9U,KAAKyO,OAAOsG,aAAe/U,KAAKgV,gBAAgB,EAAGhV,KAAKiV,cAAc,EAAGjV,KAAKkV,cAAclV,KAAKyO,OAAOqF,SAAW,EAAE,EAAG9T,KAAKsU,WAAW,EAAGtU,KAAKyO,OAAO0G,UAAY,UAAY,OAAOnV,KAAKyO,OAAO0G,QAAWnV,KAAKyO,OAAO0G,QAAU,CAAEC,OAAQpV,KAAKyO,OAAO0G,OAAQ,EAAKhV,IAAIsF,EAAEyK,QAAQlQ,KAAKyO,OAAO0G,OAAO,IAAMnV,KAAKyO,OAAO0G,QAAU,CAAEpB,QAAS/T,KAAKyO,OAAO0G,OAAQ,GAAInV,KAAKqV,SAASrV,KAAKyO,OAAO0G,OAAO,GAAInV,KAAKyO,OAAOjP,iBAAmBQ,KAAKsV,mBAAmBtV,KAAKyO,OAAOjP,eAAe,EAAGQ,KAAKyO,OAAOhP,iBAAmBO,KAAKuV,mBAAmBvV,KAAKyO,OAAOhP,eAAe,EAAIO,KAAKgP,oBAAsB7O,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,+CAA+C,EAAKxI,KAAK+O,kBAAoB5O,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,+CAA+C,EAAIxI,KAAKe,UAAUkO,OAAOjP,KAAKgP,mBAAmB,EAAGhP,KAAKe,UAAUkO,OAAOjP,KAAK+O,iBAAiB,EAAG/O,KAAKyO,OAAOE,QAAU3O,KAAKwV,aAAa,CACnsC,EACCrV,IAAIC,IAAIQ,UAAY,CACpBqI,OAAQ,EACRC,OAAQ,EACRF,MAAO,EACPyM,WAAY,EACZC,WAAY,EACZC,UAAW,EACX7M,MAAO,EACPlG,OAAQ,EACRwR,mBAAoB,SAAUtV,GAC7BkB,KAAKe,UAAU4B,IAAI,mBAAoB7D,CAAC,CACzC,EACAyV,OAAQ,WACP,IAAIzV,EAAIkB,KAAK2V,UACb3V,KAAK8I,MAAQ9I,KAAK4C,OAAS5C,KAAKkU,aAAelU,KAAKmU,eAAkBnU,KAAK2V,UAAY3V,KAAK4C,OAAS5C,KAAKmU,cAAiBnU,KAAKyV,WAAa5R,KAAKE,IAAI/D,KAAK8I,MAAQ9I,KAAKkU,aAAelU,KAAK2V,SAAS,GAAK,EAAI3V,KAAK2V,aAAiB3V,KAAK2V,UAAY3V,KAAK8I,MAAQ9I,KAAKkU,aAAgBlU,KAAK0V,WAAa7R,KAAKE,IAAI/D,KAAK4C,OAAS5C,KAAKmU,cAAgBnU,KAAK2V,SAAS,GAAK,EAAI3V,KAAK2V,YAAe3V,KAAKgJ,OAAShJ,KAAK2V,UAAY7W,EAAKkB,KAAKiJ,QAAUjJ,KAAK2V,UAAY7W,EAAKkB,KAAKkJ,QAAUlJ,KAAK2V,UAAY7W,CAC1e,EACAwV,WAAY,WACVtU,KAAK8I,MAAQ9I,KAAKe,UAAU+H,MAAM,EAAK9I,KAAK4C,OAAS5C,KAAKe,UAAU6B,OAAO,EAAI5C,KAAKuU,OAAO,EAAGvU,KAAKmH,OAAOL,QAAQ9G,KAAK8I,MAAO9I,KAAK4C,MAAM,EAAG5C,KAAK4V,eAAe,CAClK,EACAC,MAAO,WACN,IAAI/W,EAAGgB,EACP,IAAKhB,KAAKkB,KAAK2O,OAAQ,IAAK7O,EAAI,EAAGA,EAAIE,KAAK2O,OAAO7P,GAAG+C,OAAQ/B,CAAC,GAAIE,KAAK2O,OAAO7P,GAAGgB,GAAG0Q,MAAM,EAC1FxQ,KAAKgJ,MAAQhJ,KAAK2V,UAAa3V,KAAKiJ,OAASjJ,KAAKyV,WAAczV,KAAKkJ,OAASlJ,KAAK0V,WAAa1V,KAAK4V,eAAe,CACtH,EACAA,eAAgB,WACf,IAAI9W,EACHgB,EACAC,EAAIC,KAAKkU,aAAelU,KAAKgJ,OAAShJ,KAAK8I,OAAUhK,GAAKkB,KAAK8I,MAAQ9I,KAAKkU,aAAelU,KAAKgJ,QAAU,EAAIhJ,KAAKgJ,QAAUhJ,KAAK8I,MAAQ9I,KAAKkU,aAAelU,KAAKgJ,QAAU,EAAIhJ,KAAKgJ,SAAYlK,EAAI,GAAKkB,KAAK8I,MAAQ9I,KAAKkU,aAAelU,KAAKgJ,OAAShJ,KAAKgJ,OAC/PjK,EAAIiB,KAAKmU,cAAgBnU,KAAKgJ,OAAShJ,KAAK4C,QAAW9C,GAAKE,KAAK4C,OAAS5C,KAAKmU,cAAgBnU,KAAKgJ,QAAU,EAAIhJ,KAAKgJ,QAAUhJ,KAAK4C,OAAS5C,KAAKmU,cAAgBnU,KAAKgJ,QAAU,EAAIhJ,KAAKgJ,SAAYlJ,EAAI,GAAKE,KAAK4C,OAAS5C,KAAKmU,cAAgBnU,KAAKgJ,OAAShJ,KAAKgJ,OACxQhJ,KAAKkJ,OAASpJ,EAAKE,KAAKkJ,OAASpJ,EAAKE,KAAKkJ,OAASnK,IAAMiB,KAAKkJ,OAASnK,GAAIiB,KAAKiJ,OAASnK,EAAKkB,KAAKiJ,OAASnK,EAAKkB,KAAKiJ,OAASlJ,IAAMC,KAAKiJ,OAASlJ,GAAIC,KAAKmH,OAAO4B,qBAAqB/I,KAAKgJ,MAAOhJ,KAAKiJ,OAAQjJ,KAAKkJ,MAAM,EAAGlJ,KAAK8T,SAAW9T,KAAK8V,kBAAkB,EAAG9V,KAAK+V,iBAAiB,EAAG/V,KAAKe,UAAU2D,QAAQ,iBAAkB,CAAC1E,KAAKgJ,MAAQhJ,KAAK2V,UAAW3V,KAAKiJ,OAAQjJ,KAAKkJ,OAAO,CACpY,EACA0L,oBAAqB,WACpB,IAAI9U,EACHC,EACAhB,EAAI,CAAA,EACJsC,EAAIrB,KACLA,KAAKyO,OAAOuH,YACVhW,KAAKe,UACJkV,UAAU,SAAUnX,GACpB,OAAOC,IAAOsC,EAAE4H,SAAWnJ,EAAIhB,EAAEoX,OAAS7U,EAAE2H,MAAS3H,EAAE6H,SAAWnJ,EAAIjB,EAAEqX,OAAS9U,EAAE2H,MAAQ3H,EAAEuU,eAAe,EAAI9V,EAAIhB,EAAEoX,MAASnW,EAAIjB,EAAEqX,OAAS,CAAA,CAC/I,CAAC,EACAC,UAAU,SAAUtX,GACpB,OAAQC,EAAI,CAAA,EAAMe,EAAIhB,EAAEoX,MAASnW,EAAIjB,EAAEqX,MAAQ,CAAA,CAChD,CAAC,EACDnW,KAAKqW,mBAAqB,WAC1BtX,EAAI,CAAA,CACL,EACAoB,IAAIsF,EAAE,MAAM,EAAE6Q,QAAQtW,KAAKqW,kBAAkB,GAC7CrW,KAAKyO,OAAO8H,cACXvW,KAAKe,UAAUiB,WAAW,SAAUlD,EAAGgB,EAAGC,EAAGhB,GAC5C,IAAIqD,EAAIjC,IAAIsF,EAAEpE,EAAEN,SAAS,EAAE+K,OAAO,EACjC9I,EAAIlE,EAAEoX,MAAQ9T,EAAEoI,KAChBvH,EAAInE,EAAEqX,MAAQ/T,EAAEsI,IAChBtI,EAAIyB,KAAK2J,IAAI,EAAInM,EAAEoN,OAAO+H,kBAAoB,IAAK1X,EAAEmF,YAAcnF,EAAE4E,MAAM,EAC5ErC,EAAEoV,IAAIC,KAAK,EAAGrV,EAAE0L,SAAS1L,EAAE2H,MAAQ5G,EAAGY,EAAGC,CAAC,EAAGnE,EAAE6X,eAAe,CAC/D,CAAC,CACJ,EACAhC,yBAA0B,WACzB,SAAS7V,EAAEA,GACV,IAAIgB,EACHC,EACAhB,EAAID,EAAE8X,cAAcC,QACrB,cAAgB/X,EAAEqE,OAASzB,EAAI,GAAI,GAAK3C,EAAE8C,QAAU,GAAKH,IAAO5B,EAAIuE,EAAE4E,OAAUlJ,EAAIsE,EAAE6E,OAAU7E,EAAE4E,SAAWhG,EAAIlE,EAAE,GAAGmX,OAAS7R,EAAE2E,MAAS3E,EAAE6E,SAAW7H,EAAItC,EAAE,GAAGoX,OAAS9R,EAAE2E,MAAQ3E,EAAEuR,eAAe,EAAGvR,EAAEoS,IAAIC,KAAK,EAAI5W,GAAKuE,EAAE4E,QAAUlJ,GAAKsE,EAAE6E,QAAWpK,EAAE6X,eAAe,GAAK1T,EAAIlE,EAAE,GAAGmX,MAAS7U,EAAItC,EAAE,GAAGoX,OAAU,GAAKpX,EAAE8C,SAAW,GAAKH,GAAM3B,EAAI8D,KAAKiK,KAAKjK,KAAK2J,IAAIzO,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,MAAO,CAAC,EAAIrS,KAAK2J,IAAIzO,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,MAAO,CAAC,CAAC,EAAInT,EAAIqB,EAAE0I,SAAS3K,EAAIrC,EAAGuB,EAAGC,CAAC,EAAG8C,EAAEoS,IAAIC,KAAK,EAAG5X,EAAE6X,eAAe,IAAO7X,EAAIqB,IAAIsF,EAAEpB,EAAEtD,SAAS,EAAE+K,OAAO,EAAKxK,EAAIvC,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,OAASnX,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,OAAS,EAAInX,EAAE,GAAGmX,OAASnX,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,OAAS,EAAK3U,EAAIxC,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,OAASpX,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,OAAS,EAAIpX,EAAE,GAAGoX,OAASpX,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,OAAS,EAAK7U,GAAKxC,EAAE0L,KAAQjJ,GAAKzC,EAAE4L,IAAOtI,EAAIiC,EAAE2E,MAAShG,EAAIa,KAAKiK,KAAKjK,KAAK2J,IAAIzO,EAAE,GAAGmX,MAAQnX,EAAE,GAAGmX,MAAO,CAAC,EAAIrS,KAAK2J,IAAIzO,EAAE,GAAGoX,MAAQpX,EAAE,GAAGoX,MAAO,CAAC,CAAC,IAAOzU,EAAI3C,EAAE8C,MACz5B,CACA,IAAIO,EACHY,EACAC,EACA5B,EACAC,EACAC,EACAG,EACA2C,EAAIrE,KACLG,IAAIsF,EAAEzF,KAAKe,SAAS,EAAE0D,KAAK,aAAc3F,CAAC,EAAGqB,IAAIsF,EAAEzF,KAAKe,SAAS,EAAE0D,KAAK,YAAa3F,CAAC,CACvF,EACA+V,kBAAmB,WAClB,IAAIzS,EACHf,EAAIrB,KACLA,KAAKe,UAAUkV,UAAU,WACxB7T,EAAI,CAAA,CACL,CAAC,EACApC,KAAKe,UAAU+V,SAAS,gCAAiC,qBAAsB,SAAUhY,GACxF,IAAIgB,EAAI,CAAC,KAAOK,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,OAAO,EAAEgR,SAAW5W,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,OAAO,GAAGG,QAAQ,mBAAmB,EAAI,SAAW,SACzHnG,EAAI,UAAYD,EAAIK,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,WAAW,EAAI5F,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,YAAY,EACjFhH,GAAK,UAAYe,EAAIuB,EAAE0S,QAAU1S,EAAEyS,SAAS/T,GAAGsQ,QAC/CjO,EAAI,UAAYtC,EAAIuB,EAAEwS,QAAQmD,MAAMjX,GAAGyG,KAAOnF,EAAEyS,QAAQ/T,GAAGmS,OAAO1L,MAAQ,GAC1ExD,EAAI7C,IAAIsF,EAAEwR,MAAMnX,EAAI,oBAAoB,EACxCmD,EAAI9C,IAAIsF,EAAEwR,MAAMnX,EAAI,iBAAiB,EACtC,aAAehB,EAAEqE,MAAQ9B,EAAEN,UAAU2D,QAAQzB,EAAG,CAAClD,EAAE,EAAGkD,EAAEiU,mBAAmB,GAAKnY,EAAEsT,WAAW,CAAA,CAAE,EAAGhR,EAAEoV,IAAI5D,KAAKzQ,CAAC,EAAGf,EAAEN,UAAU2D,QAAQ1B,EAAG,CAAC3B,EAAEoV,IAAK1W,EAAE,EAAGiD,EAAEkU,mBAAmB,IAAM7V,EAAEoV,IAAIU,KAAK,EAAI9V,EAAE+V,SAAW/V,EAAEoV,IAAI3N,MAAM,EAAKzH,EAAEgW,UAAYhW,EAAEoV,IAAI7T,OAAO,KAAQ7D,EAAEsT,WAAW,CAAA,CAAE,EAAGhR,EAAEoV,IAAIC,KAAK,EAAGrV,EAAEN,UAAU2D,QAAQ5E,EAAI,iBAAkB,CAACC,EAAE,EAC9U,CAAC,EACDC,KAAKe,UAAU+V,SAAS,gCAAiC,YAAa,WACrE1U,EAAI,CAAA,CACL,CAAC,EACDpC,KAAKe,UAAU+V,SAAS,gCAAiC,UAAW,WACnE,IAAIhY,EAAI,CAAC,KAAOqB,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,OAAO,EAAEgR,QAAU5W,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,OAAO,EAAEgR,QAAU5W,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,OAAO,GAAGG,QAAQ,mBAAmB,EAAI,SAAW,SAC5JpG,EAAI,UAAYhB,EAAIqB,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,WAAW,EAAI5F,IAAIsF,EAAEzF,IAAI,EAAE+F,KAAK,YAAY,EACjFhG,EAAII,IAAIsF,EAAEwR,MAAMnY,EAAI,kBAAkB,EACtCC,GAAK,UAAYD,EAAIuC,EAAE0S,QAAU1S,EAAEyS,SAAShU,GAAGuQ,QAChDjO,IAAMf,EAAEN,UAAU2D,QAAQ3E,EAAG,CAACD,EAAE,GAAK,UAAYhB,GAAKuC,EAAEoN,OAAO6I,mBAAuB,UAAYxY,GAAKuC,EAAEoN,OAAO8I,oBAAwBxX,CAAAA,EAAEmX,mBAAmB,IAAM7V,EAAEoN,OAAO3P,EAAI,mBAAqBuC,EAAEmW,cAAc1Y,EAAI,GAAG,EAAGC,EAAEuT,YAAY,CAACvT,EAAE8I,UAAU,GAC3P,CAAC,CACH,EACAmN,gBAAiB,WAChB,IAAIlW,EAAIkB,KACRG,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,mBAAmB,EAAEqK,KAAK,GAAG,EAAEjD,SAAS5P,KAAKe,SAAS,EAC9EZ,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,oBAAoB,EAAE2G,KAAK,UAAU,EAAES,SAAS5P,KAAKe,SAAS,EACvFf,KAAKe,UAAU0W,KAAK,oBAAoB,EAAEC,MAAM,WAC/C5Y,EAAEiO,SAASjO,EAAEkK,MAAQlK,EAAE2P,OAAOkJ,SAAU7Y,EAAEgK,MAAQ,EAAGhK,EAAE8D,OAAS,EAAG,CAAA,EAAI9D,EAAE2P,OAAOmJ,WAAW,CAC5F,CAAC,EACD5X,KAAKe,UAAU0W,KAAK,qBAAqB,EAAEC,MAAM,WAChD5Y,EAAEiO,SAASjO,EAAEkK,MAAQlK,EAAE2P,OAAOkJ,SAAU7Y,EAAEgK,MAAQ,EAAGhK,EAAE8D,OAAS,EAAG,CAAA,EAAI9D,EAAE2P,OAAOmJ,WAAW,CAC5F,CAAC,CACH,EACA9C,UAAW,WACV,IAAI/V,EAAIiB,KACPA,KAAKyW,IAAMtW,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,gBAAgB,EAAEoH,SAASzP,IAAIsF,EAAE,MAAM,CAAC,EAC5EzF,KAAKe,UAAUkV,UAAU,SAAUnX,GAClC,IAAIgB,EAAIhB,EAAEoX,MAAQ,GAAKnX,EAAEqY,SACxBrX,EAAIjB,EAAEqX,MAAQ,GAAKpX,EAAEsY,UACtBvX,EAAI,IAAMA,EAAIhB,EAAEoX,MAAQ,IAAKnW,EAAI,IAAMA,EAAIjB,EAAEqX,MAAQ,IAAKpX,EAAE0X,IAAI9T,IAAI,CAAE6H,KAAM1K,EAAG4K,IAAK3K,CAAE,CAAC,CACxF,CAAC,CACH,EACAgN,SAAU,SAAUjO,EAAGgB,EAAGC,EAAGhB,EAAGqD,GAC/B,IAAIY,EACHC,EACA5B,EACAC,EACAC,EACAG,EACA2C,EACAL,EACAwO,EACAqF,EAAI1X,IAAIsF,EAAEwR,MAAM,iBAAiB,EACjCa,EAAI9X,KACJ+X,EAAI,EACJC,EAAInU,KAAKE,IAAIF,KAAKyH,MAAO,IAAMxM,EAAIkB,KAAKgJ,OAAUnF,KAAKC,IAAIhF,EAAGkB,KAAKgJ,KAAK,CAAC,CAAC,EAC1EiP,EAAI,IAAI9X,IAAIsF,EAAEC,SACf,OACC5G,EAAIkB,KAAKyO,OAAOyJ,QAAUlY,KAAK2V,UAAa7W,EAAIkB,KAAKyO,OAAOyJ,QAAUlY,KAAK2V,UAAa7W,EAAIkB,KAAKyO,OAAO0J,QAAUnY,KAAK2V,YAAc7W,EAAIkB,KAAKyO,OAAO0J,QAAUnY,KAAK2V,WACpK,KAAA,IAAW7V,GAAK,KAAA,IAAWC,IAAO4X,SAAW7Y,EAAIkB,KAAKgJ,MAASwJ,EAAIzT,GAAMiF,EAAIlE,EAAKE,KAAKkU,cAAgBlU,KAAK8I,OAAS9I,KAAKkU,aAAepV,IAAO,EAAIiB,EAAKC,KAAKmU,eAAiBnU,KAAK4C,QAAU5C,KAAKmU,cAAgBrV,IAAO,IAAOkF,EAAIhE,KAAKiJ,QAAW0O,SAAW,GAAK7Y,EAAKgB,EAAIE,KAAKkJ,QAAWyO,SAAW,GAAK7Y,EAAKiB,IACnTqC,GAAK,EAAI4V,GACJ/U,EAAIjD,KAAKgJ,MACV3H,GAAKvC,EAAImE,GAAK+U,EACd1W,EAAItB,KAAKiJ,OAASjJ,KAAKgJ,MACvBtH,EAAI1B,KAAKkJ,OAASlJ,KAAKgJ,MACvBzH,GAAKyC,EAAIlF,EAAIwC,GAAK0W,EAClB3T,GAAKmO,EAAI1T,EAAI4C,GAAKsW,EAClBhV,EAAIoV,YAAY,WAChBL,GAAK,EAAKD,EAAE9O,MAAQ/F,EAAI5B,EAAI0W,EAAKD,EAAE7O,QAAU3H,EAAIC,EAAIwW,GAAKD,EAAE9O,MAAS8O,EAAE5O,QAAUxH,EAAI2C,EAAI0T,GAAKD,EAAE9O,MAAQ8O,EAAElC,eAAe,EAAGmC,GAAKC,IAAMK,cAAcrV,CAAC,EAAG8U,EAAE/W,UAAU2D,QAAQmT,EAAG,CAAC/Y,EAAIgZ,EAAEnC,UAAU,EAAGsC,EAAEnS,QAAQ,EAC/M,EAAG,EAAE,IACH9F,KAAKiJ,OAASjF,EAAKhE,KAAKkJ,OAASsJ,EAAKxS,KAAKgJ,MAAQlK,EAAIkB,KAAK4V,eAAe,EAAG5V,KAAKe,UAAU2D,QAAQmT,EAAG,CAAC/Y,EAAIkB,KAAK2V,UAAU,EAAGsC,EAAEnS,QAAQ,GAC9ImS,CAEF,EACA5C,SAAU,SAAUvW,GACnB,IAAIgB,EAAGC,EAAGhB,EAAGqD,EAAGY,EAChB,IAAMlE,EAAIA,GAAK,IAAIsW,OAAUrW,EAAI,CAACD,EAAEsW,QAAWtW,EAAEiV,UAAYhV,EAAID,EAAEiV,SAAUhV,EAAI,CAChF,IAAKqD,EAAI,EAAGA,EAAIrD,EAAE8C,OAAQO,CAAC,GAAIpC,KAAK+T,QAAQhV,EAAEqD,MAAQrC,EAAIC,KAAK+T,QAAQhV,EAAEqD,IAAIiO,QAAQI,MAAM/H,QAAQ,KAAO5I,EAAI,KAAA,IAAWA,EAAIC,EAAI,CAAEuK,EAAGzG,KAAKqB,IAAIpF,EAAEwK,EAAGvK,EAAEuK,CAAC,EAAGG,EAAG5G,KAAKqB,IAAIpF,EAAE2K,EAAG1K,EAAE0K,CAAC,EAAG3B,MAAOjF,KAAKC,IAAIhE,EAAEwK,EAAIxK,EAAEgJ,MAAO/I,EAAEuK,EAAIvK,EAAE+I,KAAK,EAAIjF,KAAKqB,IAAIpF,EAAEwK,EAAGvK,EAAEuK,CAAC,EAAG1H,OAAQiB,KAAKC,IAAIhE,EAAE2K,EAAI3K,EAAE8C,OAAQ7C,EAAE0K,EAAI1K,EAAE6C,MAAM,EAAIiB,KAAKqB,IAAIpF,EAAE2K,EAAG1K,EAAE0K,CAAC,CAAE,GAC3T,OAAOzK,KAAK+M,SAASlJ,KAAKqB,IAAIlF,KAAK8I,MAAQhJ,EAAEgJ,MAAO9I,KAAK4C,OAAS9C,EAAE8C,MAAM,EAAG,EAAE9C,EAAEwK,EAAIxK,EAAEgJ,MAAQ,GAAI,EAAEhJ,EAAE2K,EAAI3K,EAAE8C,OAAS,GAAI,CAAA,EAAI9D,EAAEwZ,OAAO,CACxI,CACA,OAAOxZ,EAAEqS,KAAOrS,EAAEwS,KAAQtO,EAAIhD,KAAKuY,cAAczZ,EAAEqS,IAAKrS,EAAEwS,GAAG,EAAKxS,EAAEwL,EAAItK,KAAKiJ,OAASjG,EAAEsH,EAAItK,KAAKgJ,MAASlK,EAAE2L,EAAIzK,KAAKkJ,OAASlG,EAAEyH,EAAIzK,KAAKgJ,OAAUlK,EAAEwL,GAAKxL,EAAE2L,IAAO3L,EAAEwL,GAAK,CAACtK,KAAKkU,aAAgBpV,EAAE2L,GAAK,CAACzK,KAAKmU,eAAiBnU,KAAK+M,SAASjO,EAAEkK,MAAQhJ,KAAK2V,UAAW7W,EAAEwL,EAAGxL,EAAE2L,EAAG,CAAA,EAAI3L,EAAEwZ,OAAO,CAC9R,EACAE,YAAa,SAAU1Z,GACtB,IAAIgB,EACHC,EAAI,GACL,IAAKD,KAAKE,KAAKlB,GAAIkB,KAAKlB,GAAGgB,GAAGuQ,QAAQxI,YAAc9H,EAAEwF,KAAKzF,CAAC,EAC5D,OAAOC,CACR,EACA0Y,mBAAoB,WACnB,OAAOzY,KAAKwY,YAAY,SAAS,CAClC,EACAE,mBAAoB,WACnB,OAAO1Y,KAAKwY,YAAY,SAAS,CAClC,EACAlG,YAAa,SAAUxT,EAAGgB,GACzB,GAAwCK,IAAIsF,EAAEyK,QAAhBpQ,EAAzB,UAAY,OAAOA,EAAU,CAACA,GAAmBA,CAAC,EAAI,IAAKC,EAAI,EAAGA,EAAID,EAAE+B,OAAQ9B,CAAC,GAAIC,KAAKlB,GAAGgB,EAAEC,IAAIsQ,QAAQiC,YAAY,CAAA,CAAE,OACzH,IAAK,IAAIvS,KAAKD,EAAGE,KAAKlB,GAAGiB,GAAGsQ,QAAQiC,YAAY,CAAC,CAACxS,EAAEC,EAAE,CAC5D,EACAuV,mBAAoB,SAAUxW,GAC7BkB,KAAKsS,YAAY,UAAWxT,CAAC,CAC9B,EACAyW,mBAAoB,SAAUzW,GAC7BkB,KAAKsS,YAAY,UAAWxT,CAAC,CAC9B,EACA0Y,cAAe,SAAU1Y,GACxB,IAAK,IAAIgB,EAAI,GAAIC,EAAIC,KAAKwY,YAAY1Z,CAAC,EAAGC,EAAI,EAAGA,EAAIgB,EAAE8B,OAAQ9C,CAAC,GAAIe,EAAEC,EAAEhB,IAAM,CAAA,EAC9EiB,KAAKsS,YAAYxT,EAAGgB,CAAC,CACtB,EACA6Y,qBAAsB,WACrB3Y,KAAKwX,cAAc,SAAS,CAC7B,EACAoB,qBAAsB,WACrB5Y,KAAKwX,cAAc,SAAS,CAC7B,EACAqB,aAAc,WACb,OAAO7Y,IACR,EACA8Y,cAAe,SAAUha,GACxB,OAAOkB,KAAK6T,QAAQmD,MAAMlY,GAAG0H,IAC9B,EACAyO,cAAe,WACd,IAAInW,EACHgB,EACAC,EAAIC,KACL,IAAKlB,KAAOkB,KAAK+Y,kBAAoB/Y,KAAK+Y,mBAAqB/Y,KAAKmH,OAAOK,SAAS,EAAIxH,KAAK6T,QAAQmD,MACnGlX,EAAI,IAAIK,IAAIoS,OAAO,CAAE7D,IAAK1O,KAAMyL,KAAMzL,KAAK6T,QAAQmD,MAAMlY,GAAG2M,KAAMiH,KAAM5T,EAAG4I,MAAOvH,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIxE,KAAKyO,OAAOuK,WAAW,EAAGhG,WAAY7S,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIxE,KAAKyO,OAAOwK,gBAAgB,EAAG9R,OAAQnH,KAAKmH,OAAQ8L,YAAajT,KAAK+Y,kBAAmBnM,MAAO,OAAS5M,KAAKmH,OAAOkF,KAAOrM,KAAKyO,OAAOyK,QAAUlZ,KAAKyO,OAAOyK,OAAOnF,QAAU,IAAK,CAAC,EACvV5T,IAAIsF,EAAE3F,EAAE2Q,KAAK,EAAEhM,KAAK,WAAY,SAAU3F,EAAGgB,GAC5CC,EAAEgB,UAAU2D,QAAQ,4BAA6B,CAACvE,IAAIsF,EAAEzF,KAAKsG,IAAI,EAAEP,KAAK,WAAW,EAAGjG,EAAGC,EAAE0Y,mBAAmB,EAAE,CACjH,CAAC,EACAzY,KAAK+T,QAAQjV,GAAK,CAAEuR,QAASvQ,EAAGoS,OAAQlS,KAAK6T,QAAQmD,MAAMlY,EAAG,CAClE,EACAoW,cAAe,SAAUpW,GACxB,IAAIgB,EACHC,EACAhB,EACAqD,EACAY,EAAIhD,KACL,GAAMA,KAAKmZ,aAAenZ,KAAKmZ,cAAgBnZ,KAAKmH,OAAOK,SAAS,EAAKxH,KAAKoZ,kBAAoBpZ,KAAKoZ,mBAAqBpZ,KAAKmH,OAAOK,SAAS,EAAIrH,IAAIsF,EAAEyK,QAAQpR,CAAC,EAAI,IAAKsD,EAAItD,EAAE+B,MAAM,EAAG/B,EAAI,GAAIgB,EAAI,EAAGA,EAAIsC,EAAEP,OAAQ/B,CAAC,GAAIhB,EAAEgB,GAAKsC,EAAEtC,GACvO,IAAKA,KAAKhB,EACRC,EAAID,EAAEgB,aAAca,MAAQ,CAAE0Y,OAAQva,EAAEgB,EAAG,EAAIhB,EAAEgB,GACjD,CAAA,KAAQC,EAAIC,KAAKsZ,kBAAkBva,CAAC,KACjCgB,EAAI,IAAII,IAAIgT,OAAO,CAAEzE,IAAK1O,KAAM0H,MAAOvH,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIxE,KAAKyO,OAAOe,YAAa,CAAEvH,QAASlJ,EAAE2I,OAAS,EAAG,CAAC,EAAGsL,WAAY7S,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIxE,KAAKyO,OAAO8K,gBAAgB,EAAGhG,MAAOzT,EAAG6J,GAAI5J,EAAEuK,EAAGV,GAAI7J,EAAE0K,EAAGiJ,MAAO1T,KAAKmZ,aAAchS,OAAQnH,KAAKmH,OAAQ8L,YAAajT,KAAKoZ,kBAAmBxM,MAAO,OAAS5M,KAAKmH,OAAOkF,KAAOrM,KAAKyO,OAAOyK,QAAUlZ,KAAKyO,OAAOyK,OAAOpF,QAAU,IAAK,CAAC,EAChY3T,IAAIsF,EAAE1F,EAAE0Q,KAAK,EAAEhM,KAAK,WAAY,SAAU3F,EAAGgB,GAC5CkD,EAAEjC,UAAU2D,QAAQ,4BAA6B,CAACvE,IAAIsF,EAAEzF,KAAKsG,IAAI,EAAEP,KAAK,YAAY,EAAGjG,EAAGkD,EAAE0V,mBAAmB,EAAE,CAClH,CAAC,EACD1Y,KAAK8T,QAAQhU,IAAME,KAAKwZ,cAAc,CAAC1Z,EAAE,EACxCE,KAAK8T,QAAQhU,GAAK,CAAEuQ,QAAStQ,EAAGmS,OAAQnT,CAAE,EAC/C,EACA+W,kBAAmB,WAClB,IAAIhX,EAAGgB,EACP,IAAKhB,KAAKkB,KAAK8T,QAAS,CAAA,KAAQhU,EAAIE,KAAKsZ,kBAAkBtZ,KAAK8T,QAAQhV,GAAGoT,MAAM,IAAMlS,KAAK8T,QAAQhV,GAAGuR,QAAQtI,SAAS,CAAE4B,GAAI7J,EAAEwK,EAAGV,GAAI9J,EAAE2K,CAAE,CAAC,CAC7I,EACAsL,iBAAkB,WACjB,IAAK,IAAIjX,KAAKkB,KAAK+T,QAAS/T,KAAK+T,QAAQjV,GAAGuR,QAAQ6C,oBAAoB,EACxE,IAAKpU,KAAKkB,KAAK8T,QAAS9T,KAAK8T,QAAQhV,GAAGuR,QAAQ6C,oBAAoB,CACrE,EACAoG,kBAAmB,SAAUxa,GAC5B,OAAOqB,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAK+K,WAAazZ,KAAKuY,cAAc7X,MAAMV,KAAMlB,EAAEua,QAAU,CAAC,EAAG,EAAE,EAAI,CAAE/O,EAAGxL,EAAE4a,OAAO,GAAK1Z,KAAKgJ,MAAQhJ,KAAKiJ,OAASjJ,KAAKgJ,MAAOyB,EAAG3L,EAAE4a,OAAO,GAAK1Z,KAAKgJ,MAAQhJ,KAAKkJ,OAASlJ,KAAKgJ,KAAM,CACzN,EACA2Q,UAAW,SAAU7a,EAAGgB,EAAGC,GAC1B,IAAIhB,EACHqD,EACAY,EAAI,GACJC,EAAI,GACJlD,EAAIA,GAAK,GACV,IAAKiD,EAAElE,GAAKgB,EAAGsC,EAAI,EAAGA,EAAIrC,EAAE8B,OAAQO,CAAC,GAAKrD,EAAI,GAAK,KAAA,IAAWgB,EAAEqC,KAAOrD,EAAED,GAAKiB,EAAEqC,IAAKa,EAAEsC,KAAKxG,CAAC,EAC7FiB,KAAK4Z,WAAW5W,EAAGC,CAAC,CACrB,EACA2W,WAAY,SAAU9a,EAAGgB,GACxB,IAAIC,EACJ,IAAKD,EAAIA,GAAK,GAAIE,KAAKkV,cAAcpW,CAAC,EAAGiB,EAAI,EAAGA,EAAID,EAAE+B,OAAQ9B,CAAC,GAAIC,KAAK2O,OAAOmF,QAAQ/T,GAAGoQ,UAAUrQ,EAAEC,IAAM,EAAE,CAC/G,EACAyZ,cAAe,SAAU1a,GACxB,IAAK,IAAIgB,EAAI,EAAGA,EAAIhB,EAAE+C,OAAQ/B,CAAC,GAAIE,KAAK8T,QAAQhV,EAAEgB,IAAIuQ,QAAQzJ,OAAO,EAAG,OAAO5G,KAAK8T,QAAQhV,EAAEgB,GAC/F,EACA+Z,iBAAkB,WACjB,IAAI/a,EACHgB,EAAI,GACL,IAAKhB,KAAKkB,KAAK8T,QAAShU,EAAEyF,KAAKzG,CAAC,EAChCkB,KAAKwZ,cAAc1Z,CAAC,CACrB,EACAyY,cAAe,SAAUzZ,EAAGgB,GAC3B,IAAIC,EAAII,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAK+K,WACrC1a,EAAIgB,EAAE+Z,gBACP,OAAOha,EAAI,CAAC,IAAMf,IAAMe,GAAK,KAAOhB,EAAIqB,IAAIuQ,KAAK3Q,EAAEoD,MAAMrE,EAAGgB,EAAGf,CAAC,EAAI,CAAC,EAAEe,EAAIE,KAAK+Z,iBAAiBjb,EAAEwL,EAAGxL,EAAE2L,CAAC,KAAQ1L,EAAIe,EAAEka,KAAQlb,EAAEwL,GAAMxL,EAAEwL,EAAIvL,EAAE,GAAGuL,IAAMvL,EAAE,GAAGuL,EAAIvL,EAAE,GAAGuL,GAAMxK,EAAEgJ,MAAQ9I,KAAKgJ,MAASlK,EAAE2L,GAAM3L,EAAE2L,EAAI1L,EAAE,GAAG0L,IAAM1L,EAAE,GAAG0L,EAAI1L,EAAE,GAAG0L,GAAM3K,EAAE8C,OAAS5C,KAAKgJ,MAAQ,CAAEsB,EAAGxL,EAAEwL,EAAItK,KAAKiJ,OAASjJ,KAAKgJ,MAAQlJ,EAAE0K,KAAOxK,KAAKgJ,MAAOyB,EAAG3L,EAAE2L,EAAIzK,KAAKkJ,OAASlJ,KAAKgJ,MAAQlJ,EAAE4K,IAAM1K,KAAKgJ,KAAM,EAC5X,EACAiR,cAAe,SAAUnb,EAAGgB,GAC3B,IAAK,IAAIC,EAAGhB,EAAGqD,EAAGY,EAAGC,EAAI9C,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAK+K,WAAYpY,EAAI4B,EAAE6W,gBAAiBxY,EAAInB,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAKwL,OAAQ3Y,EAAI,EAAGA,EAAID,EAAEO,OAAQN,CAAC,GAAI,GAAMxC,GAAKgB,EAAIuB,EAAEC,IAAIyY,KAAQ5X,EAAItD,GAAKkB,KAAKiJ,OAASjJ,KAAKgJ,MAAQjJ,EAAEyK,KAAOxK,KAAKgJ,OAAUhG,EAAIlD,GAAKE,KAAKkJ,OAASlJ,KAAKgJ,MAAQjJ,EAAE2K,IAAM1K,KAAKgJ,OAAU5G,EAAKA,GAAKrC,EAAE+I,MAAQ9I,KAAKgJ,QAAWjK,EAAE,GAAGuL,EAAIvL,EAAE,GAAGuL,GAAKvL,EAAE,GAAGuL,EAAKtH,EAAKA,GAAKjD,EAAE6C,OAAS5C,KAAKgJ,QAAWjK,EAAE,GAAG0L,EAAI1L,EAAE,GAAG0L,GAAK1L,EAAE,GAAG0L,EAAIrI,EAAIrD,EAAE,GAAGuL,GAAKlI,EAAIrD,EAAE,GAAGuL,GAAKtH,EAAIjE,EAAE,GAAG0L,GAAKzH,EAAIjE,EAAE,GAAG0L,EAAI,OAAOtK,IAAIuQ,KAAKzN,EAAEE,KAAO,QAAQf,EAAG,CAACY,EAAG3B,CAAC,EACphB,MAAO,CAAA,CACR,EACA0Y,iBAAkB,SAAUjb,EAAGgB,GAC9B,IAAK,IAAIC,EAAGhB,EAAIoB,IAAIC,IAAIC,KAAKL,KAAKyO,OAAOC,KAAKwL,OAAQ9X,EAAI,EAAGA,EAAIrD,EAAE8C,OAAQO,CAAC,GAAI,GAAItD,GAAKiB,EAAIhB,EAAEqD,GAAG4X,MAAM,GAAG1P,GAAKxL,EAAIiB,EAAE,GAAGuK,GAAKxK,EAAIC,EAAE,GAAG0K,GAAK3K,EAAIC,EAAE,GAAG0K,EAAG,OAAO1L,EAAEqD,EAClK,EACAoT,aAAc,WACb,IAAI1W,EAAGgB,EACP,IAAKA,KAAOE,KAAK2O,OAAS,CAAEmF,QAAS,GAAIC,QAAS,EAAG,EAAI/T,KAAKyO,OAAOE,OAAS,IAAK7P,EAAI,EAAGA,EAAIkB,KAAKyO,OAAOE,OAAO7O,GAAG+B,OAAQ/C,CAAC,GAAIkB,KAAK2O,OAAO7O,GAAGhB,GAAK,IAAIqB,IAAI2P,WAAW9P,KAAKyO,OAAOE,OAAO7O,GAAGhB,GAAIkB,KAAKF,GAAIE,IAAI,CAChN,EACA4G,OAAQ,WACP5G,KAAKyW,IAAI7P,OAAO,EAAG5G,KAAKe,UAAU6F,OAAO,EAAGzG,IAAIsF,EAAE1C,MAAM,EAAE6B,OAAO,SAAU5E,KAAKqU,QAAQ,EAAGlU,IAAIsF,EAAE,MAAM,EAAEb,OAAO,UAAW5E,KAAKqW,kBAAkB,CACnJ,CACD,EACClW,IAAIC,IAAIC,KAAO,GACfF,IAAIC,IAAIuT,cAAgB,CAAEjF,IAAK,gBAAiBvP,gBAAiB,UAAW4V,YAAa,CAAA,EAAIwB,aAAc,CAAA,EAAIC,kBAAmB,EAAGR,UAAW,CAAA,EAAIkC,QAAS,EAAGC,QAAS,EAAGR,SAAU,IAAKC,YAAa,CAAA,EAAIN,kBAAmB,CAAA,EAAIC,kBAAmB,CAAA,EAAI7C,gBAAiB,CAAA,EAAIsE,YAAa,CAAE/Q,QAAS,CAAE0H,KAAM,QAASwK,eAAgB,EAAG1K,OAAQ,OAAQ2K,eAAgB,EAAGC,iBAAkB,CAAE,EAAGnS,MAAO,CAAEiS,eAAgB,GAAKG,OAAQ,SAAU,EAAGnS,SAAU,CAAEwH,KAAM,QAAS,EAAGvH,cAAe,EAAG,EAAG6Q,iBAAkB,CAAEhR,QAAS,CAAEsS,cAAe,UAAWC,YAAa,KAAMC,cAAe,OAAQH,OAAQ,UAAW3K,KAAM,OAAQ,EAAGzH,MAAO,CAAEoS,OAAQ,SAAU,CAAE,EAAG9K,YAAa,CAAEvH,QAAS,CAAE0H,KAAM,OAAQF,OAAQ,UAAW0K,eAAgB,EAAGC,eAAgB,EAAGC,iBAAkB,EAAGpX,EAAG,CAAE,EAAGiF,MAAO,CAAEuH,OAAQ,QAAS2K,eAAgB,EAAGE,OAAQ,SAAU,EAAGnS,SAAU,CAAEwH,KAAM,MAAO,EAAGvH,cAAe,EAAG,EAAGmR,iBAAkB,CAAEtR,QAAS,CAAEsS,cAAe,UAAWC,YAAa,KAAMC,cAAe,OAAQH,OAAQ,UAAW3K,KAAM,OAAQ,EAAGzH,MAAO,CAAEoS,OAAQ,SAAU,CAAE,CAAE,EACziCna,IAAIC,IAAIoU,UAAY,CAAEkG,gBAAiB,gBAAiBC,aAAc,aAAcC,YAAa,YAAaC,cAAe,cAAeC,iBAAkB,iBAAkBC,gBAAiB,gBAAiBC,aAAc,aAAcC,YAAa,YAAaC,cAAe,cAAeC,iBAAkB,iBAAkBC,iBAAkB,gBAAiB,EAC7Wjb,IAAIkb,SAAW,SAAUvc,GACzB,IAAIgB,EAAIE,KACPA,KAAKK,KAAO,GACXL,KAAKyO,OAAStO,IAAIsF,EAAEjB,OAAO,CAAA,EAAI,GAAIrE,IAAIkb,SAAS1H,cAAe7U,CAAC,EAChEkB,KAAKyO,OAAO6M,SAAWtb,KAAKyO,OAAO6M,UAAYnW,OAAOC,UACtDpF,KAAKyO,OAAO8M,KAAOvb,KAAKyO,OAAO8M,MAAQ,GACvCvb,KAAKyO,OAAO8M,KAAKC,cAAgB,EACjCxb,KAAKyb,QAAU,CAACzb,KAAK0b,OAAO1b,KAAKyO,OAAO8M,KAAK7M,IAAK1O,KAAKyO,OAAO8M,IAAI,GAClEvb,KAAK2b,kBAAoB3b,KAAKyb,QAAQ,GAAG5H,QAAQ4F,WAAWtW,KAC5DnD,KAAK4b,WAAa,GACnB5b,KAAKyO,OAAO1N,UAAU4B,IAAI,CAAE4H,SAAU,UAAW,CAAC,EACjDvK,KAAK6b,WAAa1b,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,mBAAmB,EAAEqK,KAAK,MAAM,EAAEjD,SAAS5P,KAAKyO,OAAO1N,SAAS,EAC5Gf,KAAK6b,WAAWnF,KAAK,EACrB1W,KAAK6b,WAAWnE,MAAM,WACrB5X,EAAEgc,OAAO,CACV,CAAC,EACA9b,KAAK+b,QAAU5b,IAAIsF,EAAE,QAAQ,EAAE+C,SAAS,oBAAoB,EAAEoH,SAAS5P,KAAKyO,OAAO1N,SAAS,EAC7Ff,KAAK+b,QAAQrF,KAAK,CACpB,EACCvW,IAAIkb,SAASza,UAAY,CACzB8a,OAAQ,SAAU5c,EAAGgB,GACpB,IAAIC,EAAII,IAAIsF,EAAE,QAAQ,EAAE9C,IAAI,CAAEmG,MAAO,OAAQlG,OAAQ,MAAO,CAAC,EAC7D,OACC5C,KAAKyO,OAAO1N,UAAUkO,OAAOlP,CAAC,EAC7BC,KAAKK,KAAKvB,GAAK,IAAIqB,IAAIC,IAAID,IAAIsF,EAAEjB,OAAO1E,EAAG,CAAEiB,UAAWhB,CAAE,CAAC,CAAC,EAC7DC,KAAKyO,OAAO6M,SAAWxb,EAAE0b,eACxBxb,KAAKK,KAAKvB,GAAGiC,UAAU6K,GAAG,yBAA0B,CAAEoQ,MAAOhc,IAAK,EAAG,SAAUlB,EAAGgB,GACjF,IAAIC,EAAIjB,EAAEoB,KAAK8b,MACdld,EAAIiB,EAAE0O,OAAOwN,cAAcnc,EAAGC,CAAC,EAC/BA,EAAEmc,kBAAoB,YAAcnc,EAAEmc,iBAAiBC,MAAM,GAAMpc,EAAEqc,UAAUtd,EAAGgB,CAAC,CACrF,CAAC,EACFE,KAAKK,KAAKvB,EAEZ,EACAud,YAAa,SAAUvd,GACtB,IAAIgB,EAAIE,KACPD,EAAII,IAAIsF,EAAEC,SAAS,EACpB,OACC1F,KAAK4b,WAAW9c,GACbiB,EAAE+F,QAAQ,EACV3F,IAAIsF,EAAElG,IAAIS,KAAKyO,OAAO6N,aAAaxd,EAAGkB,IAAI,CAAC,EAAEqJ,KAC7C,WACEvJ,EAAE8b,WAAW9c,GAAK,CAAA,EAAKiB,EAAE+F,QAAQ,CACnC,EACA,WACC/F,EAAE6F,OAAO,CACV,CACA,EACH7F,CAEF,EACAqc,UAAW,SAAUtd,EAAGgB,GACvB,IAAIC,EAAIC,KAAKyb,QAAQzb,KAAKyb,QAAQ5Z,OAAS,GAC1C9C,EAAIiB,KACJoC,EAAIrC,EAAEsV,SAAS,CAAED,OAAQtV,EAAGwY,QAAS,CAAA,CAAG,CAAC,EACzCtV,EAAIhD,KAAKqc,YAAYvc,CAAC,EACvBsC,EAAEiH,KAAK,WACN,YAAcrG,EAAEmZ,MAAM,GAAKpd,EAAEgd,QAAQ5E,KAAK,CAC3C,CAAC,EACAnU,EAAEuZ,OAAO,WACRxd,EAAEgd,QAAQrF,KAAK,CAChB,CAAC,EACA1W,KAAKkc,iBAAmB/b,IAAIsF,EAAE+W,KAAKxZ,EAAGZ,CAAC,EACxCpC,KAAKkc,iBAAiB7S,KAAK,WAC1BtJ,EAAE0O,OAAO1N,UAAU2V,KAAK,EAAG3X,EAAEsB,KAAKvB,GAAKC,EAAEsB,KAAKvB,GAAG2P,OAAO1N,UAAUoW,KAAK,EAAIpY,EAAE2c,OAAO5c,EAAG,CAAE4P,IAAK5P,EAAG0c,cAAezb,EAAE0O,OAAO+M,cAAgB,CAAE,CAAC,EAAGzc,EAAE0c,QAAQlW,KAAKxG,EAAEsB,KAAKvB,EAAE,EAAGC,EAAE8c,WAAW1E,KAAK,CAC7L,CAAC,CACH,EACA2E,OAAQ,WACP,IAAIhd,EAAIkB,KAAKyb,QAAQgB,IAAI,EACxB3c,EAAIE,KAAKyb,QAAQzb,KAAKyb,QAAQ5Z,OAAS,GACvC9B,EAAIC,KACLlB,EAAEuW,SAAS,CAAErM,MAAO,EAAGsB,EAAG,GAAKG,EAAG,GAAK6N,QAAS,CAAA,CAAG,CAAC,EAAEjP,KAAK,WAC1DvK,EAAE2P,OAAO1N,UAAU2V,KAAK,EAAG5W,EAAE2O,OAAO1N,UAAUoW,KAAK,EAAGrX,EAAEwU,WAAW,EAAG,IAAMvU,EAAE0b,QAAQ5Z,QAAU9B,EAAE8b,WAAWnF,KAAK,EAAG5W,EAAEuV,SAAS,CAAErM,MAAO,EAAGsB,EAAG,GAAKG,EAAG,GAAK6N,QAAS,CAAA,CAAG,CAAC,CAC1K,CAAC,CACF,CACD,EACCnY,IAAIkb,SAAS1H,cAAgB,CAC7BsI,cAAe,SAAUnd,EAAGgB,GAC3B,OAAOhB,EAAE4d,YAAY,EAAI,IAAM5c,EAAE6b,kBAAoB,KACtD,EACAW,aAAc,SAAUxd,EAAGgB,GAC1B,MAAO,0BAA4BhB,EAAE4d,YAAY,EAAI,IAAM5c,EAAE6b,kBAAoB,QAClF,CACD"}
!function(y){"use strict";var n="simpleCalendar",a={months:["january","february","march","april","may","june","july","august","september","october","november","december"],days:["sunday","monday","tuesday","wednesday","thursday","friday","saturday"],displayYear:!0,fixedStartDay:!0,displayEvent:!0,disableEventDetails:!1,disableEmptyDetails:!1,events:[],onInit:function(t){},onMonthChange:function(t,e){},onDateSelect:function(t,e){},onEventSelect:function(){},onEventCreate:function(t){},onDayCreate:function(t,e,n,a){}};function e(t,e){this.element=t,this.settings=y.extend({},a,e),this._defaults=a,this._name=n,this.currentDate=new Date,this.init()}y.extend(e.prototype,{init:function(){var t=y(this.element),e=this.currentDate,n=y('<div class="calendar"></div>'),a=y('<header><h2 class="month"></h2><a class="simple-calendar-btn btn-prev" href="#"></a><a class="simple-calendar-btn btn-next" href="#"></a></header>');this.updateHeader(e,a),n.append(a),this.buildCalendar(e,n),t.append(n),this.bindEvents(),this.settings.onInit(this)},updateHeader:function(t,e){var n=this.settings.months[t.getMonth()];n+=this.settings.displayYear?' <div class="year">'+t.getFullYear():"</div>",e.find(".month").html(n)},buildCalendar:function(t,e){var n=this,a=(e.find("table").remove(),y("<table></table>")),s=y("<thead></thead>"),i=y("<tbody></tbody>"),d=t.getFullYear(),r=t.getMonth(),h=new Date(d,r,1),o=new Date(d,r+1,0),l=h.getDay();if(!1!==this.settings.fixedStartDay){for(l=!0===this.settings.fixedStartDay?1:this.settings.fixedStartDay;h.getDay()!==l;)h.setDate(h.getDate()-1);for(;o.getDay()!==(l+6)%7;)o.setDate(o.getDate()+1)}for(var c=l;c<l+7;c++)s.append(y("<td>"+this.settings.days[c%7].substring(0,3)+"</td>"));for(var u=h;u<=o;u.setDate(u.getDate())){for(var v=y("<tr></tr>"),c=0;c<7;c++){var f=y('<td><div class="day" data-date="'+u.toISOString()+'">'+u.getDate()+"</div></td>"),g=f.find(".day"),D=(u.toDateString()===(new Date).toDateString()&&g.addClass("today"),u.getMonth()!=t.getMonth()&&g.addClass("wrong-month"),n.getDateEvents(u));D.length&&n.settings.displayEvent?g.addClass(n.settings.disableEventDetails?"has-event disabled":"has-event"):g.addClass(n.settings.disableEmptyDetails?"disabled":""),g.data("todayEvents",D),this.settings.onDayCreate(g,u.getDate(),r,d),v.append(f),u.setDate(u.getDate()+1)}i.append(v)}a.append(s),a.append(i);var p=y('<div class="event-container"><div class="close"></div><div class="event-wrapper"></div></div>');e.append(a),e.append(p)},changeMonth:function(t){this.currentDate.setMonth(this.currentDate.getMonth()+t,1),this.buildCalendar(this.currentDate,y(this.element).find(".calendar")),this.updateHeader(this.currentDate,y(this.element).find(".calendar header")),this.settings.onMonthChange(this.currentDate.getMonth(),this.currentDate.getFullYear())},bindEvents:function(){var a=this;y(a.element).off(),y(a.element).on("click",".btn-prev",function(t){a.changeMonth(-1),t.preventDefault()}),y(a.element).on("click",".btn-next",function(t){a.changeMonth(1),t.preventDefault()}),y(a.element).on("click",".day",function(t){var e=new Date(y(this).data("date")),n=a.getDateEvents(e);y(this).hasClass("disabled")||(a.fillUp(t.pageX,t.pageY),a.displayEvents(n)),a.settings.onDateSelect(e,n)}),y(a.element).on("click",".event-container .close",function(t){a.empty(t.pageX,t.pageY)})},displayEvents:function(t){var a=this,s=y(this.element).find(".event-wrapper");t.forEach(function(t){var e=new Date(t.startDate),n=new Date(t.endDate),e=y('<div class="event"> <div class="event-hour">'+e.getHours()+":"+(e.getMinutes()<10?"0":"")+e.getMinutes()+'</div> <div class="event-date">'+a.formatDateEvent(e,n)+'</div> <div class="event-summary">'+t.summary+"</div></div>");e.data("event",t),e.click(a.settings.onEventSelect),a.settings.onEventCreate(e),s.append(e)})},addEvent:function(t){this.settings.events=[...this.settings.events,t],this.buildCalendar(this.currentDate,y(this.element).find(".calendar"))},setEvents:function(t){this.settings.events=t,this.buildCalendar(this.currentDate,y(this.element).find(".calendar"))},fillUp:function(t,e){var n=y(this.element),a=n.offset(),s=y('<div class="filler" style=""></div>');s.css("left",t-a.left),s.css("top",e-a.top),n.find(".calendar").append(s),s.animate({width:"300%",height:"300%"},500,function(){n.find(".event-container").show(),s.hide()})},empty:function(t,e){var n=y(this.element),a=(n.offset(),n.find(".filler"));a.css("width","300%"),a.css("height","300%"),a.show(),n.find(".event-container").hide().find(".event").remove(),a.animate({width:"0%",height:"0%"},500,function(){a.remove()})},getDateEvents:function(e){var n=this;return n.settings.events.filter(function(t){return n.isDayBetween(new Date(e),new Date(t.startDate),new Date(t.endDate))})},isDayBetween:function(t,e,n){return e.setHours(0,0,0),n.setHours(23,59,59,999),t.setHours(12,0,0),e<=t&&t<=n},formatDateEvent:function(t,e){var n="";return n+=this.settings.days[t.getDay()]+" - "+t.getDate()+" "+this.settings.months[t.getMonth()].substring(0,3),e.getDate()!==t.getDate()&&(n+=" to "+e.getDate()+" "+this.settings.months[e.getMonth()].substring(0,3)),n}}),y.fn[n]=function(t){return this.each(function(){y.data(this,"plugin_"+n)||y.data(this,"plugin_"+n,new e(this,t))})}}(jQuery,(window,document));
//# sourceMappingURL=jquery.calendar.min.js.map

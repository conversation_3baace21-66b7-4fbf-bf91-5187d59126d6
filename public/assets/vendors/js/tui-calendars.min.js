"use strict";var CalendarList=[];function CalendarInfo(){this.id=null,this.name=null,this.checked=!0,this.color=null,this.bgColor=null,this.borderColor=null,this.dragBgColor=null}function addCalendar(r){CalendarList.push(r)}function findCalendar(o){var a;return CalendarList.forEach(function(r){r.id===o&&(a=r)}),a||CalendarList[0]}function hexToRGBA(r){return"rgba("+parseInt(r.slice(1,3),16)+", "+parseInt(r.slice(3,5),16)+", "+parseInt(r.slice(5,7),16)+", "+(parseInt(r.slice(7,9),16)/255||1)+")"}!function(){var r=new CalendarInfo;r.id=String(1),r.name="Office",r.color="#5485e4",r.bgColor="#5485e4",r.dragBgColor="#5485e4",r.borderColor="#5485e4",addCalendar(r),(r=new CalendarInfo).id=String(2),r.name="Family",r.color="#25b865",r.bgColor="#25b865",r.dragBgColor="#25b865",r.borderColor="#25b865",addCalendar(r),(r=new CalendarInfo).id=String(3),r.name="Friend",r.color="#d13b4c",r.bgColor="#d13b4c",r.dragBgColor="#d13b4c",r.borderColor="#d13b4c",addCalendar(r),(r=new CalendarInfo).id=String(4),r.name="Travel",r.color="#17a2b8",r.bgColor="#17a2b8",r.dragBgColor="#17a2b8",r.borderColor="#17a2b8",addCalendar(r),(r=new CalendarInfo).id=String(5),r.name="Privete",r.color="#e49e3d",r.bgColor="#e49e3d",r.dragBgColor="#e49e3d",r.borderColor="#e49e3d",addCalendar(r),(r=new CalendarInfo).id=String(6),r.name="Holidays",r.color="#5856d6",r.bgColor="#5856d6",r.dragBgColor="#5856d6",r.borderColor="#5856d6",addCalendar(r),(r=new CalendarInfo).id=String(7),r.name="Company",r.color="#3dc7be",r.bgColor="#3dc7be",r.dragBgColor="#3dc7be",r.borderColor="#3dc7be",addCalendar(r),(r=new CalendarInfo).id=String(8),r.name="Birthdays",r.color="#475e77",r.bgColor="#475e77",r.dragBgColor="#475e77",r.borderColor="#475e77",addCalendar(r)}();
//# sourceMappingURL=tui-calendars.min.js.map

{"version": 3, "file": "pace.min.js", "sources": ["pace.min.js"], "sourcesContent": ["/*!\r\n * pace.js v1.2.4\r\n * https://github.com/CodeByZach/pace/\r\n * Licensed MIT © HubSpot, Inc.\r\n */\r\n(function () {\r\n\tvar AjaxMonitor,\r\n\t\tBar,\r\n\t\tDocumentMonitor,\r\n\t\tElementMonitor,\r\n\t\tElementTracker,\r\n\t\tEventLagMonitor,\r\n\t\tEvented,\r\n\t\tEvents,\r\n\t\tNoTargetError,\r\n\t\tPace,\r\n\t\tRequestIntercept,\r\n\t\tSOURCE_KEYS,\r\n\t\tScaler,\r\n\t\tSocketRequestTracker,\r\n\t\tXHRRequestTracker,\r\n\t\taddEventListener,\r\n\t\tanimation,\r\n\t\tavgAmplitude,\r\n\t\tbar,\r\n\t\tcancelAnimation,\r\n\t\tcancelAnimationFrame,\r\n\t\tdefaultOptions,\r\n\t\textend,\r\n\t\textendNative,\r\n\t\tgetFromDOM,\r\n\t\tgetIntercept,\r\n\t\thandlePushState,\r\n\t\tignoreStack,\r\n\t\tinit,\r\n\t\tnow,\r\n\t\toptions,\r\n\t\trequestAnimationFrame,\r\n\t\tresult,\r\n\t\trunAnimation,\r\n\t\tscalers,\r\n\t\tshouldIgnoreURL,\r\n\t\tshouldTrack,\r\n\t\tsource,\r\n\t\tsources,\r\n\t\tuniScaler,\r\n\t\t_WebSocket,\r\n\t\t_XDomainRequest,\r\n\t\t_XMLHttpRequest,\r\n\t\t_i,\r\n\t\t_intercept,\r\n\t\t_len,\r\n\t\t_pushState,\r\n\t\t_ref,\r\n\t\t_ref1,\r\n\t\t_replaceState,\r\n\t\t__slice = [].slice,\r\n\t\t__hasProp = {}.hasOwnProperty,\r\n\t\t__extends = function (child, parent) {\r\n\t\t\tfor (var key in parent) {\r\n\t\t\t\tif (__hasProp.call(parent, key)) child[key] = parent[key];\r\n\t\t\t}\r\n\t\t\tfunction ctor() {\r\n\t\t\t\tthis.constructor = child;\r\n\t\t\t}\r\n\t\t\tctor.prototype = parent.prototype;\r\n\t\t\tchild.prototype = new ctor();\r\n\t\t\tchild.__super__ = parent.prototype;\r\n\t\t\treturn child;\r\n\t\t},\r\n\t\t__indexOf =\r\n\t\t\t[].indexOf ||\r\n\t\t\tfunction (item) {\r\n\t\t\t\tfor (var i = 0, l = this.length; i < l; i++) {\r\n\t\t\t\t\tif (i in this && this[i] === item) return i;\r\n\t\t\t\t}\r\n\t\t\t\treturn -1;\r\n\t\t\t},\r\n\t\t__bind = function (fn, me) {\r\n\t\t\treturn function () {\r\n\t\t\t\treturn fn.apply(me, arguments);\r\n\t\t\t};\r\n\t\t};\r\n\r\n\tdefaultOptions = {\r\n\t\tclassName: \"\",\r\n\t\tcatchupTime: 100,\r\n\t\tinitialRate: 0.03,\r\n\t\tminTime: 250,\r\n\t\tghostTime: 100,\r\n\t\tmaxProgressPerFrame: 20,\r\n\t\teaseFactor: 1.25,\r\n\t\tstartOnPageLoad: true,\r\n\t\trestartOnPushState: true,\r\n\t\trestartOnRequestAfter: 500,\r\n\t\ttarget: \"body\",\r\n\t\telements: {\r\n\t\t\tcheckInterval: 100,\r\n\t\t\tselectors: [\"body\"],\r\n\t\t},\r\n\t\teventLag: {\r\n\t\t\tminSamples: 10,\r\n\t\t\tsampleCount: 3,\r\n\t\t\tlagThreshold: 3,\r\n\t\t},\r\n\t\tajax: {\r\n\t\t\ttrackMethods: [\"GET\"],\r\n\t\t\ttrackWebSockets: true,\r\n\t\t\tignoreURLs: [],\r\n\t\t},\r\n\t};\r\n\r\n\tnow = function () {\r\n\t\tvar _ref;\r\n\t\treturn (_ref = typeof performance !== \"undefined\" && performance !== null ? (typeof performance.now === \"function\" ? performance.now() : void 0) : void 0) != null ? _ref : +new Date();\r\n\t};\r\n\r\n\trequestAnimationFrame = window.requestAnimationFrame || window.mozRequestAnimationFrame || window.webkitRequestAnimationFrame || window.msRequestAnimationFrame;\r\n\r\n\tcancelAnimationFrame = window.cancelAnimationFrame || window.mozCancelAnimationFrame;\r\n\r\n\taddEventListener = function (obj, event, callback) {\r\n\t\tif (typeof obj.addEventListener === \"function\") {\r\n\t\t\treturn obj.addEventListener(event, callback, false);\r\n\t\t} else {\r\n\t\t\treturn (function () {\r\n\t\t\t\tif (typeof obj[\"on\" + event] !== \"function\" || typeof obj[\"on\" + event].eventListeners !== \"object\") {\r\n\t\t\t\t\tvar eventListeners = new Events();\r\n\t\t\t\t\tif (typeof obj[\"on\" + event] === \"function\") {\r\n\t\t\t\t\t\teventListeners.on(event, obj[\"on\" + event]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tobj[\"on\" + event] = function (evt) {\r\n\t\t\t\t\t\treturn eventListeners.trigger(event, evt);\r\n\t\t\t\t\t};\r\n\t\t\t\t\tobj[\"on\" + event].eventListeners = eventListeners;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvar eventListeners = obj[\"on\" + event].eventListeners;\r\n\t\t\t\t}\r\n\t\t\t\teventListeners.on(event, callback);\r\n\t\t\t})();\r\n\t\t}\r\n\t};\r\n\r\n\tif (requestAnimationFrame == null) {\r\n\t\trequestAnimationFrame = function (fn) {\r\n\t\t\treturn setTimeout(fn, 50);\r\n\t\t};\r\n\t\tcancelAnimationFrame = function (id) {\r\n\t\t\treturn clearTimeout(id);\r\n\t\t};\r\n\t}\r\n\r\n\trunAnimation = function (fn) {\r\n\t\tvar last, tick;\r\n\t\tlast = now();\r\n\t\ttick = function () {\r\n\t\t\tvar diff;\r\n\t\t\tdiff = now() - last;\r\n\t\t\tif (diff >= 33) {\r\n\t\t\t\tlast = now();\r\n\t\t\t\treturn fn(diff, function () {\r\n\t\t\t\t\treturn requestAnimationFrame(tick);\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\treturn setTimeout(tick, 33 - diff);\r\n\t\t\t}\r\n\t\t};\r\n\t\treturn tick();\r\n\t};\r\n\r\n\tresult = function () {\r\n\t\tvar args, key, obj;\r\n\t\t(obj = arguments[0]), (key = arguments[1]), (args = 3 <= arguments.length ? __slice.call(arguments, 2) : []);\r\n\t\tif (typeof obj[key] === \"function\") {\r\n\t\t\treturn obj[key].apply(obj, args);\r\n\t\t} else {\r\n\t\t\treturn obj[key];\r\n\t\t}\r\n\t};\r\n\r\n\textend = function () {\r\n\t\tvar key, out, source, sources, val, _i, _len;\r\n\t\t(out = arguments[0]), (sources = 2 <= arguments.length ? __slice.call(arguments, 1) : []);\r\n\t\tfor (_i = 0, _len = sources.length; _i < _len; _i++) {\r\n\t\t\tsource = sources[_i];\r\n\t\t\tif (source) {\r\n\t\t\t\tfor (key in source) {\r\n\t\t\t\t\tif (!__hasProp.call(source, key)) continue;\r\n\t\t\t\t\tval = source[key];\r\n\t\t\t\t\tif (out[key] != null && typeof out[key] === \"object\" && val != null && typeof val === \"object\") {\r\n\t\t\t\t\t\textend(out[key], val);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tout[key] = val;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn out;\r\n\t};\r\n\r\n\tavgAmplitude = function (arr) {\r\n\t\tvar count, sum, v, _i, _len;\r\n\t\tsum = count = 0;\r\n\t\tfor (_i = 0, _len = arr.length; _i < _len; _i++) {\r\n\t\t\tv = arr[_i];\r\n\t\t\tsum += Math.abs(v);\r\n\t\t\tcount++;\r\n\t\t}\r\n\t\treturn sum / count;\r\n\t};\r\n\r\n\tgetFromDOM = function (key, json) {\r\n\t\tvar data, e, el;\r\n\t\tif (key == null) {\r\n\t\t\tkey = \"options\";\r\n\t\t}\r\n\t\tif (json == null) {\r\n\t\t\tjson = true;\r\n\t\t}\r\n\t\tel = document.querySelector(\"[data-pace-\" + key + \"]\");\r\n\t\tif (!el) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tdata = el.getAttribute(\"data-pace-\" + key);\r\n\t\tif (!json) {\r\n\t\t\treturn data;\r\n\t\t}\r\n\t\ttry {\r\n\t\t\treturn JSON.parse(data);\r\n\t\t} catch (_error) {\r\n\t\t\te = _error;\r\n\t\t\treturn typeof console !== \"undefined\" && console !== null ? console.error(\"Error parsing inline pace options\", e) : void 0;\r\n\t\t}\r\n\t};\r\n\r\n\tEvented = (function () {\r\n\t\tfunction Evented() {}\r\n\r\n\t\tEvented.prototype.on = function (event, handler, ctx, once) {\r\n\t\t\tvar _base;\r\n\t\t\tif (once == null) {\r\n\t\t\t\tonce = false;\r\n\t\t\t}\r\n\t\t\tif (this.bindings == null) {\r\n\t\t\t\tthis.bindings = {};\r\n\t\t\t}\r\n\t\t\tif ((_base = this.bindings)[event] == null) {\r\n\t\t\t\t_base[event] = [];\r\n\t\t\t}\r\n\t\t\treturn this.bindings[event].push({\r\n\t\t\t\thandler: handler,\r\n\t\t\t\tctx: ctx,\r\n\t\t\t\tonce: once,\r\n\t\t\t});\r\n\t\t};\r\n\r\n\t\tEvented.prototype.once = function (event, handler, ctx) {\r\n\t\t\treturn this.on(event, handler, ctx, true);\r\n\t\t};\r\n\r\n\t\tEvented.prototype.off = function (event, handler) {\r\n\t\t\tvar i, _ref, _results;\r\n\t\t\tif (((_ref = this.bindings) != null ? _ref[event] : void 0) == null) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (handler == null) {\r\n\t\t\t\treturn delete this.bindings[event];\r\n\t\t\t} else {\r\n\t\t\t\ti = 0;\r\n\t\t\t\t_results = [];\r\n\t\t\t\twhile (i < this.bindings[event].length) {\r\n\t\t\t\t\tif (this.bindings[event][i].handler === handler) {\r\n\t\t\t\t\t\t_results.push(this.bindings[event].splice(i, 1));\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t_results.push(i++);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn _results;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tEvented.prototype.trigger = function () {\r\n\t\t\tvar args, ctx, event, handler, i, once, _ref, _ref1, _results;\r\n\t\t\t(event = arguments[0]), (args = 2 <= arguments.length ? __slice.call(arguments, 1) : []);\r\n\t\t\tif ((_ref = this.bindings) != null ? _ref[event] : void 0) {\r\n\t\t\t\ti = 0;\r\n\t\t\t\t_results = [];\r\n\t\t\t\twhile (i < this.bindings[event].length) {\r\n\t\t\t\t\t(_ref1 = this.bindings[event][i]), (handler = _ref1.handler), (ctx = _ref1.ctx), (once = _ref1.once);\r\n\t\t\t\t\thandler.apply(ctx != null ? ctx : this, args);\r\n\t\t\t\t\tif (once) {\r\n\t\t\t\t\t\t_results.push(this.bindings[event].splice(i, 1));\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t_results.push(i++);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn _results;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\treturn Evented;\r\n\t})();\r\n\r\n\tPace = window.Pace || {};\r\n\r\n\twindow.Pace = Pace;\r\n\r\n\textend(Pace, Evented.prototype);\r\n\r\n\toptions = Pace.options = extend({}, defaultOptions, window.paceOptions, getFromDOM());\r\n\r\n\t_ref = [\"ajax\", \"document\", \"eventLag\", \"elements\"];\r\n\tfor (_i = 0, _len = _ref.length; _i < _len; _i++) {\r\n\t\tsource = _ref[_i];\r\n\t\tif (options[source] === true) {\r\n\t\t\toptions[source] = defaultOptions[source];\r\n\t\t}\r\n\t}\r\n\r\n\tNoTargetError = (function (_super) {\r\n\t\t__extends(NoTargetError, _super);\r\n\r\n\t\tfunction NoTargetError() {\r\n\t\t\t_ref1 = NoTargetError.__super__.constructor.apply(this, arguments);\r\n\t\t\treturn _ref1;\r\n\t\t}\r\n\r\n\t\treturn NoTargetError;\r\n\t})(Error);\r\n\r\n\tBar = (function () {\r\n\t\tfunction Bar() {\r\n\t\t\tthis.progress = 0;\r\n\t\t}\r\n\r\n\t\tBar.prototype.getElement = function () {\r\n\t\t\tvar targetElement;\r\n\t\t\tif (this.el == null) {\r\n\t\t\t\ttargetElement = document.querySelector(options.target);\r\n\t\t\t\tif (!targetElement) {\r\n\t\t\t\t\tthrow new NoTargetError();\r\n\t\t\t\t}\r\n\t\t\t\tthis.el = document.createElement(\"div\");\r\n\t\t\t\tthis.el.className = \"pace pace-active\";\r\n\t\t\t\tdocument.body.className = document.body.className.replace(/(pace-done )|/, \"pace-running \");\r\n\t\t\t\tvar _custom_class_name = options.className !== \"\" ? \" \" + options.className : \"\";\r\n\t\t\t\tthis.el.innerHTML = '<div class=\"pace-progress' + _custom_class_name + '\">\\n  <div class=\"pace-progress-inner\"></div>\\n</div>\\n<div class=\"pace-activity\"></div>';\r\n\t\t\t\tif (targetElement.firstChild != null) {\r\n\t\t\t\t\ttargetElement.insertBefore(this.el, targetElement.firstChild);\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttargetElement.appendChild(this.el);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn this.el;\r\n\t\t};\r\n\r\n\t\tBar.prototype.finish = function () {\r\n\t\t\tvar el;\r\n\t\t\tel = this.getElement();\r\n\t\t\tel.className = el.className.replace(\"pace-active\", \"pace-inactive\");\r\n\t\t\treturn (document.body.className = document.body.className.replace(\"pace-running \", \"pace-done \"));\r\n\t\t};\r\n\r\n\t\tBar.prototype.update = function (prog) {\r\n\t\t\tthis.progress = prog;\r\n\t\t\tPace.trigger(\"progress\", prog);\r\n\t\t\treturn this.render();\r\n\t\t};\r\n\r\n\t\tBar.prototype.destroy = function () {\r\n\t\t\ttry {\r\n\t\t\t\tthis.getElement().parentNode.removeChild(this.getElement());\r\n\t\t\t} catch (_error) {\r\n\t\t\t\tNoTargetError = _error;\r\n\t\t\t}\r\n\t\t\treturn (this.el = void 0);\r\n\t\t};\r\n\r\n\t\tBar.prototype.render = function () {\r\n\t\t\tvar el, key, progressStr, transform, _j, _len1, _ref2;\r\n\t\t\tif (document.querySelector(options.target) == null) {\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t\tel = this.getElement();\r\n\t\t\ttransform = \"translate3d(\" + this.progress + \"%, 0, 0)\";\r\n\t\t\t_ref2 = [\"webkitTransform\", \"msTransform\", \"transform\"];\r\n\t\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\t\tkey = _ref2[_j];\r\n\t\t\t\tel.children[0].style[key] = transform;\r\n\t\t\t}\r\n\t\t\tif (!this.lastRenderedProgress || this.lastRenderedProgress | (0 !== this.progress) | 0) {\r\n\t\t\t\tel.children[0].setAttribute(\"data-progress-text\", \"\" + (this.progress | 0) + \"%\");\r\n\t\t\t\tif (this.progress >= 100) {\r\n\t\t\t\t\tprogressStr = \"99\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\tprogressStr = this.progress < 10 ? \"0\" : \"\";\r\n\t\t\t\t\tprogressStr += this.progress | 0;\r\n\t\t\t\t}\r\n\t\t\t\tel.children[0].setAttribute(\"data-progress\", \"\" + progressStr);\r\n\t\t\t}\r\n\t\t\tPace.trigger(\"change\", this.progress);\r\n\t\t\treturn (this.lastRenderedProgress = this.progress);\r\n\t\t};\r\n\r\n\t\tBar.prototype.done = function () {\r\n\t\t\treturn this.progress >= 100;\r\n\t\t};\r\n\r\n\t\treturn Bar;\r\n\t})();\r\n\r\n\tEvents = (function () {\r\n\t\tfunction Events() {\r\n\t\t\tthis.bindings = {};\r\n\t\t}\r\n\r\n\t\tEvents.prototype.trigger = function (name, val) {\r\n\t\t\tvar binding, _j, _len1, _ref2, _results;\r\n\t\t\tif (this.bindings[name] != null) {\r\n\t\t\t\t_ref2 = this.bindings[name];\r\n\t\t\t\t_results = [];\r\n\t\t\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\t\t\tbinding = _ref2[_j];\r\n\t\t\t\t\t_results.push(binding.call(this, val));\r\n\t\t\t\t}\r\n\t\t\t\treturn _results;\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tEvents.prototype.on = function (name, fn) {\r\n\t\t\tvar _base;\r\n\t\t\tif ((_base = this.bindings)[name] == null) {\r\n\t\t\t\t_base[name] = [];\r\n\t\t\t}\r\n\t\t\treturn this.bindings[name].push(fn);\r\n\t\t};\r\n\r\n\t\treturn Events;\r\n\t})();\r\n\r\n\t_XMLHttpRequest = window.XMLHttpRequest;\r\n\r\n\t_XDomainRequest = window.XDomainRequest;\r\n\r\n\t_WebSocket = window.WebSocket;\r\n\r\n\textendNative = function (to, from) {\r\n\t\tvar e, key, _results;\r\n\t\t_results = [];\r\n\t\tfor (key in from.prototype) {\r\n\t\t\ttry {\r\n\t\t\t\tif (to[key] == null && typeof from[key] !== \"function\") {\r\n\t\t\t\t\tif (typeof Object.defineProperty === \"function\") {\r\n\t\t\t\t\t\t_results.push(\r\n\t\t\t\t\t\t\tObject.defineProperty(to, key, {\r\n\t\t\t\t\t\t\t\tget: (function (key) {\r\n\t\t\t\t\t\t\t\t\treturn function () {\r\n\t\t\t\t\t\t\t\t\t\treturn from.prototype[key];\r\n\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t})(key),\r\n\t\t\t\t\t\t\t\tconfigurable: true,\r\n\t\t\t\t\t\t\t\tenumerable: true,\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t_results.push((to[key] = from.prototype[key]));\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t_results.push(void 0);\r\n\t\t\t\t}\r\n\t\t\t} catch (_error) {\r\n\t\t\t\te = _error;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn _results;\r\n\t};\r\n\r\n\tignoreStack = [];\r\n\r\n\tPace.ignore = function () {\r\n\t\tvar args, fn, ret;\r\n\t\t(fn = arguments[0]), (args = 2 <= arguments.length ? __slice.call(arguments, 1) : []);\r\n\t\tignoreStack.unshift(\"ignore\");\r\n\t\tret = fn.apply(null, args);\r\n\t\tignoreStack.shift();\r\n\t\treturn ret;\r\n\t};\r\n\r\n\tPace.track = function () {\r\n\t\tvar args, fn, ret;\r\n\t\t(fn = arguments[0]), (args = 2 <= arguments.length ? __slice.call(arguments, 1) : []);\r\n\t\tignoreStack.unshift(\"track\");\r\n\t\tret = fn.apply(null, args);\r\n\t\tignoreStack.shift();\r\n\t\treturn ret;\r\n\t};\r\n\r\n\tshouldTrack = function (method) {\r\n\t\tvar _ref2;\r\n\t\tif (method == null) {\r\n\t\t\tmethod = \"GET\";\r\n\t\t}\r\n\t\tif (ignoreStack[0] === \"track\") {\r\n\t\t\treturn \"force\";\r\n\t\t}\r\n\t\tif (!ignoreStack.length && options.ajax) {\r\n\t\t\tif (method === \"socket\" && options.ajax.trackWebSockets) {\r\n\t\t\t\treturn true;\r\n\t\t\t} else if (((_ref2 = method.toUpperCase()), __indexOf.call(options.ajax.trackMethods, _ref2) >= 0)) {\r\n\t\t\t\treturn true;\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn false;\r\n\t};\r\n\r\n\tRequestIntercept = (function (_super) {\r\n\t\t__extends(RequestIntercept, _super);\r\n\r\n\t\tfunction RequestIntercept() {\r\n\t\t\tvar monitorXHR,\r\n\t\t\t\t_this = this;\r\n\t\t\tRequestIntercept.__super__.constructor.apply(this, arguments);\r\n\t\t\tmonitorXHR = function (req) {\r\n\t\t\t\tvar _open;\r\n\t\t\t\t_open = req.open;\r\n\t\t\t\treturn (req.open = function (type, url, async) {\r\n\t\t\t\t\tif (shouldTrack(type)) {\r\n\t\t\t\t\t\t_this.trigger(\"request\", {\r\n\t\t\t\t\t\t\ttype: type,\r\n\t\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\t\trequest: req,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn _open.apply(req, arguments);\r\n\t\t\t\t});\r\n\t\t\t};\r\n\t\t\twindow.XMLHttpRequest = function (flags) {\r\n\t\t\t\tvar req;\r\n\t\t\t\treq = new _XMLHttpRequest(flags);\r\n\t\t\t\tmonitorXHR(req);\r\n\t\t\t\treturn req;\r\n\t\t\t};\r\n\t\t\ttry {\r\n\t\t\t\textendNative(window.XMLHttpRequest, _XMLHttpRequest);\r\n\t\t\t} catch (_error) {}\r\n\t\t\tif (_XDomainRequest != null) {\r\n\t\t\t\twindow.XDomainRequest = function () {\r\n\t\t\t\t\tvar req;\r\n\t\t\t\t\treq = new _XDomainRequest();\r\n\t\t\t\t\tmonitorXHR(req);\r\n\t\t\t\t\treturn req;\r\n\t\t\t\t};\r\n\t\t\t\ttry {\r\n\t\t\t\t\textendNative(window.XDomainRequest, _XDomainRequest);\r\n\t\t\t\t} catch (_error) {}\r\n\t\t\t}\r\n\t\t\tif (_WebSocket != null && options.ajax.trackWebSockets) {\r\n\t\t\t\twindow.WebSocket = function (url, protocols) {\r\n\t\t\t\t\tvar req;\r\n\t\t\t\t\tif (protocols != null) {\r\n\t\t\t\t\t\treq = new _WebSocket(url, protocols);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treq = new _WebSocket(url);\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (shouldTrack(\"socket\")) {\r\n\t\t\t\t\t\t_this.trigger(\"request\", {\r\n\t\t\t\t\t\t\ttype: \"socket\",\r\n\t\t\t\t\t\t\turl: url,\r\n\t\t\t\t\t\t\tprotocols: protocols,\r\n\t\t\t\t\t\t\trequest: req,\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn req;\r\n\t\t\t\t};\r\n\t\t\t\ttry {\r\n\t\t\t\t\textendNative(window.WebSocket, _WebSocket);\r\n\t\t\t\t} catch (_error) {}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn RequestIntercept;\r\n\t})(Events);\r\n\r\n\t_intercept = null;\r\n\r\n\tgetIntercept = function () {\r\n\t\tif (_intercept == null) {\r\n\t\t\t_intercept = new RequestIntercept();\r\n\t\t}\r\n\t\treturn _intercept;\r\n\t};\r\n\r\n\tshouldIgnoreURL = function (url) {\r\n\t\tvar pattern, _j, _len1, _ref2;\r\n\t\t_ref2 = options.ajax.ignoreURLs;\r\n\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\tpattern = _ref2[_j];\r\n\t\t\tif (typeof pattern === \"string\") {\r\n\t\t\t\tif (url.indexOf(pattern) !== -1) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tif (pattern.test(url)) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn false;\r\n\t};\r\n\r\n\tgetIntercept().on(\"request\", function (_arg) {\r\n\t\tvar after, args, request, type, url;\r\n\t\t(type = _arg.type), (request = _arg.request), (url = _arg.url);\r\n\t\tif (shouldIgnoreURL(url)) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\tif (!Pace.running && (options.restartOnRequestAfter !== false || shouldTrack(type) === \"force\")) {\r\n\t\t\targs = arguments;\r\n\t\t\tafter = options.restartOnRequestAfter || 0;\r\n\t\t\tif (typeof after === \"boolean\") {\r\n\t\t\t\tafter = 0;\r\n\t\t\t}\r\n\t\t\treturn setTimeout(function () {\r\n\t\t\t\tvar stillActive, _j, _len1, _ref2, _ref3, _results;\r\n\t\t\t\tif (type === \"socket\") {\r\n\t\t\t\t\tstillActive = request.readyState < 1;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tstillActive = 0 < (_ref2 = request.readyState) && _ref2 < 4;\r\n\t\t\t\t}\r\n\t\t\t\tif (stillActive) {\r\n\t\t\t\t\tPace.restart();\r\n\t\t\t\t\t_ref3 = Pace.sources;\r\n\t\t\t\t\t_results = [];\r\n\t\t\t\t\tfor (_j = 0, _len1 = _ref3.length; _j < _len1; _j++) {\r\n\t\t\t\t\t\tsource = _ref3[_j];\r\n\t\t\t\t\t\tif (source instanceof AjaxMonitor) {\r\n\t\t\t\t\t\t\tsource.watch.apply(source, args);\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t_results.push(void 0);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn _results;\r\n\t\t\t\t}\r\n\t\t\t}, after);\r\n\t\t}\r\n\t});\r\n\r\n\tAjaxMonitor = (function () {\r\n\t\tfunction AjaxMonitor() {\r\n\t\t\tthis.complete = __bind(this.complete, this);\r\n\t\t\tvar _this = this;\r\n\t\t\tthis.elements = [];\r\n\t\t\tgetIntercept().on(\"request\", function () {\r\n\t\t\t\treturn _this.watch.apply(_this, arguments);\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\tAjaxMonitor.prototype.watch = function (_arg) {\r\n\t\t\tvar request, tracker, type, url;\r\n\t\t\t(type = _arg.type), (request = _arg.request), (url = _arg.url);\r\n\t\t\tif (shouldIgnoreURL(url)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (type === \"socket\") {\r\n\t\t\t\ttracker = new SocketRequestTracker(request, this.complete);\r\n\t\t\t} else {\r\n\t\t\t\ttracker = new XHRRequestTracker(request, this.complete);\r\n\t\t\t}\r\n\t\t\treturn this.elements.push(tracker);\r\n\t\t};\r\n\r\n\t\tAjaxMonitor.prototype.complete = function (tracker) {\r\n\t\t\treturn (this.elements = this.elements.filter(function (e) {\r\n\t\t\t\treturn e !== tracker;\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\treturn AjaxMonitor;\r\n\t})();\r\n\r\n\tXHRRequestTracker = (function () {\r\n\t\tfunction XHRRequestTracker(request, completeCallback) {\r\n\t\t\tvar event,\r\n\t\t\t\tsize,\r\n\t\t\t\t_j,\r\n\t\t\t\t_len1,\r\n\t\t\t\t_onreadystatechange,\r\n\t\t\t\t_ref2,\r\n\t\t\t\t_this = this;\r\n\t\t\tthis.progress = 0;\r\n\t\t\tif (window.ProgressEvent != null) {\r\n\t\t\t\tsize = null;\r\n\t\t\t\taddEventListener(\r\n\t\t\t\t\trequest,\r\n\t\t\t\t\t\"progress\",\r\n\t\t\t\t\tfunction (evt) {\r\n\t\t\t\t\t\tif (evt.lengthComputable) {\r\n\t\t\t\t\t\t\treturn (_this.progress = (100 * evt.loaded) / evt.total);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\treturn (_this.progress = _this.progress + (100 - _this.progress) / 2);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfalse\r\n\t\t\t\t);\r\n\t\t\t\t_ref2 = [\"load\", \"abort\", \"timeout\", \"error\"];\r\n\t\t\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\t\t\tevent = _ref2[_j];\r\n\t\t\t\t\taddEventListener(\r\n\t\t\t\t\t\trequest,\r\n\t\t\t\t\t\tevent,\r\n\t\t\t\t\t\tfunction () {\r\n\t\t\t\t\t\t\tcompleteCallback(_this);\r\n\t\t\t\t\t\t\treturn (_this.progress = 100);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfalse\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t_onreadystatechange = request.onreadystatechange;\r\n\t\t\t\trequest.onreadystatechange = function () {\r\n\t\t\t\t\tvar _ref3;\r\n\t\t\t\t\tif ((_ref3 = request.readyState) === 0 || _ref3 === 4) {\r\n\t\t\t\t\t\tcompleteCallback(_this);\r\n\t\t\t\t\t\t_this.progress = 100;\r\n\t\t\t\t\t} else if (request.readyState === 3) {\r\n\t\t\t\t\t\t_this.progress = 50;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn typeof _onreadystatechange === \"function\" ? _onreadystatechange.apply(null, arguments) : void 0;\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn XHRRequestTracker;\r\n\t})();\r\n\r\n\tSocketRequestTracker = (function () {\r\n\t\tfunction SocketRequestTracker(request, completeCallback) {\r\n\t\t\tvar event,\r\n\t\t\t\t_j,\r\n\t\t\t\t_len1,\r\n\t\t\t\t_ref2,\r\n\t\t\t\t_this = this;\r\n\t\t\tthis.progress = 0;\r\n\t\t\t_ref2 = [\"error\", \"open\"];\r\n\t\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\t\tevent = _ref2[_j];\r\n\t\t\t\taddEventListener(\r\n\t\t\t\t\trequest,\r\n\t\t\t\t\tevent,\r\n\t\t\t\t\tfunction () {\r\n\t\t\t\t\t\tcompleteCallback(_this);\r\n\t\t\t\t\t\treturn (_this.progress = 100);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfalse\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn SocketRequestTracker;\r\n\t})();\r\n\r\n\tElementMonitor = (function () {\r\n\t\tfunction ElementMonitor(options) {\r\n\t\t\tvar selector, _j, _len1, _ref2;\r\n\t\t\tif (options == null) {\r\n\t\t\t\toptions = {};\r\n\t\t\t}\r\n\t\t\tthis.complete = __bind(this.complete, this);\r\n\t\t\tthis.elements = [];\r\n\t\t\tif (options.selectors == null) {\r\n\t\t\t\toptions.selectors = [];\r\n\t\t\t}\r\n\t\t\t_ref2 = options.selectors;\r\n\t\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\t\tselector = _ref2[_j];\r\n\t\t\t\tthis.elements.push(new ElementTracker(selector, this.complete));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tElementMonitor.prototype.complete = function (tracker) {\r\n\t\t\treturn (this.elements = this.elements.filter(function (e) {\r\n\t\t\t\treturn e !== tracker;\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\treturn ElementMonitor;\r\n\t})();\r\n\r\n\tElementTracker = (function () {\r\n\t\tfunction ElementTracker(selector, completeCallback) {\r\n\t\t\tthis.selector = selector;\r\n\t\t\tthis.completeCallback = completeCallback;\r\n\t\t\tthis.progress = 0;\r\n\t\t\tthis.check();\r\n\t\t}\r\n\r\n\t\tElementTracker.prototype.check = function () {\r\n\t\t\tvar _this = this;\r\n\t\t\tif (document.querySelector(this.selector)) {\r\n\t\t\t\treturn this.done();\r\n\t\t\t} else {\r\n\t\t\t\treturn setTimeout(function () {\r\n\t\t\t\t\treturn _this.check();\r\n\t\t\t\t}, options.elements.checkInterval);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tElementTracker.prototype.done = function () {\r\n\t\t\tthis.completeCallback(this);\r\n\t\t\tthis.completeCallback = null;\r\n\t\t\treturn (this.progress = 100);\r\n\t\t};\r\n\r\n\t\treturn ElementTracker;\r\n\t})();\r\n\r\n\tDocumentMonitor = (function () {\r\n\t\tDocumentMonitor.prototype.states = {\r\n\t\t\tloading: 0,\r\n\t\t\tinteractive: 50,\r\n\t\t\tcomplete: 100,\r\n\t\t};\r\n\r\n\t\tfunction DocumentMonitor() {\r\n\t\t\tvar _onreadystatechange,\r\n\t\t\t\t_ref2,\r\n\t\t\t\t_this = this;\r\n\t\t\tthis.progress = (_ref2 = this.states[document.readyState]) != null ? _ref2 : 100;\r\n\t\t\t_onreadystatechange = document.onreadystatechange;\r\n\t\t\tdocument.onreadystatechange = function () {\r\n\t\t\t\tif (_this.states[document.readyState] != null) {\r\n\t\t\t\t\t_this.progress = _this.states[document.readyState];\r\n\t\t\t\t}\r\n\t\t\t\treturn typeof _onreadystatechange === \"function\" ? _onreadystatechange.apply(null, arguments) : void 0;\r\n\t\t\t};\r\n\t\t}\r\n\r\n\t\treturn DocumentMonitor;\r\n\t})();\r\n\r\n\tEventLagMonitor = (function () {\r\n\t\tfunction EventLagMonitor() {\r\n\t\t\tvar avg,\r\n\t\t\t\tinterval,\r\n\t\t\t\tlast,\r\n\t\t\t\tpoints,\r\n\t\t\t\tsamples,\r\n\t\t\t\t_this = this;\r\n\t\t\tthis.progress = 0;\r\n\t\t\tavg = 0;\r\n\t\t\tsamples = [];\r\n\t\t\tpoints = 0;\r\n\t\t\tlast = now();\r\n\t\t\tinterval = setInterval(function () {\r\n\t\t\t\tvar diff;\r\n\t\t\t\tdiff = now() - last - 50;\r\n\t\t\t\tlast = now();\r\n\t\t\t\tsamples.push(diff);\r\n\t\t\t\tif (samples.length > options.eventLag.sampleCount) {\r\n\t\t\t\t\tsamples.shift();\r\n\t\t\t\t}\r\n\t\t\t\tavg = avgAmplitude(samples);\r\n\t\t\t\tif (++points >= options.eventLag.minSamples && avg < options.eventLag.lagThreshold) {\r\n\t\t\t\t\t_this.progress = 100;\r\n\t\t\t\t\treturn clearInterval(interval);\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn (_this.progress = 100 * (3 / (avg + 3)));\r\n\t\t\t\t}\r\n\t\t\t}, 50);\r\n\t\t}\r\n\r\n\t\treturn EventLagMonitor;\r\n\t})();\r\n\r\n\tScaler = (function () {\r\n\t\tfunction Scaler(source) {\r\n\t\t\tthis.source = source;\r\n\t\t\tthis.last = this.sinceLastUpdate = 0;\r\n\t\t\tthis.rate = options.initialRate;\r\n\t\t\tthis.catchup = 0;\r\n\t\t\tthis.progress = this.lastProgress = 0;\r\n\t\t\tif (this.source != null) {\r\n\t\t\t\tthis.progress = result(this.source, \"progress\");\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tScaler.prototype.tick = function (frameTime, val) {\r\n\t\t\tvar scaling;\r\n\t\t\tif (val == null) {\r\n\t\t\t\tval = result(this.source, \"progress\");\r\n\t\t\t}\r\n\t\t\tif (val >= 100) {\r\n\t\t\t\tthis.done = true;\r\n\t\t\t}\r\n\t\t\tif (val === this.last) {\r\n\t\t\t\tthis.sinceLastUpdate += frameTime;\r\n\t\t\t} else {\r\n\t\t\t\tif (this.sinceLastUpdate) {\r\n\t\t\t\t\tthis.rate = (val - this.last) / this.sinceLastUpdate;\r\n\t\t\t\t}\r\n\t\t\t\tthis.catchup = (val - this.progress) / options.catchupTime;\r\n\t\t\t\tthis.sinceLastUpdate = 0;\r\n\t\t\t\tthis.last = val;\r\n\t\t\t}\r\n\t\t\tif (val > this.progress) {\r\n\t\t\t\tthis.progress += this.catchup * frameTime;\r\n\t\t\t}\r\n\t\t\tscaling = 1 - Math.pow(this.progress / 100, options.easeFactor);\r\n\t\t\tthis.progress += scaling * this.rate * frameTime;\r\n\t\t\tthis.progress = Math.min(this.lastProgress + options.maxProgressPerFrame, this.progress);\r\n\t\t\tthis.progress = Math.max(0, this.progress);\r\n\t\t\tthis.progress = Math.min(100, this.progress);\r\n\t\t\tthis.lastProgress = this.progress;\r\n\t\t\treturn this.progress;\r\n\t\t};\r\n\r\n\t\treturn Scaler;\r\n\t})();\r\n\r\n\tsources = null;\r\n\r\n\tscalers = null;\r\n\r\n\tbar = null;\r\n\r\n\tuniScaler = null;\r\n\r\n\tanimation = null;\r\n\r\n\tcancelAnimation = null;\r\n\r\n\tPace.running = false;\r\n\r\n\thandlePushState = function () {\r\n\t\tif (options.restartOnPushState) {\r\n\t\t\treturn Pace.restart();\r\n\t\t}\r\n\t};\r\n\r\n\tif (window.history.pushState != null) {\r\n\t\t_pushState = window.history.pushState;\r\n\t\twindow.history.pushState = function () {\r\n\t\t\thandlePushState();\r\n\t\t\treturn _pushState.apply(window.history, arguments);\r\n\t\t};\r\n\t}\r\n\r\n\tif (window.history.replaceState != null) {\r\n\t\t_replaceState = window.history.replaceState;\r\n\t\twindow.history.replaceState = function () {\r\n\t\t\thandlePushState();\r\n\t\t\treturn _replaceState.apply(window.history, arguments);\r\n\t\t};\r\n\t}\r\n\r\n\tSOURCE_KEYS = {\r\n\t\tajax: AjaxMonitor,\r\n\t\telements: ElementMonitor,\r\n\t\tdocument: DocumentMonitor,\r\n\t\teventLag: EventLagMonitor,\r\n\t};\r\n\r\n\t(init = function () {\r\n\t\tvar type, _j, _k, _len1, _len2, _ref2, _ref3, _ref4;\r\n\t\tPace.sources = sources = [];\r\n\t\t_ref2 = [\"ajax\", \"elements\", \"document\", \"eventLag\"];\r\n\t\tfor (_j = 0, _len1 = _ref2.length; _j < _len1; _j++) {\r\n\t\t\ttype = _ref2[_j];\r\n\t\t\tif (options[type] !== false) {\r\n\t\t\t\tsources.push(new SOURCE_KEYS[type](options[type]));\r\n\t\t\t}\r\n\t\t}\r\n\t\t_ref4 = (_ref3 = options.extraSources) != null ? _ref3 : [];\r\n\t\tfor (_k = 0, _len2 = _ref4.length; _k < _len2; _k++) {\r\n\t\t\tsource = _ref4[_k];\r\n\t\t\tsources.push(new source(options));\r\n\t\t}\r\n\t\tPace.bar = bar = new Bar();\r\n\t\tscalers = [];\r\n\t\treturn (uniScaler = new Scaler());\r\n\t})();\r\n\r\n\tPace.stop = function () {\r\n\t\tPace.trigger(\"stop\");\r\n\t\tPace.running = false;\r\n\t\tbar.destroy();\r\n\t\tcancelAnimation = true;\r\n\t\tif (animation != null) {\r\n\t\t\tif (typeof cancelAnimationFrame === \"function\") {\r\n\t\t\t\tcancelAnimationFrame(animation);\r\n\t\t\t}\r\n\t\t\tanimation = null;\r\n\t\t}\r\n\t\treturn init();\r\n\t};\r\n\r\n\tPace.restart = function () {\r\n\t\tPace.trigger(\"restart\");\r\n\t\tPace.stop();\r\n\t\treturn Pace.start();\r\n\t};\r\n\r\n\tPace.go = function () {\r\n\t\tvar start;\r\n\t\tPace.running = true;\r\n\t\tbar.render();\r\n\t\tstart = now();\r\n\t\tcancelAnimation = false;\r\n\t\treturn (animation = runAnimation(function (frameTime, enqueueNextFrame) {\r\n\t\t\tvar avg, count, done, element, elements, i, j, remaining, scaler, scalerList, sum, _j, _k, _len1, _len2, _ref2;\r\n\t\t\tremaining = 100 - bar.progress;\r\n\t\t\tcount = sum = 0;\r\n\t\t\tdone = true;\r\n\t\t\tfor (i = _j = 0, _len1 = sources.length; _j < _len1; i = ++_j) {\r\n\t\t\t\tsource = sources[i];\r\n\t\t\t\tscalerList = scalers[i] != null ? scalers[i] : (scalers[i] = []);\r\n\t\t\t\telements = (_ref2 = source.elements) != null ? _ref2 : [source];\r\n\t\t\t\tfor (j = _k = 0, _len2 = elements.length; _k < _len2; j = ++_k) {\r\n\t\t\t\t\telement = elements[j];\r\n\t\t\t\t\tscaler = scalerList[j] != null ? scalerList[j] : (scalerList[j] = new Scaler(element));\r\n\t\t\t\t\tdone &= scaler.done;\r\n\t\t\t\t\tif (scaler.done) {\r\n\t\t\t\t\t\tcontinue;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tcount++;\r\n\t\t\t\t\tsum += scaler.tick(frameTime);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tavg = sum / count;\r\n\t\t\tbar.update(uniScaler.tick(frameTime, avg));\r\n\t\t\tif (bar.done() || done || cancelAnimation) {\r\n\t\t\t\tbar.update(100);\r\n\t\t\t\tPace.trigger(\"done\");\r\n\t\t\t\treturn setTimeout(function () {\r\n\t\t\t\t\tbar.finish();\r\n\t\t\t\t\tPace.running = false;\r\n\t\t\t\t\treturn Pace.trigger(\"hide\");\r\n\t\t\t\t}, Math.max(options.ghostTime, Math.max(options.minTime - (now() - start), 0)));\r\n\t\t\t} else {\r\n\t\t\t\treturn enqueueNextFrame();\r\n\t\t\t}\r\n\t\t}));\r\n\t};\r\n\r\n\tPace.start = function (_options) {\r\n\t\textend(options, _options);\r\n\t\tPace.running = true;\r\n\t\ttry {\r\n\t\t\tbar.render();\r\n\t\t} catch (_error) {\r\n\t\t\tNoTargetError = _error;\r\n\t\t}\r\n\t\tif (!document.querySelector(\".pace\")) {\r\n\t\t\treturn setTimeout(Pace.start, 50);\r\n\t\t} else {\r\n\t\t\tPace.trigger(\"start\");\r\n\t\t\treturn Pace.go();\r\n\t\t}\r\n\t};\r\n\r\n\tif (typeof define === \"function\" && define.amd) {\r\n\t\tdefine(function () {\r\n\t\t\treturn Pace;\r\n\t\t});\r\n\t} else if (typeof exports === \"object\") {\r\n\t\tmodule.exports = Pace;\r\n\t} else {\r\n\t\tif (options.startOnPageLoad) {\r\n\t\t\tPace.start();\r\n\t\t}\r\n\t}\r\n}.call(this));\r\n"], "names": ["__extends", "child", "parent", "key", "__has<PERSON>rop", "call", "ctor", "this", "constructor", "prototype", "__super__", "__bind", "fn", "me", "apply", "arguments", "AjaxMonitor", "Bar", "ElementTracker", "EventLagMonitor", "Events", "NoTargetError", "Pace", "RequestIntercept", "SOURCE_KEYS", "Scaler", "SocketRequestTracker", "XHRRequestTracker", "addEventListener", "animation", "avgAmplitude", "bar", "cancelAnimation", "cancelAnimationFrame", "defaultOptions", "extend", "extendNative", "getFromDOM", "getIntercept", "handlePushState", "ignoreStack", "init", "now", "options", "requestAnimationFrame", "result", "runAnimation", "scalers", "shouldIgnoreURL", "shouldTrack", "source", "sources", "uniScaler", "_WebSocket", "_XDomainRequest", "_XMLHttpRequest", "_i", "_intercept", "_len", "_pushState", "_ref", "_replaceState", "_super", "__slice", "slice", "hasOwnProperty", "__indexOf", "indexOf", "item", "i", "l", "length", "Evented", "className", "catchupTime", "initialRate", "minTime", "ghostTime", "maxProgressPerFrame", "easeFactor", "startOnPageLoad", "restartOnPushState", "restartOnRequestAfter", "target", "elements", "checkInterval", "selectors", "eventLag", "minSamples", "sampleCount", "lagThreshold", "ajax", "trackMethods", "trackWebSockets", "ignoreURLs", "performance", "Date", "window", "mozRequestAnimationFrame", "webkitRequestAnimationFrame", "msRequestAnimationFrame", "mozCancelAnimationFrame", "obj", "event", "callback", "eventListeners", "on", "evt", "trigger", "setTimeout", "id", "clearTimeout", "last", "tick", "diff", "args", "val", "out", "arr", "count", "v", "sum", "Math", "abs", "json", "el", "document", "querySelector", "data", "getAttribute", "JSON", "parse", "_error", "console", "error", "handler", "ctx", "once", "_base", "bindings", "push", "off", "_results", "splice", "_ref1", "paceOptions", "progress", "monitorXHR", "_this", "req", "_open", "open", "type", "url", "async", "request", "XMLHttpRequest", "flags", "XDomainRequest", "WebSocket", "protocols", "complete", "watch", "ElementMonitor", "selector", "_j", "_len1", "_ref2", "completeCallback", "check", "DocumentMonitor", "_onreadystatechange", "states", "readyState", "onreadystatechange", "sinceLastUpdate", "rate", "catchup", "lastProgress", "Error", "getElement", "targetElement", "createElement", "body", "replace", "_custom_class_name", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "append<PERSON><PERSON><PERSON>", "finish", "update", "prog", "render", "destroy", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "progressStr", "transform", "children", "style", "lastRenderedProgress", "setAttribute", "done", "name", "binding", "to", "from", "Object", "defineProperty", "get", "configurable", "enumerable", "e", "ignore", "unshift", "ret", "shift", "track", "method", "toUpperCase", "pattern", "test", "_arg", "running", "after", "_ref3", "stillActive", "restart", "tracker", "filter", "ProgressEvent", "lengthComputable", "loaded", "total", "loading", "interactive", "avg", "interval", "points", "samples", "setInterval", "clearInterval", "frameTime", "scaling", "pow", "min", "max", "history", "pushState", "replaceState", "_k", "_len2", "_ref4", "extraSources", "stop", "start", "go", "enqueueNextFrame", "j", "scaler", "scalerList", "element", "_options", "define", "amd", "exports", "module"], "mappings": "AAKA,CAAC,WAqDa,SAAZA,EAAsBC,EAAOC,GAC5B,IAAK,IAAIC,KAAOD,EACXE,EAAUC,KAAKH,EAAQC,CAAG,IAAGF,EAAME,GAAOD,EAAOC,IAEtD,SAASG,IACRC,KAAKC,YAAcP,CACpB,CACAK,EAAKG,UAAYP,EAAOO,UACxBR,EAAMQ,UAAY,IAAIH,EACtBL,EAAMS,UAAYR,EAAOO,SAE1B,CASS,SAATE,EAAmBC,EAAIC,GACtB,OAAO,WACN,OAAOD,EAAGE,MAAMD,EAAIE,SAAS,CAC9B,CACD,CA5ED,IAAIC,EACHC,EAGAC,EACAC,EAEAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEAC,EAwQ0BC,EAvQ1BC,EAAU,GAAGC,MACb5D,EAAY,GAAG6D,eAafC,GACC,GAAGC,SACH,SAAUC,GACT,IAAK,IAAIC,EAAI,EAAGC,EAAI/D,KAAKgE,OAAQF,EAAIC,EAAGD,CAAC,GACxC,GAAIA,KAAK9D,MAAQA,KAAK8D,KAAOD,EAAM,OAAOC,EAE3C,MAAO,CAAC,CACT,EA+JD,SAASG,KA4EV,IApOAtC,EAAiB,CAChBuC,UAAW,GACXC,YAAa,IACbC,YAAa,IACbC,QAAS,IACTC,UAAW,IACXC,oBAAqB,GACrBC,WAAY,KACZC,gBAAiB,CAAA,EACjBC,mBAAoB,CAAA,EACpBC,sBAAuB,IACvBC,OAAQ,OACRC,SAAU,CACTC,cAAe,IACfC,UAAW,CAAC,OACb,EACAC,SAAU,CACTC,WAAY,GACZC,YAAa,EACbC,aAAc,CACf,EACAC,KAAM,CACLC,aAAc,CAAC,OACfC,gBAAiB,CAAA,EACjBC,WAAY,EACb,CACD,EAEApD,EAAM,WACL,IAAIkB,EACJ,OAA8J,OAAtJA,EAA8B,aAAvB,OAAOmC,aAA+C,OAAhBA,aAAmD,YAA3B,OAAOA,YAAYrD,IAAqBqD,YAAYrD,IAAI,EAAc,KAAA,GAAkBkB,EAAO,CAAC,IAAIoC,IAClL,EAEApD,EAAwBqD,OAAOrD,uBAAyBqD,OAAOC,0BAA4BD,OAAOE,6BAA+BF,OAAOG,wBAExInE,EAAuBgE,OAAOhE,sBAAwBgE,OAAOI,wBAE7DzE,EAAmB,SAAU0E,EAAKC,EAAOC,GACxC,GAAoC,YAAhC,OAAOF,EAAI1E,iBACd,OAAO0E,EAAI1E,iBAAiB2E,EAAOC,EAAU,CAAA,CAAK,EAE3C,IAWDC,EAV4B,YAA7B,OAAOH,EAAI,KAAOC,IAAqE,UAA5C,OAAOD,EAAI,KAAOC,GAAOE,gBACnEA,EAAiB,IAAIrF,EACQ,YAA7B,OAAOkF,EAAI,KAAOC,IACrBE,EAAeC,GAAGH,EAAOD,EAAI,KAAOC,EAAM,EAE3CD,EAAI,KAAOC,GAAS,SAAUI,GAC7B,OAAOF,EAAeG,QAAQL,EAAOI,CAAG,CACzC,EACAL,EAAI,KAAOC,GAAOE,eAAiBA,GAE/BA,EAAiBH,EAAI,KAAOC,GAAOE,eAExCA,EAAeC,GAAGH,EAAOC,CAAQ,CAGpC,EAE6B,MAAzB5D,IACHA,EAAwB,SAAUhC,GACjC,OAAOiG,WAAWjG,EAAI,EAAE,CACzB,EACAqB,EAAuB,SAAU6E,GAChC,OAAOC,aAAaD,CAAE,CACvB,GAGDhE,EAAe,SAAUlC,GACxB,IACAoG,EAAOtE,EAAI,EACXuE,EAAO,WACN,IACAC,EAAOxE,EAAI,EAAIsE,EACf,OAAY,IAARE,GACHF,EAAOtE,EAAI,EACJ9B,EAAGsG,EAAM,WACf,OAAOtE,EAAsBqE,CAAI,CAClC,CAAC,GAEMJ,WAAWI,EAAM,GAAKC,CAAI,CAEnC,EACA,OAAOD,EAAK,CACb,EAEApE,EAAS,WACR,IACCyD,EAAMvF,UAAU,GAAMZ,EAAMY,UAAU,GAAMoG,EAAO,GAAKpG,UAAUwD,OAASR,EAAQ1D,KAAKU,UAAW,CAAC,EAAI,GACzG,MAAwB,YAApB,OAAOuF,EAAInG,GACPmG,EAAInG,GAAKW,MAAMwF,EAAKa,CAAI,EAExBb,EAAInG,EAEb,EAEAgC,EAAS,WAGR,IAFA,IAAIhC,EAAU+C,EAAiBkE,EAC9BC,EAAMtG,UAAU,GAAMoC,EAAU,GAAKpC,UAAUwD,OAASR,EAAQ1D,KAAKU,UAAW,CAAC,EAAI,GACjFyC,EAAK,EAAGE,EAAOP,EAAQoB,OAAQf,EAAKE,EAAMF,CAAE,GAEhD,GADAN,EAASC,EAAQK,GAEhB,IAAKrD,KAAO+C,EACN9C,EAAUC,KAAK6C,EAAQ/C,CAAG,IAC/BiH,EAAMlE,EAAO/C,GACG,MAAZkH,EAAIlH,IAAoC,UAApB,OAAOkH,EAAIlH,IAA4B,MAAPiH,GAA8B,UAAf,OAAOA,EAC7EjF,EAAOkF,EAAIlH,GAAMiH,CAAG,EAEpBC,EAAIlH,GAAOiH,GAKf,OAAOC,CACR,EAEAvF,EAAe,SAAUwF,GAGxB,IAFA,IAAIC,EAAYC,EAChBC,EAAMF,EAAQ,EACT/D,EAAK,EAAGE,EAAO4D,EAAI/C,OAAQf,EAAKE,EAAMF,CAAE,GAC5CgE,EAAIF,EAAI9D,GACRiE,GAAOC,KAAKC,IAAIH,CAAC,EACjBD,CAAK,GAEN,OAAOE,EAAMF,CACd,EAEAlF,EAAa,SAAUlC,EAAKyH,GAC3B,IAAaC,EAQb,GAPW,MAAP1H,IACHA,EAAM,WAEK,MAARyH,IACHA,EAAO,CAAA,GAERC,EAAKC,SAASC,cAAc,cAAgB5H,EAAM,GAAG,EACrD,CAIA,GADA6H,EAAOH,EAAGI,aAAa,aAAe9H,CAAG,EACrC,CAACyH,EACJ,OAAOI,EAER,IACC,OAAOE,KAAKC,MAAMH,CAAI,CAIvB,CAHE,MAAOI,GAER,MAA0B,aAAnB,OAAOC,SAAuC,OAAZA,QAAmBA,QAAQC,MAAM,oCADtEF,CAC4G,EAAI,KAAA,CACrH,CAVA,CAWD,EAKC5D,EAAQ/D,UAAUiG,GAAK,SAAUH,EAAOgC,EAASC,EAAKC,GACrD,IAAIC,EAUJ,OATY,MAARD,IACHA,EAAO,CAAA,GAEa,MAAjBlI,KAAKoI,WACRpI,KAAKoI,SAAW,IAEqB,OAAjCD,EAAQnI,KAAKoI,UAAUpC,KAC3BmC,EAAMnC,GAAS,IAEThG,KAAKoI,SAASpC,GAAOqC,KAAK,CAChCL,QAASA,EACTC,IAAKA,EACLC,KAAMA,CACP,CAAC,CACF,EAEAjE,EAAQ/D,UAAUgI,KAAO,SAAUlC,EAAOgC,EAASC,GAClD,OAAOjI,KAAKmG,GAAGH,EAAOgC,EAASC,EAAK,CAAA,CAAI,CACzC,EAEAhE,EAAQ/D,UAAUoI,IAAM,SAAUtC,EAAOgC,GACxC,IAAIlE,EAAGT,EAAMkF,EACb,GAA+D,OAAhC,OAAzBlF,EAAOrD,KAAKoI,UAAoB/E,EAAK2C,GAAS,KAAA,GAApD,CAGA,GAAe,MAAXgC,EACH,OAAO,OAAOhI,KAAKoI,SAASpC,GAI5B,IAFAlC,EAAI,EACJyE,EAAW,GACJzE,EAAI9D,KAAKoI,SAASpC,GAAOhC,QAC3BhE,KAAKoI,SAASpC,GAAOlC,GAAGkE,UAAYA,EACvCO,EAASF,KAAKrI,KAAKoI,SAASpC,GAAOwC,OAAO1E,EAAG,CAAC,CAAC,EAE/CyE,EAASF,KAAKvE,CAAC,EAAE,EAGnB,OAAOyE,CAbR,CAeD,EAEAtE,EAAQ/D,UAAUmG,QAAU,WAC3B,IAAU4B,EAAYD,EAASlE,EAAST,EAAMoF,EAAOF,EACpDvC,EAAQxF,UAAU,GAAMoG,EAAO,GAAKpG,UAAUwD,OAASR,EAAQ1D,KAAKU,UAAW,CAAC,EAAI,GACrF,GAA8B,OAAzB6C,EAAOrD,KAAKoI,WAAoB/E,EAAK2C,GAAiB,CAG1D,IAFAlC,EAAI,EACJyE,EAAW,GACJzE,EAAI9D,KAAKoI,SAASpC,GAAOhC,QACKgE,GAAnCS,EAAQzI,KAAKoI,SAASpC,GAAOlC,IAAsBkE,QAAWC,EAAMQ,EAAMR,IAAOC,EAAOO,EAAMP,KAC/FF,EAAQzH,MAAa,MAAP0H,EAAcA,EAAMjI,KAAM4G,CAAI,EAE3C2B,EAASF,KADNH,EACWlI,KAAKoI,SAASpC,GAAOwC,OAAO1E,EAAG,CAAC,EAEhCA,CAAC,EAFgC,EAKjD,OAAOyE,CACR,CACD,EA/DDtE,EAiEQA,EAGRlD,EAAO2E,OAAO3E,MAAQ,GAEtB2E,OAAO3E,KAAOA,EAEda,EAAOb,EAAMkD,EAAQ/D,SAAS,EAE9BkC,EAAUrB,EAAKqB,QAAUR,EAAO,GAAID,EAAgB+D,OAAOgD,YAAa5G,EAAW,CAAC,EAG/EmB,EAAK,EAAGE,GADbE,EAAO,CAAC,OAAQ,WAAY,WAAY,aACfW,OAAQf,EAAKE,EAAMF,CAAE,GAErB,CAAA,IAApBb,EADJO,EAASU,EAAKJ,MAEbb,EAAQO,GAAUhB,EAAegB,IAOlC,SAAS7B,IAER,OADQA,EAAcX,UAAUF,YAAYM,MAAMP,KAAMQ,SAAS,CAElE,CAMA,SAASE,IACRV,KAAK2I,SAAW,CACjB,CA+EA,SAAS9H,KACRb,KAAKoI,SAAW,EACjB,CAwGA,SAASpH,KACR,IAAI4H,EACHC,EAAQ7I,KACTgB,GAAiBb,UAAUF,YAAYM,MAAMP,KAAMQ,SAAS,EAC5DoI,EAAa,SAAUE,GACtB,IACAC,EAAQD,EAAIE,KACZ,OAAQF,EAAIE,KAAO,SAAUC,EAAMC,EAAKC,GAQvC,OAPIzG,EAAYuG,CAAI,GACnBJ,EAAMxC,QAAQ,UAAW,CACxB4C,KAAMA,EACNC,IAAKA,EACLE,QAASN,CACV,CAAC,EAEKC,EAAMxI,MAAMuI,EAAKtI,SAAS,CAClC,CACD,EACAkF,OAAO2D,eAAiB,SAAUC,GAEjCR,EAAM,IAAI9F,EAAgBsG,CAAK,EAE/B,OADAV,EAAWE,CAAG,EACPA,CACR,EACA,IACCjH,EAAa6D,OAAO2D,eAAgBrG,CAAe,CAClC,CAAhB,MAAO6E,IACT,GAAuB,MAAnB9E,EAAyB,CAC5B2C,OAAO6D,eAAiB,WACvB,IACAT,EAAM,IAAI/F,EAEV,OADA6F,EAAWE,CAAG,EACPA,CACR,EACA,IACCjH,EAAa6D,OAAO6D,eAAgBxG,CAAe,CAClC,CAAhB,MAAO8E,IACV,CACA,GAAkB,MAAd/E,GAAsBV,EAAQgD,KAAKE,gBAAiB,CACvDI,OAAO8D,UAAY,SAAUN,EAAKO,GACjC,IAECX,EADgB,MAAbW,EACG,IAAI3G,EAAWoG,EAAKO,CAAS,EAE7B,IAAI3G,EAAWoG,CAAG,EAUzB,OARIxG,EAAY,QAAQ,GACvBmG,EAAMxC,QAAQ,UAAW,CACxB4C,KAAM,SACNC,IAAKA,EACLO,UAAWA,EACXL,QAASN,CACV,CAAC,EAEKA,CACR,EACA,IACCjH,EAAa6D,OAAO8D,UAAW1G,CAAU,CACxB,CAAhB,MAAO+E,IACV,CACD,CAuEA,SAASpH,KACRT,KAAK0J,SAAWtJ,EAAOJ,KAAK0J,SAAU1J,IAAI,EAC1C,IAAI6I,EAAQ7I,KACZA,KAAK6E,SAAW,GAChB9C,EAAa,EAAEoE,GAAG,UAAW,WAC5B,OAAO0C,EAAMc,MAAMpJ,MAAMsI,EAAOrI,SAAS,CAC1C,CAAC,CACF,CA2GA,SAASoJ,GAAexH,GACvB,IAAIyH,EAAUC,EAAIC,EAAOC,EAUzB,IATe,MAAX5H,IACHA,EAAU,IAEXpC,KAAK0J,SAAWtJ,EAAOJ,KAAK0J,SAAU1J,IAAI,EAC1CA,KAAK6E,SAAW,GACS,MAArBzC,EAAQ2C,YACX3C,EAAQ2C,UAAY,IAGhB+E,EAAK,EAAGC,GADbC,EAAQ5H,EAAQ2C,WACWf,OAAQ8F,EAAKC,EAAOD,CAAE,GAChDD,EAAWG,EAAMF,GACjB9J,KAAK6E,SAASwD,KAAK,IAAI1H,EAAekJ,EAAU7J,KAAK0J,QAAQ,CAAC,CAEhE,CAYA,SAAS/I,GAAekJ,EAAUI,GACjCjK,KAAK6J,SAAWA,EAChB7J,KAAKiK,iBAAmBA,EACxBjK,KAAK2I,SAAW,EAChB3I,KAAKkK,MAAM,CACZ,CA6BA,SAASC,KACR,IAAIC,EACHJ,EACAnB,EAAQ7I,KACTA,KAAK2I,SAAyD,OAA7CqB,EAAQhK,KAAKqK,OAAO9C,SAAS+C,aAAuBN,EAAQ,IAC7EI,EAAsB7C,SAASgD,mBAC/BhD,SAASgD,mBAAqB,WAI7B,OAHyC,MAArC1B,EAAMwB,OAAO9C,SAAS+C,cACzBzB,EAAMF,SAAWE,EAAMwB,OAAO9C,SAAS+C,aAEF,YAA/B,OAAOF,EAAqCA,EAAoB7J,MAAM,KAAMC,SAAS,EAAI,KAAA,CACjG,CACD,CAwCA,SAASU,GAAOyB,GACf3C,KAAK2C,OAASA,EACd3C,KAAKyG,KAAOzG,KAAKwK,gBAAkB,EACnCxK,KAAKyK,KAAOrI,EAAQgC,YACpBpE,KAAK0K,QAAU,EACf1K,KAAK2I,SAAW3I,KAAK2K,aAAe,EACjB,MAAf3K,KAAK2C,SACR3C,KAAK2I,SAAWrG,EAAOtC,KAAK2C,OAAQ,UAAU,EAEhD,CAtjB0BY,EASxBqH,MARFnL,EAAUqB,EAAeyC,CAAM,EADhCzC,EAQQA,EAQPJ,EAAIR,UAAU2K,WAAa,WAC1B,IAAIC,EACJ,GAAe,MAAX9K,KAAKsH,GAAY,CAEpB,GAAI,EADJwD,EAAgBvD,SAASC,cAAcpF,EAAQwC,MAAM,GAEpD,MAAM,IAAI9D,EAEXd,KAAKsH,GAAKC,SAASwD,cAAc,KAAK,EACtC/K,KAAKsH,GAAGpD,UAAY,mBACpBqD,SAASyD,KAAK9G,UAAYqD,SAASyD,KAAK9G,UAAU+G,QAAQ,gBAAiB,eAAe,EAC1F,IAAIC,EAA2C,KAAtB9I,EAAQ8B,UAAmB,IAAM9B,EAAQ8B,UAAY,GAC9ElE,KAAKsH,GAAG6D,UAAY,4BAA8BD,EAAqB,2FACvC,MAA5BJ,EAAcM,WACjBN,EAAcO,aAAarL,KAAKsH,GAAIwD,EAAcM,UAAU,EAE5DN,EAAcQ,YAAYtL,KAAKsH,EAAE,CAEnC,CACA,OAAOtH,KAAKsH,EACb,EAEA5G,EAAIR,UAAUqL,OAAS,WACtB,IACAjE,EAAKtH,KAAK6K,WAAW,EAErB,OADAvD,EAAGpD,UAAYoD,EAAGpD,UAAU+G,QAAQ,cAAe,eAAe,EAC1D1D,SAASyD,KAAK9G,UAAYqD,SAASyD,KAAK9G,UAAU+G,QAAQ,gBAAiB,YAAY,CAChG,EAEAvK,EAAIR,UAAUsL,OAAS,SAAUC,GAGhC,OAFAzL,KAAK2I,SAAW8C,EAChB1K,EAAKsF,QAAQ,WAAYoF,CAAI,EACtBzL,KAAK0L,OAAO,CACpB,EAEAhL,EAAIR,UAAUyL,QAAU,WACvB,IACC3L,KAAK6K,WAAW,EAAEe,WAAWC,YAAY7L,KAAK6K,WAAW,CAAC,CAG3D,CAFE,MAAOhD,GACR/G,EAAgB+G,CACjB,CACA,OAAQ7H,KAAKsH,GAAK,KAAA,CACnB,EAEA5G,EAAIR,UAAUwL,OAAS,WACtB,IAAIpE,EAASwE,EAAaC,EAAWjC,EAAIC,EAAOC,EAChD,GAA8C,MAA1CzC,SAASC,cAAcpF,EAAQwC,MAAM,EACxC,MAAO,CAAA,EAKR,IAHA0C,EAAKtH,KAAK6K,WAAW,EACrBkB,EAAY,eAAiB/L,KAAK2I,SAAW,WAExCmB,EAAK,EAAGC,GADbC,EAAQ,CAAC,kBAAmB,cAAe,cAChBhG,OAAQ8F,EAAKC,EAAOD,CAAE,GAEhDxC,EAAG0E,SAAS,GAAGC,MADTjC,EAAMF,IACgBiC,EAa7B,OAXI,CAAC/L,KAAKkM,sBAAwBlM,KAAKkM,qBAAwB,IAAMlM,KAAK2I,SAAY,KACrFrB,EAAG0E,SAAS,GAAGG,aAAa,sBAA4C,EAAhBnM,KAAK2I,UAAgB,GAAG,EAC3D,KAAjB3I,KAAK2I,SACRmD,EAAc,MAEdA,EAAc9L,KAAK2I,SAAW,GAAK,IAAM,GACzCmD,GAA+B,EAAhB9L,KAAK2I,UAErBrB,EAAG0E,SAAS,GAAGG,aAAa,gBAAiB,GAAKL,CAAW,GAE9D/K,EAAKsF,QAAQ,SAAUrG,KAAK2I,QAAQ,EAC5B3I,KAAKkM,qBAAuBlM,KAAK2I,QAC1C,EAEAjI,EAAIR,UAAUkM,KAAO,WACpB,OAAwB,KAAjBpM,KAAK2I,QACb,EA5EDjI,EA8EQA,EAQPG,GAAOX,UAAUmG,QAAU,SAAUgG,EAAMxF,GAC1C,IAAIyF,EAASxC,EAAIC,EAAOC,EAAOzB,EAC/B,GAA2B,MAAvBvI,KAAKoI,SAASiE,GAAe,CAGhC,IADA9D,EAAW,GACNuB,EAAK,EAAGC,GAFbC,EAAQhK,KAAKoI,SAASiE,IAEKrI,OAAQ8F,EAAKC,EAAOD,CAAE,GAChDwC,EAAUtC,EAAMF,GAChBvB,EAASF,KAAKiE,EAAQxM,KAAKE,KAAM6G,CAAG,CAAC,EAEtC,OAAO0B,CACR,CACD,EAEA1H,GAAOX,UAAUiG,GAAK,SAAUkG,EAAMhM,GACrC,IAAI8H,EAIJ,OAHqC,OAAhCA,EAAQnI,KAAKoI,UAAUiE,KAC3BlE,EAAMkE,GAAQ,IAERrM,KAAKoI,SAASiE,GAAMhE,KAAKhI,CAAE,CACnC,EAxBDQ,EA0BQA,GAGRmC,EAAkB0C,OAAO2D,eAEzBtG,EAAkB2C,OAAO6D,eAEzBzG,EAAa4C,OAAO8D,UAEpB3H,EAAe,SAAU0K,EAAIC,GAC5B,IAAO5M,EACP2I,EAAW,GACX,IAAK3I,KAAO4M,EAAKtM,UAChB,IACgB,MAAXqM,EAAG3M,IAAqC,YAArB,OAAO4M,EAAK5M,GACG,YAAjC,OAAO6M,OAAOC,eACjBnE,EAASF,KACRoE,OAAOC,eAAeH,EAAI3M,EAAK,CAC9B+M,IAAK,SAAW/M,GACf,OAAO,WACN,OAAO4M,EAAKtM,UAAUN,EACvB,CACA,EAAEA,CAAG,EACNgN,aAAc,CAAA,EACdC,WAAY,CAAA,CACb,CAAC,CACF,EAEAtE,EAASF,KAAMkE,EAAG3M,GAAO4M,EAAKtM,UAAUN,EAAK,EAG9C2I,EAASF,KAAK,KAAA,CAAM,CAItB,CAFE,MAAOR,GACRiF,CACD,CAED,OAAOvE,CACR,EAEAtG,EAAc,GAEdlB,EAAKgM,OAAS,WACb,IACC1M,EAAKG,UAAU,GAAMoG,EAAO,GAAKpG,UAAUwD,OAASR,EAAQ1D,KAAKU,UAAW,CAAC,EAAI,GAIlF,OAHAyB,EAAY+K,QAAQ,QAAQ,EAC5BC,EAAM5M,EAAGE,MAAM,KAAMqG,CAAI,EACzB3E,EAAYiL,MAAM,EACXD,CACR,EAEAlM,EAAKoM,MAAQ,WACZ,IACC9M,EAAKG,UAAU,GAAMoG,EAAO,GAAKpG,UAAUwD,OAASR,EAAQ1D,KAAKU,UAAW,CAAC,EAAI,GAIlF,OAHAyB,EAAY+K,QAAQ,OAAO,EAC3BC,EAAM5M,EAAGE,MAAM,KAAMqG,CAAI,EACzB3E,EAAYiL,MAAM,EACXD,CACR,EAEAvK,EAAc,SAAU0K,GAKvB,GAHc,MAAVA,IACHA,EAAS,OAEa,UAAnBnL,EAAY,GACf,MAAO,QAER,GAAI,CAACA,EAAY+B,QAAU5B,EAAQgD,KAAM,CACxC,GAAe,WAAXgI,GAAuBhL,EAAQgD,KAAKE,gBACvC,MAAO,CAAA,EACD,GAAM0E,EAAQoD,EAAOC,YAAY,EAAwD,GAApD1J,GAAU7D,KAAKsC,EAAQgD,KAAKC,aAAc2E,CAAK,EAC1F,MAAO,CAAA,CAET,CACA,MAAO,CAAA,CACR,EAGCvK,EAAUuB,GAiERH,CAjEgC,EADnCG,EAiEQA,GAGRkC,EAAa,KASbT,EAAkB,SAAUyG,GAG3B,IAFA,IAAIoE,EACJtD,EAAQ5H,EAAQgD,KAAKG,WAChBuE,EAAK,EAAGC,EAAQC,EAAMhG,OAAQ8F,EAAKC,EAAOD,CAAE,GAEhD,GAAuB,UAAnB,OADJwD,EAAUtD,EAAMF,KAEf,GAA6B,CAAC,IAA1BZ,EAAItF,QAAQ0J,CAAO,EACtB,MAAO,CAAA,CACR,MAEA,GAAIA,EAAQC,KAAKrE,CAAG,EACnB,MAAO,CAAA,EAIV,MAAO,CAAA,CACR,GAvBAnH,EAAe,WAId,OAFCmB,EADiB,MAAdA,EACU,IAAIlC,EAEXkC,CACR,GAoBa,EAAEiD,GAAG,UAAW,SAAUqH,GACtC,IAAW5G,EACVqC,EAAOuE,EAAKvE,KAAQG,EAAUoE,EAAKpE,QAAWF,EAAMsE,EAAKtE,IAC1D,OAAIzG,EAAgByG,CAAG,GAGlBnI,EAAK0M,SAA8C,CAAA,IAAlCrL,EAAQuC,uBAAyD,UAAtBjC,EAAYuG,CAAI,EAAjF,KAAA,GACCrC,EAAOpG,UAEc,WAAjB,OADJkN,EAAQtL,EAAQuC,uBAAyB,KAExC+I,EAAQ,GAEFpH,WAAW,WACjB,IAAiBwD,EAAIC,EAAc4D,EAAOpF,EAEzCqF,EADY,WAAT3E,EACWG,EAAQkB,WAAa,EAErB,GAAKN,EAAQZ,EAAQkB,aAAeN,EAAQ,EAE3D,GAAI4D,EAAa,CAIhB,IAHA7M,EAAK8M,QAAQ,EAEbtF,EAAW,GACNuB,EAAK,EAAGC,GAFb4D,EAAQ5M,EAAK6B,SAEcoB,OAAQ8F,EAAKC,EAAOD,CAAE,GAAI,CAEpD,IADAnH,EAASgL,EAAM7D,cACOrJ,EAAa,CAClCkC,EAAOgH,MAAMpJ,MAAMoC,EAAQiE,CAAI,EAC/B,KACD,CACC2B,EAASF,KAAK,KAAA,CAAM,CAEtB,CACA,OAAOE,CACR,CACD,EAAGmF,CAAK,EAEV,CAAC,EAYAjN,GAAYP,UAAUyJ,MAAQ,SAAU6D,GACvC,IACCvE,EAAOuE,EAAKvE,KAAQG,EAAUoE,EAAKpE,QAAWF,EAAMsE,EAAKtE,IAC1D,GAAIzG,CAAAA,EAAgByG,CAAG,EAQvB,OAJC4E,EAAU,IADE,WAAT7E,EACW9H,EAEAC,GAFqBgI,EAASpJ,KAAK0J,QAAQ,EAInD1J,KAAK6E,SAASwD,KAAKyF,CAAO,CAClC,EAEArN,GAAYP,UAAUwJ,SAAW,SAAUoE,GAC1C,OAAQ9N,KAAK6E,SAAW7E,KAAK6E,SAASkJ,OAAO,SAAUjB,GACtD,OAAOA,IAAMgB,CACd,CAAC,CACF,EA5BDrN,EA8BQA,GAGRW,EACC,SAA2BgI,EAASa,GACnC,IAECH,EACAC,EACAK,EACAJ,EACAnB,EAAQ7I,KAET,GADAA,KAAK2I,SAAW,EACY,MAAxBjD,OAAOsI,cAeV,IAbA3M,EACC+H,EACA,WACA,SAAUhD,GACT,OAAIA,EAAI6H,iBACCpF,EAAMF,SAAY,IAAMvC,EAAI8H,OAAU9H,EAAI+H,MAE1CtF,EAAMF,SAAWE,EAAMF,UAAY,IAAME,EAAMF,UAAY,CAErE,CAED,EAEKmB,EAAK,EAAGC,GADbC,EAAQ,CAAC,OAAQ,QAAS,UAAW,UACVhG,OAAQ8F,EAAKC,EAAOD,CAAE,GAEhDzI,EACC+H,EAFOY,EAAMF,GAIb,WAEC,OADAG,EAAiBpB,CAAK,EACdA,EAAMF,SAAW,GAC1B,CAED,OAGDyB,EAAsBhB,EAAQmB,mBAC9BnB,EAAQmB,mBAAqB,WAC5B,IAAIoD,EAOJ,OANqC,KAAhCA,EAAQvE,EAAQkB,aAA+B,IAAVqD,GACzC1D,EAAiBpB,CAAK,EACtBA,EAAMF,SAAW,KACgB,IAAvBS,EAAQkB,aAClBzB,EAAMF,SAAW,IAEoB,YAA/B,OAAOyB,EAAqCA,EAAoB7J,MAAM,KAAMC,SAAS,EAAI,KAAA,CACjG,CAEF,EAKDW,EACC,SAA8BiI,EAASa,GAQtC,IAPA,IAGCD,EACAnB,EAAQ7I,KAGJ8J,EAFL9J,KAAK2I,SAAW,EAEHoB,GADbC,EAAQ,CAAC,QAAS,SACShG,OAAQ8F,EAAKC,EAAOD,CAAE,GAEhDzI,EACC+H,EAFOY,EAAMF,GAIb,WAEC,OADAG,EAAiBpB,CAAK,EACdA,EAAMF,SAAW,GAC1B,CAED,CAEF,EAuBAiB,GAAe1J,UAAUwJ,SAAW,SAAUoE,GAC7C,OAAQ9N,KAAK6E,SAAW7E,KAAK6E,SAASkJ,OAAO,SAAUjB,GACtD,OAAOA,IAAMgB,CACd,CAAC,CACF,EAtBDlE,EAwBQA,GAWPjJ,GAAeT,UAAUgK,MAAQ,WAChC,IAAIrB,EAAQ7I,KACZ,OAAIuH,SAASC,cAAcxH,KAAK6J,QAAQ,EAChC7J,KAAKoM,KAAK,EAEV9F,WAAW,WACjB,OAAOuC,EAAMqB,MAAM,CACpB,EAAG9H,EAAQyC,SAASC,aAAa,CAEnC,EAEAnE,GAAeT,UAAUkM,KAAO,WAG/B,OAFApM,KAAKiK,iBAAiBjK,IAAI,EAC1BA,KAAKiK,iBAAmB,KAChBjK,KAAK2I,SAAW,GACzB,EAvBDhI,EAyBQA,GAIPwJ,GAAgBjK,UAAUmK,OAAS,CAClC+D,QAAS,EACTC,YAAa,GACb3E,SAAU,GACX,EALDS,EAqBQA,GAGRvJ,EACC,WACC,IAAI0N,EACHC,EACA9H,EACA+H,EACAC,EACA5F,EAAQ7I,KACTA,KAAK2I,SAAW,EAEhB8F,EAAU,GACVD,EAFM,EAGN/H,EAAOtE,EAAI,EACXoM,EAAWG,YAAY,WACtB,IACA/H,EAAOxE,EAAI,EAAIsE,EAAO,GAOtB,OANAA,EAAOtE,EAAI,EACXsM,EAAQpG,KAAK1B,CAAI,EACb8H,EAAQzK,OAAS5B,EAAQ4C,SAASE,aACrCuJ,EAAQvB,MAAM,EAEfoB,EAAM/M,EAAakN,CAAO,EACtB,EAAED,GAAUpM,EAAQ4C,SAASC,YAAcqJ,EAAMlM,EAAQ4C,SAASG,cACrE0D,EAAMF,SAAW,IACVgG,cAAcJ,CAAQ,GAErB1F,EAAMF,SAAkB,GAAK2F,EAAM,GAAlB,GAE3B,EAAG,EAAE,CACN,EAiBApN,GAAOhB,UAAUwG,KAAO,SAAUkI,EAAW/H,GA2B5C,OAtBW,MAFVA,EADU,MAAPA,EACGvE,EAAOtC,KAAK2C,OAAQ,UAAU,EAEjCkE,KACH7G,KAAKoM,KAAO,CAAA,GAETvF,IAAQ7G,KAAKyG,KAChBzG,KAAKwK,iBAAmBoE,GAEpB5O,KAAKwK,kBACRxK,KAAKyK,MAAQ5D,EAAM7G,KAAKyG,MAAQzG,KAAKwK,iBAEtCxK,KAAK0K,SAAW7D,EAAM7G,KAAK2I,UAAYvG,EAAQ+B,YAC/CnE,KAAKwK,gBAAkB,EACvBxK,KAAKyG,KAAOI,GAETA,EAAM7G,KAAK2I,WACd3I,KAAK2I,UAAY3I,KAAK0K,QAAUkE,GAEjCC,EAAU,EAAI1H,KAAK2H,IAAI9O,KAAK2I,SAAW,IAAKvG,EAAQoC,UAAU,EAC9DxE,KAAK2I,UAAYkG,EAAU7O,KAAKyK,KAAOmE,EACvC5O,KAAK2I,SAAWxB,KAAK4H,IAAI/O,KAAK2K,aAAevI,EAAQmC,oBAAqBvE,KAAK2I,QAAQ,EACvF3I,KAAK2I,SAAWxB,KAAK6H,IAAI,EAAGhP,KAAK2I,QAAQ,EACzC3I,KAAK2I,SAAWxB,KAAK4H,IAAI,IAAK/O,KAAK2I,QAAQ,EAC3C3I,KAAK2K,aAAe3K,KAAK2I,SAClB3I,KAAK2I,QACb,EAxCDzH,EA0CQA,GAaRO,EAFAH,EAFAuB,EAFArB,EAFAgB,EAFAI,EAAU,KAYV7B,EAAK0M,QAAU,CAAA,EAEfzL,EAAkB,WACjB,GAAII,EAAQsC,mBACX,OAAO3D,EAAK8M,QAAQ,CAEtB,EAEgC,MAA5BnI,OAAOuJ,QAAQC,YAClB9L,EAAasC,OAAOuJ,QAAQC,UAC5BxJ,OAAOuJ,QAAQC,UAAY,WAE1B,OADAlN,EAAgB,EACToB,EAAW7C,MAAMmF,OAAOuJ,QAASzO,SAAS,CAClD,GAGkC,MAA/BkF,OAAOuJ,QAAQE,eAClB7L,EAAgBoC,OAAOuJ,QAAQE,aAC/BzJ,OAAOuJ,QAAQE,aAAe,WAE7B,OADAnN,EAAgB,EACTsB,EAAc/C,MAAMmF,OAAOuJ,QAASzO,SAAS,CACrD,GAGDS,EAAc,CACbmE,KAAM3E,EACNoE,SAAU+E,EACVrC,SAAU4C,EACVnF,SAAUpE,CACX,GAECsB,EAAO,WACP,IAAI+G,EAAMa,EAAIsF,EAAIrF,EAAOsF,EAAOrF,EAAO2D,EAAO2B,EAG9C,IAFAvO,EAAK6B,QAAUA,EAAU,GAEpBkH,EAAK,EAAGC,GADbC,EAAQ,CAAC,OAAQ,WAAY,WAAY,aACdhG,OAAQ8F,EAAKC,EAAOD,CAAE,GAE1B,CAAA,IAAlB1H,EADJ6G,EAAOe,EAAMF,KAEZlH,EAAQyF,KAAK,IAAIpH,EAAYgI,GAAM7G,EAAQ6G,EAAK,CAAC,EAInD,IAAKmG,EAAK,EAAGC,GADbC,EAA0C,OAAjC3B,EAAQvL,EAAQmN,cAAwB5B,EAAQ,IAC9B3J,OAAQoL,EAAKC,EAAOD,CAAE,GAChDzM,EAAS2M,EAAMF,GACfxM,EAAQyF,KAAK,IAAI1F,EAAOP,CAAO,CAAC,EAIjC,OAFArB,EAAKS,IAAMA,EAAM,IAAId,EACrB8B,EAAU,GACFK,EAAY,IAAI3B,CACzB,GAAG,EAEHH,EAAKyO,KAAO,WAWX,OAVAzO,EAAKsF,QAAQ,MAAM,EACnBtF,EAAK0M,QAAU,CAAA,EACfjM,EAAImK,QAAQ,EACZlK,EAAkB,CAAA,EACD,MAAbH,IACiC,YAAhC,OAAOI,GACVA,EAAqBJ,CAAS,EAE/BA,EAAY,MAENY,EAAK,CACb,EAEAnB,EAAK8M,QAAU,WAGd,OAFA9M,EAAKsF,QAAQ,SAAS,EACtBtF,EAAKyO,KAAK,EACHzO,EAAK0O,MAAM,CACnB,EAEA1O,EAAK2O,GAAK,WACT,IAAID,EAKJ,OAJA1O,EAAK0M,QAAU,CAAA,EACfjM,EAAIkK,OAAO,EACX+D,EAAQtN,EAAI,EACZV,EAAkB,CAAA,EACVH,EAAYiB,EAAa,SAAUqM,EAAWe,GAEnCnO,EAAImH,SAGtB,IAJA,IAA+B9D,EAAa+K,EAAcC,EAAQC,EAAY5I,EAAK4C,EAAIsF,EAAWC,EAAOrF,EAEzGhD,EAAQE,EAAM,EACdkF,EAAO,CAAA,EACFtI,EAAIgG,EAAK,EAAGC,EAAQnH,EAAQoB,OAAQ8F,EAAKC,EAAOjG,EAAI,EAAEgG,EAI1D,IAHAnH,EAASC,EAAQkB,GACjBgM,EAA2B,MAAdtN,EAAQsB,GAAatB,EAAQsB,GAAMtB,EAAQsB,GAAK,GAExD8L,EAAIR,EAAK,EAAGC,GADjBxK,EAAwC,OAA5BmF,EAAQrH,EAAOkC,UAAoBmF,EAAQ,CAACrH,IACtBqB,OAAQoL,EAAKC,EAAOO,EAAI,EAAER,EAC3DW,EAAUlL,EAAS+K,GAEnBxD,IADAyD,EAA0B,MAAjBC,EAAWF,GAAaE,EAAWF,GAAME,EAAWF,GAAK,IAAI1O,EAAO6O,CAAO,GACrE3D,KACXyD,EAAOzD,OAGXpF,CAAK,GACLE,GAAO2I,EAAOnJ,KAAKkI,CAAS,GAK9B,OADApN,EAAIgK,OAAO3I,EAAU6D,KAAKkI,EADpB1H,EAAMF,CAC4B,CAAC,EACrCxF,EAAI4K,KAAK,GAAKA,GAAQ3K,GACzBD,EAAIgK,OAAO,GAAG,EACdzK,EAAKsF,QAAQ,MAAM,EACZC,WAAW,WAGjB,OAFA9E,EAAI+J,OAAO,EACXxK,EAAK0M,QAAU,CAAA,EACR1M,EAAKsF,QAAQ,MAAM,CAC3B,EAAGc,KAAK6H,IAAI5M,EAAQkC,UAAW6C,KAAK6H,IAAI5M,EAAQiC,SAAWlC,EAAI,EAAIsN,GAAQ,CAAC,CAAC,CAAC,GAEvEE,EAAiB,CAE1B,CAAC,CACF,EAEA5O,EAAK0O,MAAQ,SAAUO,GACtBpO,EAAOQ,EAAS4N,CAAQ,EACxBjP,EAAK0M,QAAU,CAAA,EACf,IACCjM,EAAIkK,OAAO,CAGZ,CAFE,MAAO7D,GACR/G,EAAgB+G,CACjB,CACA,OAAKN,SAASC,cAAc,OAAO,GAGlCzG,EAAKsF,QAAQ,OAAO,EACbtF,EAAK2O,GAAG,GAHRpJ,WAAWvF,EAAK0O,MAAO,EAAE,CAKlC,EAEsB,YAAlB,OAAOQ,QAAyBA,OAAOC,IAC1CD,OAAO,WACN,OAAOlP,CACR,CAAC,EAC4B,UAAnB,OAAOoP,QACjBC,OAAOD,QAAUpP,EAEbqB,EAAQqC,iBACX1D,EAAK0O,MAAM,CAGd,EAAE3P,KAAKE,IAAK"}
{"version": 3, "file": "tui-calendars.min.js", "sources": ["tui-calendars.min.js"], "sourcesContent": ["\"use strict\";\r\n\r\n/* eslint-disable require-jsdoc, no-unused-vars */\r\n\r\nvar CalendarList = [];\r\n\r\nfunction CalendarInfo() {\r\n\tthis.id = null;\r\n\tthis.name = null;\r\n\tthis.checked = true;\r\n\tthis.color = null;\r\n\tthis.bgColor = null;\r\n\tthis.borderColor = null;\r\n\tthis.dragBgColor = null;\r\n}\r\n\r\nfunction addCalendar(calendar) {\r\n\tCalendarList.push(calendar);\r\n}\r\n\r\nfunction findCalendar(id) {\r\n\tvar found;\r\n\r\n\tCalendarList.forEach(function (calendar) {\r\n\t\tif (calendar.id === id) {\r\n\t\t\tfound = calendar;\r\n\t\t}\r\n\t});\r\n\r\n\treturn found || CalendarList[0];\r\n}\r\n\r\nfunction hexToRGBA(hex) {\r\n\tvar radix = 16;\r\n\tvar r = parseInt(hex.slice(1, 3), radix),\r\n\t\tg = parseInt(hex.slice(3, 5), radix),\r\n\t\tb = parseInt(hex.slice(5, 7), radix),\r\n\t\ta = parseInt(hex.slice(7, 9), radix) / 255 || 1;\r\n\tvar rgba = \"rgba(\" + r + \", \" + g + \", \" + b + \", \" + a + \")\";\r\n\r\n\treturn rgba;\r\n}\r\n\r\n(function () {\r\n\tvar calendar;\r\n\tvar id = 0;\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Office\";\r\n\tcalendar.color = \"#5485e4\";\r\n\tcalendar.bgColor = \"#5485e4\";\r\n\tcalendar.dragBgColor = \"#5485e4\";\r\n\tcalendar.borderColor = \"#5485e4\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Family\";\r\n\tcalendar.color = \"#25b865\";\r\n\tcalendar.bgColor = \"#25b865\";\r\n\tcalendar.dragBgColor = \"#25b865\";\r\n\tcalendar.borderColor = \"#25b865\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Friend\";\r\n\tcalendar.color = \"#d13b4c\";\r\n\tcalendar.bgColor = \"#d13b4c\";\r\n\tcalendar.dragBgColor = \"#d13b4c\";\r\n\tcalendar.borderColor = \"#d13b4c\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Travel\";\r\n\tcalendar.color = \"#17a2b8\";\r\n\tcalendar.bgColor = \"#17a2b8\";\r\n\tcalendar.dragBgColor = \"#17a2b8\";\r\n\tcalendar.borderColor = \"#17a2b8\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Privete\";\r\n\tcalendar.color = \"#e49e3d\";\r\n\tcalendar.bgColor = \"#e49e3d\";\r\n\tcalendar.dragBgColor = \"#e49e3d\";\r\n\tcalendar.borderColor = \"#e49e3d\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Holidays\";\r\n\tcalendar.color = \"#5856d6\";\r\n\tcalendar.bgColor = \"#5856d6\";\r\n\tcalendar.dragBgColor = \"#5856d6\";\r\n\tcalendar.borderColor = \"#5856d6\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Company\";\r\n\tcalendar.color = \"#3dc7be\";\r\n\tcalendar.bgColor = \"#3dc7be\";\r\n\tcalendar.dragBgColor = \"#3dc7be\";\r\n\tcalendar.borderColor = \"#3dc7be\";\r\n\taddCalendar(calendar);\r\n\r\n\tcalendar = new CalendarInfo();\r\n\tid += 1;\r\n\tcalendar.id = String(id);\r\n\tcalendar.name = \"Birthdays\";\r\n\tcalendar.color = \"#475e77\";\r\n\tcalendar.bgColor = \"#475e77\";\r\n\tcalendar.dragBgColor = \"#475e77\";\r\n\tcalendar.borderColor = \"#475e77\";\r\n\taddCalendar(calendar);\r\n})();\r\n"], "names": ["CalendarList", "CalendarInfo", "this", "id", "name", "checked", "color", "bgColor", "borderColor", "dragBgColor", "addCalendar", "calendar", "push", "findCalendar", "found", "for<PERSON>ach", "hexToRGBA", "hex", "parseInt", "slice", "String"], "mappings": "AAAA,aAIA,IAAIA,aAAe,GAEnB,SAASC,eACRC,KAAKC,GAAK,KACVD,KAAKE,KAAO,KACZF,KAAKG,QAAU,CAAA,EACfH,KAAKI,MAAQ,KACbJ,KAAKK,QAAU,KACfL,KAAKM,YAAc,KACnBN,KAAKO,YAAc,IACpB,CAEA,SAASC,YAAYC,GACpBX,aAAaY,KAAKD,CAAQ,CAC3B,CAEA,SAASE,aAAaV,GACrB,IAAIW,EAQJ,OANAd,aAAae,QAAQ,SAAUJ,GAC1BA,EAASR,KAAOA,IACnBW,EAAQH,EAEV,CAAC,EAEMG,GAASd,aAAa,EAC9B,CAEA,SAASgB,UAAUC,GAQlB,MAFW,QAJHC,SAASD,EAAIE,MAAM,EAAG,CAAC,EADnB,EAC2B,EAId,KAHpBD,SAASD,EAAIE,MAAM,EAAG,CAAC,EAFhB,EAEwB,EAGA,KAF/BD,SAASD,EAAIE,MAAM,EAAG,CAAC,EAHhB,EAGwB,EAEW,MAD1CD,SAASD,EAAIE,MAAM,EAAG,CAAC,EAJhB,EAIwB,EAAI,KAAO,GACW,GAG3D,CAEA,CAAA,WACC,IAGAR,EAAW,IAAIV,aAEfU,EAASR,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,SAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,SAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,SAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,SAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,UAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,WAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,UAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,GAEpBA,EAAW,IAAIV,cAENE,GAAKiB,OADdjB,CACuB,EACvBQ,EAASP,KAAO,YAChBO,EAASL,MAAQ,UACjBK,EAASJ,QAAU,UACnBI,EAASF,YAAc,UACvBE,EAASH,YAAc,UACvBE,YAAYC,CAAQ,CACpB,EAAE"}